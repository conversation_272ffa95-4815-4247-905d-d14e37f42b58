RemoveKanabisProcBuilding(playerid)
{
    RemoveBuildingForPlayer(playerid, 13360, 870.539, -24.601, 64.117, 0.250);
    RemoveBuildingForPlayer(playerid, 12957, 861.523, -25.382, 62.851, 0.250);
    RemoveBuildingForPlayer(playerid, 759, 857.718, -9.882, 62.359, 0.250);
    RemoveBuildingForPlayer(playerid, 759, 853.539, -21.273, 62.406, 0.250);
    RemoveBuildingForPlayer(playerid, 12937, 873.992, -22.757, 65.101, 0.250);
}

CreateKanabisProcExt()
{
    new STREAMER_TAG_OBJECT:kanabistaxta;
    
    //drug selling
    kanabistaxta = CreateDynamicObject(19445, -2426.763671, -2783.678955, 1.927773, 360.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0x00000000);
    kanabistaxta = CreateDynamicObject(19445, -2430.242919, -2783.678955, 1.927773, 360.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0x00000000);
    kanabistaxta = CreateDynamicObject(19445, -2430.246093, -2788.517822, 0.649771, 630.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(19445, -2426.754882, -2788.519775, 0.647772, 630.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(19445, -2424.940917, -2783.649658, 3.717772, 540.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(19445, -2431.911132, -2783.649658, 3.717773, 540.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(1492, -2429.800781, -2778.916748, 2.000000, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 8869, "buildingsitevge", "ws_woodenscreen1", 0x00000000);
    kanabistaxta = CreateDynamicObject(1492, -2426.778808, -2778.916748, 2.000000, 0.000000, 0.000000, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 8869, "buildingsitevge", "ws_woodenscreen1", 0x00000000);
    kanabistaxta = CreateDynamicObject(19426, -2430.595703, -2778.926757, 3.719999, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(19426, -2431.056152, -2778.924804, 3.721997, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(19426, -2425.812988, -2778.922851, 3.723998, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(19426, -2426.003173, -2778.924804, 3.721997, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(19445, -2426.763671, -2783.678955, 5.377785, 0.000000, 89.999992, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 12821, "alleystuff", "planks01", 0x00000000);
    kanabistaxta = CreateDynamicObject(19445, -2430.242919, -2783.678955, 5.378785, 0.000000, 89.999992, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 12821, "alleystuff", "planks01", 0x00000000);
    kanabistaxta = CreateDynamicObject(19426, -2424.921142, -2787.803955, 3.724997, 0.000007, 0.000000, 179.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(2662, -2427.637207, -2779.001708, 4.980000, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(2662, -2429.388671, -2779.001708, 4.980000, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(2662, -2429.388671, -2779.001708, 4.980000, -0.000007, 0.000000, 0.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(2662, -2427.637207, -2779.001708, 4.980000, -0.000007, 0.000000, 0.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(2662, -2427.637207, -2778.831542, 4.980000, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(2662, -2429.388671, -2778.831542, 4.980000, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(2662, -2429.388671, -2778.831542, 4.980000, -0.000007, 0.000007, 0.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(2662, -2427.637207, -2778.831542, 4.980000, -0.000007, 0.000007, 0.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3306, "cunte_house1", "woodwalllight2256", 0x00000000);
    kanabistaxta = CreateDynamicObject(19930, -2429.746826, -2784.998291, 2.013711, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 18081, "cj_barb", "ab_panel_woodgrime", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 12821, "alleystuff", "Gen_Crate", 0x00000000);
    kanabistaxta = CreateDynamicObject(2199, -2429.482421, -2786.943115, 1.895540, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 2, 18081, "cj_barb", "ab_panel_woodgrime", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 5, 19480, "signsurf", "sign", 0x00000000);
    kanabistaxta = CreateDynamicObject(2199, -2430.862548, -2786.943115, 1.895540, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 2, 18081, "cj_barb", "ab_panel_woodgrime", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 5, 19480, "signsurf", "sign", 0x00000000);
    kanabistaxta = CreateDynamicObject(2199, -2431.753417, -2785.993164, 1.515540, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 2, 18081, "cj_barb", "ab_panel_woodgrime", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 5, 19480, "signsurf", "sign", 0x00000000);
    kanabistaxta = CreateDynamicObject(2199, -2431.753417, -2784.632080, 1.515540, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 2, 18081, "cj_barb", "ab_panel_woodgrime", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 5, 19480, "signsurf", "sign", 0x00000000);
    kanabistaxta = CreateDynamicObject(19918, -2429.629638, -2786.696777, 2.864146, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    kanabistaxta = CreateDynamicObject(1782, -2430.025390, -2785.199707, 2.978447, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 1, 18239, "cuntwrestcs_t", "des_metalwinwee", 0x00000000);
    kanabistaxta = CreateDynamicObject(2199, -2428.941894, -2787.363525, 1.895540, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 2, 18081, "cj_barb", "ab_panel_woodgrime", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 5, 19480, "signsurf", "sign", 0x00000000);
    kanabistaxta = CreateDynamicObject(2749, -2429.646240, -2786.564453, 2.954632, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 7650, "vgnusedcar", "lightblue2_32", 0x00000000);
    kanabistaxta = CreateDynamicObject(19918, -2430.987548, -2786.696777, 2.864146, 0.000000, 0.000007, -14.500000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    kanabistaxta = CreateDynamicObject(2749, -2430.970458, -2786.564453, 2.954632, 89.999992, 89.999992, -104.499992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 7650, "vgnusedcar", "lightblue2_32", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 18081, "cj_barb", "ab_panel_woodgrime", 0x00000000);
    kanabistaxta = CreateDynamicObject(19918, -2430.948242, -2786.696777, 2.530505, 14.499197, -89.380233, -0.155147, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    kanabistaxta = CreateDynamicObject(2749, -2431.038574, -2786.564453, 2.548542, 0.600010, 255.499969, 90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 7650, "vgnusedcar", "lightblue2_32", 0x00000000);
    kanabistaxta = CreateDynamicObject(19918, -2431.523925, -2786.033447, 2.641304, 5.199235, -89.405410, 89.849136, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    kanabistaxta = CreateDynamicObject(2749, -2431.657470, -2786.123779, 2.637720, 0.592139, 264.800476, 179.903106, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 7650, "vgnusedcar", "lightblue2_32", 0x00000000);
    kanabistaxta = CreateDynamicObject(19918, -2429.615478, -2786.693603, 2.394146, 0.000000, 0.000007, 12.500000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    kanabistaxta = CreateDynamicObject(2749, -2429.660400, -2786.567626, 2.484632, 89.999992, 89.999992, -77.499992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 7650, "vgnusedcar", "lightblue2_32", 0x00000000);
    kanabistaxta = CreateDynamicObject(19918, -2431.573730, -2784.290039, 2.474147, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    kanabistaxta = CreateDynamicObject(2749, -2431.706054, -2784.306640, 2.564631, 89.999992, 154.471191, -64.471237, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 7650, "vgnusedcar", "lightblue2_32", 0x00000000);
    kanabistaxta = CreateDynamicObject(19918, -2431.578125, -2786.274169, 3.304147, 0.000022, 0.000000, 179.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    kanabistaxta = CreateDynamicObject(2749, -2431.561523, -2786.406494, 3.394632, 89.999992, 210.844589, -30.844644, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 7650, "vgnusedcar", "lightblue2_32", 0x00000000);
    kanabistaxta = CreateDynamicObject(1718, -2429.430175, -2784.797119, 2.933711, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 16640, "a51", "a51_metal1", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 18239, "cuntwrestcs_t", "des_metalwinwee", 0x00000000);
    kanabistaxta = CreateDynamicObject(1718, -2428.820800, -2786.139892, 2.013710, 0.000000, 0.000000, -163.400009, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 16640, "a51", "a51_metal1", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 18239, "cuntwrestcs_t", "des_metalwinwee", 0x00000000);
    kanabistaxta = CreateDynamicObject(1840, -2428.693115, -2787.444091, 3.683711, 0.000000, 0.000000, 15.700000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 18901, "matclothes", "boxingcap", 0x00000000);
    kanabistaxta = CreateDynamicObject(2751, -2428.657958, -2786.708740, 3.753711, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3820, "boxhses_sfsx", "LAbluewall", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 3820, "boxhses_sfsx", "LAbluewall", 0x00000000);
    kanabistaxta = CreateDynamicObject(2751, -2428.657958, -2786.708740, 3.853712, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3820, "boxhses_sfsx", "LAbluewall", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 3820, "boxhses_sfsx", "LAbluewall", 0x00000000);
    kanabistaxta = CreateDynamicObject(2199, -2431.753417, -2783.252685, 1.515540, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 2, 18081, "cj_barb", "ab_panel_woodgrime", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 5, 19480, "signsurf", "sign", 0x00000000);
    kanabistaxta = CreateDynamicObject(2199, -2431.753417, -2781.891601, 1.515540, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 2, 18081, "cj_barb", "ab_panel_woodgrime", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 5, 19480, "signsurf", "sign", 0x00000000);
    kanabistaxta = CreateDynamicObject(19918, -2431.523925, -2783.292968, 2.641304, 5.199243, -89.405410, 89.849113, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    kanabistaxta = CreateDynamicObject(2749, -2431.657470, -2783.383300, 2.637720, 0.592139, 264.800476, 179.903060, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 7650, "vgnusedcar", "lightblue2_32", 0x00000000);
    kanabistaxta = CreateDynamicObject(19918, -2431.573730, -2781.549560, 2.474147, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    kanabistaxta = CreateDynamicObject(2749, -2431.706054, -2781.566162, 2.564631, 89.999992, 166.631469, -76.631500, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 7650, "vgnusedcar", "lightblue2_32", 0x00000000);
    kanabistaxta = CreateDynamicObject(2199, -2431.753417, -2780.371582, 1.965541, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 2, 18081, "cj_barb", "ab_panel_woodgrime", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 5, 19480, "signsurf", "sign", 0x00000000);
    kanabistaxta = CreateDynamicObject(2199, -2431.363037, -2779.961181, 0.635541, 0.000007, 0.000000, 269.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 2, 18081, "cj_barb", "ab_panel_woodgrime", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 5, 19480, "signsurf", "sign", 0x00000000);
    kanabistaxta = CreateDynamicObject(19478, -2425.458251, -2782.696533, 3.996536, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 18001, "genintintbarbera", "barberslight1", 0x00000000);
    kanabistaxta = CreateDynamicObject(19478, -2425.458251, -2784.126220, 3.996536, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 18001, "genintintbarbera", "barberslight1", 0x00000000);
    kanabistaxta = CreateDynamicObject(2747, -2424.642578, -2784.151855, 3.652909, 89.999992, 0.000011, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 4829, "airport_las", "liftdoorsac256", 0xFFFFFFFF);
    SetDynamicObjectMaterial(kanabistaxta, 1, 18800, "mroadhelix1", "road1-3", 0x00000000);
    kanabistaxta = CreateDynamicObject(19478, -2425.458251, -2783.705810, 3.996536, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 18001, "genintintbarbera", "barberslight1", 0x00000000);
    kanabistaxta = CreateDynamicObject(2747, -2424.642578, -2782.703369, 3.656816, 89.999992, 0.000011, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 4829, "airport_las", "liftdoorsac256", 0xFFFFFFFF);
    SetDynamicObjectMaterial(kanabistaxta, 1, 18800, "mroadhelix1", "road1-3", 0x00000000);
    kanabistaxta = CreateDynamicObject(19478, -2425.458251, -2783.155517, 3.996536, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 18001, "genintintbarbera", "barberslight1", 0x00000000);
    kanabistaxta = CreateDynamicObject(19922, -2425.599121, -2783.553222, 1.973837, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 18081, "cj_barb", "ab_panel_woodgrime", 0x00000000);
    kanabistaxta = CreateDynamicObject(2558, -2425.417968, -2779.595947, 3.933710, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 14701, "lahss2int2", "HS2_Curt1", 0x00000000);
    kanabistaxta = CreateDynamicObject(2558, -2425.397949, -2779.461669, 3.626722, 0.000000, 52.699989, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 14701, "lahss2int2", "HS2_Curt1", 0x00000000);
    kanabistaxta = CreateDynamicObject(2558, -2425.417968, -2786.665283, 3.933710, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 14701, "lahss2int2", "HS2_Curt1", 0x00000000);
    kanabistaxta = CreateDynamicObject(2558, -2425.397949, -2786.531005, 3.626722, -0.000007, 52.699989, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 14701, "lahss2int2", "HS2_Curt1", 0x00000000);
    kanabistaxta = CreateDynamicObject(849, -2427.776855, -2785.541503, 2.013710, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 4, 19480, "signsurf", "sign", 0x00000000);
    kanabistaxta = CreateDynamicObject(849, -2430.148193, -2785.141113, 2.013710, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 4, 19480, "signsurf", "sign", 0x00000000);
    kanabistaxta = CreateDynamicObject(1569, -2431.953125, -2781.814453, 2.000000, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 3314, "ce_burbhouse", "sw_door13", 0x00000000);
    kanabistaxta = CreateDynamicObject(2558, -2430.516113, -2784.546875, 5.853712, 270.000000, 810.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 14701, "lahss2int2", "HS2_Curt1", 0x00000000);
    kanabistaxta = CreateDynamicObject(2558, -2430.516113, -2785.896728, 5.853712, 270.000000, 810.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 14701, "lahss2int2", "HS2_Curt1", 0x00000000);
    kanabistaxta = CreateDynamicObject(2558, -2430.516113, -2787.227539, 5.853712, 270.000000, 810.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 14701, "lahss2int2", "HS2_Curt1", 0x00000000);
    kanabistaxta = CreateDynamicObject(2558, -2432.367675, -2787.839111, 3.908534, -0.000027, 987.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 14701, "lahss2int2", "HS2_Curt1", 0x00000000);
    kanabistaxta = CreateDynamicObject(2558, -2432.367675, -2786.491210, 3.837888, -0.000027, 987.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 14701, "lahss2int2", "HS2_Curt1", 0x00000000);
    kanabistaxta = CreateDynamicObject(2558, -2432.367675, -2785.162353, 3.768239, -0.000027, 987.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 14701, "lahss2int2", "HS2_Curt1", 0x00000000);
    kanabistaxta = CreateDynamicObject(19126, -2429.769775, -2786.686035, 2.310390, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 19480, "signsurf", "sign", 0x00000000);
    kanabistaxta = CreateDynamicObject(19126, -2431.379638, -2786.686035, 2.310390, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 19480, "signsurf", "sign", 0x00000000);
    kanabistaxta = CreateDynamicObject(19126, -2431.439941, -2784.255615, 2.310390, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 19480, "signsurf", "sign", 0x00000000);
    kanabistaxta = CreateDynamicObject(19126, -2431.439941, -2782.336181, 2.310390, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 19480, "signsurf", "sign", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(1271, -2430.249511, -2782.821044, 2.281842, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19627, -2425.070068, -2782.404296, 3.838089, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18634, -2425.063232, -2783.814208, 3.517288, 89.999992, 89.999992, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18635, -2425.114257, -2783.268066, 3.438674, -0.000014, 6.299999, -89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19627, -2425.070068, -2782.504394, 3.838089, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18635, -2425.114257, -2783.459960, 3.447587, -0.000014, 6.299999, -89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18633, -2425.069580, -2784.467773, 3.537307, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19627, -2425.070068, -2782.614501, 3.838089, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19627, -2425.070068, -2782.304199, 3.838089, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18633, -2425.069580, -2784.467773, 3.857620, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18635, -2425.114257, -2783.077392, 3.439774, -0.000014, 6.299999, -89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2372, -2424.388183, -2784.016845, 4.320631, 0.000000, -89.999984, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1428, -2425.417968, -2785.738037, 3.273727, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19900, -2425.858886, -2786.502929, 2.013710, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -2427.905517, -2787.789550, 2.413711, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -2426.234375, -2787.789550, 2.413711, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -2428.681152, -2781.461914, 2.013710, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -2427.201416, -2780.160644, 2.013710, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19807, -2428.703613, -2786.459228, 3.393709, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2074, -2428.556396, -2783.351318, 5.063714, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2991, -2423.862792, -2783.565673, 2.570020, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2991, -2423.862792, -2783.565673, 3.820019, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 


    kanabistaxta = CreateDynamicObject(19389, 860.575683, -22.326232, 63.938545, 0.000000, 0.000000, 70.700012, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19368, 857.551818, -21.267549, 63.945293, 0.000000, 0.000000, 70.599975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19368, 862.550781, -21.225208, 63.945293, 0.000000, 0.000000, 160.599975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19368, 863.614013, -18.206888, 63.945293, 0.000000, 0.000000, 160.599975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19368, 864.680358, -15.179142, 63.945293, 0.000000, 0.000000, 160.599975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19368, 878.278259, -15.642258, 63.945293, 0.000000, 0.000000, 160.599975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19368, 877.215087, -18.660572, 63.945293, 0.000000, 0.000000, 160.599975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19458, 857.519775, -16.252279, 63.941066, 0.000000, 0.000000, -19.400007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19458, 858.943542, -12.269092, 63.941066, 0.000000, 0.000000, -19.400007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(1499, 859.823730, -22.150074, 62.187744, 0.000000, 0.000000, -19.400016, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 1, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19458, 874.148132, -12.597970, 63.941066, 0.000000, 0.000000, 70.599990, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19458, 865.073791, -9.402565, 63.941066, 0.000000, 0.000000, 70.599990, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19458, 859.309204, -16.680925, 65.611045, 0.000000, 90.000000, -19.400007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19458, 861.893432, -17.591058, 65.601028, 0.000000, 90.000000, -19.400007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19458, 864.403442, -11.519769, 65.601028, 0.000000, 90.000000, 70.599990, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19458, 864.529113, -11.161354, 65.571037, 0.000000, 90.000000, 70.599990, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19458, 873.546691, -14.336820, 65.571037, 0.000000, 90.000000, 70.599990, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19458, 859.309204, -16.680925, 65.661048, -0.000001, 90.000007, -19.400001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 716, "gta_tree_bevhills", "trunk3", 0x00000000);
    kanabistaxta = CreateDynamicObject(19458, 861.893432, -17.591058, 65.651031, -0.000001, 90.000007, -19.400001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 716, "gta_tree_bevhills", "trunk3", 0x00000000);
    kanabistaxta = CreateDynamicObject(19458, 864.403442, -11.519769, 65.651031, 0.000007, 90.000000, 70.599983, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 716, "gta_tree_bevhills", "trunk3", 0x00000000);
    kanabistaxta = CreateDynamicObject(19458, 864.529113, -11.161354, 65.621040, 0.000007, 90.000000, 70.599983, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 716, "gta_tree_bevhills", "trunk3", 0x00000000);
    kanabistaxta = CreateDynamicObject(19458, 873.546691, -14.336820, 65.621040, 0.000007, 90.000000, 70.599983, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 716, "gta_tree_bevhills", "trunk3", 0x00000000);
    kanabistaxta = CreateDynamicObject(19368, 866.638549, -14.734575, 63.945293, 0.000000, 0.000000, 250.599975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(19368, 869.649841, -15.869979, 63.945293, 0.000000, 0.000000, -111.299995, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 14415, "carter_block_2", "mp_carter_cage", 0x00000000);
    kanabistaxta = CreateDynamicObject(1433, 859.288024, -20.261240, 62.430461, 0.000000, 0.000000, -15.700000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 16640, "a51", "Metalox64", 0x00000000);
    kanabistaxta = CreateDynamicObject(3041, 873.362670, -16.535741, 62.155323, 0.000000, 0.000000, 157.099975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 16640, "a51", "Metalox64", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 2, 1597, "centralresac1", "hedge2_128", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 3, 16640, "a51", "metpat64", 0x00000000);
    kanabistaxta = CreateDynamicObject(2607, 877.121520, -17.207826, 62.525318, 0.000000, 0.000000, 70.800003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 16640, "a51", "Metalox64", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 2, 16640, "a51", "Metalox64", 0x00000000);
    kanabistaxta = CreateDynamicObject(19997, 877.617065, -15.411379, 62.175319, 0.000000, 0.000000, -19.300008, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 16640, "a51", "Metalox64", 0x00000000);
    kanabistaxta = CreateDynamicObject(19637, 877.204895, -16.736213, 62.885295, -0.000012, 0.000036, -19.299991, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 1597, "centralresac1", "hedge2_128", 0x00000000);
    kanabistaxta = CreateDynamicObject(19637, 876.888061, -17.642248, 62.885295, -0.000012, 0.000036, -19.299991, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 1597, "centralresac1", "fuzzyplant256", 0x00000000);
    kanabistaxta = CreateDynamicObject(19903, 877.613830, -15.457737, 62.945278, 0.000000, 0.000000, 160.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 1, 16640, "a51", "Metalox64", 0x00000000);
    kanabistaxta = CreateDynamicObject(19592, 876.778991, -15.049476, 62.695335, 0.000000, 0.000000, -47.899993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 16640, "a51", "Metalox64", 0x00000000);
    kanabistaxta = CreateDynamicObject(1361, 875.524169, -13.777487, 62.351505, 0.000000, 0.000000, 161.400024, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 1, 4003, "cityhall_tr_lan", "foliage256", 0x00000000);
    kanabistaxta = CreateDynamicObject(1361, 874.130737, -13.308612, 62.351505, 0.000000, 0.000000, 161.400024, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 1, 4003, "cityhall_tr_lan", "foliage256", 0x00000000);
    kanabistaxta = CreateDynamicObject(2162, 870.501770, -11.555076, 62.135318, 0.000000, 0.000000, -19.499982, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 1486, "break_bar", "CJ_bottle2", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 16640, "a51", "Metalox64", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 2, 16640, "a51", "metpat64", 0x00000000);
    kanabistaxta = CreateDynamicObject(2164, 872.252746, -12.097869, 62.195312, 0.000000, 0.000000, -19.800001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 1486, "break_bar", "CJ_bottle2", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 1, 16640, "a51", "Metalox64", 0x00000000);
    SetDynamicObjectMaterial(kanabistaxta, 2, 16640, "a51", "a51_floorpanel1", 0x00000000);
    kanabistaxta = CreateDynamicObject(19997, 869.046203, -11.594354, 62.175319, 0.000000, 0.000000, -19.300008, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 16640, "a51", "Metalox64", 0x00000000);
    kanabistaxta = CreateDynamicObject(925, 858.998229, -16.375093, 63.187625, 0.000000, 0.000000, -19.300010, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 16640, "a51", "Metalox64", 0x00000000);
    kanabistaxta = CreateDynamicObject(925, 860.313354, -12.618778, 63.187625, 0.000000, 0.000000, -19.300010, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 16640, "a51", "Metalox64", 0x00000000);
    kanabistaxta = CreateDynamicObject(1723, 869.061889, -14.959012, 62.195312, 0.000000, 0.000000, 159.100006, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 1, 16640, "a51", "Metalox64", 0x00000000);
    kanabistaxta = CreateDynamicObject(939, 863.099792, -9.982826, 62.615341, 0.000000, 0.000000, -19.399995, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(kanabistaxta, 0, 16640, "a51", "Metalox64", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(13360, 870.539001, -24.601600, 64.117202, 0.000000, 0.000000, -22.617166, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(12957, 864.982788, -21.085136, 62.853332, 0.000000, 0.500002, -22.299989, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(12937, 873.992004, -22.757799, 65.101600, 0.000000, 0.000000, -22.617166, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(639, 856.918945, -17.940097, 63.610996, 0.000000, 0.000000, -19.599994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(639, 859.532592, -10.601466, 63.610996, 0.000000, 0.000000, -19.599994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(639, 858.411926, -13.747934, 63.610996, 0.000000, 0.000000, -19.599994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(639, 863.450805, -8.748605, 63.610996, 0.000000, 0.000000, 250.400009, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(639, 876.054443, -13.236927, 63.610996, 0.000000, 0.000000, 250.400009, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(639, 869.827453, -11.019594, 63.610996, 0.000000, 0.000000, 250.400009, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(635, 857.266418, -21.268178, 65.089378, -0.499989, -91.000022, 70.799957, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(635, 860.637756, -22.442184, 65.120498, -0.499989, -91.000022, 70.799957, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(635, 858.795959, -21.832542, 65.103050, -0.499989, -91.000022, 70.799957, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1361, 858.366577, -22.438528, 62.951499, 0.000000, 0.000000, -24.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1361, 856.523864, -21.880939, 63.061485, 0.000000, 0.000000, -24.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1810, 857.917480, -20.191947, 62.295513, 0.000000, 0.000000, 113.199989, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1810, 857.947082, -19.281616, 62.335510, 0.000000, 0.000000, 68.899993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1670, 859.270507, -20.252939, 62.956516, 0.000000, 0.000000, 91.600013, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1223, 872.625183, -17.267942, 60.675296, 0.000000, 0.000000, 63.699993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(639, 877.843383, -17.005567, 63.610996, 0.000000, 0.000000, 520.400024, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 868.638183, -11.748360, 62.995273, 0.000000, 0.000000, 70.699981, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 869.176391, -11.936756, 62.995273, 0.000000, 0.000000, 70.699981, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 868.940063, -11.854123, 63.115268, 0.000000, 0.000000, 70.699981, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 869.411193, -11.415320, 62.985286, 0.000000, 0.000000, 160.699981, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 869.361633, -11.588497, 63.115268, 0.000000, 0.000000, 70.699981, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 860.127624, -18.981599, 65.523529, 0.000000, 0.000000, 71.199996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 862.563842, -11.824931, 65.523529, 0.000000, 0.000000, 71.199996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 861.333190, -15.441134, 65.523529, 0.000000, 0.000000, 71.199996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 869.694519, -13.407261, 65.523529, 0.000000, 0.000000, 161.199996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 875.939575, -15.385578, 65.433532, 0.000000, 0.000000, 161.199996, 0, 0, -1, 200.00, 200.00);
}