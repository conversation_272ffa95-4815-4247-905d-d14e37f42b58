CreateBusExt()
{
    new STREAMER_TAG_OBJECT: bstxt;

    bstxt = CreateDynamicObject(18766, 1784.751586, -1921.391845, 12.152805, 89.999992, 180.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    bstxt = CreateDynamicObject(18766, 1784.751586, -1911.402221, 12.152805, 89.999992, 180.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    bstxt = CreateDynamicObject(18766, 1784.751586, -1901.423583, 12.152805, 89.999992, 180.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    bstxt = CreateDynamicObject(18766, 1784.761596, -1898.522949, 12.162805, 89.999992, 180.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    bstxt = CreateDynamicObject(3578, 1806.403320, -1889.729980, 12.349791, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 10370, "alleys_sfs", "ws_asphalt", 0x00000000);
    bstxt = CreateDynamicObject(3578, 1796.124511, -1889.729980, 12.349791, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 10370, "alleys_sfs", "ws_asphalt", 0x00000000);
    bstxt = CreateDynamicObject(19087, 1803.480957, -1940.193359, 12.542994, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 1975, "texttest", "kb_red", 0x00000000);
    bstxt = CreateDynamicObject(19087, 1803.510986, -1940.193359, 12.542994, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 1975, "texttest", "kb_red", 0x00000000);
    bstxt = CreateDynamicObject(19353, 1810.917480, -1938.045898, 14.259842, 0.000000, -0.000015, 179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bstxt = CreateDynamicObject(19482, 1810.825195, -1938.068115, 15.366360, 0.000007, -0.000015, 179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bstxt, 0, "RULES ((OOC))", 130, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    bstxt = CreateDynamicObject(19482, 1810.825195, -1938.068115, 14.796357, 0.000000, -0.000015, 179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bstxt, 0, "- Wajib antre, dilarang menyelak!", 130, "Arial", 17, 1, 0xFFFFFFFF, 0x00000000, 1);
    bstxt = CreateDynamicObject(19482, 1810.825195, -1938.068115, 14.446356, 0.000000, -0.000015, 179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bstxt, 0, "- Jika disconnect harap antre ulang", 130, "Arial", 17, 1, 0xFFFFFFFF, 0x00000000, 1);
    bstxt = CreateDynamicObject(19482, 1810.825195, -1938.068115, 14.096357, 0.000000, -0.000015, 179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bstxt, 0, "- Dilarang mendorong, memukul,", 130, "Arial", 17, 1, 0xFFFFFFFF, 0x00000000, 1);
    bstxt = CreateDynamicObject(19482, 1810.825195, -1938.068115, 13.846356, 0.000000, -0.000015, 179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bstxt, 0, "membuat keributan di dalam antrean", 130, "Arial", 17, 1, 0xFFFFFFFF, 0x00000000, 1);
    bstxt = CreateDynamicObject(19482, 1810.825195, -1938.068115, 13.476349, 0.000000, -0.000015, 179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bstxt, 0, "- Keluar garis, wajib antre ulang!", 130, "Arial", 17, 1, 0xFFFFFFFF, 0x00000000, 1);
    bstxt = CreateDynamicObject(18766, 1797.999633, -1939.830322, 10.414104, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    bstxt = CreateDynamicObject(18762, 1803.089843, -1939.829467, 10.686502, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    bstxt = CreateDynamicObject(18762, 1792.919189, -1939.829467, 10.686502, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    bstxt = CreateDynamicObject(19980, 1791.325439, -1941.532592, 11.519252, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bstxt, 3, "Antre dimulai dari sini\nDimohon tertib dan tidak menyelak\nWajib menjaga tata tertib yang ada", 130, "Arial", 33, 1, 0xFFFFFFFF, 0xFF000000, 1);
    bstxt = CreateDynamicObject(18765, 1756.168090, -1890.589721, 14.388359, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 10101, "2notherbuildsfe", "sl_vicwall01", 0x00000000);
    bstxt = CreateDynamicObject(18765, 1756.138061, -1895.490478, 14.368358, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 10101, "2notherbuildsfe", "sl_vicwall01", 0x00000000);
    bstxt = CreateDynamicObject(18765, 1766.057495, -1895.490478, 10.108368, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bstxt = CreateDynamicObject(18765, 1766.057495, -1890.599731, 10.118369, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bstxt = CreateDynamicObject(18765, 1769.338623, -1895.490478, 10.118369, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bstxt = CreateDynamicObject(18765, 1769.338623, -1890.599731, 10.128369, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bstxt = CreateDynamicObject(3578, 1767.543212, -1893.060668, 12.619794, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 10370, "alleys_sfs", "ws_asphalt", 0x00000000);
    bstxt = CreateDynamicObject(3578, 1767.543212, -1900.200805, 12.619794, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 10370, "alleys_sfs", "ws_asphalt", 0x00000000);
    bstxt = CreateDynamicObject(3578, 1767.543212, -1885.939819, 12.619794, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bstxt, 0, 10370, "alleys_sfs", "ws_asphalt", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(8841, 1784.646728, -1908.885742, 15.388759, 0.000030, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 1785.058349, -1922.764404, 13.267741, 0.000000, -0.000015, 179.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 1785.058349, -1914.833496, 13.267741, 0.000000, -0.000015, 179.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 1785.058349, -1905.013916, 13.267741, 0.000000, -0.000015, 179.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 1785.058349, -1896.874145, 13.267741, 0.000000, -0.000015, 179.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(984, 1784.625732, -1918.860107, 13.230842, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(984, 1784.625732, -1901.059570, 13.230842, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 1784.177368, -1896.874145, 13.267741, -0.000007, 0.000015, 0.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 1784.177368, -1904.805053, 13.267741, -0.000007, 0.000015, 0.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 1784.177368, -1914.624633, 13.267741, -0.000007, 0.000015, 0.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 1784.177368, -1922.764404, 13.267741, -0.000007, 0.000015, 0.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8843, 1802.229125, -1886.932006, 12.411387, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8843, 1802.229125, -1892.572753, 12.411387, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8843, 1778.168212, -1909.863647, 12.411387, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8843, 1799.538696, -1909.863647, 12.411387, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8843, 1788.489257, -1930.983642, 12.411387, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19957, 1811.630371, -1889.481811, 12.297898, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 1811.620727, -1889.959594, 11.766740, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, 1800.338989, -1940.192016, 12.543215, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, 1796.398071, -1940.192016, 12.543215, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1799.969116, -1940.196899, 12.985775, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, 1792.458618, -1940.192016, 12.543215, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1796.029663, -1940.196899, 12.985775, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, 1810.487182, -1938.023925, 12.891855, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1797.991455, -1939.702636, 13.281069, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1318, 1793.995117, -1940.975341, 12.496841, 180.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1318, 1800.176513, -1940.975341, 12.496841, 180.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 1776.973144, -1942.350585, 13.106476, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 1772.794555, -1942.350585, 13.106476, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 1769.403198, -1937.689697, 13.106475, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1770.216796, -1942.405517, 13.073877, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1781.076660, -1942.405517, 13.073877, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(17951, 1761.166625, -1896.684204, 14.373235, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(17951, 1761.166625, -1889.454711, 14.373235, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8843, 1760.928833, -1889.452758, 12.661390, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8843, 1760.928833, -1896.713134, 12.661390, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 1761.171630, -1895.139526, 12.346748, 0.000000, 0.000000, 90.499992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 1761.198730, -1898.289550, 12.346748, 0.000000, 0.000000, 90.499992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19957, 1761.150512, -1885.981567, 12.113161, 0.000000, 0.000000, 90.499992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19978, 1782.215454, -1900.968627, 12.027753, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19978, 1782.215454, -1918.799804, 12.027753, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19978, 1787.307128, -1918.799804, 12.027753, -0.000000, 0.000007, 89.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19978, 1787.307128, -1900.968627, 12.027753, -0.000000, 0.000007, 89.999938, 0, 0, -1, 200.00, 200.00); 
}