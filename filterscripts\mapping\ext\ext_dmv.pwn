RemoveDMVBuilding(playerid)
{
    RemoveBuildingForPlayer(playerid, 1408, 2089.229, -1349.810, 23.421, 0.250);
    RemoveBuildingForPlayer(playerid, 1408, 2089.229, -1355.260, 23.421, 0.250);
    RemoveBuildingForPlayer(playerid, 1408, 2089.229, -1360.709, 23.421, 0.250);
    RemoveBuildingForPlayer(playerid, 1408, 2089.229, -1366.160, 23.421, 0.250);
    RemoveBuildingForPlayer(playerid, 1408, 2089.229, -1371.619, 23.421, 0.250);
    RemoveBuildingForPlayer(playerid, 1408, 2110.370, -1360.709, 23.421, 0.250);
    RemoveBuildingForPlayer(playerid, 1408, 2110.379, -1366.160, 23.421, 0.250);
    RemoveBuildingForPlayer(playerid, 1408, 2110.379, -1371.619, 23.421, 0.250);
}

CreateDMVExt()
{
    new STREAMER_TAG_OBJECT: dmvstxt;

    //LOS SANTOS LS DMV
    dmvstxt = CreateDynamicObject(18981, 2097.681884, -1361.887451, 22.524269, 180.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dmvstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    dmvstxt = CreateDynamicObject(18981, 2094.361083, -1361.887451, 22.514268, 180.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dmvstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    dmvstxt = CreateDynamicObject(19445, 2081.935302, -1354.190429, 24.698316, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dmvstxt, 0, 3906, "libertyhi5", "walldirtynewa256128", 0x00000000);
    dmvstxt = CreateDynamicObject(19445, 2081.935302, -1363.819702, 24.698316, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dmvstxt, 0, 3906, "libertyhi5", "walldirtynewa256128", 0x00000000);
    dmvstxt = CreateDynamicObject(19445, 2081.905273, -1369.589599, 24.688316, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dmvstxt, 0, 3906, "libertyhi5", "walldirtynewa256128", 0x00000000);
    dmvstxt = CreateDynamicObject(19445, 2086.674560, -1349.320434, 24.698316, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dmvstxt, 0, 3906, "libertyhi5", "walldirtynewa256128", 0x00000000);
    dmvstxt = CreateDynamicObject(19445, 2096.313232, -1349.320434, 24.698316, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dmvstxt, 0, 3906, "libertyhi5", "walldirtynewa256128", 0x00000000);
    dmvstxt = CreateDynamicObject(19445, 2110.126708, -1363.979736, 24.698316, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dmvstxt, 0, 3906, "libertyhi5", "walldirtynewa256128", 0x00000000);
    dmvstxt = CreateDynamicObject(19445, 2110.116699, -1369.579833, 24.688316, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dmvstxt, 0, 3906, "libertyhi5", "walldirtynewa256128", 0x00000000);
    dmvstxt = CreateDynamicObject(19426, 2096.916259, -1359.705322, 26.296230, 90.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dmvstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    dmvstxt = CreateDynamicObject(19426, 2105.037109, -1359.705322, 26.296230, 90.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dmvstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    dmvstxt = CreateDynamicObject(18667, 2096.918212, -1359.798706, 26.322954, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(dmvstxt, 0, "DMV", 100, "Arial", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    dmvstxt = CreateDynamicObject(18667, 2105.077392, -1359.798706, 26.322954, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(dmvstxt, 0, "DMV", 100, "Arial", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(8843, 2086.872070, -1362.390014, 23.033294, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11313, 2091.953125, -1353.557250, 23.610136, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1238, 2084.484863, -1351.390014, 23.299869, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1238, 2084.484863, -1354.410156, 23.299869, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1238, 2084.484863, -1357.578857, 23.299869, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1238, 2089.315429, -1351.390014, 23.299869, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1238, 2089.315429, -1354.410156, 23.299869, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1238, 2089.315429, -1357.578857, 23.299869, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 

    // dmvstxt = CreateDynamicObject(18766, -2026.909912, -80.119285, 32.734943, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(dmvstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    // dmvstxt = CreateDynamicObject(18766, -2026.909912, -80.069274, 31.014944, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(dmvstxt, 0, 3899, "hospital2", "black", 0x00000000);
    // dmvstxt = CreateDynamicObject(19483, -2027.144531, -79.564636, 35.021461, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(dmvstxt, 0, "OF MOTOR", 90, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    // dmvstxt = CreateDynamicObject(19483, -2025.524414, -79.564636, 35.021461, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(dmvstxt, 0, "DEPARTMENT", 90, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    // dmvstxt = CreateDynamicObject(18762, -2023.147216, -80.099143, 32.731071, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(dmvstxt, 0, 3899, "hospital2", "black", 0x00000000);
    // dmvstxt = CreateDynamicObject(18762, -2030.667724, -80.099143, 32.731071, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterial(dmvstxt, 0, 3899, "hospital2", "black", 0x00000000);
    // dmvstxt = CreateDynamicObject(19483, -2026.904296, -79.564636, 35.381439, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(dmvstxt, 0, "STATE OF SAN ANDREAS", 90, "Arial", 24, 1, 0xFFFFFFFF, 0x00000000, 1);
    // dmvstxt = CreateDynamicObject(19483, -2028.524780, -79.564636, 35.021461, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(dmvstxt, 0, "VEHICLES", 90, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    // dmvstxt = CreateDynamicObject(19483, -2093.914550, -87.364624, 34.181522, 360.000000, 270.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(dmvstxt, 0, "KEEP CLEAR", 90, "Calibri", 63, 1, 0xFFFFFF00, 0x00000000, 1);
    // dmvstxt = CreateDynamicObject(19483, -2089.665283, -87.364624, 34.181522, 360.000000, 270.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(dmvstxt, 0, "KEEP CLEAR", 90, "Calibri", 63, 1, 0xFFFFFF00, 0x00000000, 1);
    // dmvstxt = CreateDynamicObject(19483, -2093.914550, -86.854606, 34.181522, 360.000000, 270.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(dmvstxt, 0, "DMV ONLY", 90, "Calibri", 63, 1, 0xFFFFFF00, 0x00000000, 1);
    // dmvstxt = CreateDynamicObject(19483, -2089.665283, -86.854606, 34.181522, 360.000000, 270.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    // SetDynamicObjectMaterialText(dmvstxt, 0, "DMV ONLY", 90, "Calibri", 63, 1, 0xFFFFFF00, 0x00000000, 1);
    // /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    // CreateDynamicObject(984, -2017.035644, -95.804435, 34.965663, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(984, -2089.307373, -80.094474, 34.945671, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(984, -2068.644775, -80.094474, 34.945671, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19866, -2078.919189, -80.074172, 34.301177, 0.000000, 0.000000, 90.199996, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19121, -2082.136718, -80.065170, 34.841499, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19121, -2075.817626, -80.065170, 34.841499, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(886, -2023.458862, -83.935226, 34.472232, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(780, -2032.474487, -84.404167, 34.469097, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(747, -2020.191162, -83.983673, 34.379665, 0.000000, 0.000000, -69.900001, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(758, -2028.383911, -84.727432, 34.438972, 0.000000, 0.000000, -79.800010, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(640, -2034.592285, -79.894729, 34.722805, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(640, -2019.211914, -79.894729, 34.722805, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(640, -2016.771606, -83.974723, 34.722805, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(640, -2037.551635, -83.974723, 34.722805, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19121, -2037.535522, -80.725158, 34.851493, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19121, -2037.535522, -87.345191, 34.851493, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19121, -2016.795410, -87.345191, 34.851493, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(19121, -2016.795410, -80.735229, 34.851493, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1256, -2032.735839, -88.273658, 34.940658, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(1256, -2023.175170, -88.273658, 34.940658, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    // CreateDynamicObject(11015, -2028.130004, -111.273002, 36.132801, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
}