static ccsst;

CreateHouseStandardInt()
{
    ccsst = CreateDynamicObject(18981, 141.893615, -738.354003, -36.132820, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-40-percent", 0x00000000);
    ccsst = CreateDynamicObject(19445, 129.573852, -744.789062, -33.902790, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    ccsst = CreateDynamicObject(3089, 129.660568, -746.504394, -34.313880, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1649, "wglass", "carshowwin2", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18642, "taser1", "metalshinydented1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 3906, "libertyhi5", "italyawalll64", 0x00000000);
    ccsst = CreateDynamicObject(3089, 129.660568, -743.524291, -34.313880, 0.000000, 0.000000, 270.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18642, "taser1", "metalshinydented1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18642, "taser1", "metalshinydented1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 3906, "libertyhi5", "italyawalll64", 0x00000000);
    ccsst = CreateDynamicObject(18762, 130.080505, -750.088317, -34.582813, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 3979, "civic01_lan", "sl_laoffblok2wall1", 0x00000000);
    ccsst = CreateDynamicObject(18762, 129.360458, -749.087890, -34.582813, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 3979, "civic01_lan", "sl_laoffblok2wall1", 0x00000000);
    ccsst = CreateDynamicObject(19445, 135.273895, -750.558166, -33.902790, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    ccsst = CreateDynamicObject(19353, 140.030563, -752.076232, -33.912857, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    ccsst = CreateDynamicObject(19353, 141.670547, -750.916137, -34.142845, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14383, "burg_1", "kit_windo_12", 0x00000000);
    ccsst = CreateDynamicObject(19353, 143.330627, -752.076232, -33.912857, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    ccsst = CreateDynamicObject(19445, 148.093887, -750.558166, -33.902790, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    ccsst = CreateDynamicObject(19325, 143.699234, -749.918151, -34.362815, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, -1, "none", "none", 0x90FFFFFF);
    ccsst = CreateDynamicObject(18762, 143.730590, -748.968017, -36.072803, 0.000000, 90.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    ccsst = CreateDynamicObject(19445, 152.903961, -745.847717, -33.902790, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 13691, "bevcunto2_lahills", "stonewall3_la", 0x00000000);
    ccsst = CreateDynamicObject(19383, 145.044754, -741.036376, -33.902866, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 13691, "bevcunto2_lahills", "stonewall3_la", 0x00000000);
    ccsst = CreateDynamicObject(19353, 143.360641, -739.527038, -33.912857, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 13691, "bevcunto2_lahills", "stonewall3_la", 0x00000000);
    ccsst = CreateDynamicObject(19445, 151.464035, -741.028381, -33.902790, 0.000000, 0.000000, 270.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 13691, "bevcunto2_lahills", "stonewall3_la", 0x00000000);
    ccsst = CreateDynamicObject(19445, 132.113983, -740.068908, -33.902790, 0.000000, 0.000000, 270.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    ccsst = CreateDynamicObject(19383, 138.504943, -740.036010, -33.902866, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    ccsst = CreateDynamicObject(19353, 141.670684, -740.037353, -33.912857, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    ccsst = CreateDynamicObject(1499, 144.271850, -741.086303, -35.632820, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    ccsst = CreateDynamicObject(1499, 137.731872, -740.075927, -35.632820, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 3306, "cunte_house1", "darkplanks1", 0x00000000);
    ccsst = CreateDynamicObject(18981, 141.893615, -738.354003, -31.682859, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    ccsst = CreateDynamicObject(18980, 130.823364, -740.028137, -31.862831, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    ccsst = CreateDynamicObject(18980, 129.563385, -740.028137, -31.842830, 0.000000, 90.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    ccsst = CreateDynamicObject(18980, 142.653366, -750.687316, -31.942832, 0.000000, 90.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    ccsst = CreateDynamicObject(18980, 152.873306, -753.697448, -31.862831, 0.000000, 90.000000, 270.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    ccsst = CreateDynamicObject(18980, 154.913253, -740.977539, -31.872831, 0.000000, 90.000000, 540.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    ccsst = CreateDynamicObject(19445, 131.013854, -735.188781, -33.902790, 0.000000, 0.000000, 360.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    ccsst = CreateDynamicObject(19445, 135.823913, -730.297851, -33.902790, 0.000000, 0.000000, 450.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    ccsst = CreateDynamicObject(19445, 131.193786, -727.937438, -33.902790, 0.000000, 0.000000, 540.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    ccsst = CreateDynamicObject(19445, 140.723663, -735.158447, -33.902790, 0.000000, 0.000000, 720.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    ccsst = CreateDynamicObject(2632, 133.767913, -731.371276, -35.632820, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14383, "burg_1", "la_carp3", 0x00000000);
    ccsst = CreateDynamicObject(2131, 140.155242, -731.043090, -35.622798, 0.000000, 0.000000, 270.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 15048, "labigsave", "ah_carp1", 0x00000000);
    ccsst = CreateDynamicObject(2267, 140.541900, -734.020019, -33.782829, 360.000000, 90.000000, -90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 14801, "lee_bdupsmain", "ahomcarpet2", 0x00000000);
    ccsst = CreateDynamicObject(19445, 143.383438, -735.158447, -33.902790, 0.000000, 0.000000, 720.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14471, "carls_kit1", "wall1", 0x00000000);
    ccsst = CreateDynamicObject(19445, 152.033386, -736.159179, -33.902790, 0.000000, 0.000000, 720.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14471, "carls_kit1", "wall1", 0x00000000);
    ccsst = CreateDynamicObject(19445, 151.873443, -740.909057, -33.902790, 0.000000, 0.000000, 810.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14471, "carls_kit1", "wall1", 0x00000000);
    ccsst = CreateDynamicObject(19445, 148.223510, -733.459106, -33.902790, 0.000007, 0.000000, 89.999977, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14471, "carls_kit1", "wall1", 0x00000000);
    ccsst = CreateDynamicObject(19445, 152.033386, -726.539916, -33.902790, 0.000000, 0.000000, 720.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14471, "carls_kit1", "wall1", 0x00000000);
    ccsst = CreateDynamicObject(18981, 155.973678, -728.543273, -36.112823, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    ccsst = CreateDynamicObject(14481, 151.140518, -739.585083, -33.622829, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-20-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 18646, "matcolours", "grey-20-percent", 0x00000000);
    ccsst = CreateDynamicObject(2632, 145.672195, -739.111694, -35.652824, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    ccsst = CreateDynamicObject(2632, 149.542175, -735.692260, -35.652824, 0.000007, 0.000000, 89.999977, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    ccsst = CreateDynamicObject(19923, 145.959884, -749.853393, -35.632820, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    ccsst = CreateDynamicObject(19927, 147.900634, -750.099792, -35.592823, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-20-percent", 0x00000000);
    ccsst = CreateDynamicObject(19929, 152.498153, -748.026550, -35.612819, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-60-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-20-percent", 0x00000000);
    ccsst = CreateDynamicObject(19929, 152.498153, -746.576354, -35.612819, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-60-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-20-percent", 0x00000000);
    ccsst = CreateDynamicObject(1703, 137.429870, -749.957092, -35.632820, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-50-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 18646, "matcolours", "blue", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 4, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 5, 18646, "matcolours", "grey-30-percent", 0x00000000);
    ccsst = CreateDynamicObject(1703, 138.629821, -746.216918, -35.632820, 0.000000, 0.000000, 270.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-50-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 18646, "matcolours", "blue", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 4, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 5, 18646, "matcolours", "grey-30-percent", 0x00000000);
    ccsst = CreateDynamicObject(1742, 133.989715, -750.562133, -35.632820, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    ccsst = CreateDynamicObject(1742, 132.309783, -750.562133, -34.712818, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    ccsst = CreateDynamicObject(19474, 135.613845, -747.092834, -35.402809, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    ccsst = CreateDynamicObject(2632, 135.997894, -746.961059, -35.632820, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14383, "burg_1", "la_carp3", 0x00000000);
    ccsst = CreateDynamicObject(2632, 135.997894, -748.840881, -35.632820, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14383, "burg_1", "la_carp3", 0x00000000);
    ccsst = CreateDynamicObject(2632, 135.997894, -745.270935, -35.622821, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14383, "burg_1", "la_carp3", 0x00000000);
    ccsst = CreateDynamicObject(19825, 138.232238, -750.442016, -33.049167, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1654, "dynamite", "clock64", 0xFFFFFFFF);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(2395, 140.541564, -732.731079, -32.602806, 360.000000, 90.000000, 270.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2566, 132.279098, -732.886413, -35.062770, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2395, 132.391510, -730.441040, -32.602806, 360.000000, 90.000000, 360.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2270, 137.021911, -730.896362, -34.012817, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2271, 138.228912, -730.874328, -34.432819, 0.000000, 0.000000, 360.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2200, 131.246047, -738.847351, -35.632820, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2163, 131.176162, -736.066894, -35.632820, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2251, 131.447509, -736.197265, -33.852828, 0.000000, 0.000000, -129.099990, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2241, 136.943130, -739.290344, -35.082805, 0.000000, 0.000000, 32.900001, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2200, 133.438659, -739.807128, -35.632820, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2517, 143.976074, -734.996826, -35.612823, 0.000000, 0.000007, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2522, 151.437622, -735.340759, -35.612823, 0.000007, 0.000000, 89.999977, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2303, 146.222946, -734.045227, -35.612823, 0.000000, 0.000007, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2200, 143.433029, -737.313720, -34.482830, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2131, 152.801147, -743.614196, -34.842884, 0.000000, 0.000000, 630.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2132, 151.374740, -749.991088, -35.632820, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2141, 147.674423, -750.019348, -33.012828, 360.000000, 450.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2141, 152.814453, -745.119445, -32.862831, 360.000000, 90.000000, 270.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2141, 145.154418, -750.079467, -33.012828, 360.000000, 630.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2341, 152.342010, -749.995666, -35.632820, 0.000000, 0.000000, 270.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2131, 152.801147, -743.614196, -37.262882, 0.000000, 0.000000, 630.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2134, 149.373229, -749.988830, -35.632820, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2125, 132.808181, -747.092651, -35.342823, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2125, 133.508209, -748.803283, -35.342823, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2125, 138.648223, -744.983215, -35.342823, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11737, 130.806015, -745.078125, -35.632820, 0.000000, 0.000000, 270.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2251, 138.825225, -749.545166, -34.802814, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2195, 130.139328, -740.651489, -35.112831, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2195, 142.469238, -740.651489, -35.112831, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2269, 133.589431, -740.647277, -34.192825, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2271, 135.174362, -740.640258, -33.892818, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2069, 130.022476, -742.748718, -35.602806, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2108, 136.641876, -731.504028, -31.122823, 0.000000, 180.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 136.078903, -745.595703, -32.112834, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 150.188949, -745.595703, -32.112834, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 137.998947, -735.855712, -32.112834, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 148.668991, -733.895690, -32.112834, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00);
}

CreateHouseKontemInt()
{
    static hkt;
    hkt = CreateDynamicObject(19376, 1964.180053, -2397.075683, -10.000000, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    hkt = CreateDynamicObject(19376, 1964.180053, -2387.460693, -10.000000, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 16640, "a51", "ws_stationfloor", 0x00000000);
    hkt = CreateDynamicObject(19376, 1974.671508, -2387.460693, -10.000000, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 16640, "a51", "ws_stationfloor", 0x00000000);
    hkt = CreateDynamicObject(14407, 1974.822265, -2383.557373, -9.582031, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    hkt = CreateDynamicObject(19376, 1974.221069, -2382.605957, -4.978515, 0.000000, 180.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 6332, "rodeo01_law2", "rodwall01_LAw2", 0x00000000);
    hkt = CreateDynamicObject(19376, 1974.671508, -2390.193359, -6.632812, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    hkt = CreateDynamicObject(19376, 1974.671508, -2390.203369, -6.466796, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-60-percent", 0x00000000);
    hkt = CreateDynamicObject(19376, 1964.601684, -2382.605957, -4.978515, 0.000000, 180.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 6332, "rodeo01_law2", "rodwall01_LAw2", 0x00000000);
    hkt = CreateDynamicObject(19376, 1959.748291, -2387.261474, -4.978515, 0.000000, 180.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 6332, "rodeo01_law2", "rodwall01_LAw2", 0x00000000);
    hkt = CreateDynamicObject(19376, 1959.748291, -2396.862548, -4.978515, 0.000000, 180.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 6332, "rodeo01_law2", "rodwall01_LAw2", 0x00000000);
    hkt = CreateDynamicObject(19376, 1964.609375, -2400.766357, -4.978515, 0.000000, 180.000000, 450.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 6332, "rodeo01_law2", "rodwall01_LAw2", 0x00000000);
    hkt = CreateDynamicObject(19376, 1969.409667, -2397.022705, -4.978515, 0.000000, 180.000000, 540.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 6332, "rodeo01_law2", "rodwall01_LAw2", 0x00000000);
    hkt = CreateDynamicObject(19376, 1974.149780, -2392.278076, -4.978515, 0.000000, 180.000000, 630.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 6332, "rodeo01_law2", "rodwall01_LAw2", 0x00000000);
    hkt = CreateDynamicObject(19376, 1979.002563, -2387.444580, -4.978515, 0.000000, 180.000000, 720.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 6332, "rodeo01_law2", "rodwall01_LAw2", 0x00000000);
    hkt = CreateDynamicObject(19376, 1964.250488, -2387.460693, -6.632812, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    hkt = CreateDynamicObject(19376, 1964.190185, -2397.055419, -6.632812, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    hkt = CreateDynamicObject(19376, 1964.249389, -2387.490722, -6.466796, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-60-percent", 0x00000000);
    hkt = CreateDynamicObject(19376, 1964.179321, -2397.121582, -6.466796, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    hkt = CreateDynamicObject(19357, 1978.376098, -2383.799560, -6.466796, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-60-percent", 0x00000000);
    hkt = CreateDynamicObject(19376, 1976.523315, -2385.598876, -11.980468, 0.000000, 180.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 10386, "mountainsfs", "ws_stonewall", 0x00000000);
    hkt = CreateDynamicObject(19355, 1971.585693, -2390.305175, -8.484375, 180.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 6332, "rodeo01_law2", "rodwall01_LAw2", 0x00000000);
    hkt = CreateDynamicObject(19395, 1971.605712, -2387.792724, -8.166015, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 6332, "rodeo01_law2", "rodwall01_LAw2", 0x00000000);
    hkt = CreateDynamicObject(19355, 1971.605712, -2393.485839, -8.484375, 180.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 6332, "rodeo01_law2", "rodwall01_LAw2", 0x00000000);
    hkt = CreateDynamicObject(19395, 1967.726318, -2392.281982, -8.187500, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 6332, "rodeo01_law2", "rodwall01_LAw2", 0x00000000);
    hkt = CreateDynamicObject(19376, 1961.350341, -2392.278076, -4.978515, 0.000000, 180.000000, 450.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 6332, "rodeo01_law2", "rodwall01_LAw2", 0x00000000);
    hkt = CreateDynamicObject(19395, 1967.726318, -2392.281982, -4.677734, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    hkt = CreateDynamicObject(19376, 1964.190185, -2387.460693, -3.140625, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    hkt = CreateDynamicObject(19376, 1964.190185, -2397.143310, -3.140625, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    hkt = CreateDynamicObject(19376, 1974.669921, -2387.460693, -3.140625, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    hkt = CreateDynamicObject(19325, 1969.048583, -2383.717041, -4.597656, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, -1, "none", "none", 0x90FFFFFF);
    hkt = CreateDynamicObject(18762, 1971.960205, -2385.795898, -9.208984, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    hkt = CreateDynamicObject(19426, 1975.110839, -2385.403808, -8.015625, -57.800064, 179.999984, -90.000007, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    hkt = CreateDynamicObject(19445, 1961.368041, -2392.243652, -4.687500, 180.000000, 360.000000, -90.000022, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    hkt = CreateDynamicObject(19445, 1967.975585, -2381.230224, -8.027343, 89.999992, 90.000007, -90.000022, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 10386, "mountainsfs", "ws_stonewall", 0x00000000);
    hkt = CreateDynamicObject(19445, 1969.635498, -2382.951904, -8.027343, 89.999992, 180.000000, -89.999977, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 10386, "mountainsfs", "ws_stonewall", 0x00000000);
    hkt = CreateDynamicObject(19445, 1971.337768, -2381.230224, -8.027343, 89.999992, 90.000007, -90.000022, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 10386, "mountainsfs", "ws_stonewall", 0x00000000);
    hkt = CreateDynamicObject(19445, 1958.847290, -2385.183105, -8.027343, 89.999992, 135.000000, -45.000015, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 10386, "mountainsfs", "ws_stonewall", 0x00000000);
    hkt = CreateDynamicObject(19445, 1960.568969, -2383.523193, -8.027343, 89.999992, 225.000015, -44.999992, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 10386, "mountainsfs", "ws_stonewall", 0x00000000);
    hkt = CreateDynamicObject(19445, 1958.847290, -2381.821044, -8.027343, 89.999992, 135.000000, -45.000015, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 10386, "mountainsfs", "ws_stonewall", 0x00000000);
    hkt = CreateDynamicObject(19445, 1964.626708, -2382.631347, -8.183593, 180.000000, 360.000000, -90.000022, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14383, "burg_1", "kit_windo_12", 0x00000000);
    hkt = CreateDynamicObject(19426, 1973.646972, -2385.443847, -8.939453, -57.800064, 179.999984, -90.000007, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    hkt = CreateDynamicObject(19426, 1973.646972, 0.000000, -8.324218, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    hkt = CreateDynamicObject(19426, 1975.110839, -2382.731201, -8.015625, -57.800071, 179.999984, -89.999984, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    hkt = CreateDynamicObject(19426, 1973.646972, -2382.771240, -8.939453, -57.800071, 179.999984, -89.999984, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    hkt = CreateDynamicObject(19376, 1964.609375, -2400.746337, -11.951171, 0.000000, 180.000000, 450.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    hkt = CreateDynamicObject(19445, 1959.766845, -2397.171630, -8.183593, 180.000000, 360.000000, -0.000022, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14383, "burg_1", "kit_windo_12", 0x00000000);
    hkt = CreateDynamicObject(19445, 1958.353271, -2402.212890, -11.482421, 89.999992, 154.471221, -64.471214, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 10386, "mountainsfs", "ws_stonewall", 0x00000000);
    hkt = CreateDynamicObject(19445, 1960.074951, -2400.552978, -11.482421, 89.999992, 244.471252, -64.471199, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 10386, "mountainsfs", "ws_stonewall", 0x00000000);
    hkt = CreateDynamicObject(19445, 1958.353271, -2398.850830, -11.482421, 89.999992, 154.471221, -64.471214, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 10386, "mountainsfs", "ws_stonewall", 0x00000000);
    hkt = CreateDynamicObject(19445, 1962.096313, -2402.283447, -11.544921, 89.999992, 196.533340, -16.533351, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 10386, "mountainsfs", "ws_stonewall", 0x00000000);
    hkt = CreateDynamicObject(19445, 1960.436401, -2400.561767, -11.544921, 89.999992, 286.533355, -16.533344, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 10386, "mountainsfs", "ws_stonewall", 0x00000000);
    hkt = CreateDynamicObject(19445, 1958.734252, -2402.283447, -11.544921, 89.999992, 196.533340, -16.533351, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 10386, "mountainsfs", "ws_stonewall", 0x00000000);
    hkt = CreateDynamicObject(19376, 1969.399658, -2397.022705, -11.972656, 0.000000, 180.000000, 540.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    hkt = CreateDynamicObject(19445, 1977.254638, -2385.690673, -8.183593, 360.000000, 360.000000, -90.000022, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14471, "carls_kit1", "wall1", 0x00000000);
    hkt = CreateDynamicObject(19445, 1978.953979, -2390.263183, -8.183593, 360.000000, 360.000000, -0.000022, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14471, "carls_kit1", "wall1", 0x00000000);
    hkt = CreateDynamicObject(19445, 1976.542846, -2392.253417, -8.183593, 360.000000, 360.000000, 89.999977, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14471, "carls_kit1", "wall1", 0x00000000);
    hkt = CreateDynamicObject(1499, 1966.945800, -2392.277099, -9.951171, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    SetDynamicObjectMaterial(hkt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    hkt = CreateDynamicObject(1499, 1971.604980, -2388.527343, -9.933593, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-40-percent", 0x00000000);
    SetDynamicObjectMaterial(hkt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    hkt = CreateDynamicObject(19445, 1964.626708, -2382.631347, -4.687500, 180.000000, 360.000000, -90.000022, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14383, "burg_1", "kit_windo_12", 0x00000000);
    hkt = CreateDynamicObject(18762, 1969.270019, -2386.876953, -3.914062, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    hkt = CreateDynamicObject(18762, 1969.659667, -2392.327636, -3.914062, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    hkt = CreateDynamicObject(1499, 1966.946777, -2392.287109, -6.408203, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    SetDynamicObjectMaterial(hkt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    hkt = CreateDynamicObject(1702, 1963.898315, -2383.692871, -9.914062, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(hkt, 1, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    hkt = CreateDynamicObject(1702, 1967.358398, -2385.244384, -9.914062, 0.000000, 0.000000, 270.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(hkt, 1, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    hkt = CreateDynamicObject(19445, 1968.357543, -2384.262695, -8.183593, 180.000000, 360.000000, -0.000022, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    hkt = CreateDynamicObject(19997, 1967.162963, -2384.045654, -9.933593, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    hkt = CreateDynamicObject(19474, 1964.182617, -2386.115722, -9.914062, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-40-percent", 0x00000000);
    SetDynamicObjectMaterial(hkt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(hkt, 2, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    hkt = CreateDynamicObject(2566, 1966.821289, -2396.022705, -9.402343, 0.000000, 0.000000, -90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 3584, "comedprj1_la", "walljunkdet1", 0x00000000);
    SetDynamicObjectMaterial(hkt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(hkt, 2, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    hkt = CreateDynamicObject(2395, 1969.255737, -2396.136962, -7.191406, 0.000000, 90.000000, 630.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-40-percent", 0x00000000);
    hkt = CreateDynamicObject(2167, 1966.872924, -2400.674560, -9.873046, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    hkt = CreateDynamicObject(2164, 1965.523193, -2400.584228, -9.914062, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    hkt = CreateDynamicObject(2632, 1967.294067, -2397.695800, -9.914062, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    hkt = CreateDynamicObject(2091, 1961.870239, -2392.655029, -9.914062, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(hkt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(hkt, 2, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    hkt = CreateDynamicObject(19939, 1961.751342, -2393.086914, -8.748046, 90.000000, 180.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-40-percent", 0x00000000);
    hkt = CreateDynamicObject(19939, 1961.751342, -2393.086914, -9.314453, 90.000000, 180.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-40-percent", 0x00000000);
    hkt = CreateDynamicObject(2632, 1962.783935, -2393.345214, -9.914062, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    hkt = CreateDynamicObject(19376, 1976.811157, -2390.509521, -9.980468, 0.000000, 90.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    hkt = CreateDynamicObject(2632, 1976.734863, -2387.785156, -9.914062, 0.000000, 0.000000, 270.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    hkt = CreateDynamicObject(19923, 1965.874633, -2382.756835, -6.380859, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    hkt = CreateDynamicObject(19929, 1960.274780, -2387.192626, -6.380859, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 1, 18646, "matcolours", "grey-30-percent", 0x00000000);
    hkt = CreateDynamicObject(19929, 1960.274780, -2387.192626, -3.113281, 0.000000, 180.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 1, 18646, "matcolours", "grey-30-percent", 0x00000000);
    hkt = CreateDynamicObject(19929, 1968.405517, -2384.851806, -6.380859, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 1, 18646, "matcolours", "grey-30-percent", 0x00000000);
    hkt = CreateDynamicObject(2566, 1962.375366, -2399.978271, -5.886718, -0.000007, 0.000000, 89.999961, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 3584, "comedprj1_la", "walljunkdet1", 0x00000000);
    SetDynamicObjectMaterial(hkt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(hkt, 2, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    hkt = CreateDynamicObject(2395, 1959.940917, -2399.864013, -3.675781, -0.000007, 90.000000, 89.999961, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-40-percent", 0x00000000);
    hkt = CreateDynamicObject(2632, 1961.902587, -2398.305175, -6.398437, 0.000007, 0.000000, -89.999992, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    hkt = CreateDynamicObject(2161, 1969.314208, -2394.703613, -5.234375, 0.000000, 0.000000, 270.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(hkt, 2, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    hkt = CreateDynamicObject(2161, 1969.314208, -2396.013916, -6.160156, 0.000000, 0.000000, 270.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(hkt, 2, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    hkt = CreateDynamicObject(2204, 1961.222045, -2392.369628, -6.380859, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    hkt = CreateDynamicObject(2167, 1965.650390, -2400.722656, -6.380859, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    hkt = CreateDynamicObject(2163, 1967.440551, -2400.693115, -6.380859, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(hkt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(3089, 1959.832519, -2386.408935, -8.580078, 0.000000, 0.000000, 270.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3089, 1959.832519, -2389.291259, -8.580078, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1731, 1959.989257, -2390.212646, -8.103515, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1731, 1959.989257, -2385.673339, -8.103515, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2251, 1967.023559, -2384.129394, -8.210937, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2125, 1962.709960, -2384.407470, -9.619140, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2241, 1961.216186, -2383.632812, -9.443359, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2241, 1960.235351, -2390.883789, -9.443359, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2269, 1964.389526, -2391.681152, -8.382812, 0.000000, 0.000000, 900.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1965.080322, -2392.611572, -8.406250, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2241, 1960.525512, -2400.045654, -9.443359, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2522, 1978.382568, -2387.595703, -9.894531, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2517, 1977.327636, -2391.490722, -9.894531, 0.000000, 0.000000, 270.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2169, 1974.909301, -2391.782958, -9.832031, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2739, 1972.214965, -2390.937255, -9.894531, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2514, 1973.288574, -2391.630126, -9.894531, 0.000000, 0.000000, 180.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2131, 1961.367675, -2383.202392, -6.380859, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2132, 1963.398193, -2383.241210, -6.408203, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19924, 1965.952636, -2383.770507, -3.328125, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2241, 1968.897338, -2400.296875, -5.919921, 0.000000, 0.000000, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2270, 1968.832397, -2398.558593, -4.710937, 0.000000, 0.000000, -90.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1970.632324, -2386.885986, -3.189453, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1967.552368, -2396.547119, -3.189453, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1962.701904, -2388.175537, -6.660156, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1977.240722, -2389.015869, -6.660156, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1962.341308, -2396.705810, -6.660156, 0.000000, 0.000000, 90.000000, -1, 0, -1, 200.00, 200.00);
}

CreateHouseModernInt()
{
    static mmsxt;

    mmsxt = CreateDynamicObject(19379, 1472.656005, -1086.040527, 212.352462, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 16640, "a51", "ws_stationfloor", 0x00000000);
    mmsxt = CreateDynamicObject(19325, 1467.951416, -1090.698486, 213.903411, 0.000038, 0.000000, 89.999885, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, -1, "none", "none", 0x90FFFFFF);
    mmsxt = CreateDynamicObject(19379, 1472.656005, -1085.960449, 215.922164, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1470.737060, -1082.597900, 214.128219, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1467.521606, -1087.783935, 209.918624, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-40-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19940, 1473.738647, -1090.548828, 213.358261, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    mmsxt = CreateDynamicObject(3089, 1466.096557, -1081.044189, 213.858261, 0.000076, 0.000000, 89.999771, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 1, 18646, "matcolours", "grey-20-percent", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 2, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1472.837036, -1090.686035, 214.088180, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-60-percent", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1474.473632, -1090.552124, 209.918624, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1473.357543, -1090.687988, 214.128219, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-60-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19940, 1472.487670, -1090.548828, 213.360275, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    mmsxt = CreateDynamicObject(19940, 1473.738647, -1090.338623, 213.148361, 0.000083, 90.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    mmsxt = CreateDynamicObject(19940, 1472.487426, -1090.336669, 213.148361, 0.000083, 90.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    mmsxt = CreateDynamicObject(1499, 1474.779785, -1084.202148, 212.438400, 0.000076, 0.000000, 89.999771, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 1, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1476.288696, -1084.960571, 214.098068, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1473.078613, -1084.967529, 211.258285, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-60-percent", 0x00000000);
    mmsxt = CreateDynamicObject(1742, 1476.759277, -1084.918945, 212.428390, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 2, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1473.078613, -1084.687255, 211.258285, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-60-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1476.288696, -1084.690307, 214.098068, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1471.584594, -1089.650268, 212.498458, -89.999992, 90.000122, 89.999961, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1467.367797, -1089.021118, 214.098068, 0.000000, -0.000090, 179.999450, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(19448, 1477.968994, -1080.240844, 214.158309, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1467.521606, -1090.153808, 209.918624, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-40-percent", 0x00000000);
    mmsxt = CreateDynamicObject(1499, 1477.000244, -1077.932495, 212.438400, 0.000000, -0.000076, 179.999542, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    mmsxt = CreateDynamicObject(19379, 1483.146240, -1086.040527, 212.352462, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 16640, "a51", "ws_stationfloor", 0x00000000);
    mmsxt = CreateDynamicObject(19325, 1494.591308, -1066.608032, 217.913375, 0.000038, 0.000000, 89.999885, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, -1, "none", "none", 0x90FFFFFF);
    mmsxt = CreateDynamicObject(19360, 1467.367797, -1085.821411, 214.098068, 0.000000, -0.000090, 179.999450, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1479.448242, -1084.970581, 214.098068, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19379, 1462.155517, -1085.960449, 215.922164, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1471.950439, -1085.113769, 209.918624, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1474.633056, -1085.110717, 209.918624, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1474.711303, -1084.552490, 209.918624, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1482.598266, -1084.970581, 214.098068, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(14409, 1482.789794, -1087.115234, 212.587966, 0.000076, 0.000000, 89.999771, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 1, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    mmsxt = CreateDynamicObject(19353, 1488.440307, -1067.500000, 217.838729, 0.000000, 0.000045, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19353, 1488.440307, -1070.609985, 217.838729, 0.000000, 0.000045, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19325, 1476.271240, -1090.698486, 213.903411, 0.000038, 0.000000, 89.999885, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, -1, "none", "none", 0x90FFFFFF);
    mmsxt = CreateDynamicObject(19360, 1485.738159, -1084.970581, 214.098068, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1486.408203, -1086.610961, 215.818054, 0.000083, 90.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-60-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1486.408203, -1089.970703, 215.818054, 0.000083, 90.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-60-percent", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1479.501342, -1085.142944, 213.078628, -0.000000, 0.000069, -0.000060, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1479.501342, -1085.142944, 210.358627, -0.000000, 0.000069, -0.000060, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1476.596923, -1072.482421, 217.578048, -0.000061, -0.000013, -89.999893, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1481.404541, -1080.270385, 216.218429, -89.999992, 90.000122, 89.999961, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1481.147216, -1089.021118, 214.098068, 0.000083, -0.000012, 89.999656, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1479.627075, -1090.581054, 214.098068, 0.000000, -0.000098, 179.999374, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1484.207397, -1089.021118, 214.098068, 0.000083, -0.000012, 89.999656, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1483.211181, -1085.142944, 213.078598, -0.000000, 0.000069, -0.000060, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1481.147216, -1089.021118, 217.578048, 0.000091, -0.000012, 89.999633, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1479.627075, -1090.581054, 217.578048, 0.000000, -0.000105, 179.999328, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1484.207397, -1089.021118, 217.578048, 0.000091, -0.000012, 89.999633, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1487.407226, -1089.021118, 217.578048, 0.000091, -0.000012, 89.999633, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1488.057128, -1086.540405, 217.578048, 0.000015, -0.000090, 179.999404, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(19379, 1483.025512, -1080.150512, 216.032485, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19379, 1483.025512, -1080.200561, 215.902481, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19445, 1477.904296, -1085.593505, 217.848434, 0.000000, 0.000076, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19445, 1477.844360, -1085.593505, 217.588455, 0.000000, 0.000076, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19445, 1477.844360, -1090.513671, 217.598449, 0.000076, 0.000000, 89.999771, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19379, 1483.025512, -1070.701293, 216.032485, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19379, 1493.465820, -1070.701293, 216.032485, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19445, 1488.354248, -1080.193969, 217.848434, 0.000000, 0.000076, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1488.057128, -1089.720214, 217.578048, 0.000015, -0.000090, 179.999404, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1489.757080, -1085.010375, 217.578048, -0.000061, -0.000013, -89.999893, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(19448, 1477.799438, -1077.379272, 217.808303, 0.000007, -0.000076, 179.999511, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14383, "burg_1", "kit_windo_12", 0x00000000);
    mmsxt = CreateDynamicObject(19391, 1488.366699, -1073.773193, 217.868469, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1478.541625, -1084.412841, 216.148605, -0.000000, 0.000069, -0.000060, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1481.011596, -1084.412841, 216.148605, -0.000000, 0.000069, -0.000060, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1483.762573, -1084.192626, 216.148605, 0.000000, -0.000105, 179.999237, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1481.292602, -1084.192626, 216.148605, 0.000000, -0.000105, 179.999237, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    mmsxt = CreateDynamicObject(19445, 1477.904296, -1067.752807, 217.848434, 0.000000, 0.000075, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19445, 1482.724609, -1065.853759, 217.848434, 0.000076, 0.000000, 89.999771, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19445, 1483.575439, -1065.853759, 217.848434, 0.000076, 0.000000, 89.999771, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19445, 1488.365966, -1089.763549, 217.848434, 0.000000, -0.000076, 179.999542, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19445, 1482.043212, -1089.162963, 217.848434, -0.000076, 0.000000, -89.999771, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1465.777954, -1084.317138, 214.128219, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1466.036499, -1082.850708, 214.098068, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1465.866333, -1083.000854, 212.758102, 0.000076, 90.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1467.384765, -1082.840698, 211.108139, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1465.697875, -1081.326538, 211.118209, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1466.036499, -1079.649902, 214.098068, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1467.647827, -1079.229492, 214.098068, -0.000083, 0.000000, -89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19379, 1463.995483, -1076.428710, 212.772323, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 16640, "a51", "ws_stationfloor", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1469.207397, -1081.081176, 214.098068, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19379, 1463.869506, -1076.729003, 212.502426, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 16640, "a51", "ws_stationfloor", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1467.647827, -1081.160888, 211.098068, -0.000083, 0.000000, -89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 16640, "a51", "ws_stationfloor", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1467.507690, -1081.461181, 210.818161, -0.000083, 0.000000, -89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 16640, "a51", "ws_stationfloor", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1479.217407, -1090.781372, 216.038070, 0.000083, 89.999984, 89.999656, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1479.487670, -1090.781372, 215.858078, 0.000083, 89.999984, 89.999656, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1467.521606, -1084.054199, 209.918624, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-40-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1469.207397, -1077.871826, 214.098068, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1479.626953, -1066.161743, 217.578048, 0.000091, -0.000012, 89.999633, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1478.206909, -1067.761840, 217.578048, 0.000015, -0.000090, 179.999404, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1476.596923, -1072.482421, 217.578048, -0.000061, -0.000013, -89.999893, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(19379, 1462.157470, -1076.330932, 215.922164, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1473.109619, -1080.230590, 214.098068, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    mmsxt = CreateDynamicObject(19391, 1474.776855, -1083.442504, 214.158493, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1473.256225, -1082.594848, 214.128219, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19379, 1483.025512, -1070.701293, 219.652511, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19379, 1483.025512, -1080.121093, 219.652511, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1469.613403, -1082.742553, 209.918624, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1469.072875, -1079.450561, 209.918624, -0.000083, 0.000000, -89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1471.615356, -1082.739501, 209.918624, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19448, 1477.968994, -1076.240356, 214.158309, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19379, 1472.656005, -1076.407104, 212.352462, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 16640, "a51", "ws_stationfloor", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1473.112670, -1079.430053, 214.098068, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1473.256225, -1081.924194, 214.128219, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(2298, 1477.165527, -1081.399291, 212.434249, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 16640, "a51", "ws_stationfloor", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 1, 18646, "matcolours", "grey-20-percent", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 2, 18646, "matcolours", "grey-20-percent", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 3, 3820, "boxhses_sfsx", "tanboard1", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 12, 18646, "matcolours", "grey-20-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19379, 1472.656494, -1076.330932, 215.922164, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    mmsxt = CreateDynamicObject(2323, 1476.375488, -1079.596557, 212.404342, -0.000083, 0.000000, -89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 1, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 2, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    mmsxt = CreateDynamicObject(19391, 1476.277343, -1077.921997, 214.158493, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1473.066162, -1077.923828, 214.128219, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1476.276489, -1074.764038, 214.128219, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1473.587890, -1076.261596, 214.098068, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1473.065795, -1074.764038, 214.128219, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19379, 1472.656005, -1073.181030, 212.356491, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 16640, "a51", "ws_stationfloor", 0x00000000);
    mmsxt = CreateDynamicObject(2257, 1473.701782, -1076.358398, 213.483749, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14738, "whorebar", "AH_mirror", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 1, 4828, "airport3_las", "mirrwind1_LAn", 0x00000000);
    mmsxt = CreateDynamicObject(19379, 1483.025512, -1087.691040, 219.652511, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1477.402221, -1084.550537, 209.918624, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19445, 1491.572753, -1089.162963, 217.848434, -0.000076, 0.000000, -89.999771, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14674, "civic02cj", "sl_hotelwall1", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1478.206909, -1070.941894, 217.578048, 0.000015, -0.000090, 179.999404, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(2395, 1481.404541, -1077.930297, 216.218429, -89.999992, 90.000122, 89.999961, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19445, 1493.105468, -1075.383789, 217.848434, -0.000076, 0.000000, -89.999771, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    mmsxt = CreateDynamicObject(19445, 1497.745605, -1071.333374, 217.848434, 0.000000, 0.000076, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19379, 1493.465820, -1070.701293, 219.542495, 0.000000, 90.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1490.027099, -1066.591308, 217.798049, 0.000091, -0.000012, 89.999633, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14577, "casinovault01", "vaultWall", 0x00000000);
    mmsxt = CreateDynamicObject(1499, 1488.319824, -1074.502197, 216.108383, 0.000075, 0.000000, 89.999771, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-20-percent", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 1, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    mmsxt = CreateDynamicObject(2298, 1493.517089, -1071.216674, 216.134246, -0.000070, 0.000000, -89.999763, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 16640, "a51", "ws_stationfloor", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 1, 18646, "matcolours", "grey-20-percent", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 2, 18646, "matcolours", "grey-20-percent", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 3, 3820, "boxhses_sfsx", "tanboard1", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 12, 18646, "matcolours", "grey-20-percent", 0x00000000);
    mmsxt = CreateDynamicObject(2323, 1494.307128, -1073.019409, 216.104339, 0.000070, 0.000000, 89.999732, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 1, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    SetDynamicObjectMaterial(mmsxt, 2, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    mmsxt = CreateDynamicObject(19360, 1497.665039, -1072.593505, 217.848434, 0.000007, -0.000076, 179.999511, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    mmsxt = CreateDynamicObject(2167, 1497.628417, -1068.218017, 216.118423, -0.000076, 0.000000, -89.999771, -1, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mmsxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(2395, 1474.473632, -1090.552124, 215.808395, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19786, 1473.024780, -1090.694335, 214.378402, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1760, 1472.034423, -1086.673583, 212.438400, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1755, 1470.936279, -1088.747436, 212.438400, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1755, 1475.037719, -1087.716430, 212.438400, -0.000083, 0.000000, -89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1472.477416, -1084.829345, 212.998275, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1474.477416, -1084.829345, 212.998275, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2169, 1477.206787, -1089.289794, 212.438400, -0.000055, -0.000061, -137.400070, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1476.533813, -1088.965454, 212.438400, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1473.129882, -1088.083374, 215.838302, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1471.486450, -1084.829345, 213.388046, 89.999992, 180.000106, -89.999961, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1471.486450, -1084.829345, 215.388168, 89.999992, 180.000106, -89.999961, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1472.477416, -1084.829345, 215.838180, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1474.467895, -1084.829345, 215.838180, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1474.686889, -1084.829345, 213.388046, 89.999992, 180.000106, -89.999961, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1474.686889, -1084.829345, 215.388046, 89.999992, 180.000106, -89.999961, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1473.685913, -1084.829345, 213.388046, 89.999992, 180.000106, -89.999961, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1472.534790, -1084.829345, 213.388046, 89.999992, 180.000106, -89.999961, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1473.685913, -1084.829345, 215.388107, 89.999992, 180.000106, -89.999961, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1472.534790, -1084.829345, 215.388046, 89.999992, 180.000106, -89.999961, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1472.477416, -1084.829345, 213.778244, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1472.477416, -1084.829345, 214.788192, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1474.458862, -1084.829345, 214.788192, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1474.477294, -1084.829345, 213.778244, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, 1475.550903, -1085.569091, 213.748397, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2265, 1476.232666, -1085.535644, 214.888351, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2264, 1475.332885, -1085.545288, 214.788436, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2262, 1477.143554, -1085.536132, 214.828414, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2069, 1477.619018, -1088.532226, 212.468429, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19896, 1477.150024, -1088.945922, 213.128219, -0.000061, 0.000055, -47.799995, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19896, 1476.135131, -1089.865722, 213.118148, -0.000061, 0.000055, -47.799995, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19896, 1476.135131, -1089.865722, 212.898178, -0.000061, 0.000055, -47.799995, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19896, 1476.135131, -1089.865722, 212.638290, -0.000061, 0.000055, -47.799995, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19896, 1477.150024, -1088.945922, 212.638290, -0.000061, 0.000055, -47.799995, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19896, 1477.150024, -1088.945922, 212.888290, -0.000061, 0.000055, -47.799995, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2194, 1473.107910, -1084.887207, 215.088119, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2395, 1474.633056, -1085.110717, 215.808700, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2395, 1467.521606, -1090.146240, 215.818710, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2395, 1467.521606, -1087.454956, 215.818710, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2123, 1484.089233, -1067.745971, 216.736557, 0.000098, 0.000000, 89.999702, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2123, 1483.258422, -1068.456665, 216.736557, 0.000000, -0.000098, 179.999404, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2123, 1484.029174, -1069.256958, 216.736557, -0.000098, 0.000000, -89.999702, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2123, 1484.849365, -1068.486206, 216.736557, 0.000000, 0.000098, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2125, 1481.865722, -1071.691162, 216.444442, 0.000000, 0.000105, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19926, 1481.003662, -1071.219604, 216.115951, 0.000000, 0.000098, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2125, 1481.865722, -1070.720214, 216.444442, 0.000000, 0.000112, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2136, 1478.768066, -1071.664916, 216.131454, 0.000098, 0.000000, 89.999702, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2137, 1478.781738, -1069.712158, 216.133346, 0.000098, 0.000000, 89.999702, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2127, 1478.734252, -1068.705200, 216.140670, 0.000098, 0.000000, 89.999702, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, 1477.878051, -1071.057617, 217.843063, 0.000098, 0.000000, 89.999702, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1666, 1478.747192, -1069.916259, 217.257797, 0.000000, 0.000098, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1666, 1478.677124, -1070.086425, 217.257797, 0.000000, 0.000098, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1478.370727, -1071.153198, 218.440536, 0.000000, 0.000098, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2740, 1463.866577, -1089.166870, 215.716232, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2740, 1463.866577, -1087.576538, 215.716232, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19579, 1480.830566, -1070.496459, 217.031417, 0.000026, 0.000093, 15.699990, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, 1480.867553, -1071.518066, 217.043197, 0.000000, -0.000098, 179.999404, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19468, 1480.839599, -1071.299804, 216.836532, 0.000000, 0.000098, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19468, 1480.839599, -1071.670166, 216.836532, 0.000000, 0.000098, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1902, 1480.952514, -1071.941650, 216.947738, 0.000000, 0.000098, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1902, 1480.862426, -1071.941650, 216.947738, 0.000000, 0.000098, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1902, 1480.762329, -1071.941650, 216.947738, 0.000000, 0.000098, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3850, 1465.708862, -1081.329711, 213.362716, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19087, 1467.428100, -1081.322631, 213.915206, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19087, 1467.443115, -1081.322631, 213.915206, 89.999992, 180.000106, -89.999961, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19360, 1469.035644, -1079.940917, 210.818161, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2267, 1466.153930, -1082.971069, 214.532211, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2270, 1468.633056, -1080.583740, 213.921737, -0.000083, 0.000000, -89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2270, 1468.633056, -1081.294433, 214.521652, -0.000083, 0.000000, -89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2270, 1468.633056, -1081.995117, 213.921737, -0.000083, 0.000000, -89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2258, 1470.197387, -1082.694335, 214.204330, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2258, 1471.898803, -1082.694335, 214.204330, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2258, 1473.599853, -1082.694335, 214.204330, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1470.125000, -1082.470214, 215.838180, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1472.116821, -1082.470214, 215.838180, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1473.708129, -1082.472167, 215.838180, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 1476.557495, -1084.429199, 213.598190, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2139, 1474.152954, -1075.850830, 212.420516, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2139, 1474.152954, -1076.839721, 212.420516, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1474.161499, -1076.844604, 212.682479, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2525, 1475.633300, -1075.331909, 212.440231, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2526, 1477.430297, -1076.566284, 212.387191, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1475.700683, -1080.012695, 215.838302, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2395, 1466.170654, -1083.733764, 215.818710, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2395, 1466.170654, -1080.002197, 215.818710, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2395, 1466.961425, -1084.163696, 215.818710, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2395, 1465.970458, -1079.371337, 215.818710, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2395, 1469.073364, -1079.451416, 215.818710, -0.000083, 0.000000, -89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 1473.237182, -1080.091796, 214.487594, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19893, 1476.944335, -1089.741577, 213.228439, -0.000035, -0.000074, -153.899490, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1827, 1473.017456, -1088.380004, 212.434371, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14705, 1471.977905, -1084.754638, 214.018295, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2855, 1473.711303, -1084.839355, 213.181869, 86.100013, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2855, 1474.666381, -1084.839355, 213.216964, 86.100013, 0.000000, -89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2855, 1474.151855, -1084.874389, 214.788314, 0.000083, 0.000000, 89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2030, 1482.671752, -1077.903320, 216.537399, 0.000000, 0.000098, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2125, 1484.266357, -1078.020751, 216.444442, 0.000000, 0.000105, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2125, 1481.056030, -1077.760864, 216.444442, 0.000000, 0.000105, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2030, 1484.081542, -1068.523803, 216.537399, 0.000000, 0.000098, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2125, 1482.436035, -1079.551025, 216.444442, 0.000000, 0.000105, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19581, 1478.320312, -1071.210693, 217.871871, 89.999992, 180.000137, -89.999961, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19586, 1478.312011, -1070.929321, 217.889022, 89.999992, 180.000137, -89.999961, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19575, 1482.453491, -1078.027099, 216.972091, 0.000000, 0.000076, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1482.660156, -1077.913452, 219.558303, 0.000000, -0.000083, 179.999496, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19583, 1480.754150, -1070.529174, 217.351669, 89.999992, 101.900154, -89.999961, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19583, 1480.881103, -1070.502197, 217.351669, 89.999992, 101.900154, -89.999961, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19574, 1484.163574, -1068.598999, 216.975814, 0.000000, 0.000098, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19575, 1484.003417, -1068.499389, 216.965866, 0.000000, 0.000098, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19576, 1484.073486, -1068.558959, 216.975814, 0.000000, 0.000098, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19578, 1484.077514, -1068.425292, 216.965805, 0.000022, -0.000093, 165.299407, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19578, 1483.974243, -1068.640991, 216.965805, -0.000065, 0.000072, -41.899997, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2269, 1489.014648, -1070.478637, 217.958419, 0.000076, 0.000000, 89.999771, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19787, 1477.578125, -1080.098388, 213.820419, -0.000083, 0.000000, -89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2394, 1476.577270, -1084.319091, 213.201828, 0.000000, 0.000083, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2384, 1476.935302, -1084.456298, 213.697799, -0.000083, 0.000000, -89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2384, 1476.145019, -1084.456298, 213.697799, -0.000083, 0.000000, -89.999748, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2161, 1488.519531, -1068.908569, 216.118423, 0.000075, 0.000000, 89.999771, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2241, 1466.701904, -1082.609497, 213.358398, 0.000000, 0.000076, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2241, 1479.031860, -1089.929199, 212.928390, 0.000000, 0.000076, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2251, 1479.119506, -1083.434204, 216.988418, 0.000000, 0.000076, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2251, 1478.489624, -1072.624389, 216.988418, 0.000000, 0.000076, 0.000000, -1, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2251, 1488.649414, -1071.164428, 216.988418, 0.000000, 0.000076, 0.000000, -1, 0, -1, 200.00, 200.00);
}













CreateCustomHouseInt()
{
    ccsst = CreateDynamicObject(8533, 1684.635986, -1456.537109, 3001.841308, 0.000000, 0.000091, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14651, "ab_trukstpd", "Bow_bar_flooring", 0xFF669999);
    ccsst = CreateDynamicObject(19378, 1679.969238, -1451.158081, 3006.961425, -0.000007, 0.000000, -89.999977, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1670.379272, -1451.158081, 3006.961425, -0.000007, 0.000000, -89.999977, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1668.318481, -1455.719482, 3006.961425, 0.000000, 0.000007, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1668.318481, -1465.329589, 3006.961425, 0.000000, 0.000007, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1668.318481, -1474.958984, 3006.961425, 0.000000, 0.000007, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19383, 1677.528564, -1459.667968, 3003.562255, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19426, 1677.539062, -1457.352661, 3003.563232, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1681.508544, -1474.096923, 3006.961425, 0.000007, 0.000000, 89.999977, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1691.108642, -1474.096923, 3006.961425, 0.000007, 0.000000, 89.999977, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1700.728759, -1474.096923, 3006.961425, 0.000007, 0.000000, 89.999977, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1701.579467, -1469.496948, 3006.961425, 0.000000, -0.000007, 179.999954, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1701.579467, -1459.867431, 3006.961425, 0.000000, -0.000007, 179.999954, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1701.579467, -1450.257202, 3006.961425, 0.000000, -0.000007, 179.999954, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19353, 1680.405395, -1467.466918, 3004.153320, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19445, 1682.554565, -1456.633789, 3003.562988, 0.000000, 0.000000, 270.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14471, "carls_kit1", "wall3", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1681.918701, -1472.217773, 3000.666259, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14569, "traidman", "luxebrown_law", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1687.859130, -1464.379638, 3006.961425, 0.000000, 0.000000, 450.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1692.589111, -1459.647705, 3006.961425, 0.000000, 0.000000, 360.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19445, 1695.380981, -1469.672363, 3003.582763, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19426, 1678.874145, -1465.668334, 3005.332519, 90.000000, 90.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19353, 1678.872070, -1464.087036, 3004.372558, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1692.589111, -1450.017944, 3006.961425, 0.000000, 0.000000, 360.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19376, 1686.127197, -1475.345458, 3005.838378, 0.000000, 90.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14651, "ab_trukstpd", "Bow_bar_flooring", 0xFF669999);
    ccsst = CreateDynamicObject(19426, 1678.699951, -1474.688842, 3004.129394, 0.000000, 90.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14651, "ab_trukstpd", "Bow_bar_flooring", 0xFF669999);
    ccsst = CreateDynamicObject(19353, 1678.872070, -1464.087036, 3000.892578, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1676.989868, -1477.567260, 3006.961425, 0.000000, 0.000000, 180.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19353, 1677.535278, -1462.847656, 3003.562500, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19376, 1696.627563, -1475.355468, 3005.838378, 0.000000, 90.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14651, "ab_trukstpd", "Bow_bar_flooring", 0xFF669999);
    ccsst = CreateDynamicObject(19376, 1694.535644, -1465.735839, 3005.838378, 0.000000, 90.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14651, "ab_trukstpd", "Bow_bar_flooring", 0xFF669999);
    ccsst = CreateDynamicObject(19376, 1696.665527, -1456.116333, 3005.838378, 0.000000, 90.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14651, "ab_trukstpd", "Bow_bar_flooring", 0xFF669999);
    ccsst = CreateDynamicObject(19353, 1676.995849, -1472.267944, 3002.260742, 54.099964, 0.000012, -0.000009, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19353, 1678.875854, -1468.987182, 3000.920898, 0.000000, 0.000007, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19353, 1676.995849, -1469.448730, 3000.221435, 54.099964, 0.000012, -0.000009, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19376, 1684.048461, -1465.725952, 3005.838378, 0.000000, 90.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14651, "ab_trukstpd", "Bow_bar_flooring", 0xFF669999);
    ccsst = CreateDynamicObject(2920, 1678.882934, -1472.514404, 3011.252929, 0.000000, 180.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1678.882934, -1472.104003, 3011.252929, 0.000000, 180.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1678.882934, -1471.663574, 3011.252929, 0.000000, 180.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1678.882934, -1471.173217, 3011.252929, 0.000000, 180.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1678.882934, -1470.692749, 3011.252929, 0.000000, 180.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1678.882934, -1470.232299, 3011.252929, 0.000000, 180.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(8533, 1684.635986, -1456.537109, 3009.380126, 0.000000, 180.000091, 360.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2920, 1682.071533, -1470.333496, 2998.222412, 0.000000, 360.000000, 45.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 4828, "airport3_las", "gnhotelwall02_128", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1697.359497, -1451.278198, 3006.961425, 0.000000, 0.000000, 270.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19376, 1705.015258, -1465.737060, 3005.838378, 0.000000, 90.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14651, "ab_trukstpd", "Bow_bar_flooring", 0xFF669999);
    ccsst = CreateDynamicObject(19378, 1682.268066, -1464.369628, 3006.961425, 0.000000, 0.000000, 450.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19353, 1680.405395, -1465.607299, 3004.153320, 0.000007, 0.000000, 89.999977, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19353, 1680.405395, -1465.607299, 3000.680908, 0.000007, 0.000000, 89.999977, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19353, 1681.926025, -1464.075805, 3004.153320, 0.000014, -0.000022, 179.999786, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(14407, 1678.955078, -1470.973632, 3001.040527, 0.000000, 0.000007, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14651, "ab_trukstpd", "Bow_bar_flooring", 0xFF669999);
    ccsst = CreateDynamicObject(14407, 1680.905517, -1472.383666, 3002.741943, 0.000000, -0.000007, 179.999954, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14651, "ab_trukstpd", "Bow_bar_flooring", 0xFF669999);
    ccsst = CreateDynamicObject(19353, 1681.926025, -1464.075805, 3000.680908, 0.000014, -0.000022, 179.999786, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19353, 1678.876708, -1471.072753, 3003.814208, 54.099964, -0.000012, 179.999969, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19353, 1678.875854, -1468.987182, 3004.393066, 0.000000, 0.000007, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19376, 1673.658569, -1459.635986, 3005.277832, 0.000000, 90.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1680.948852, -1472.217773, 3005.137695, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1671.898803, -1474.096923, 3006.961425, 0.000007, 0.000000, 89.999977, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19376, 1676.656372, -1469.256835, 3008.972656, 0.000000, 45.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19376, 1667.748168, -1469.206420, 3005.267822, 0.000000, 90.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19376, 1672.648681, -1464.385498, 3010.439453, 0.000000, 180.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19376, 1673.658569, -1450.036499, 3005.277832, 0.000000, 90.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19376, 1684.098754, -1452.916625, 3005.277832, 0.000000, 90.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2920, 1676.973144, -1471.654174, 3001.215087, 0.000000, 360.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(19426, 1679.099975, -1474.688842, 3004.139404, 0.000000, 90.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14651, "ab_trukstpd", "Bow_bar_flooring", 0xFF669999);
    ccsst = CreateDynamicObject(2920, 1676.973144, -1471.243774, 3001.215087, 0.000000, 360.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1676.973144, -1470.803344, 3001.215087, 0.000000, 360.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1676.973144, -1470.312988, 3001.215087, 0.000000, 360.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1676.973144, -1469.832519, 3001.215087, 0.000000, 360.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1676.973144, -1469.372070, 3001.215087, 0.000000, 360.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1676.973144, -1471.654174, 3015.206787, 0.000000, 540.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1676.973144, -1471.243774, 3015.206787, 0.000000, 540.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1676.973144, -1470.803344, 3015.206787, 0.000000, 540.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1676.973144, -1470.312988, 3015.206787, 0.000000, 540.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1676.973144, -1469.832519, 3015.206787, 0.000000, 540.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1676.973144, -1469.372070, 3015.206787, 0.000000, 540.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(19445, 1705.000854, -1469.682373, 3003.582763, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14569, "traidman", "luxebrown_law", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2920, 1678.882934, -1469.751831, 3011.252929, 0.000000, 180.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1678.882934, -1469.271362, 3011.252929, 0.000000, 180.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1678.882934, -1468.800903, 3011.252929, 0.000000, 180.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(19353, 1668.364135, -1460.555053, 3003.522460, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFCC);
    ccsst = CreateDynamicObject(19786, 1668.591674, -1460.537475, 3004.172851, 2.999996, 0.000000, 91.399986, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 6490, "tvstudio_law2", "tvstud03_LAw2", 0x00000000);
    ccsst = CreateDynamicObject(18880, 1668.517333, -1467.690917, 3001.513671, 0.000000, 0.000000, 180.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(19353, 1675.423706, -1459.034667, 3003.522460, 0.000000, 0.000007, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFCC);
    ccsst = CreateDynamicObject(19922, 1671.745239, -1459.821899, 3001.539794, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14581, "ab_mafiasuitea", "cof_wood2", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19922, 1671.745239, -1461.232299, 3001.539794, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14581, "ab_mafiasuitea", "cof_wood2", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19938, 1668.826049, -1459.343505, 3003.382568, 0.000000, 0.000006, 179.499908, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFCC);
    ccsst = CreateDynamicObject(19940, 1668.822998, -1460.550903, 3003.382324, 0.000000, 0.000020, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFCC);
    ccsst = CreateDynamicObject(19353, 1675.423706, -1462.244873, 3003.522460, 0.000000, 0.000007, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFCC);
    ccsst = CreateDynamicObject(19938, 1668.827758, -1461.774902, 3003.382568, -0.000006, 0.000000, 89.800064, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFCC);
    ccsst = CreateDynamicObject(2132, 1671.657104, -1473.494628, 3001.841308, 0.000000, -0.000014, 179.999908, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 2, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2339, 1669.669311, -1473.490600, 3001.841308, 0.000000, -0.000014, 179.999908, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 2, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2339, 1672.649536, -1473.490600, 3001.841308, 0.000000, -0.000014, 179.999908, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 2, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2131, 1674.649902, -1473.481201, 3001.841308, 0.000000, -0.000014, 179.999908, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2163, 1672.640869, -1473.994750, 3003.412841, 0.000000, -0.000014, 179.999908, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2339, 1668.669067, -1473.490600, 3001.841308, 0.000000, -0.000014, 179.999908, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 2, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2339, 1668.919311, -1472.740478, 3001.841308, 0.000000, -0.000007, 449.999938, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 2, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19353, 1676.529663, -1455.491210, 3003.472900, 0.000000, 0.000000, 45.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19383, 1675.415771, -1452.809448, 3003.572998, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1492, 1675.403808, -1453.558349, 3001.811279, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 4828, "airport3_las", "gnhotelwall02_128", 0x00000000);
    ccsst = CreateDynamicObject(18880, 1672.867797, -1467.690917, 3001.513671, 0.000000, 0.000000, 180.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(19325, 1669.532470, -1467.671997, 3004.025634, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, -1, "none", "none", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19445, 1701.568237, -1474.551513, 3003.582763, 0.000000, 0.000000, 180.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14569, "traidman", "luxebrown_law", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19353, 1676.550903, -1455.469970, 3003.572998, 0.000000, 0.000000, 45.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14471, "carls_kit1", "wall3", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19383, 1675.435791, -1452.809448, 3003.572998, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14471, "carls_kit1", "wall3", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19445, 1680.345336, -1451.162719, 3003.562988, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14471, "carls_kit1", "wall3", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19445, 1684.795410, -1451.733276, 3003.562988, 0.000000, 0.000000, 180.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14471, "carls_kit1", "wall3", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1512, 1689.792358, -1471.221557, 3002.770751, 11.100000, 360.000000, 51.899971, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(19383, 1689.496093, -1468.569824, 3003.572998, 0.000000, 0.000000, 45.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19445, 1683.609497, -1467.462768, 3003.582763, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19445, 1705.010742, -1469.672363, 3003.582763, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(8533, 1711.344726, -1457.557983, 3005.278808, 0.000000, 180.000091, 360.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1499, 1690.044555, -1469.068359, 3001.811279, 0.000000, 0.000000, 135.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14569, "traidman", "luxebrown_law", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 4828, "airport3_las", "gnhotelwall02_128", 0x00000000);
    ccsst = CreateDynamicObject(19445, 1683.609497, -1467.472778, 3003.582763, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14569, "traidman", "luxebrown_law", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19383, 1689.489013, -1468.576904, 3003.572998, 0.000000, 0.000000, 45.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14569, "traidman", "luxebrown_law", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19445, 1695.380981, -1469.682373, 3003.582763, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14569, "traidman", "luxebrown_law", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19445, 1690.458251, -1474.091064, 3003.582763, 0.000000, 0.000000, 270.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14569, "traidman", "luxebrown_law", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19445, 1700.047729, -1474.091064, 3003.582763, 0.000000, 0.000000, 270.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14569, "traidman", "luxebrown_law", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1685.203613, -1473.617919, 3000.650146, 0.000000, 0.000000, 44.799987, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14569, "traidman", "luxebrown_law", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2920, 1685.628784, -1473.890747, 2998.222412, 0.000000, 360.000000, 45.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 4828, "airport3_las", "gnhotelwall02_128", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2920, 1680.865478, -1469.127441, 3000.820556, 45.000000, 360.000000, 45.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 4828, "airport3_las", "gnhotelwall02_128", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2920, 1681.100708, -1469.362670, 3000.487792, 45.000000, 360.000000, 45.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 4828, "airport3_las", "gnhotelwall02_128", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2920, 1686.604370, -1474.866333, 3000.443115, 135.000000, 540.000000, 405.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 4828, "airport3_las", "gnhotelwall02_128", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2308, 1682.483886, -1469.048828, 3001.841308, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    ccsst = CreateDynamicObject(2920, 1690.312133, -1469.998535, 2998.285400, 0.000000, 720.000000, 135.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 4828, "airport3_las", "gnhotelwall02_128", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2920, 1690.014770, -1470.295898, 2998.285400, 0.000000, 720.000000, 135.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 4828, "airport3_las", "gnhotelwall02_128", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2920, 1689.724487, -1470.586181, 2998.285400, 0.000000, 720.000000, 135.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 4828, "airport3_las", "gnhotelwall02_128", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2920, 1689.420043, -1470.890625, 2998.285400, 0.000000, 720.000000, 135.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 4828, "airport3_las", "gnhotelwall02_128", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2920, 1689.349975, -1471.250976, 2998.285400, 0.000000, 720.000000, 180.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 4828, "airport3_las", "gnhotelwall02_128", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2920, 1689.349975, -1471.661376, 2998.285400, 0.000000, 720.000000, 180.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 4828, "airport3_las", "gnhotelwall02_128", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2920, 1689.349975, -1472.071777, 2998.285400, 0.000000, 720.000000, 180.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 4828, "airport3_las", "gnhotelwall02_128", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2184, 1690.439086, -1470.217895, 3001.841308, 0.000000, 0.000000, -113.199996, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFCC);
    ccsst = CreateDynamicObject(2260, 1690.171386, -1471.714233, 3003.035400, 3.599997, 88.900054, 68.799987, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 4828, "airport3_las", "gnhotelwall02_128", 0x00000000);
    ccsst = CreateDynamicObject(2260, 1690.499511, -1470.987304, 3003.062500, 3.599997, 88.900054, 44.799972, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 4828, "airport3_las", "gnhotelwall02_128", 0x00000000);
    ccsst = CreateDynamicObject(19874, 1689.839477, -1471.217163, 3002.631835, 0.000000, 180.000000, 64.999992, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(1512, 1690.352172, -1470.385986, 3002.784423, 11.100000, 360.000000, 31.500017, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2801, 1671.756958, -1460.526611, 3001.991455, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 11, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    ccsst = CreateDynamicObject(19874, 1690.372314, -1470.378784, 3002.621826, 0.000000, 180.000000, 44.300022, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2260, 1690.331909, -1469.839965, 3003.136962, -3.699997, 90.900062, 224.799972, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(19874, 1690.679809, -1470.904174, 3002.602050, 0.000000, 0.000000, -14.400001, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 4828, "airport3_las", "gnhotelwall02_128", 0x00000000);
    ccsst = CreateDynamicObject(2260, 1689.538330, -1470.748291, 3003.100097, -2.900000, 91.000053, 248.799987, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(19482, 1689.867431, -1471.247924, 3003.062255, 1.699998, 0.000000, -22.100027, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(ccsst, 0, " #diWGaja", 140, "Arial", 20, 1, 0xFF946038, 0x00000000, 1);
    ccsst = CreateDynamicObject(19482, 1690.404785, -1470.441040, 3003.097412, -0.500001, 0.000000, -44.000019, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(ccsst, 0, " SAMP", 140, "Arial", 20, 1, 0xFF946038, 0x00000000, 1);
    ccsst = CreateDynamicObject(11724, 1668.703735, -1460.548339, 3002.441406, 0.000014, 0.000000, 92.599884, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14581, "ab_mafiasuitea", "cof_wood2", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 1, 16136, "des_telescopestuff", "stoneclad1", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 2, 8391, "ballys01", "CJ_blackplastic", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2281, 1671.609252, -1451.749633, 3004.103271, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2161, 1671.443481, -1451.284790, 3001.860595, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1742, 1672.777587, -1451.271118, 3001.861328, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 2, 1355, "break_s_bins", "CJ_WOOD_DARK", 0x00000000);
    ccsst = CreateDynamicObject(1742, 1670.015502, -1451.343383, 3001.841308, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 2, 1355, "break_s_bins", "CJ_WOOD_DARK", 0x00000000);
    ccsst = CreateDynamicObject(19923, 1668.536987, -1471.253295, 3001.841308, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2339, 1668.919311, -1469.780395, 3001.841308, 0.000000, -0.000007, 449.999938, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 2, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2637, 1672.197143, -1470.713134, 3002.251464, 0.000000, 0.000007, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 3587, "snpedhusxref", "sjmlawood2", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19426, 1669.605468, -1474.340576, 3004.252929, 0.000000, 90.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(19426, 1668.156250, -1473.670898, 3004.252929, 0.000000, 90.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(19426, 1667.206787, -1469.748901, 3004.252929, 0.000000, 90.000000, 180.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2395, 1670.540771, -1472.032836, 3001.891357, 270.000000, 360.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14383, "burg_1", "carpet4kb", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(957, 1670.626586, -1473.738037, 3004.142333, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 5042, "bombshop_las", "lightcover1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(957, 1669.496337, -1473.738037, 3004.142333, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 5042, "bombshop_las", "lightcover1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(957, 1668.615478, -1472.858276, 3004.142333, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 5042, "bombshop_las", "lightcover1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(957, 1668.615478, -1469.768310, 3004.142333, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 5042, "bombshop_las", "lightcover1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(14869, 1668.852294, -1453.302734, 3002.662109, 0.000000, 0.000000, 180.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1826, 1669.142822, -1454.546875, 3001.841308, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(18880, 1696.998413, -1471.071044, 3005.307128, 0.000000, 180.000000, 180.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(18880, 1696.998413, -1473.881225, 3005.307128, 0.000000, 180.000000, 180.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(19383, 1695.006591, -1466.914306, 3003.583007, 0.000000, 0.000000, 45.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19426, 1696.116333, -1468.796630, 3003.572753, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19353, 1692.750000, -1464.653930, 3003.583007, 0.000000, 0.000000, 45.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1499, 1694.429809, -1466.395996, 3001.811279, 0.000000, 0.000000, 315.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14569, "traidman", "luxebrown_law", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 4828, "airport3_las", "gnhotelwall02_128", 0x00000000);
    ccsst = CreateDynamicObject(19786, 1668.581665, -1460.537719, 3004.173339, 2.999995, 0.000000, 91.399986, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(2395, 1681.952514, -1453.970703, 3001.891357, 270.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 16640, "a51", "ws_stationfloor", 0x00000000);
    ccsst = CreateDynamicObject(2395, 1681.962524, -1456.710327, 3001.891357, 270.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 16640, "a51", "ws_stationfloor", 0x00000000);
    ccsst = CreateDynamicObject(2395, 1678.231445, -1456.710327, 3001.891357, 270.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 16640, "a51", "ws_stationfloor", 0x00000000);
    ccsst = CreateDynamicObject(2395, 1678.231445, -1453.970092, 3001.891357, 270.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 16640, "a51", "ws_stationfloor", 0x00000000);
    ccsst = CreateDynamicObject(18880, 1677.849609, -1456.420898, 3001.753906, 0.000000, 90.000000, 450.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(3003, 1699.940185, -1453.545654, 3002.742187, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 19267, "mapmarkers", "samporange", 0x00000000);
    ccsst = CreateDynamicObject(3003, 1700.110351, -1454.195800, 3002.742187, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(18880, 1668.517333, -1455.058959, 3001.513671, 0.000000, -0.000007, 179.999954, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(18880, 1672.867797, -1455.058959, 3001.513671, 0.000000, -0.000007, 179.999954, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(19325, 1669.532470, -1455.040039, 3004.025634, 0.000007, 0.000000, 89.999977, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, -1, "none", "none", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2272, 1674.847656, -1460.551635, 3004.090576, 0.000000, 0.000000, 270.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 4828, "airport3_las", "gnhotelwall02_128", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 4981, "wiresetc2_las", "lasunionclk", 0x00000000);
    ccsst = CreateDynamicObject(19399, 1693.910034, -1464.364257, 3007.659912, 0.000022, 0.000000, 89.999931, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19383, 1697.113037, -1464.361450, 3007.657958, 0.000022, 0.000000, 89.999931, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19399, 1700.320800, -1464.364257, 3007.659912, 0.000022, 0.000000, 89.999931, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1499, 1697.856323, -1464.387329, 3005.903076, 0.000000, 0.000000, 180.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14569, "traidman", "luxebrown_law", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 4828, "airport3_las", "gnhotelwall02_128", 0x00000000);
    ccsst = CreateDynamicObject(19353, 1695.525512, -1462.344604, 3007.656005, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19353, 1698.735839, -1462.344604, 3007.656005, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19383, 1694.432373, -1452.522827, 3007.645751, 0.000000, 0.000000, 45.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19383, 1699.784301, -1452.514160, 3007.645751, 0.000000, 0.000000, 315.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19353, 1697.109619, -1453.624145, 3007.655761, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19466, 1697.151123, -1453.732299, 3008.165283, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 8421, "pirateland", "tislndskullrock_256", 0x00000000);
    ccsst = CreateDynamicObject(2357, 1697.160034, -1457.835937, 3006.344726, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1739, 1698.755981, -1456.439453, 3006.805175, 0.000000, 0.000022, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 1, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1739, 1698.755981, -1457.409423, 3006.805175, 0.000000, 0.000022, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 1, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1739, 1698.755981, -1458.359497, 3006.805175, 0.000000, 0.000022, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 1, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1739, 1698.755981, -1459.330200, 3006.805175, 0.000000, 0.000022, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 1, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1739, 1695.533569, -1459.330200, 3006.805175, 0.000007, 0.000014, 179.999877, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 1, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1739, 1695.533569, -1458.360229, 3006.805175, 0.000007, 0.000014, 179.999877, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 1, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1739, 1695.533569, -1457.410156, 3006.805175, 0.000007, 0.000014, 179.999877, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 1, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1739, 1695.533569, -1456.439453, 3006.805175, 0.000007, 0.000014, 179.999877, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 1, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2273, 1697.131958, -1457.075439, 3007.235107, 270.000000, 0.000000, 360.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1355, "break_s_bins", "CJ_WOOD_DARK", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 8421, "pirateland", "tislndskullrock_256", 0x00000000);
    ccsst = CreateDynamicObject(19631, 1697.009399, -1455.881225, 3006.747314, -5.299999, 105.199966, 72.000015, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    ccsst = CreateDynamicObject(19874, 1697.375244, -1456.098510, 3006.745117, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14651, "ab_trukstpd", "Bow_bar_tabletop_wood", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1739, 1697.134765, -1454.729248, 3006.805175, 0.000000, 0.000022, 90.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 1, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1739, 1697.134765, -1460.889404, 3006.805175, 0.000000, 0.000022, 270.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ccsst, 1, 1355, "break_s_bins", "CJ_WOOD_DARK", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(2262, 1693.156250, -1454.353881, 3007.924804, 0.000000, 0.000000, 450.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 6985, "vgnfremnt2", "vgntext1_256", 0x00000000);
    ccsst = CreateDynamicObject(2262, 1693.156250, -1455.904174, 3007.924804, 0.000000, 0.000000, 450.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 4510, "barrierblk", "warnsigns2", 0x00000000);
    ccsst = CreateDynamicObject(2262, 1693.156250, -1457.694946, 3007.924804, 0.000000, 0.000000, 450.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 4510, "barrierblk", "warnsigns1", 0x00000000);
    ccsst = CreateDynamicObject(2262, 1693.156250, -1459.485839, 3007.924804, 0.000000, 0.000000, 450.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 6404, "beafron1_law2", "beachsig01_LAw2", 0x00000000);
    ccsst = CreateDynamicObject(2262, 1693.156250, -1461.136474, 3007.924804, 0.000000, 0.000000, 450.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 6282, "beafron2_law2", "bobo_LAw", 0x00000000);
    ccsst = CreateDynamicObject(2262, 1700.997070, -1461.136352, 3007.924804, 0.000000, -0.000007, -90.000007, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 14603, "bikeskool", "lw_pistol_128", 0x00000000);
    ccsst = CreateDynamicObject(2262, 1700.997070, -1459.586059, 3007.924804, 0.000000, -0.000007, -90.000007, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 3077, "blkbrdx", "WCTitle", 0x00000000);
    ccsst = CreateDynamicObject(2262, 1700.997070, -1457.795288, 3007.924804, 0.000000, -0.000007, -90.000007, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 3113, "carrierxr", "ws_decklines", 0x00000000);
    ccsst = CreateDynamicObject(2262, 1700.997070, -1456.004394, 3007.924804, 0.000000, -0.000007, -90.000007, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 10770, "carrier_sfse", "ws_carrierdecals", 0x00000000);
    ccsst = CreateDynamicObject(2262, 1700.997070, -1454.353759, 3007.924804, 0.000000, -0.000007, -90.000007, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 14415, "carter_block_2", "ws_doormat", 0x00000000);
    ccsst = CreateDynamicObject(19378, 1685.677612, -1467.486083, 3011.063232, 0.000007, 0.000000, 89.999977, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19383, 1691.566772, -1468.599975, 3007.666015, 0.000000, 0.000000, 45.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(19378, 1692.677490, -1474.496459, 3011.063232, 0.000007, 0.000000, 179.999969, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 17545, "burnsground", "newall10_seamless", 0xFFFFFFFF);
    ccsst = CreateDynamicObject(1499, 1692.117797, -1469.103271, 3005.924316, 0.000000, 0.000000, 135.000000, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14569, "traidman", "luxebrown_law", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 4828, "airport3_las", "gnhotelwall02_128", 0x00000000);
    ccsst = CreateDynamicObject(18880, 1693.898315, -1469.712280, 3005.476318, 0.000000, -0.000029, 179.999816, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(18880, 1700.308349, -1469.712280, 3005.476318, 0.000000, -0.000029, 179.999816, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ccsst = CreateDynamicObject(19325, 1697.103149, -1469.693359, 3007.988281, 0.000029, 0.000000, 89.999908, 2, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, -1, "none", "none", 0xFFFFFFFF);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(1566, 1677.494384, -1460.447998, 3002.972167, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2528, 1684.214843, -1455.829345, 3001.841308, 0.000000, 0.000000, 270.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1728, 1672.853637, -1463.052856, 3001.841308, 0.000000, 0.000000, -179.799972, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1728, 1670.742675, -1457.978027, 3001.841308, 0.000000, 0.000007, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1728, 1674.753540, -1459.554687, 3001.851318, 0.000000, 0.000000, -89.499992, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2865, 1671.625244, -1473.620483, 3002.901611, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19581, 1669.160766, -1470.679687, 3002.861328, 0.000000, 0.000000, -30.599996, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19873, 1684.634643, -1455.442260, 3002.491210, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19582, 1668.917114, -1471.070190, 3002.892333, 0.000000, 0.000000, 29.700000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2522, 1684.222290, -1453.067504, 3001.841308, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2517, 1680.609985, -1455.047241, 3001.841308, 0.000000, 0.000000, 540.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1676.945312, -1455.010498, 3002.091552, 0.000000, 0.000000, 135.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 1677.709594, -1455.774780, 3002.091552, 0.000000, 0.000000, 135.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2298, 1687.566162, -1470.260009, 3001.841308, 0.000000, 0.000000, 135.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2394, 1701.149414, -1472.931884, 3003.883300, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2323, 1687.237182, -1472.511718, 3001.841308, 0.000000, 0.000000, 180.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19173, 1685.272460, -1467.573852, 3003.802490, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2853, 1682.322509, -1468.785888, 3002.641357, 0.000000, 0.000000, -42.800010, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1691.828369, -1471.986572, 3001.841308, 0.000000, 0.000000, -116.699928, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 1690.466064, -1471.307983, 3002.632080, 0.000000, 0.000000, 61.400001, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2854, 1689.988891, -1472.200927, 3002.612060, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16151, 1693.733032, -1460.063598, 3002.171630, 0.000000, 0.000000, 179.699935, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19823, 1694.755004, -1460.957275, 3002.832275, 0.000000, 0.000000, 118.100006, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1670, 1694.549438, -1462.592041, 3002.811523, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1665, 1694.578735, -1461.772216, 3002.812255, 0.000000, 0.000000, 61.800003, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1666, 1694.768188, -1461.170410, 3002.901855, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19897, 1694.611938, -1461.582275, 3002.821777, 0.000000, 0.000000, 17.099998, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1692.928466, -1458.856933, 3001.871337, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1692.953247, -1458.500122, 3001.911376, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1692.916015, -1458.722045, 3002.221679, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2674, 1697.983398, -1456.122680, 3001.861572, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(926, 1693.835937, -1451.749511, 3002.131591, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2673, 1693.581787, -1452.109985, 3001.941650, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2808, 1700.354125, -1456.455444, 3002.501953, 0.000000, 0.000000, -178.299896, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2808, 1700.382690, -1460.008422, 3002.471679, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2808, 1700.400756, -1460.901000, 3002.481933, 0.000000, 0.000000, 179.799957, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2808, 1700.341186, -1464.266479, 3002.401855, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14651, 1699.573120, -1453.429199, 3003.931396, 0.000000, 0.000000, 88.899948, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1328, 1693.077026, -1451.762207, 3002.331787, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2637, 1700.403808, -1458.351562, 3002.271728, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2637, 1700.396484, -1462.693115, 3002.261230, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1670, 1700.368408, -1458.146362, 3002.722167, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1544, 1700.200561, -1462.620239, 3002.681884, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1666, 1700.029418, -1462.438232, 3002.812011, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1666, 1700.029418, -1462.878662, 3002.812255, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19896, 1699.762695, -1462.489990, 3002.721923, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1665, 1700.338012, -1462.810668, 3002.701660, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1692.936889, -1454.927856, 3001.841308, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1692.939453, -1455.239746, 3001.841308, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1692.937866, -1455.075317, 3002.101562, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1692.942016, -1454.589721, 3001.841308, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19572, 1692.932495, -1454.607910, 3002.151611, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14705, 1701.185546, -1469.988769, 3002.081298, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(325, 1701.153808, -1469.996337, 3002.191650, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19586, 1668.966064, -1470.531372, 3002.832031, 0.000000, 0.000000, -63.800003, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14556, 1700.544555, -1472.745117, 3003.332031, 0.000000, 0.000000, 450.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19632, 1668.796020, -1460.540771, 3001.891357, 0.000014, 0.000000, 92.399955, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2396, 1701.194702, -1473.545532, 3002.972412, 0.000000, 0.000000, 270.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2398, 1701.278076, -1472.329833, 3002.982421, 0.000000, 0.000000, 270.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1544, 1671.355957, -1451.443115, 3003.211425, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1543, 1671.955078, -1451.483886, 3003.202148, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19874, 1676.408447, -1455.088134, 3003.022460, 0.000000, 0.000000, -41.900001, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19924, 1668.435546, -1471.258300, 3004.624023, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2123, 1671.682739, -1469.574462, 3002.451904, 0.000007, 0.000000, 89.999977, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2123, 1672.713378, -1469.574462, 3002.451904, 0.000007, 0.000000, 89.999977, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2123, 1672.713256, -1471.775024, 3002.451904, -0.000007, -0.000007, -89.999984, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2123, 1671.682617, -1471.775024, 3002.451904, -0.000007, -0.000007, -89.999984, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18673, 1668.949462, -1471.095581, 3001.270751, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2832, 1669.529296, -1473.614135, 3002.891845, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19830, 1672.955810, -1473.429321, 3002.772216, 0.000000, 0.000000, 270.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2426, 1668.633789, -1469.794311, 3002.881835, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11743, 1668.593139, -1472.893676, 3002.882324, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, 1668.619628, -1472.542358, 3002.952148, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, 1668.749755, -1472.412231, 3002.952148, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2221, 1671.792724, -1470.535278, 3002.761962, 0.000000, 0.000000, -67.100028, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11718, 1668.734741, -1473.464965, 3002.922119, 0.000000, 0.000000, 153.500015, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2026, 1672.182495, -1470.685546, 3005.186523, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2858, 1672.743041, -1470.687133, 3002.692138, 0.000000, 0.000000, 42.900001, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1668.808593, -1460.468017, 3005.258544, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2260, 1668.893432, -1462.975585, 3003.623046, 0.000007, 0.000000, 89.999977, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, 1668.912597, -1463.905517, 3003.572509, 0.000007, 0.000000, 89.999977, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2262, 1668.882934, -1465.015380, 3003.651611, 0.000007, 0.000000, 89.999977, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2264, 1668.922851, -1466.086181, 3003.633056, 0.000007, 0.000000, 89.999977, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2265, 1668.892944, -1466.895629, 3003.603027, 0.000007, 0.000000, 89.999977, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, 1668.640014, -1468.922241, 3001.841308, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2726, 1694.508544, -1457.076416, 3003.161376, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19466, 1676.624511, -1455.405883, 3004.343017, 0.000000, 0.000000, 45.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(349, 1700.962524, -1472.677490, 3001.985107, 76.600044, 0.000000, 270.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2926, 1700.987792, -1472.956542, 3001.931396, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1575, 1701.066528, -1473.256347, 3001.841308, 0.000000, 0.000000, 58.399993, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(348, 1701.069091, -1472.986328, 3002.001464, 90.000000, 0.000000, 82.400032, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2689, 1701.379272, -1472.952026, 3002.852294, 0.000000, 0.000000, 270.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19325, 1696.987548, -1474.418579, 3003.372802, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11707, 1682.677490, -1451.307250, 3003.302490, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11705, 1686.128295, -1473.571166, 3002.842285, 0.000000, 0.000000, 166.899887, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1696.989013, -1460.468017, 3005.338623, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(338, 1698.177246, -1454.438720, 3002.792236, 90.299949, 0.000000, 122.500007, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3003, 1699.309570, -1453.615722, 3002.742187, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1431, 1691.265136, -1464.846923, 3002.381835, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2358, 1689.818725, -1464.837890, 3001.951416, 0.000000, 0.000000, -176.700012, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2358, 1692.849731, -1465.236083, 3001.951416, 0.000000, 0.000000, 140.300018, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2358, 1692.805175, -1465.289916, 3002.201660, 0.000000, 0.000000, 140.300018, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2358, 1692.703491, -1465.647094, 3001.951416, 0.000000, 0.000000, 140.300018, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19466, 1700.323242, -1464.321899, 3007.815185, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19466, 1693.913085, -1464.321899, 3007.815185, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2048, 1696.964721, -1462.234130, 3008.615966, 0.000000, 0.000000, 180.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2386, 1700.897338, -1473.281127, 3002.111572, 0.000000, 0.000000, 270.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2384, 1701.015625, -1472.587646, 3002.121582, 0.000000, 0.000000, 270.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1431, 1696.651611, -1453.210083, 3006.444580, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1271, 1698.261108, -1453.158081, 3006.264648, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2042, 1698.305541, -1453.158813, 3006.705078, 0.000000, 0.000000, 14.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2043, 1696.809814, -1453.312744, 3007.095458, 0.000000, 0.000000, -86.599990, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2358, 1696.259033, -1453.128662, 3007.105468, 0.000000, 0.000000, 29.699998, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1575, 1697.538208, -1453.278930, 3006.364746, 0.000000, 0.000000, 83.000015, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(348, 1697.394042, -1453.200439, 3006.553955, -79.500007, 0.000000, -49.500000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19826, 1699.839965, -1462.429565, 3007.735351, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19826, 1694.509033, -1462.429565, 3007.735351, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2196, 1697.805297, -1452.999389, 3006.973876, 0.000000, 0.000000, -35.199993, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16779, 1697.131469, -1457.922363, 3009.485107, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19827, 1675.509033, -1463.304199, 3003.402587, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19827, 1675.509033, -1458.004394, 3003.402587, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2344, 1672.924316, -1460.204467, 3002.341796, 0.000000, 0.000000, -19.099998, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19807, 1672.967041, -1460.661987, 3002.401855, 0.000000, 0.000000, 79.500000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2332, 1701.074218, -1471.436523, 3002.279541, 0.000000, 0.000000, 270.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1550, 1701.205932, -1470.794189, 3002.161621, 1.399999, 0.000000, 169.100021, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 1693.698120, -1473.960571, 3003.843017, 0.000000, 0.000000, 180.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2196, 1689.993896, -1471.858276, 3002.611816, 0.000000, 0.000000, 24.799999, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1805, 1700.771850, -1467.404663, 3002.081542, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1805, 1699.931030, -1468.854858, 3002.081542, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1805, 1699.931030, -1468.854858, 3002.081542, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1805, 1699.140502, -1468.994995, 3002.081542, 0.000000, 0.000000, 0.000000, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2674, 1700.244384, -1468.098754, 3001.861572, 0.000000, 0.000000, -86.800003, 2, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1690.338867, -1474.488403, 3005.338623, 0.000000, 0.000000, 90.000000, 2, 0, -1, 200.00, 200.00);

    //YURIX
    ccsst = CreateDynamicObject(18981, 1978.055175, 2428.360107, -10.804260, 0.000000, 270.000000, -179.999984, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14623, "mafcasmain", "casino_carp", 0x00000000);
    ccsst = CreateDynamicObject(18981, 2003.054199, 2428.360107, -10.804260, 0.000000, 270.000000, -179.999938, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14623, "mafcasmain", "casino_carp", 0x00000000);
    ccsst = CreateDynamicObject(18981, 1978.055175, 2453.267089, -10.804260, 0.000000, 270.000000, -179.999938, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14623, "mafcasmain", "casino_carp", 0x00000000);
    ccsst = CreateDynamicObject(18981, 2003.054199, 2453.267089, -10.804260, 0.000000, 270.000000, -179.999893, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14623, "mafcasmain", "casino_carp", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1980.321899, 2433.545654, -8.618469, 0.000000, 0.000029, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1985.051147, 2428.656738, -8.618713, 0.000029, 0.000000, 89.999908, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1994.681396, 2428.656738, -8.618713, 0.000029, 0.000000, 89.999908, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2004.301025, 2428.656738, -8.618713, 0.000029, 0.000000, 89.999908, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1980.321899, 2443.174560, -8.618713, 0.000000, 0.000029, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1985.061157, 2444.302490, -8.618713, 0.000044, 0.000000, 89.999862, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1994.691406, 2444.302490, -8.378479, 0.000044, 0.000000, 89.999862, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2004.301025, 2444.302490, -8.618713, 0.000051, 0.000000, 89.999839, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2003.151367, 2433.545654, -8.438536, 0.000000, 0.000075, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2003.151367, 2443.174560, -8.348448, 0.000000, 0.000067, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(3361, 1991.418334, 2443.326904, -8.425169, 0.000000, -0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 14385, "trailerkb", "tr_wall2", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 4830, "airport2", "sanairtex3", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 14385, "trailerkb", "tr_wood1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 4, 14385, "trailerkb", "tr_wall2", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1981.211914, 2439.571533, -8.628662, -0.000007, -0.000014, -90.000053, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1986.081420, 2444.302490, -8.628662, 0.000014, -0.000037, 179.999725, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1986.081420, 2431.662597, -8.618652, 0.000014, -0.000045, 179.999679, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1985.901611, 2431.662597, -8.618652, 0.000014, -0.000045, 179.999679, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    ccsst = CreateDynamicObject(11714, 1980.406005, 2437.973388, -9.063902, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 5986, "chateau_lawn", "doorkb_1_256", 0x00000000);
    ccsst = CreateDynamicObject(18981, 1973.496215, 2428.360107, -6.374145, 0.000000, 270.000000, -179.999847, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 9525, "boigas_sfw", "GEwhite1_64", 0x00000000);
    ccsst = CreateDynamicObject(18981, 1973.496215, 2453.339599, -6.374145, 0.000000, 270.000000, -179.999847, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 9525, "boigas_sfw", "GEwhite1_64", 0x00000000);
    ccsst = CreateDynamicObject(2290, 1984.048583, 2431.329101, -10.219238, 0.000007, -0.000014, 179.999786, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2290, 1980.977294, 2432.389648, -10.219238, 0.000014, 0.000007, 89.999893, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1981.151367, 2430.565673, -8.618713, 0.000029, 0.000000, 89.999908, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(2290, 1985.285278, 2434.505371, -10.219238, -0.000029, -0.000014, -90.000038, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1579, 1981.122070, 2434.241699, -9.539244, 89.999992, 302.331451, -76.631500, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(1579, 1982.109375, 2431.321289, -9.539244, 89.999992, 222.631454, -76.631500, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(1579, 1983.979248, 2431.328613, -9.539244, 89.999992, 293.531433, -76.631500, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(1579, 1985.293090, 2432.565917, -9.539244, 89.999992, 320.929229, -84.929252, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(1579, 1985.285766, 2434.436035, -9.539244, 89.999992, 391.829254, -84.929252, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(948, 1985.566040, 2435.293457, -10.219238, 0.000007, -0.000007, 179.999832, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2270, 1985.321655, 2431.159179, -7.943908, 0.000000, -0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(948, 1980.765625, 2435.183349, -10.219238, 0.000007, -0.000007, 179.999832, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2270, 1980.900878, 2431.159179, -7.943908, 0.000000, -0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2257, 1983.005371, 2430.672607, -8.109251, 0.000007, -0.000007, 179.999832, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 16150, "ufo_bar", "ufo_pics1", 0x00000000);
    ccsst = CreateDynamicObject(1827, 1979.999755, 2432.909667, -8.179260, -0.000006, 90.000015, 0.000006, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 19480, "signsurf", "sign", 0x00000000);
    ccsst = CreateDynamicObject(1827, 1979.999755, 2433.049804, -7.889282, -0.000006, 90.000015, 0.000006, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 19480, "signsurf", "sign", 0x00000000);
    ccsst = CreateDynamicObject(1827, 1979.999755, 2433.490234, -8.139282, -0.000006, 90.000015, 0.000006, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 19480, "signsurf", "sign", 0x00000000);
    ccsst = CreateDynamicObject(2270, 1985.321655, 2431.159179, -9.244627, 0.000000, -0.000014, 179.999908, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2270, 1980.900878, 2431.159179, -9.244627, 0.000000, -0.000014, 179.999908, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2270, 1984.621093, 2431.159179, -7.943908, 0.000000, -0.000014, 179.999908, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2270, 1984.621093, 2431.159179, -9.244627, 0.000000, -0.000022, 179.999862, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2270, 1981.531372, 2431.159179, -7.943908, 0.000000, -0.000014, 179.999908, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2270, 1981.531372, 2431.159179, -9.244627, 0.000000, -0.000022, 179.999862, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(11728, 1980.333984, 2436.112792, -8.644104, 0.000007, 0.000000, 89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(2247, 1983.035400, 2433.651611, -9.193785, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 3902, "libertyhi3", "glass2_64", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1985.051147, 2428.656738, -5.158507, 0.000037, 0.000000, 89.999885, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(2387, 1982.947998, 2433.513671, -10.619444, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 3922, "bistro", "Marble2", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 4, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1994.681396, 2428.656738, -5.158507, 0.000037, 0.000000, 89.999885, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2004.301025, 2428.656738, -5.158507, 0.000037, 0.000000, 89.999885, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1985.061157, 2444.302490, -5.158507, 0.000051, 0.000000, 89.999839, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1990.090454, 2444.302490, -5.158507, 0.000051, 0.000000, 89.999839, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2004.550781, 2433.545654, -5.158507, 0.000000, 0.000082, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2004.550781, 2443.174560, -5.158507, 0.000000, 0.000082, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1986.081420, 2444.302490, -5.168457, 0.000014, -0.000045, 179.999679, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1986.081420, 2431.662597, -5.158446, 0.000014, -0.000051, 179.999633, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2002.091308, 2444.302490, -5.158507, 0.000051, 0.000000, 89.999839, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1986.081420, 2437.994384, -2.058897, 89.999992, -153.434982, -26.565401, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19378, 1999.201049, 2439.875244, -6.374693, 0.000000, 270.000000, -179.999984, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14623, "mafcasmain", "casino_carp", 0x00000000);
    ccsst = CreateDynamicObject(19378, 1999.191040, 2439.875244, -6.564575, 0.000000, 270.000000, -179.999984, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19378, 1999.191040, 2439.875244, -6.384704, 0.000000, 270.000000, -179.999984, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(19378, 1999.201049, 2430.241943, -6.374693, 0.000000, 270.000000, -179.999938, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14623, "mafcasmain", "casino_carp", 0x00000000);
    ccsst = CreateDynamicObject(19378, 1999.191040, 2430.241943, -6.564575, 0.000000, 270.000000, -179.999938, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19378, 1999.191040, 2430.241943, -6.384704, 0.000000, 270.000000, -179.999938, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(19935, 1994.436645, 2442.227783, -6.294311, 0.000007, 0.000000, 89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    ccsst = CreateDynamicObject(19940, 1994.012939, 2441.049316, -5.508482, 0.000000, 270.000000, -179.999984, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    ccsst = CreateDynamicObject(19940, 1994.012939, 2439.058837, -5.508482, 0.000000, 270.000000, -179.999984, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    ccsst = CreateDynamicObject(19935, 1994.436645, 2437.867919, -6.294311, 0.000007, 0.000000, 89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    ccsst = CreateDynamicObject(19940, 1994.012939, 2436.676757, -5.508482, 0.000000, 270.000000, -179.999938, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    ccsst = CreateDynamicObject(19940, 1994.012939, 2434.686279, -5.508482, 0.000000, 270.000000, -179.999938, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    ccsst = CreateDynamicObject(19935, 1994.436645, 2433.487060, -6.294311, 0.000007, 0.000000, 89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    ccsst = CreateDynamicObject(19940, 1994.012939, 2432.306152, -5.508482, 0.000000, 270.000000, -179.999893, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    ccsst = CreateDynamicObject(19940, 1994.012939, 2430.315673, -5.508482, 0.000000, 270.000000, -179.999893, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    ccsst = CreateDynamicObject(19935, 1994.436645, 2429.125732, -6.294311, 0.000007, 0.000000, 89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    ccsst = CreateDynamicObject(19935, 1994.436645, 2428.695800, -6.294311, 0.000007, 0.000000, 89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1985.051147, 2428.656738, -1.798400, 0.000045, 0.000000, 89.999862, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1994.681396, 2428.656738, -1.798400, 0.000045, 0.000000, 89.999862, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1985.061157, 2444.302490, -1.798400, 0.000059, 0.000000, 89.999816, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2004.550781, 2433.545654, -1.798400, 0.000000, 0.000090, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2004.550781, 2443.174560, -1.798400, 0.000000, 0.000090, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1986.081420, 2444.302490, -1.808349, 0.000014, -0.000051, 179.999633, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1986.081420, 2431.662597, -1.798339, 0.000014, -0.000059, 179.999588, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2004.301025, 2428.656738, -1.798400, 0.000045, 0.000000, 89.999862, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1994.691406, 2444.302490, -1.798400, 0.000059, 0.000000, 89.999816, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2004.301025, 2444.302490, -1.798400, 0.000059, 0.000000, 89.999816, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(18981, 1998.495117, 2428.360107, -0.283508, 0.000000, 270.000000, -179.999801, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 9525, "boigas_sfw", "GEwhite1_64", 0x00000000);
    ccsst = CreateDynamicObject(18981, 1998.495117, 2453.339599, -0.283508, 0.000000, 270.000000, -179.999801, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 9525, "boigas_sfw", "GEwhite1_64", 0x00000000);
    ccsst = CreateDynamicObject(19378, 1988.551025, 2428.661132, -5.484984, 89.999992, 180.000000, -89.999946, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 8482, "csrspalace02", "marble01_128", 0x00000000);
    ccsst = CreateDynamicObject(18766, 1989.897338, 2428.388671, -5.504333, 0.000000, 270.000000, -179.999984, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(18766, 1989.897338, 2433.058105, -0.394104, -89.999992, 180.000000, 89.999992, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(2257, 1989.886352, 2428.932861, -5.828734, 0.000007, -0.000014, 179.999786, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 3945, "bistro_alpha", "creme128", 0x00000000);
    ccsst = CreateDynamicObject(2257, 1989.846313, 2428.932861, -7.898985, 0.000000, 180.000000, 179.999786, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 3945, "bistro_alpha", "creme128", 0x00000000);
    ccsst = CreateDynamicObject(2315, 1990.357788, 2429.366699, -10.304260, 0.000000, 0.000022, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2315, 1987.957641, 2429.366699, -10.304260, 0.000000, 0.000022, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19378, 1988.551025, 2433.411376, -0.714781, 0.000000, 90.000015, 0.000045, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 8482, "csrspalace02", "marble01_128", 0x00000000);
    ccsst = CreateDynamicObject(2290, 1988.883544, 2434.123291, -10.364990, 0.000000, 0.000029, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2290, 1991.835083, 2433.272460, -10.364990, -0.000029, 0.000000, -89.999908, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1579, 1991.794067, 2433.319335, -9.684996, 89.999992, 56.000022, -89.999961, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(1746, 1991.873535, 2434.145507, -10.337402, 0.000000, 0.000014, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1579, 1991.690429, 2431.420410, -9.684996, 89.999992, 135.700012, -89.999961, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(1579, 1990.822631, 2434.131103, -9.684996, 89.999992, 56.000022, -89.999961, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(1579, 1988.952636, 2434.123535, -9.684996, 89.999992, 126.900009, -89.999961, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(2290, 1987.940185, 2431.276855, -10.364990, 0.000007, 0.000007, 89.999916, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1579, 1987.981201, 2431.229980, -9.684996, 89.999992, 229.225997, -83.225975, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(1579, 1988.084960, 2433.128906, -9.684996, 89.999992, 308.925964, -83.225975, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(1746, 1987.903198, 2434.145507, -10.337402, 0.000000, 0.000014, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1994.801147, 2429.863037, -8.158264, 0.000000, 0.000068, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19428, 1994.810913, 2435.071533, -8.214964, 0.000000, 0.000014, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1994.801147, 2429.863037, -11.627867, 0.000000, 0.000059, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19428, 1994.830932, 2442.653076, -8.394714, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19428, 1994.830932, 2442.653076, -11.654907, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19428, 1994.830932, 2444.203857, -8.394714, 0.000000, 0.000014, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19428, 1994.830932, 2444.203857, -11.654907, 0.000000, 0.000014, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19428, 1994.830932, 2441.042236, -8.214964, 0.000000, 0.000014, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2002.670776, 2434.576904, -8.228331, 0.000037, 0.000000, 89.999885, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19397, 1996.483398, 2434.582763, -8.224060, 0.000014, 0.000000, 89.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(1499, 1995.707885, 2434.563476, -10.583740, 0.000000, 0.000014, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2002.670776, 2434.576904, -11.618286, 0.000037, 0.000000, 89.999885, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19428, 1994.810913, 2435.071533, -11.714598, 0.000000, 0.000014, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    ccsst = CreateDynamicObject(19428, 1994.830932, 2441.042236, -11.724487, 0.000000, 0.000014, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1995.601806, 2429.863037, -8.158264, 0.000000, 0.000082, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19397, 1996.483398, 2434.592773, -8.823973, 0.000014, 0.000000, 89.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1995.601806, 2429.863037, -11.627867, 0.000000, 0.000075, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2003.151367, 2433.545654, -11.938475, 0.000000, 0.000081, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2003.151367, 2443.174560, -11.838438, 0.000000, 0.000075, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2001.740844, 2429.747558, -8.538635, 0.000000, 0.000075, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19379, 2000.171264, 2429.707275, -10.384155, 0.000000, 270.000000, -179.999984, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    ccsst = CreateDynamicObject(19466, 2000.036499, 2430.870117, -9.360960, 0.000007, -0.000029, 179.999771, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-20-percent", 0x50FFFFFF);
    ccsst = CreateDynamicObject(19466, 2000.036499, 2430.870117, -7.431396, 0.000007, -0.000029, 179.999771, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-20-percent", 0x50FFFFFF);
    ccsst = CreateDynamicObject(11707, 1999.934448, 2431.872558, -9.039366, -0.000029, 270.000000, -89.999923, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 3, 19480, "signsurf", "sign", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2000.419921, 2429.775390, -8.618713, 0.000029, 0.000000, 89.999908, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19937, 1997.267456, 2430.909912, -9.683287, 0.000007, 269.999969, 89.999938, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19937, 1998.447387, 2430.909912, -10.283630, 89.999992, 420.324005, -60.324039, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19937, 1996.096801, 2430.909912, -10.283630, 89.999992, 420.324005, -60.324039, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19428, 1997.263549, 2429.795410, -9.302734, 0.000014, 180.000000, 89.999923, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 3922, "bistro", "Marble", 0x00000000);
    ccsst = CreateDynamicObject(19787, 1997.275756, 2429.842529, -8.649597, 0.000000, 89.999984, 179.999862, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    ccsst = CreateDynamicObject(2515, 2000.880615, 2430.122070, -9.326109, 0.000007, -0.000014, 179.999862, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 19480, "signsurf", "sign", 0x00000000);
    ccsst = CreateDynamicObject(11707, 2000.944946, 2434.423583, -9.039366, 0.000000, 720.000000, 0.000091, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 3, 14801, "lee_bdupsmain", "ahomcarpet2", 0x00000000);
    ccsst = CreateDynamicObject(2140, 2001.982910, 2443.707519, -10.669981, 0.000000, 0.000020, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1736, "cj_ammo", "CJ_Black_metal", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 18996, "mattextures", "sampblack", 0x00000000);
    ccsst = CreateDynamicObject(19173, 2001.907470, 2443.508056, -8.972961, 0.000000, 90.000015, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ccsst = CreateDynamicObject(19173, 2002.667724, 2443.508056, -8.972961, 0.000000, 89.999984, 179.999908, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ccsst = CreateDynamicObject(2140, 2002.553222, 2443.707519, -10.669981, 0.000000, 0.000020, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1736, "cj_ammo", "CJ_Black_metal", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 18996, "mattextures", "sampblack", 0x00000000);
    ccsst = CreateDynamicObject(2265, 2002.656616, 2443.019531, -8.884338, 0.000000, 0.000014, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 2669, "cj_chris", "Bow_Fence_Metal", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18996, "mattextures", "sampblack", 0x00000000);
    ccsst = CreateDynamicObject(11707, 2002.342041, 2443.492431, -8.874205, 0.000000, 90.000015, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 2669, "cj_chris", "Bow_Fence_Metal", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 19962, "samproadsigns", "materialtext1", 0x00000000);
    ccsst = CreateDynamicObject(11707, 2002.202026, 2443.492431, -8.874205, 0.000000, 90.000015, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 2669, "cj_chris", "Bow_Fence_Metal", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 19962, "samproadsigns", "materialtext1", 0x00000000);
    ccsst = CreateDynamicObject(2714, 2002.696899, 2443.479980, -8.665344, 0.000000, 0.000073, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterialText(ccsst, 0, "T", 130, "Wingdings", 40, 0, 0xFF363636, 0x00000000, 1);
    ccsst = CreateDynamicObject(2714, 2002.626831, 2443.479980, -8.665344, 0.000000, 0.000073, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterialText(ccsst, 0, "-10", 130, "Arial", 30, 0, 0xFF363636, 0x00000000, 1);
    ccsst = CreateDynamicObject(2714, 2001.655761, 2443.477294, -7.938415, 0.000004, 0.000036, -0.000059, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterialText(ccsst, 0, "SAMSUNG", 130, "Arial", 25, 0, 0xFF000000, 0x00000000, 1);
    ccsst = CreateDynamicObject(2714, 2002.659545, 2443.482666, -8.398254, 0.000000, 0.000080, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterialText(ccsst, 0, "n", 130, "Webdings", 50, 0, 0xFFFBBB02, 0x00000000, 1);
    ccsst = CreateDynamicObject(19527, 2002.454956, 2444.274414, -8.502014, 5.300036, 270.000000, 86.499801, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 6349, "sunbill_law2", "SunBillB01", 0x00000000);
    SetDynamicObjectMaterialText(ccsst, 1, "   +32     +32     +32     +32", 100, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    ccsst = CreateDynamicObject(2714, 2002.662719, 2443.479980, -8.569151, 0.000000, 0.000073, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterialText(ccsst, 0, "AccuWeather", 130, "Arial", 25, 0, 0xFFCDCDCD, 0x00000000, 1);
    ccsst = CreateDynamicObject(2714, 2002.772705, 2443.479980, -8.218811, 0.000000, 0.000073, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterialText(ccsst, 0, "SUN 12:30", 130, "Arial", 20, 1, 0xFF363636, 0x00000000, 1);
    ccsst = CreateDynamicObject(2714, 2002.662841, 2443.479980, -8.609190, 0.000000, 0.000073, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterialText(ccsst, 0, "SAMSUNG", 130, "Arial", 17, 1, 0xFFFFFFFF, 0x00000000, 1);
    ccsst = CreateDynamicObject(2139, 2000.973388, 2443.613525, -10.304260, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 3922, "bistro", "marblekb_256128", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(2139, 1999.983520, 2443.613525, -10.304260, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 3922, "bistro", "marblekb_256128", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(2139, 1999.013305, 2443.613525, -10.304260, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 3922, "bistro", "marblekb_256128", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(2139, 1998.033081, 2443.613525, -10.304260, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 3922, "bistro", "marblekb_256128", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(2139, 1997.062988, 2443.613525, -10.304260, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 3922, "bistro", "marblekb_256128", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(2518, 1996.545898, 2443.542724, -10.044310, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 6871, "vegascourtbld", "marbletilewal1_256", 0x00000000);
    ccsst = CreateDynamicObject(2518, 1997.176513, 2443.542724, -10.044310, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 6871, "vegascourtbld", "marbletilewal1_256", 0x00000000);
    ccsst = CreateDynamicObject(18762, 1998.805175, 2441.858642, -9.903868, 89.999992, 179.999984, -90.000007, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19935, 1996.796752, 2441.379394, -6.754210, 0.000000, 270.000000, -90.000007, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19935, 1997.767333, 2441.379394, -6.754210, 0.000000, 270.000000, -90.000007, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19935, 1998.747802, 2441.379394, -6.754210, 0.000000, 270.000000, -90.000007, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19935, 1999.707763, 2441.379394, -6.754210, 0.000000, 270.000000, -90.000007, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19935, 2000.697387, 2441.379394, -6.754210, 0.000000, 270.000000, -90.000007, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2350, 1996.642211, 2440.415527, -9.974487, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 5, 16640, "a51", "ws_metalpanel1", 0x00000000);
    ccsst = CreateDynamicObject(2350, 1997.952758, 2440.415527, -9.974487, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 5, 16640, "a51", "ws_metalpanel1", 0x00000000);
    ccsst = CreateDynamicObject(2350, 1999.112670, 2440.415527, -9.974487, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 5, 16640, "a51", "ws_metalpanel1", 0x00000000);
    ccsst = CreateDynamicObject(2350, 2000.532470, 2440.415527, -9.974487, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 5, 16640, "a51", "ws_metalpanel1", 0x00000000);
    ccsst = CreateDynamicObject(2115, 2000.666503, 2436.445800, -10.304260, 0.000000, 0.000014, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    ccsst = CreateDynamicObject(2115, 1998.696044, 2436.445800, -10.304260, 0.000000, 0.000014, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    ccsst = CreateDynamicObject(2123, 1998.674682, 2434.944580, -9.744261, -0.000007, 0.000000, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(2123, 1999.714965, 2434.944580, -9.744261, -0.000007, 0.000000, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(2123, 2000.614868, 2434.944580, -9.744261, -0.000007, 0.000000, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(2123, 2001.705688, 2434.944580, -9.744261, -0.000007, 0.000000, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(2123, 2001.705688, 2437.946533, -9.744261, 0.000007, 0.000000, 89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(2123, 2000.685546, 2437.946533, -9.744261, 0.000007, 0.000000, 89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(2123, 1999.696044, 2437.946533, -9.744261, 0.000014, 0.000000, 89.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(2123, 1998.576049, 2437.946533, -9.744261, 0.000014, 0.000000, 89.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(2163, 1994.682495, 2433.806884, -10.304260, -0.000014, 0.000000, -89.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2163, 1994.682495, 2432.066650, -10.304260, -0.000014, 0.000000, -89.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2163, 1994.682495, 2430.316894, -10.304260, -0.000014, 0.000000, -89.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19878, 1994.534790, 2429.981445, -9.314024, 0.000007, 0.000000, 89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18901, "matclothes", "bandanaskull", 0x00000000);
    ccsst = CreateDynamicObject(2257, 1985.866088, 2428.932861, -7.898985, 0.000007, 180.000000, 89.999794, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 3945, "bistro_alpha", "creme128", 0x00000000);
    ccsst = CreateDynamicObject(2257, 1986.177124, 2432.184326, -7.898985, 0.000007, 180.000000, 89.999794, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 4981, "wiresetc2_las", "lasunionclk", 0x00000000);
    ccsst = CreateDynamicObject(19397, 1996.483398, 2444.304687, -4.713684, 0.000000, 0.000000, -90.000007, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(1499, 1997.258422, 2444.282958, -6.423767, 0.000000, 0.000000, 179.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1994.691406, 2444.302490, -11.868530, 0.000044, 0.000000, 89.999862, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(18981, 1998.503051, 2456.897949, -6.754333, 0.000000, 270.000000, -179.999893, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14623, "mafcasmain", "casino_carp", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1986.081420, 2453.898437, -5.168457, 0.000014, -0.000059, 179.999588, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1986.081420, 2453.898437, -1.808349, 0.000014, -0.000067, 179.999542, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1986.081420, 2463.516601, -5.168457, 0.000014, -0.000067, 179.999542, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1986.081420, 2463.516601, -1.808349, 0.000014, -0.000074, 179.999496, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2004.552001, 2452.797363, -5.168457, 0.000014, -0.000067, 179.999542, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2004.552001, 2452.797363, -1.808349, 0.000014, -0.000074, 179.999496, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2004.552001, 2462.415527, -5.168457, 0.000014, -0.000075, 179.999496, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2004.552001, 2462.415527, -1.808349, 0.000014, -0.000081, 179.999450, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2000.080078, 2461.936035, -5.168457, -0.000007, -0.000068, -90.000328, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2000.080078, 2461.936035, -1.808349, -0.000007, -0.000075, -90.000373, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1990.461914, 2461.936035, -5.168457, -0.000007, -0.000075, -90.000373, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1990.461914, 2461.936035, -1.808349, -0.000007, -0.000082, -90.000419, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1995.681518, 2465.844238, -5.168457, -0.000007, -0.000037, -0.000357, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1995.681518, 2465.844238, -1.808349, -0.000007, -0.000044, -0.000403, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1995.681518, 2456.226074, -5.168457, -0.000007, -0.000045, -0.000403, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1995.681518, 2456.226074, -1.808349, -0.000007, -0.000051, -0.000449, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1995.521362, 2465.844238, -5.168457, -0.000007, -0.000029, -0.000357, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1995.521362, 2465.844238, -1.808349, -0.000007, -0.000037, -0.000403, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1995.521362, 2456.226074, -5.168457, -0.000007, -0.000037, -0.000403, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1995.521362, 2456.226074, -1.808349, -0.000007, -0.000044, -0.000449, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(18981, 1998.495117, 2456.821777, -2.693542, 0.000000, 270.000000, -179.999801, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 9525, "boigas_sfw", "GEwhite1_64", 0x00000000);
    ccsst = CreateDynamicObject(1579, 1981.093139, 2432.521972, -9.539244, 89.999992, 332.233276, -16.533351, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1986.250610, 2456.219482, -4.838500, 0.000014, -0.000059, 179.999588, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1986.250610, 2465.819091, -4.838500, 0.000014, -0.000059, 179.999588, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1990.711914, 2461.675781, -4.958312, -0.000007, -0.000075, -90.000373, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19397, 1993.843017, 2451.434814, -4.713684, 0.000000, 0.000000, -90.000007, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19397, 1993.843017, 2451.614990, -4.713684, 0.000000, 0.000000, -90.000007, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1987.431762, 2451.435058, -4.958312, -0.000007, -0.000075, -90.000373, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1987.431762, 2451.625244, -4.958312, -0.000007, -0.000075, -90.000373, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19397, 1996.992675, 2451.434814, -4.713684, 0.000000, 0.000000, -90.000007, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2003.331542, 2451.435058, -4.958312, -0.000007, -0.000075, -90.000373, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(1499, 1997.758789, 2451.412597, -6.423767, 0.000000, 0.000000, 179.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(1499, 1994.638916, 2451.412597, -6.423767, 0.000000, 0.000000, 179.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(1746, 1999.754150, 2455.506835, -6.277403, 0.000000, 0.000029, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1746, 2000.724487, 2455.506835, -6.277403, 0.000000, 0.000029, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1746, 1999.754150, 2456.467773, -6.277403, 0.000000, 0.000036, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1746, 2000.724487, 2456.467773, -6.277403, 0.000000, 0.000036, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1746, 1999.754150, 2457.428466, -6.277403, 0.000000, 0.000044, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1746, 2000.724487, 2457.428466, -6.277403, 0.000000, 0.000044, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1746, 1999.754150, 2458.419433, -6.277403, 0.000000, 0.000051, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1746, 2000.724487, 2458.419433, -6.277403, 0.000000, 0.000051, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1746, 1999.754150, 2459.370361, -6.277403, 0.000000, 0.000059, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1746, 2000.724487, 2459.370361, -6.277403, 0.000000, 0.000059, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1746, 1999.754150, 2454.550292, -6.277403, 0.000000, 0.000075, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1746, 2000.724487, 2454.550292, -6.277403, 0.000000, 0.000075, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2290, 2001.899169, 2450.850830, -6.318968, 0.000000, 0.000029, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    ccsst = CreateDynamicObject(2290, 2003.939941, 2450.720703, -6.318968, -0.000022, 0.000000, -89.999931, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    ccsst = CreateDynamicObject(2387, 2001.392211, 2448.098388, -6.258728, 0.000000, 0.000029, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 14581, "ab_mafiasuitea", "ab_books", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 4, 4010, "lanblokb2", "bluewhitebuildwall2", 0x00000000);
    ccsst = CreateDynamicObject(2290, 2003.947998, 2447.711914, -6.318968, -0.000007, 0.000014, -90.000015, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    ccsst = CreateDynamicObject(1579, 2003.795288, 2448.868652, -5.638977, 89.999992, 135.699996, -89.999969, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(1579, 2003.838256, 2450.858642, -5.638977, 89.999992, 56.000022, -89.999961, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(1579, 2001.968261, 2450.851074, -5.638977, 89.999992, 126.900009, -89.999961, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(2290, 2003.947998, 2445.670898, -6.318968, -0.000014, -0.000007, 179.999862, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    ccsst = CreateDynamicObject(19893, 2001.363769, 2448.616210, -5.258728, 0.000015, -0.000024, 145.399978, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 14802, "lee_bdupsflat", "Bdup_Poster", 0x00000000);
    ccsst = CreateDynamicObject(2257, 2004.392578, 2450.436035, -4.208983, -0.000007, 0.000014, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 1583, "targets", "target1", 0x00000000);
    ccsst = CreateDynamicObject(1579, 2002.095947, 2445.815673, -5.638977, 89.999992, 380.171173, -64.471237, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(1579, 2003.955810, 2445.772705, -5.638977, 89.999992, 300.471191, -64.471237, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(1579, 2003.948486, 2447.642578, -5.638977, 89.999992, 371.371185, -64.471237, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(2257, 2004.504150, 2446.628417, -4.208983, -0.000007, 0.000014, -90.000015, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 16150, "ufo_bar", "ufo_pics1", 0x00000000);
    ccsst = CreateDynamicObject(2290, 1999.063110, 2450.834228, -6.318968, -0.000022, 0.000000, -0.000127, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    ccsst = CreateDynamicObject(2290, 2001.057617, 2445.670898, -6.318968, -0.000014, -0.000014, 179.999816, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    ccsst = CreateDynamicObject(1579, 1999.205566, 2445.815673, -5.638977, 89.999992, 392.331451, -76.631500, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(1579, 2001.065429, 2445.772705, -5.638977, 89.999992, 312.631469, -76.631500, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(1579, 2000.915161, 2450.689697, -5.638977, 89.999992, 549.777465, -54.077556, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(1579, 1999.055297, 2450.732666, -5.638977, 89.999992, 470.077484, -54.077556, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    ccsst = CreateDynamicObject(18762, 2001.219726, 2444.804931, -6.254333, 89.999992, 179.999984, -90.000007, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(18762, 2006.149658, 2444.804931, -6.254333, 89.999992, 179.999984, -90.000007, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(18762, 2001.219726, 2444.804931, -3.083128, 89.999992, 179.999984, -89.999984, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(18762, 2006.149658, 2444.804931, -3.083128, 89.999992, 179.999984, -89.999984, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2000.471679, 2445.193359, -4.388366, 89.999992, 442.874969, -172.875091, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1649, "wglass", "carshowwin2", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2002.722656, 2445.193359, -4.388366, 89.999992, 442.874969, -172.875091, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 1649, "wglass", "carshowwin2", 0x00000000);
    ccsst = CreateDynamicObject(18762, 1998.239990, 2444.804931, -5.623106, 0.000000, 0.000000, 90.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2387, 2000.132080, 2448.098388, -6.258728, 0.000000, 0.000037, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 3, 14581, "ab_mafiasuitea", "ab_books", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 4, 4010, "lanblokb2", "bluewhitebuildwall2", 0x00000000);
    ccsst = CreateDynamicObject(19893, 2000.103637, 2448.616210, -5.258728, 0.000020, -0.000030, 145.399978, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 14802, "lee_bdupsflat", "Bdup_Poster", 0x00000000);
    ccsst = CreateDynamicObject(2257, 2004.392578, 2446.353759, -4.208983, -0.000007, 0.000014, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 1583, "targets", "target2", 0x00000000);
    ccsst = CreateDynamicObject(2257, 1994.282226, 2444.433105, -4.208983, 0.000000, 0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 15040, "cuntcuts", "piratesign01_128", 0x00000000);
    ccsst = CreateDynamicObject(2257, 1990.651611, 2444.433105, -4.208983, 0.000000, 0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 15040, "cuntcuts", "GB_tile01", 0x00000000);
    ccsst = CreateDynamicObject(2257, 1987.851196, 2444.433105, -4.208983, 0.000000, 0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 14534, "ab_wooziea", "ab_wuzibet", 0x00000000);
    ccsst = CreateDynamicObject(19379, 1990.244750, 2456.474609, -3.154541, 0.000000, 630.000000, -179.999984, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19379, 1990.244750, 2465.293701, -3.154541, 0.000000, 630.000000, -179.999984, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19604, 1987.998168, 2457.342285, -3.254028, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14564, "triad_ext", "ab_spotlite", 0x00000000);
    ccsst = CreateDynamicObject(19604, 1992.957763, 2457.342285, -3.254028, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14564, "triad_ext", "ab_spotlite", 0x00000000);
    ccsst = CreateDynamicObject(16151, 1987.394409, 2456.006835, -5.934082, 0.000000, -0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 3, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 7, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 8, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 9, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2257, 1986.353637, 2458.074218, -4.244262, 0.000007, 0.000000, 89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 7073, "vgnfremntsgn", "averysigneon1a_256", 0x00000000);
    ccsst = CreateDynamicObject(2257, 1986.353637, 2458.074218, -6.344116, 0.000007, 0.000000, 89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 7073, "vgnfremntsgn", "averysigneon2a_256", 0x00000000);
    ccsst = CreateDynamicObject(2315, 1991.442504, 2460.404541, -6.254333, 0.000000, 0.000014, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2001.071289, 2444.212402, -8.288452, 0.000051, 0.000000, 89.999839, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 6048, "mall_law", "wolf3", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2001.071289, 2444.212402, -11.838377, 0.000051, 0.000000, 89.999839, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 6048, "mall_law", "wolf3", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2002.009155, 2437.947509, -5.158507, 0.000022, 0.000082, 89.999900, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2002.009155, 2437.947509, -1.798400, 0.000022, 0.000090, 89.999900, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2002.009155, 2433.447265, -5.158507, 0.000029, 0.000082, 89.999877, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 2002.009155, 2433.447265, -1.798400, 0.000029, 0.000090, 89.999877, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1997.288818, 2425.335205, -5.158507, 0.000029, 0.000059, 179.999710, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1997.288818, 2433.063720, -1.798400, 0.000029, 0.000067, 179.999710, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(14446, 2002.722656, 2441.241210, -5.741455, -0.000007, 0.000000, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    ccsst = CreateDynamicObject(19397, 1997.284423, 2431.751464, -4.564757, 0.000007, -0.000007, 179.999923, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1997.278808, 2438.240478, -1.798400, 89.999992, -165.963668, -14.036517, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19397, 1997.284423, 2434.922363, -4.564757, 0.000007, -0.000007, 179.999923, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1997.258789, 2439.311035, -1.798400, 89.999992, -165.963668, -14.036517, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19397, 1997.284423, 2442.623291, -4.584898, 0.000007, -0.000007, 179.999923, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(19447, 1997.278808, 2442.470458, 2.000976, 89.999992, -165.963668, -14.036517, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(1499, 1997.258422, 2443.352783, -6.353758, -0.000007, 0.000007, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(1499, 1997.258422, 2435.661865, -6.353758, -0.000007, 0.000007, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(1499, 1997.258422, 2432.490478, -6.353758, -0.000007, 0.000007, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    ccsst = CreateDynamicObject(1796, 2000.908203, 2434.245849, -6.303100, -0.000007, 0.000000, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 18065, "ab_sfammumain", "gun_floor2", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2257, 2004.436889, 2441.419433, -3.756042, -0.000007, 0.000000, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2169, 2002.955566, 2437.398925, -6.294127, 0.000000, 0.000014, -0.000037, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    ccsst = CreateDynamicObject(2388, 2004.415283, 2437.252197, -6.794127, -0.000014, 0.000000, -89.999992, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(19832, 2002.669189, 2437.475585, -5.304138, 0.000031, 180.000000, 89.999984, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-60-percent", 0x00000000);
    ccsst = CreateDynamicObject(19832, 2002.819335, 2437.475585, -5.304138, 0.000031, 180.000000, 89.999984, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-60-percent", 0x00000000);
    ccsst = CreateDynamicObject(19832, 2002.819335, 2437.475585, -5.304138, -0.000028, 359.999938, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-60-percent", 0x00000000);
    ccsst = CreateDynamicObject(19832, 2002.669189, 2437.475585, -5.304138, -0.000028, 359.999938, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-60-percent", 0x00000000);
    ccsst = CreateDynamicObject(1654, 2002.740234, 2437.350097, -5.470396, -0.000007, -179.999984, -0.000006, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    ccsst = CreateDynamicObject(1806, 2003.780029, 2435.909912, -6.293762, 0.000003, 0.000014, 19.200033, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 1, 18646, "matcolours", "grey-70-percent", 0x00000000);
    ccsst = CreateDynamicObject(2167, 1997.853393, 2437.830078, -6.288756, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    ccsst = CreateDynamicObject(2167, 1997.853393, 2437.830078, -6.288756, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    ccsst = CreateDynamicObject(2167, 1997.853393, 2437.830078, -4.798950, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    ccsst = CreateDynamicObject(2163, 1998.739501, 2437.835937, -6.288756, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    ccsst = CreateDynamicObject(2163, 2000.509643, 2437.835937, -6.288756, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    ccsst = CreateDynamicObject(2270, 2001.541015, 2437.382568, -4.641540, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2270, 1998.670288, 2437.382568, -4.641540, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1796, 2000.908203, 2432.884765, -6.313109, -0.000007, 0.000000, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 18065, "ab_sfammumain", "gun_floor2", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(1796, 2000.908203, 2429.485595, -6.313109, -0.000007, 0.000000, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 1, 18065, "ab_sfammumain", "gun_floor2", 0x00000000);
    SetDynamicObjectMaterial(ccsst, 2, 18646, "matcolours", "grey-93-percent", 0x00000000);
    ccsst = CreateDynamicObject(2163, 2004.549438, 2431.457275, -6.288756, -0.000007, 0.000000, -89.999977, 222, 2, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ccsst, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(19786, 1989.875610, 2428.803710, -8.014221, 0.000000, -0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(14705, 1987.782348, 2429.221923, -9.565306, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2853, 1988.470214, 2429.307617, -9.808714, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2824, 1991.429321, 2429.263427, -9.782530, 0.000000, -0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2028, 1990.591186, 2429.281250, -9.783142, 0.000000, -0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2028, 1989.201171, 2429.281250, -9.783142, 0.000000, -0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1719, 1989.861694, 2429.254638, -9.754211, 0.000000, -0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(14705, 1992.062988, 2429.221923, -9.565306, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2247, 1996.015258, 2430.282958, -9.182556, 0.000014, 0.000007, 89.999923, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2247, 1998.386108, 2430.282958, -9.182556, 0.000014, 0.000007, 89.999923, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2515, 1997.309936, 2430.432373, -9.326109, 0.000007, -0.000014, 179.999862, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, 1996.537963, 2430.003662, -9.435240, 0.000014, 0.000007, 89.999923, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, 1996.582885, 2430.484619, -9.447447, 0.000007, -0.000014, 179.999862, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2749, 1997.974243, 2430.428466, -9.477783, 0.000007, -0.000014, 179.999862, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2750, 1997.361694, 2430.921386, -9.854002, 0.000007, -0.000014, 179.999862, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2528, 1999.201416, 2430.344238, -10.298216, 0.000000, -0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2741, 1998.466918, 2429.897460, -8.707762, 0.000000, -0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2741, 2000.836791, 2429.897460, -7.677307, 0.000000, -0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(11743, 2001.210449, 2443.856689, -9.274107, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2149, 2000.316284, 2443.877197, -9.083923, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2865, 1998.772827, 2443.812011, -9.213745, 0.000000, -0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2073, 1996.598754, 2442.018310, -7.163634, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2073, 1997.308715, 2442.018310, -7.163634, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2073, 1997.919067, 2442.018310, -7.163634, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2073, 1998.599609, 2442.018310, -7.163634, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2073, 1999.269165, 2442.018310, -7.163634, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2073, 1999.919189, 2442.018310, -7.163634, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2073, 2000.619506, 2442.018310, -7.163634, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2868, 2000.210937, 2436.481201, -9.514709, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2073, 2000.209472, 2436.446533, -7.163634, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2332, 1992.813598, 2442.066406, -9.954100, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2332, 1993.643554, 2442.066406, -9.954100, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2332, 1992.813598, 2442.066406, -9.004028, 0.000000, 0.000014, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2332, 1993.643554, 2442.066406, -9.004028, 0.000000, 0.000014, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2332, 1992.813598, 2442.066406, -8.084045, 0.000000, 0.000022, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2332, 1993.643554, 2442.066406, -8.084045, 0.000000, 0.000022, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2828, 1994.440063, 2431.762695, -9.383972, 0.000007, 0.000000, 76.799987, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(19617, 1994.724365, 2431.758789, -8.524107, -0.000007, 0.000000, -89.999977, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2484, 1994.557373, 2433.329101, -8.614134, 0.000007, 0.000000, 89.999977, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2951, 2004.513793, 2454.377197, -6.793395, -0.000014, 0.000000, -89.999992, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(348, 2004.415649, 2456.038818, -4.465270, -0.000014, 0.000000, -89.999992, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(357, 2004.286987, 2454.915283, -4.434020, 4.099980, 7.400002, -84.699913, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(357, 2004.326782, 2454.861572, -4.922301, 4.099980, 7.400002, -84.699913, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(357, 2004.375610, 2454.814208, -5.537536, 4.099980, 7.400002, -84.699913, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(355, 2004.421508, 2453.467529, -4.914488, -0.000014, 1.799998, -86.699913, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(355, 2004.424194, 2453.417724, -5.515075, -0.000014, 0.999997, -86.699913, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(348, 2004.415649, 2456.038818, -5.055113, -0.000014, 0.000000, -89.999992, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(348, 2004.415649, 2456.038818, -5.637145, -0.000014, 0.000000, -89.999992, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(957, 2004.157836, 2454.288330, -3.234802, -0.000014, 0.000000, -89.999992, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(355, 2004.381713, 2453.442871, -4.364256, -0.000007, 1.799998, -86.699935, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2951, 2004.503784, 2459.427978, -6.783386, -0.000037, 0.000000, -89.999923, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(348, 2004.405639, 2461.089599, -4.455261, -0.000037, 0.000000, -89.999923, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(357, 2004.276977, 2459.966064, -4.424011, 4.099956, 7.400002, -84.699844, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(357, 2004.316772, 2459.912353, -4.912292, 4.099956, 7.400002, -84.699844, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(357, 2004.365600, 2459.864990, -5.527525, 4.099956, 7.400002, -84.699844, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(355, 2004.411499, 2458.518310, -4.904479, -0.000037, 1.799998, -86.699844, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(355, 2004.414184, 2458.468505, -5.505064, -0.000037, 0.999998, -86.699844, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(348, 2004.405639, 2461.089599, -5.045104, -0.000037, 0.000000, -89.999923, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(348, 2004.405639, 2461.089599, -5.627136, -0.000037, 0.000000, -89.999923, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(957, 2004.147827, 2459.339111, -3.224792, -0.000037, 0.000000, -89.999923, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(355, 2004.411621, 2458.502197, -4.404234, -0.000037, 1.799998, -86.699844, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2951, 1995.785278, 2459.409423, -6.793395, 0.000014, 0.000007, 89.999855, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(348, 1995.883422, 2457.747802, -4.465270, 0.000014, 0.000007, 89.999855, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(357, 1996.011840, 2458.871337, -4.434020, 4.100011, 7.400008, 95.299934, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(357, 1995.972290, 2458.925048, -4.922301, 4.100011, 7.400008, 95.299934, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(357, 1995.923461, 2458.972412, -5.537536, 4.100011, 7.400008, 95.299934, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(355, 1995.877563, 2460.319091, -4.914488, 0.000014, 1.800004, 93.299934, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(355, 1995.874633, 2460.368896, -5.515075, 0.000014, 1.000002, 93.299934, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(348, 1995.883422, 2457.747802, -5.055113, 0.000014, 0.000007, 89.999855, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(348, 1995.883422, 2457.747802, -5.637145, 0.000014, 0.000007, 89.999855, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(957, 1996.141235, 2459.498291, -3.234802, 0.000014, 0.000007, 89.999855, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(355, 1995.917114, 2460.343750, -4.364256, 0.000022, 1.800003, 93.299911, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2951, 1995.795043, 2454.358642, -6.783386, -0.000007, 0.000007, 89.999923, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(348, 1995.893188, 2452.697021, -4.455261, -0.000007, 0.000007, 89.999923, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(357, 1996.022094, 2453.820556, -4.424011, 4.099987, 7.400009, 95.300003, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(357, 1995.982055, 2453.874267, -4.912292, 4.099987, 7.400009, 95.300003, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(357, 1995.933227, 2453.921630, -5.527525, 4.099987, 7.400009, 95.300003, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(355, 1995.887329, 2455.268310, -4.904479, -0.000006, 1.800004, 93.300003, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(355, 1995.884887, 2455.318115, -5.505064, -0.000006, 1.000005, 93.300003, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(348, 1995.893188, 2452.697021, -5.045104, -0.000007, 0.000007, 89.999923, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(348, 1995.893188, 2452.697021, -5.627136, -0.000007, 0.000007, 89.999923, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(957, 1996.151000, 2454.447509, -3.224792, -0.000007, 0.000007, 89.999923, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(355, 1995.887329, 2455.284423, -4.404234, -0.000006, 1.800004, 93.300003, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2475, 1996.907470, 2461.798339, -6.096129, 0.000000, 0.000022, -0.000037, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(19832, 1996.843017, 2461.667968, -5.278747, 0.000000, -0.000022, 179.999725, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(11736, 1996.858642, 2461.574218, -5.908629, -0.000009, 0.000018, -0.000037, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(11736, 1997.398925, 2461.574218, -5.908629, -0.000009, 0.000018, -0.000037, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(11738, 1997.444824, 2461.692626, -5.229918, -0.000000, 0.000022, -4.500038, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1242, 1996.790527, 2461.688232, -4.413513, 0.000000, -0.000022, 173.999725, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1242, 1997.359619, 2461.708740, -4.419372, 0.000000, -0.000022, 179.999725, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2475, 1998.407348, 2461.798339, -6.096129, 0.000000, 0.000029, -0.000037, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(19832, 1998.342895, 2461.667968, -5.278747, 0.000000, -0.000029, 179.999679, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(11736, 1998.358520, 2461.574218, -5.908629, -0.000009, 0.000024, -0.000037, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(11736, 1998.898803, 2461.574218, -5.908629, -0.000009, 0.000024, -0.000037, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(11738, 1998.944702, 2461.692626, -5.229918, -0.000000, 0.000029, -4.500038, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1242, 1998.290405, 2461.688232, -4.413513, 0.000001, -0.000029, 173.999679, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1242, 1998.859497, 2461.708740, -4.419372, 0.000000, -0.000029, 179.999679, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2475, 1999.907104, 2461.798339, -6.096129, 0.000000, 0.000037, -0.000037, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(19832, 1999.842651, 2461.667968, -5.278747, 0.000000, -0.000037, 179.999633, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(11736, 1999.858276, 2461.574218, -5.908629, -0.000009, 0.000033, -0.000037, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(11736, 2000.398559, 2461.574218, -5.908629, -0.000009, 0.000033, -0.000037, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(11738, 2000.444458, 2461.692626, -5.229918, -0.000001, 0.000037, -4.500038, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1242, 1999.790161, 2461.688232, -4.413513, 0.000003, -0.000037, 173.999633, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1242, 2000.359252, 2461.708740, -4.419372, 0.000000, -0.000037, 179.999633, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2475, 2001.387939, 2461.798339, -6.096129, 0.000000, 0.000045, -0.000037, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(19832, 2001.293457, 2461.667968, -5.278747, 0.000000, -0.000045, 179.999588, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(11736, 2001.309082, 2461.574218, -5.908629, -0.000009, 0.000040, -0.000037, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(11736, 2001.849365, 2461.574218, -5.908629, -0.000009, 0.000040, -0.000037, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(11738, 2001.895263, 2461.692626, -5.229918, -0.000001, 0.000045, -4.500038, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1242, 2001.240966, 2461.688232, -4.413513, 0.000003, -0.000044, 173.999588, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1242, 2001.810058, 2461.708740, -4.419372, 0.000000, -0.000045, 179.999588, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2475, 2002.877929, 2461.798339, -6.096129, 0.000000, 0.000051, -0.000037, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(19832, 2002.773437, 2461.667968, -5.278747, 0.000000, -0.000051, 179.999542, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(11736, 2002.789062, 2461.574218, -5.908629, -0.000009, 0.000048, -0.000037, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(11736, 2003.329345, 2461.574218, -5.908629, -0.000009, 0.000048, -0.000037, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(11738, 2003.375244, 2461.692626, -5.229918, -0.000003, 0.000051, -4.500038, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1242, 2002.720947, 2461.688232, -4.413513, 0.000003, -0.000051, 173.999542, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1242, 2003.290039, 2461.708740, -4.419372, 0.000000, -0.000051, 179.999542, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2003.799560, 2451.865966, -5.915954, -0.000006, 0.000024, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2475, 2003.699707, 2451.625732, -6.037047, 0.000006, -0.000024, 179.999740, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2003.099365, 2451.865966, -5.915954, -0.000006, 0.000024, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2003.099365, 2451.865966, -5.239195, -0.000006, 0.000024, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2003.779052, 2451.865966, -5.239195, -0.000006, 0.000024, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2003.099365, 2451.865966, -4.511657, -0.000006, 0.000022, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2003.779052, 2451.865966, -4.511657, -0.000006, 0.000022, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2002.299194, 2451.865966, -5.915954, -0.000006, 0.000033, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2475, 2002.199340, 2451.625732, -6.037047, 0.000006, -0.000033, 179.999694, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2001.598999, 2451.865966, -5.915954, -0.000006, 0.000033, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2001.598999, 2451.865966, -5.239195, -0.000006, 0.000033, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2002.278686, 2451.865966, -5.239195, -0.000006, 0.000033, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2001.598999, 2451.865966, -4.511657, -0.000006, 0.000029, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2002.278686, 2451.865966, -4.511657, -0.000006, 0.000029, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2000.848999, 2451.865966, -5.915954, -0.000006, 0.000040, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2475, 2000.749145, 2451.625732, -6.037047, 0.000006, -0.000040, 179.999649, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2000.148803, 2451.865966, -5.915954, -0.000006, 0.000040, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2000.148803, 2451.865966, -5.239195, -0.000006, 0.000040, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2000.828491, 2451.865966, -5.239195, -0.000006, 0.000040, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2000.148803, 2451.865966, -4.511657, -0.000006, 0.000037, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 2000.828491, 2451.865966, -4.511657, -0.000006, 0.000037, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 1999.348632, 2451.865966, -5.915954, -0.000006, 0.000048, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2475, 1999.248779, 2451.625732, -6.037047, 0.000006, -0.000048, 179.999603, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 1998.648437, 2451.865966, -5.915954, -0.000006, 0.000048, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 1998.648437, 2451.865966, -5.239195, -0.000006, 0.000048, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 1999.328125, 2451.865966, -5.239195, -0.000006, 0.000048, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 1998.648437, 2451.865966, -4.511657, -0.000006, 0.000045, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1578, 1999.328125, 2451.865966, -4.511657, -0.000006, 0.000045, -0.000006, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2852, 2001.481933, 2448.399658, -5.248717, 0.000000, 0.000022, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1667, 2002.046997, 2447.767578, -5.148742, 0.000000, 0.000029, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1667, 2001.806762, 2448.648437, -5.148742, 0.000000, 0.000029, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1520, 2001.924682, 2448.278808, -5.178771, 0.000000, 0.000029, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(19604, 2000.698242, 2444.405273, -6.254333, -89.999992, 270.000000, 89.999992, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(19604, 2005.698120, 2444.405273, -6.254333, -89.999992, 270.000000, 89.999992, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2852, 2000.221801, 2448.399658, -5.248717, 0.000000, 0.000029, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1667, 2000.786865, 2447.767578, -5.148742, 0.000000, 0.000037, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1667, 2000.546630, 2448.648437, -5.148742, 0.000000, 0.000037, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1520, 2000.664550, 2448.278808, -5.178771, 0.000000, 0.000037, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2047, 2004.422119, 2448.378662, -4.123717, -0.000007, 0.000000, -89.999977, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(14651, 1994.705688, 2446.172851, -4.154173, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(14651, 1991.005371, 2446.172851, -4.154173, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(14651, 1988.595214, 2446.172851, -4.154173, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(14820, 1992.104736, 2460.322998, -5.684326, 0.000000, 0.000014, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2232, 1990.555175, 2460.229980, -5.684508, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2232, 1993.855957, 2460.229980, -5.684508, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2872, 1994.916625, 2452.312011, -6.254333, -0.000007, 0.000000, -89.999977, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2779, 1994.915283, 2453.112548, -6.254333, -0.000007, 0.000000, -89.999977, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2778, 1994.911010, 2453.910400, -6.254333, -0.000007, 0.000000, -89.999977, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2164, 2005.222045, 2441.957031, -4.882628, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2164, 2003.959350, 2438.036621, -6.270202, 0.000000, -0.000014, 179.999908, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2164, 2002.198974, 2438.036621, -6.270202, 0.000000, -0.000014, 179.999908, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2164, 2000.459106, 2438.036621, -6.270202, 0.000000, -0.000022, 179.999862, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2164, 1998.698730, 2438.036621, -6.270202, 0.000000, -0.000022, 179.999862, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(19787, 2003.854248, 2437.621826, -4.904112, -0.000003, 0.000014, -15.900045, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 2003.332763, 2437.360107, -5.494140, 0.000000, 0.000014, -0.000037, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2484, 1999.928222, 2437.589843, -4.563537, 0.000000, -0.000007, 179.999954, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1731, 2004.286621, 2430.211669, -4.743469, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(1731, 2004.286621, 2431.913085, -4.743469, 0.000000, 0.000007, 0.000000, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2164, 1998.698730, 2428.756835, -6.270202, 0.000000, -0.000022, 179.999862, 222, 2, -1, 200.00, 200.00); 
    CreateDynamicObject(2164, 2000.468139, 2428.756835, -6.270202, 0.000000, -0.000022, 179.999862, 222, 2, -1, 200.00, 200.00);
}