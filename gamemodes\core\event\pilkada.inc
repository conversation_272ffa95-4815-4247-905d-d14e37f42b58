#include <YSI_Coding\y_hooks>

hook OnGameModeInit()
{
    static avtpss;
    avtpss = CreateDynamicObject(1444, 2454.260253, 113.257705, 26.277469, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(avtpss, 0, 19307, "goflagx2", "goflag2", 0x00000000);
    avtpss = CreateDynamicObject(1444, 2454.260253, 109.597709, 26.287469, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(avtpss, 0, 10810, "ap_build4e", "redwhite_stripe", 0x00000000);
    avtpss = CreateDynamicObject(1444, 2454.260253, 116.877662, 26.287469, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(avtpss, 0, "TPS 1\nKOTA ARIVENA", 100, "Arial", 35, 1, 0xFFFFFFFF, 0xFF000000, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(983, 2453.840576, 110.339294, 26.122755, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2453.840576, 116.759307, 26.122755, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2450.639892, 121.739318, 26.122755, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2444.238769, 121.739318, 26.122755, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2437.827636, 121.739318, 26.122755, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2431.397216, 121.739318, 26.122755, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2424.096435, 109.899353, 26.122755, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2424.096435, 116.309349, 26.122755, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2450.618652, 106.799331, 26.122755, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2444.187988, 106.799331, 26.122755, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2447.416503, 110.041488, 26.122755, 0.000000, 0.000000, -1.500005, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2453.180908, 108.024490, 25.471017, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2453.180908, 110.074409, 25.471017, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2453.180908, 112.034477, 25.471017, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2453.180908, 114.084396, 25.471017, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2451.188964, 108.024490, 25.471017, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2451.188964, 110.074409, 25.471017, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2451.188964, 112.034477, 25.471017, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2451.188964, 114.084396, 25.471017, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2449.268798, 108.024490, 25.471017, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2449.268798, 110.074409, 25.471017, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2449.268798, 112.034477, 25.471017, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2449.268798, 114.084396, 25.471017, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2453.180908, 116.144378, 25.471017, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2451.188964, 116.144378, 25.471017, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2449.268798, 116.144378, 25.471017, 0.000029, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2453.180908, 118.424369, 25.471017, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2451.188964, 118.424369, 25.471017, 0.000029, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2449.268798, 118.424369, 25.471017, 0.000037, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2447.583007, 116.429328, 26.122755, 0.000000, 0.000000, -1.500005, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2444.448974, 119.589332, 26.122755, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1737, 2426.165283, 117.381080, 25.397890, 0.000000, 0.000000, 90.199996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2424.668212, 117.894409, 25.461027, 0.000037, 0.000000, 269.999877, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1737, 2426.165283, 115.551109, 25.397890, 0.000007, 0.000000, 90.199974, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2424.668212, 116.064437, 25.461027, 0.000029, 0.000000, -90.000099, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1737, 2426.165283, 113.701179, 25.397890, 0.000014, 0.000000, 90.199951, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2424.668212, 114.214508, 25.461027, 0.000022, 0.000000, -90.000076, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1737, 2426.165283, 111.861221, 25.397890, 0.000022, 0.000000, 90.199928, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2424.668212, 112.374549, 25.461027, 0.000014, 0.000000, -90.000053, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1737, 2426.165283, 110.001235, 25.397890, 0.000029, 0.000000, 90.199905, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2424.668212, 110.514564, 25.461027, 0.000007, 0.000000, -90.000030, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2428.400878, 116.261459, 26.122755, 0.000000, 0.000000, -1.500005, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2428.273193, 111.453094, 26.122755, 0.000000, 0.000000, -1.500005, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2438.057861, 119.589332, 26.122755, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2433.287841, 119.579330, 26.122755, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2424.066406, 118.759414, 26.122755, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2429.116943, 121.749320, 26.122755, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19436, 2440.874511, 106.438934, 26.199653, 89.000045, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19436, 2439.502685, 106.448966, 26.175722, 89.000045, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19997, 2440.182373, 106.689720, 25.456640, 360.000000, 360.000000, -89.799964, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19436, 2439.325195, 106.448936, 26.169652, 89.000045, 90.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19436, 2437.953369, 106.458969, 26.145721, 89.000045, 90.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19997, 2438.633056, 106.699722, 25.426639, -0.000007, 360.000000, -89.799919, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19436, 2437.784179, 106.458938, 26.159652, 89.000045, 90.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19436, 2436.412353, 106.468971, 26.135721, 89.000045, 90.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19997, 2437.092041, 106.709724, 25.416639, -0.000014, 360.000000, -89.799896, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19436, 2436.263427, 106.468940, 26.129652, 89.000045, 90.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19436, 2434.891601, 106.478973, 26.105720, 89.000045, 90.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19997, 2435.571289, 106.719726, 25.386638, -0.000022, 360.000000, -89.799873, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19436, 2434.711914, 106.468940, 26.099651, 89.000045, 90.000000, 89.999839, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19436, 2433.340087, 106.478973, 26.075719, 89.000045, 90.000000, 89.999839, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19997, 2434.019775, 106.719726, 25.356636, -0.000029, 360.000000, -89.799850, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1737, 2431.167236, 117.865180, 25.397890, 0.000007, 0.000007, -1.099982, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2431.714599, 119.350227, 25.461027, 0.000029, -0.000007, 178.699813, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3077, 2435.528564, 118.928123, 25.407934, 0.000000, 0.000000, -0.599996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2431.427001, 110.659408, 26.122755, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2437.807861, 110.659408, 26.122755, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2442.588378, 110.659408, 26.122755, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(983, 2445.853271, 113.853706, 26.122755, 0.000000, 0.000000, -1.500005, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1258, 2443.423339, 119.197998, 26.074747, 0.000000, 0.000000, -0.100037, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1258, 2444.093994, 119.196876, 26.074747, 0.000000, 0.000000, -0.100037, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1258, 2444.774658, 119.195678, 26.074747, 0.000000, 0.000000, -0.100037, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2599, 2454.884277, 118.944931, 25.907739, 0.000000, 0.000000, 38.300010, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1483, 2437.845458, 107.877273, 27.180437, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11711, 2425.927734, 121.711257, 26.952732, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19957, 2447.660156, 119.595642, 24.635999, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19953, 2453.857910, 121.746665, 24.512590, 0.000000, 0.000000, 52.899997, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19953, 2424.156738, 120.126548, 24.566833, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19953, 2428.040527, 106.756378, 24.552085, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19953, 2447.330078, 109.089271, 24.626636, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19973, 2444.087646, 119.490798, 24.515895, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11711, 2430.112060, 119.588363, 26.711406, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 2425.928955, 121.731285, 24.908842, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1483, 2436.053955, 107.867271, 27.230438, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2434.320556, 115.749755, 25.583225, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2435.641845, 115.749755, 25.583225, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2436.882568, 115.749755, 25.583225, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2434.320556, 114.029739, 25.583225, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2435.641845, 114.029739, 25.583225, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1722, 2436.882568, 114.029739, 25.583225, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19948, 2429.126708, 121.667823, 24.481342, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00);
    
    CreateDynamicPickup(1239, 23, 2430.9790,107.7105,26.4793, 0, 0, -1, 10.5, -1, 0);
    CreateDynamic3DTextLabel("Gunakan "YELLOW"'/vote' "WHITE"di dalam bilik TPS", Y_WHITE, 2430.9790,107.7105,26.4793 + 0.5, 5.5, INVALID_PLAYER_ID, INVALID_VEHICLE_ID, 1, 0, 0, -1, 5.5, -1, 0);
    return 1;
}

Dialog:PilkadaVoting(playerid, response, listitem, inputtext[])
{
    if(!response) return ShowTDN(playerid, NOTIFICATION_INFO, "Anda telah membatalkan pilihan!");

    switch(listitem)
    {
        case 0:
        {
            static frmxts[150];
            mysql_format(g_SQL, frmxts, sizeof(frmxts), "INSERT INTO `pilkada_voters` SET `Voters_ID` = %d, `Nama` = '%e', `UCP` = '%e', `VotedNumber` = 1", AccountData[playerid][pID], AccountData[playerid][pName], AccountData[playerid][pUCP]);
            mysql_query(g_SQL, frmxts);

            ShowTDN(playerid, NOTIFICATION_INFO, "Anda berhasil memilih nomor urut 01");
        }
        case 1:
        {
            static frmxts[150];
            mysql_format(g_SQL, frmxts, sizeof(frmxts), "INSERT INTO `pilkada_voters` SET `Voters_ID` = %d, `Nama` = '%e', `UCP` = '%e', `VotedNumber` = 2", AccountData[playerid][pID], AccountData[playerid][pName], AccountData[playerid][pUCP]);
            mysql_query(g_SQL, frmxts);

            ShowTDN(playerid, NOTIFICATION_INFO, "Anda berhasil memilih nomor urut 02");
        }
    }
    return 1;
}

new Float:TPSPos[][3] =
{
    {2433.9900,107.6858,26.4795},
    {2435.6057,107.6914,26.4796},
    {2437.0869,107.6932,26.4792},
    {2438.5659,107.6744,26.4789},
    {2440.1565,107.6654,26.4787}
};

new VoteFor[2][4][64] =
{
    {"#01", "Mocca Panigoro", "Dewo Mangkuwanito", "Partai Sejahtera"},
    {"#02", "Josua Sitindaon", "Ijal Prawijaya", "Partai Generasi Muda"}
};

forward LoadVoters();
public LoadVoters()
{
    new rows = cache_num_rows();
    if(rows)
    {
        new votdnumb, numb1, numb2;
		for(new i; i < rows; i++)
		{
            cache_get_value_name_int(i, "VotedNumber", votdnumb);

            switch(votdnumb)
            {
                case 1:
                {
                    numb1++;
                }
                case 2:
                {
                    numb2++;
                }
            }
        }
        SendClientMessageToAll(Y_ARIVENA, "========== [QUICK COUNT PILKADA KOTA ARIVENA #1] ==========");
        SendClientMessageToAllEx(-1, "> %s - %s <", VoteFor[0][0], VoteFor[0][3]);
        SendClientMessageToAllEx(-1, "> %s & %s", VoteFor[0][1], VoteFor[0][2]);
        SendClientMessageToAllEx(-1, "> Suara: %d", numb1);
        SendClientMessageToAll(Y_RED, ">>>>>>>>>> "WHITE"||| "BLUE"<<<<<<<<<<");
        SendClientMessageToAllEx(-1, "> %s - %s <", VoteFor[1][0], VoteFor[1][3]);
        SendClientMessageToAllEx(-1, "> %s & %s", VoteFor[1][1], VoteFor[1][2]);
        SendClientMessageToAllEx(-1, "> Suara: %d", numb2);
        SendClientMessageToAll(Y_ARIVENA, "========== [AYO DATANG KE TPS PALOMINO!] ==========");
    }
    return 1;
}

IsPlayerNearTPS(playerid)
{
    for(new x; x < sizeof(TPSPos); x++)
    {
        if(IsPlayerInRangeOfPoint(playerid, 2.0, TPSPos[x][0], TPSPos[x][1], TPSPos[x][2]) && GetPlayerVirtualWorld(playerid) == 0 && GetPlayerInterior(playerid) == 0)
        {
            return 1;
        }
    }
    return 0;
}

YCMD:vote(playerid, params[], help)
{
    if(!IsPlayerNearTPS(playerid))
        return ShowTDN(playerid, NOTIFICATION_ERROR, "Anda tidak dekat dengan TPS manapun!");

    if(GetPlayerState(playerid) != PLAYER_STATE_ONFOOT)
        return ShowTDN(playerid, NOTIFICATION_ERROR, "Anda harus jalan kaki!");

    static frmxts[150];
    mysql_format(g_SQL, frmxts, sizeof(frmxts), "SELECT * FROM `pilkada_voters` WHERE `Voters_ID` = %d", AccountData[playerid][pID]);
    mysql_query(g_SQL, frmxts);
    if(cache_num_rows())
        return ShowTDN(playerid, NOTIFICATION_ERROR, "Anda sudah memilih!");

    new slls[512];
    format(slls, sizeof(slls), "No. Urut\tCagub\tCawagub\tPartai\n%s\t%s\t%s\t%s\n%s\t%s\t%s\t%s", 
    VoteFor[0][0], VoteFor[0][1], VoteFor[0][2], VoteFor[0][3],
    VoteFor[1][0], VoteFor[1][1], VoteFor[1][2], VoteFor[1][3]);
    Dialog_Show(playerid, "PilkadaVoting", DIALOG_STYLE_TABLIST_HEADERS, ""ARIVENA"Arivena Theater "WHITE"- Bilik TPS", slls, "Pilih", "Batal");
    return 1;
}

task QuickCountPilkada[1200000]() 
{
    mysql_pquery(g_SQL, "SELECT * FROM `pilkada_voters` WHERE `VotedNumber` >= 1", "LoadVoters");
    return 1;
}