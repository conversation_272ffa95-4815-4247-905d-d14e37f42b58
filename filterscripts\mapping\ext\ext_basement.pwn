CreateBasementExt()
{
    new STREAMER_TAG_OBJECT:bsmtxts;
    bsmtxts = CreateDynamicObject(18981, 557.540466, -1511.162841, 7.934553, 0.000000, 0.000000, -0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bsmtxts, 0, 19853, "mihouse1", "brownwall1", 0x00000000);
    bsmtxts = CreateDynamicObject(18981, 557.520446, -1501.183471, 7.944553, 0.000000, 0.000000, -0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bsmtxts, 0, 19853, "mihouse1", "brownwall1", 0x00000000);
    bsmtxts = CreateDynamicObject(2789, 557.126159, -1505.467651, 17.584571, 360.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxts, 1, "BASEMENT", 100, "Calibri", 50, 1, 0xFFFFFF00, 0x00000000, 1);
    bsmtxts = CreateDynamicObject(2789, 557.126159, -1507.857421, 17.584571, 360.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxts, 1, "PARKING", 100, "Calibri", 50, 1, 0xFFFFFF00, 0x00000000, 1);
    bsmtxts = CreateDynamicObject(18766, 550.622863, -1512.395996, 13.075661, 89.700004, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bsmtxts, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bsmtxts = CreateDynamicObject(18766, 560.622863, -1512.395996, 13.075661, 89.700004, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bsmtxts, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bsmtxts = CreateDynamicObject(18766, 550.642883, -1500.835815, 13.075661, 89.699981, 0.002914, -0.002914, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bsmtxts, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bsmtxts = CreateDynamicObject(18766, 560.642883, -1500.835815, 13.075661, 89.699981, 0.002914, -0.002914, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bsmtxts, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    bsmtxts = CreateDynamicObject(19445, 552.017333, -1500.820434, 13.494457, 0.000000, 90.299995, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bsmtxts, 0, 10778, "airportcpark_sfse", "ws_carpark3", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(17951, 556.885803, -1500.829223, 15.274082, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(17951, 556.885803, -1512.308715, 15.274082, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 556.868103, -1502.399536, 13.112814, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 556.856140, -1499.339477, 13.112814, 0.000000, 0.000000, -89.500000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19957, 556.654357, -1508.573852, 12.951488, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19957, 556.654357, -1516.212890, 12.951488, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8843, 557.576538, -1512.356445, 13.589880, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
}