-- =====================================================
-- SCRIPT PERBAIKAN DATABASE PLAYER_CHARACTERS
-- =====================================================
-- Masalah yang diperbaiki:
-- 1. Char_Admin default value = 6 (seharusnya 0)
-- 2. Tidak ada UNIQUE constraint untuk Char_Name
-- 3. Tidak ada INDEX untuk Char_UCP
-- 4. Tidak ada constraint untuk mencegah duplikasi
-- =====================================================

-- BACKUP DATA SEBELUM PERBAIKAN
-- Buat backup tabel sebelum melakukan perubahan
CREATE TABLE `player_characters_backup` AS SELECT * FROM `player_characters`;

-- =====================================================
-- PERBAIKAN 1: UBAH DEFAULT VALUE Char_Admin dari 6 ke 0
-- =====================================================

-- Update semua karakter yang memiliki admin level 6 menjadi 0 (kecuali yang memang admin)
-- HATI-HATI: Pastikan admin yang legitimate tidak terhapus
UPDATE `player_characters` 
SET `Char_Admin` = 0 
WHERE `Char_Admin` = 6 
  AND `Char_Name` NOT IN ('Ryuxi_Mozaya'); -- Ganti dengan nama admin yang legitimate

-- Ubah struktur kolom untuk mengubah default value
ALTER TABLE `player_characters` 
MODIFY COLUMN `Char_Admin` tinyint(3) UNSIGNED NOT NULL DEFAULT 0;

-- =====================================================
-- PERBAIKAN 2: TAMBAH UNIQUE CONSTRAINT untuk Char_Name
-- =====================================================

-- Hapus duplikasi nama karakter jika ada
-- HATI-HATI: Script ini akan menghapus karakter duplikat, simpan yang terbaru
DELETE p1 FROM `player_characters` p1
INNER JOIN `player_characters` p2 
WHERE p1.`pID` < p2.`pID` 
  AND p1.`Char_Name` = p2.`Char_Name`;

-- Tambah UNIQUE constraint untuk Char_Name
ALTER TABLE `player_characters` 
ADD CONSTRAINT `unique_char_name` UNIQUE (`Char_Name`);

-- =====================================================
-- PERBAIKAN 3: TAMBAH INDEX untuk performa query
-- =====================================================

-- Index untuk Char_UCP (sering digunakan dalam query)
ALTER TABLE `player_characters` 
ADD INDEX `idx_char_ucp` (`Char_UCP`);

-- Index untuk kombinasi Char_UCP + Char_Name
ALTER TABLE `player_characters` 
ADD INDEX `idx_ucp_name` (`Char_UCP`, `Char_Name`);

-- Index untuk Char_RegisterDate (untuk ORDER BY)
ALTER TABLE `player_characters` 
ADD INDEX `idx_register_date` (`Char_RegisterDate`);

-- =====================================================
-- PERBAIKAN 4: TAMBAH CONSTRAINT untuk data integrity
-- =====================================================

-- Constraint untuk memastikan Char_Admin tidak lebih dari 6
ALTER TABLE `player_characters` 
ADD CONSTRAINT `chk_admin_level` CHECK (`Char_Admin` BETWEEN 0 AND 6);

-- Constraint untuk memastikan Char_Level minimal 1
ALTER TABLE `player_characters` 
ADD CONSTRAINT `chk_char_level` CHECK (`Char_Level` >= 1);

-- Constraint untuk memastikan Char_Money tidak negatif
ALTER TABLE `player_characters` 
ADD CONSTRAINT `chk_char_money` CHECK (`Char_Money` >= 0);

-- Constraint untuk memastikan Char_BankMoney tidak negatif
ALTER TABLE `player_characters` 
ADD CONSTRAINT `chk_bank_money` CHECK (`Char_BankMoney` >= 0);

-- =====================================================
-- VERIFIKASI PERBAIKAN
-- =====================================================

-- Cek apakah masih ada karakter dengan admin level 6 yang tidak seharusnya
SELECT `Char_Name`, `Char_Admin`, `Char_UCP` 
FROM `player_characters` 
WHERE `Char_Admin` = 6;

-- Cek apakah ada duplikasi nama karakter
SELECT `Char_Name`, COUNT(*) as `count` 
FROM `player_characters` 
GROUP BY `Char_Name` 
HAVING COUNT(*) > 1;

-- Cek struktur tabel setelah perbaikan
DESCRIBE `player_characters`;

-- Cek constraint yang telah ditambahkan
SHOW CREATE TABLE `player_characters`;

-- =====================================================
-- SCRIPT ROLLBACK (JIKA DIPERLUKAN)
-- =====================================================
-- Jika terjadi masalah, gunakan script ini untuk rollback:
-- 
-- DROP TABLE `player_characters`;
-- RENAME TABLE `player_characters_backup` TO `player_characters`;
-- 
-- PERINGATAN: Script rollback akan menghilangkan semua perubahan!

-- =====================================================
-- CATATAN PENTING
-- =====================================================
-- 1. SELALU BACKUP DATABASE SEBELUM MENJALANKAN SCRIPT INI
-- 2. Test script ini di development environment terlebih dahulu
-- 3. Pastikan tidak ada player yang sedang online saat menjalankan script
-- 4. Verifikasi hasil setelah menjalankan script
-- 5. Hapus tabel backup setelah yakin perbaikan berhasil:
--    DROP TABLE `player_characters_backup`;
