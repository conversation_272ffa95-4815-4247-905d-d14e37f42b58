CreateAmmunationExt()
{
    new STREAMER_TAG_OBJECT:ammuxtad;
    ammuxtad = CreateDynamicObject(19358, -249.059509, 1219.485473, 20.412191, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -249.059509, 1213.444702, 20.412191, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -258.771942, 1209.375976, 18.512157, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -249.049514, 1211.436157, 20.412191, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -249.059509, 1222.686767, 20.412191, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    ammuxtad = CreateDynamicObject(1492, -262.210235, 1212.879760, 21.214609, 0.699985, 179.999832, 0.599997, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 16640, "a51", "a51_intdoor", 0x00000000);
    SetDynamicObjectMaterial(ammuxtad, 1, 18232, "cw_truckstopcs_t", "des_wigwamdoor", 0x00000000);
    ammuxtad = CreateDynamicObject(1492, -265.213195, 1212.937255, 21.213922, 0.699985, 179.999832, 179.800079, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 18232, "cw_truckstopcs_t", "des_wigwamdoor", 0x00000000);
    SetDynamicObjectMaterial(ammuxtad, 1, 18232, "cw_truckstopcs_t", "des_wigwamdoor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -256.819274, 1211.414428, 18.663742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -249.061950, 1209.375976, 19.662183, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    SetDynamicObjectMaterial(ammuxtad, 1, 16640, "a51", "wallgreyred128", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -249.061950, 1224.107177, 19.682184, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -265.632110, 1215.498291, 21.692184, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -265.609466, 1217.854492, 20.412191, 0.000000, 0.000003, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -265.609466, 1214.644897, 20.412191, 0.000000, 0.000003, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -265.661895, 1212.593383, 19.612182, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -265.609466, 1221.054687, 20.412191, 0.000000, 0.000003, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -265.621978, 1224.097167, 19.742185, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -261.761871, 1209.375976, 19.612176, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -261.619476, 1211.276000, 20.152187, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3850, "carshowglass_sfsx", "ws_glass_balustrade_better", 0x00000000);
    SetDynamicObjectMaterial(ammuxtad, 1, 3850, "carshowglass_sfsx", "ws_glass_balustrade_better", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -265.589508, 1222.445068, 20.412191, 0.000000, 0.000003, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -261.729522, 1218.107299, 20.412191, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -261.719512, 1219.347534, 20.412191, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -253.811950, 1209.375976, 18.512157, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -251.971984, 1209.355957, 18.522157, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -251.971984, 1209.385986, 21.662172, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -256.912078, 1209.375976, 21.662172, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -259.501800, 1209.395996, 21.662172, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -263.641906, 1212.595703, 21.662172, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -254.109436, 1209.214111, 20.092199, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3850, "carshowglass_sfsx", "ws_glass_balustrade_better", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -257.069671, 1209.194091, 20.082202, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3850, "carshowglass_sfsx", "ws_glass_balustrade_better", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -259.989624, 1209.224121, 19.892190, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3850, "carshowglass_sfsx", "ws_glass_balustrade_better", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -253.421936, 1209.375976, 19.662183, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -257.541931, 1209.365966, 19.662183, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -257.782012, 1210.306274, 22.642175, 0.000000, 89.999992, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -263.619537, 1224.007202, 20.412191, -0.000001, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -260.419616, 1224.207397, 20.412191, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -257.219543, 1224.207397, 20.412191, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -254.029586, 1224.217407, 20.412191, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -250.839553, 1224.207397, 20.412191, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -262.381866, 1216.566406, 22.652175, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -262.361877, 1221.077270, 22.632175, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -249.072082, 1211.676391, 21.682184, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -251.149612, 1209.204101, 20.072204, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3850, "carshowglass_sfsx", "ws_glass_balustrade_better", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -261.781433, 1224.096435, 21.732173, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -249.092041, 1216.667236, 21.682184, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -249.072082, 1221.357666, 21.682184, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -251.971984, 1224.096435, 21.732173, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -256.841735, 1224.086425, 21.732173, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -262.921447, 1224.076416, 21.732173, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -261.719512, 1222.547729, 20.412191, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -261.712158, 1211.457763, 21.712184, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -265.642120, 1216.388427, 21.712184, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -250.819259, 1211.414428, 18.663742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -265.632110, 1221.357666, 21.712184, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -261.711883, 1221.357666, 21.712184, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -261.711883, 1216.395629, 21.712184, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -261.741882, 1224.097167, 19.742185, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -253.819259, 1211.414428, 18.653741, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -261.701843, 1216.646118, 19.682184, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -261.691864, 1212.553588, 19.682184, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -259.819274, 1211.414428, 18.653741, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -259.819274, 1214.414428, 18.673742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(1492, -249.038116, 1217.905883, 21.217948, 0.700034, 539.999877, 89.800025, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    SetDynamicObjectMaterial(ammuxtad, 1, 18232, "cw_truckstopcs_t", "des_wigwamdoor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -256.819274, 1214.414428, 18.663742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(1492, -249.111007, 1214.964965, 21.233943, 0.700034, 539.999877, -90.199981, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 16640, "a51", "a51_intdoor", 0x00000000);
    SetDynamicObjectMaterial(ammuxtad, 1, 18232, "cw_truckstopcs_t", "des_wigwamdoor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -253.819274, 1214.414428, 18.653741, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -250.819274, 1214.414428, 18.663742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -250.819274, 1217.414428, 18.673742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -253.819274, 1217.414428, 18.663742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -256.819274, 1217.414428, 18.653741, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -259.819274, 1217.414428, 18.693742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -259.819274, 1220.414428, 18.653741, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -259.819274, 1222.524169, 18.683742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -256.819274, 1220.414428, 18.713743, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -253.819274, 1220.414428, 18.693742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -250.819274, 1220.414428, 18.653741, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -250.819274, 1222.643676, 18.663742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -253.819274, 1222.553710, 18.673742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -256.819274, 1222.544067, 18.653741, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -262.819274, 1222.414428, 18.663742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -263.819274, 1222.414428, 18.653741, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -263.819274, 1219.414428, 18.673742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -263.819274, 1216.414428, 18.673742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -263.819274, 1214.414428, 18.663742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -262.819274, 1214.414428, 18.653741, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -260.819274, 1217.414428, 18.663742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -260.819274, 1219.414428, 18.663742, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -248.632095, 1212.317138, 18.312177, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 9514, "711_sfw", "brick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -248.632095, 1217.297363, 18.312177, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 9514, "711_sfw", "brick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -248.632095, 1221.128906, 18.302177, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 9514, "711_sfw", "brick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -258.099212, 1214.625122, 24.083772, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -255.749160, 1214.624633, 24.093772, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -252.801940, 1222.066772, 23.662197, 0.000000, 89.999992, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 9514, "711_sfw", "brick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -252.269180, 1212.424560, 24.083772, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -255.729171, 1212.564697, 24.073772, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -258.389068, 1212.634643, 24.063772, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -260.721923, 1217.288574, 22.682176, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -260.741943, 1220.239257, 22.692176, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -252.269180, 1215.625732, 24.083772, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -252.269180, 1218.826171, 24.083772, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -252.269180, 1220.636840, 24.063772, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -258.099212, 1217.795410, 24.083772, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -258.099212, 1220.496093, 24.063772, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -255.279205, 1220.496093, 24.053771, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -255.279205, 1217.716064, 24.063772, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -262.869079, 1221.044677, 23.083772, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -262.689086, 1217.854736, 23.063753, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19360, -262.799102, 1214.774780, 23.091779, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(2168, -249.037109, 1209.342773, 21.572181, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 8409, "gnhotel1", "redstuff", 0x00000000);
    ammuxtad = CreateDynamicObject(2168, -250.306991, 1209.342773, 21.572181, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 8409, "gnhotel1", "redstuff", 0x00000000);
    ammuxtad = CreateDynamicObject(2168, -251.586898, 1209.342773, 21.572181, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 8409, "gnhotel1", "redstuff", 0x00000000);
    ammuxtad = CreateDynamicObject(2168, -252.866989, 1209.342773, 21.572181, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 8409, "gnhotel1", "redstuff", 0x00000000);
    ammuxtad = CreateDynamicObject(2168, -254.117279, 1209.342773, 21.572181, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 8409, "gnhotel1", "redstuff", 0x00000000);
    ammuxtad = CreateDynamicObject(2168, -255.377243, 1209.342773, 21.572181, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 8409, "gnhotel1", "redstuff", 0x00000000);
    ammuxtad = CreateDynamicObject(2168, -256.657073, 1209.342773, 21.572181, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 8409, "gnhotel1", "redstuff", 0x00000000);
    ammuxtad = CreateDynamicObject(2168, -257.947082, 1209.342773, 21.572181, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 8409, "gnhotel1", "redstuff", 0x00000000);
    ammuxtad = CreateDynamicObject(2168, -259.216979, 1209.342773, 21.572181, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 8409, "gnhotel1", "redstuff", 0x00000000);
    ammuxtad = CreateDynamicObject(2168, -260.466827, 1209.342773, 21.572181, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 8409, "gnhotel1", "redstuff", 0x00000000);
    ammuxtad = CreateDynamicObject(19358, -253.649627, 1224.197387, 20.462192, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3967, "cj_airprt", "bigbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(19482, -262.583343, 1208.859252, 21.462186, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 10101, "2notherbuildsfe", "flatdoor01_law", 0x00000000);
    SetDynamicObjectMaterialText(ammuxtad, 0, " A", 130, "Arial", 130, 1, 0xFFFFFFFF, 0x00000000, 0);
    ammuxtad = CreateDynamicObject(19482, -261.263275, 1208.859252, 21.442186, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 10101, "2notherbuildsfe", "flatdoor01_law", 0x00000000);
    SetDynamicObjectMaterialText(ammuxtad, 0, " M", 130, "Arial", 130, 1, 0xFFFFFFFF, 0x00000000, 0);
    ammuxtad = CreateDynamicObject(19482, -259.993225, 1208.859252, 21.442186, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 10101, "2notherbuildsfe", "flatdoor01_law", 0x00000000);
    SetDynamicObjectMaterialText(ammuxtad, 0, " M", 130, "Arial", 130, 1, 0xFFFFFFFF, 0x00000000, 0);
    ammuxtad = CreateDynamicObject(19482, -258.743194, 1208.859252, 21.442186, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 10101, "2notherbuildsfe", "flatdoor01_law", 0x00000000);
    SetDynamicObjectMaterialText(ammuxtad, 0, " U", 130, "Arial", 130, 1, 0xFFFFFFFF, 0x00000000, 0);
    ammuxtad = CreateDynamicObject(19482, -253.483398, 1208.859252, 21.462186, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 10101, "2notherbuildsfe", "flatdoor01_law", 0x00000000);
    SetDynamicObjectMaterialText(ammuxtad, 0, " N", 130, "Arial", 130, 1, 0xFFFFFFFF, 0x00000000, 0);
    ammuxtad = CreateDynamicObject(19482, -256.263366, 1208.859252, 21.462186, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 10101, "2notherbuildsfe", "flatdoor01_law", 0x00000000);
    SetDynamicObjectMaterialText(ammuxtad, 0, " A", 130, "Arial", 130, 1, 0xFFFFFFFF, 0x00000000, 0);
    ammuxtad = CreateDynamicObject(19482, -255.083358, 1208.859252, 21.462186, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 10101, "2notherbuildsfe", "flatdoor01_law", 0x00000000);
    SetDynamicObjectMaterialText(ammuxtad, 0, " T", 130, "Arial", 130, 1, 0xFFFFFFFF, 0x00000000, 0);
    ammuxtad = CreateDynamicObject(19482, -253.953323, 1208.859252, 21.462186, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 10101, "2notherbuildsfe", "flatdoor01_law", 0x00000000);
    SetDynamicObjectMaterialText(ammuxtad, 0, " I", 130, "Arial", 130, 1, 0xFFFFFFFF, 0x00000000, 0);
    ammuxtad = CreateDynamicObject(19482, -252.363342, 1208.859252, 21.462186, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 10101, "2notherbuildsfe", "flatdoor01_law", 0x00000000);
    SetDynamicObjectMaterialText(ammuxtad, 0, " O", 130, "Arial", 130, 1, 0xFFFFFFFF, 0x00000000, 0);
    ammuxtad = CreateDynamicObject(19482, -247.133422, 1208.859252, 21.462186, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 10101, "2notherbuildsfe", "flatdoor01_law", 0x00000000);
    SetDynamicObjectMaterialText(ammuxtad, 0, " N", 130, "Arial", 130, 1, 0xFFFFFFFF, 0x00000000, 0);
    ammuxtad = CreateDynamicObject(19427, -249.069580, 1213.258911, 19.553724, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3314, "ce_burbhouse", "sw_wallbrick_06", 0x00000000);
    ammuxtad = CreateDynamicObject(19427, -265.569366, 1213.219116, 19.553724, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3314, "ce_burbhouse", "sw_wallbrick_06", 0x00000000);
    ammuxtad = CreateDynamicObject(19427, -265.569366, 1215.049194, 19.133735, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2624, "cj_urb", "cj_bricks", 0x00000000);
    ammuxtad = CreateDynamicObject(19427, -249.069580, 1222.301269, 19.553724, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3314, "ce_burbhouse", "sw_wallbrick_06", 0x00000000);
    ammuxtad = CreateDynamicObject(19427, -265.569366, 1216.869995, 19.553724, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3314, "ce_burbhouse", "sw_wallbrick_06", 0x00000000);
    ammuxtad = CreateDynamicObject(19427, -249.069580, 1219.870605, 19.553724, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2624, "cj_urb", "cj_bricks", 0x00000000);
    ammuxtad = CreateDynamicObject(19427, -265.569366, 1218.710693, 19.113716, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2624, "cj_urb", "cj_bricks", 0x00000000);
    ammuxtad = CreateDynamicObject(19427, -265.569366, 1220.551635, 19.553724, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3314, "ce_burbhouse", "sw_wallbrick_06", 0x00000000);
    ammuxtad = CreateDynamicObject(19427, -265.569366, 1222.412353, 19.073713, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2624, "cj_urb", "cj_bricks", 0x00000000);
    ammuxtad = CreateDynamicObject(19427, -261.749420, 1218.710693, 19.113716, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2624, "cj_urb", "cj_bricks", 0x00000000);
    ammuxtad = CreateDynamicObject(19427, -261.749420, 1220.551635, 19.553724, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3314, "ce_burbhouse", "sw_wallbrick_06", 0x00000000);
    ammuxtad = CreateDynamicObject(19427, -261.749420, 1222.412353, 19.073713, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2624, "cj_urb", "cj_bricks", 0x00000000);
    ammuxtad = CreateDynamicObject(19427, -261.689392, 1218.710693, 19.113716, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2624, "cj_urb", "cj_bricks", 0x00000000);
    ammuxtad = CreateDynamicObject(19427, -261.689392, 1220.551635, 19.553724, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3314, "ce_burbhouse", "sw_wallbrick_06", 0x00000000);
    ammuxtad = CreateDynamicObject(19427, -261.689392, 1222.412353, 19.073713, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 2624, "cj_urb", "cj_bricks", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -252.801940, 1210.306274, 22.642175, 0.000000, 89.999992, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -250.082061, 1217.126953, 22.672176, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -260.721923, 1212.296997, 22.642175, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -250.072052, 1221.077270, 22.682176, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -250.062057, 1212.296997, 22.682176, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -257.782012, 1223.047485, 22.682176, 0.000000, 89.999992, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -252.801940, 1223.057495, 22.682176, 0.000000, 89.999992, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -262.601928, 1223.057495, 22.672176, 0.000000, 89.999992, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -264.662139, 1217.126953, 22.692176, -0.000001, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -264.662139, 1221.077270, 22.682176, -0.000001, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -264.672149, 1215.606201, 22.682176, -0.000001, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -262.661926, 1213.566528, 22.652175, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 3820, "boxhses_sfsx", "ws_mixedbrick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -257.782012, 1211.297119, 23.632184, -0.000000, 89.999992, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 9514, "711_sfw", "brick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -252.801940, 1211.306762, 23.632184, -0.000000, 89.999992, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 9514, "711_sfw", "brick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -250.832031, 1217.126953, 23.672199, 0.000003, 90.000000, 89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 9514, "711_sfw", "brick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -250.832092, 1220.086303, 23.682199, 0.000003, 90.000000, 89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 9514, "711_sfw", "brick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -250.792068, 1213.317626, 23.682199, 0.000003, 90.000000, 89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 9514, "711_sfw", "brick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -257.782012, 1222.067626, 23.662197, 0.000000, 89.999992, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 9514, "711_sfw", "brick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -259.821929, 1217.126953, 23.672199, -0.000001, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 9514, "711_sfw", "brick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -259.831878, 1220.086303, 23.692199, -0.000001, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 9514, "711_sfw", "brick", 0x00000000);
    ammuxtad = CreateDynamicObject(18762, -259.811981, 1213.297729, 23.642198, -0.000001, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ammuxtad, 0, 9514, "711_sfw", "brick", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(2842, -264.196960, 1211.810668, 18.732187, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18092, -254.757629, 1221.467895, 19.199157, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2061, -260.211791, 1223.872802, 18.936807, 98.299995, 90.199996, -4.899995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2049, -263.643157, 1223.886108, 20.162162, 0.000000, -0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19914, -257.131378, 1221.896850, 19.722209, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19631, -249.172088, 1222.120605, 20.595031, -39.499996, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19590, -261.567840, 1222.783203, 20.499624, -27.900001, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2459, -254.083984, 1213.187255, 18.862161, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2433, -249.604690, 1222.122070, 18.742187, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2433, -249.604690, 1219.971801, 18.742187, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19583, -253.045593, 1221.510742, 19.702184, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2040, -256.950317, 1221.227172, 19.782180, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2036, -250.925415, 1223.880981, 20.289691, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2040, -256.760345, 1221.227172, 19.782180, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2509, -258.037506, 1224.120361, 20.642179, 0.000000, -0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2509, -256.727569, 1224.120361, 20.642179, 0.000000, -0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2509, -255.417556, 1224.120361, 20.642179, 0.000000, -0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2509, -254.127517, 1224.120361, 20.642179, 0.000000, -0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2509, -252.827499, 1224.120361, 20.642179, 0.000000, -0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2040, -256.570312, 1221.227172, 19.782180, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2061, -249.783645, 1222.021606, 19.017837, 98.299995, 90.199996, 85.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(343, -259.861541, 1223.966064, 19.622190, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(360, -253.212463, 1213.328002, 19.512203, 0.000000, 360.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2061, -249.850021, 1221.214111, 19.017431, 98.299995, 90.199996, 85.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2042, -249.655166, 1219.958862, 19.022176, 0.000000, -0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18652, -255.475021, 1216.600708, 24.023925, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2475, -260.088836, 1224.056762, 18.742187, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2475, -251.259262, 1224.056762, 18.742187, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2459, -257.863922, 1213.187255, 18.862161, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2433, -261.224700, 1220.151367, 18.742187, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(922, -258.956329, 1210.320312, 19.592195, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(337, -261.509643, 1218.793334, 20.701553, -88.700027, 176.899963, -3.199975, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2033, -256.307067, 1221.208618, 19.702207, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2034, -256.147857, 1221.433715, 19.722175, 0.000000, 0.000000, 53.099983, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2035, -250.978317, 1223.876708, 19.572170, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2036, -261.118316, 1218.724609, 19.982185, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2037, -251.308288, 1223.815673, 18.922174, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2038, -250.708282, 1223.756469, 18.922176, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2039, -256.768127, 1221.685180, 19.722192, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2040, -252.655105, 1221.448974, 19.852188, 0.000000, -0.000001, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2041, -252.655105, 1221.608764, 19.902187, 0.000000, -0.000001, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2042, -252.655105, 1221.158935, 19.802185, 0.000000, -0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2043, -260.931854, 1218.788330, 19.682199, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2044, -255.202590, 1224.056518, 20.637041, 62.800029, 450.000000, -86.799957, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2045, -255.759490, 1224.066894, 20.972204, -0.000007, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(916, -256.791870, 1221.247314, 19.782215, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(912, -261.037872, 1218.741943, 19.409694, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(373, -257.710388, 1212.941406, 19.580410, 0.000000, 23.399997, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(372, -258.424530, 1224.036621, 19.990831, 0.000000, -24.800001, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(344, -260.334442, 1224.025756, 20.442203, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(346, -254.040710, 1224.124023, 20.462209, 0.000000, 0.000007, -179.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(347, -254.539627, 1224.074096, 20.582210, 0.000000, 0.000007, -179.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(348, -253.495010, 1224.093872, 20.632221, 0.000000, 0.000007, -179.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(349, -252.754791, 1224.050415, 20.139699, 0.000000, 0.000007, -179.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(350, -257.426177, 1223.997070, 20.972204, 0.000000, -0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(351, -252.908355, 1224.069458, 21.029499, 0.000000, 21.500009, -179.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(352, -257.335540, 1224.048217, 20.642198, 0.000000, -0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(353, -256.884307, 1224.085449, 20.682189, 0.000000, -0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(355, -257.746002, 1224.055908, 20.092191, 0.000000, -0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(356, -252.659225, 1224.045166, 20.667486, 0.000000, 5.600012, -179.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(357, -258.312164, 1224.056518, 20.472198, 0.000000, -0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(358, -258.277008, 1224.067138, 20.852218, 0.000000, -0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(359, -254.515838, 1224.092407, 20.962219, 0.000000, 0.000007, -179.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(360, -253.872451, 1224.049560, 19.942195, 0.000000, -0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(361, -256.643524, 1224.043823, 20.292200, 0.000000, -0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(362, -255.894531, 1224.073242, 20.225648, 0.000000, 18.999998, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(363, -259.490051, 1223.995727, 20.592214, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(364, -252.994628, 1221.803222, 19.652179, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(365, -259.869415, 1223.907836, 20.422195, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(343, -259.431488, 1223.954101, 19.642190, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(342, -260.370147, 1223.929809, 19.602186, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(341, -256.972625, 1213.173950, 19.532182, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(339, -261.182891, 1220.454956, 19.302185, 270.000000, 360.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(336, -257.553436, 1224.048950, 19.898689, 0.000000, 84.499969, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(335, -252.488433, 1224.065551, 20.189680, 0.000000, -0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(331, -256.597137, 1224.080322, 19.959690, 0.000000, -0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(326, -258.645782, 1224.093872, 19.819681, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19893, -254.869384, 1221.222045, 19.702203, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2042, -249.655166, 1219.438354, 19.022176, 0.000000, -0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2042, -249.655166, 1218.927856, 19.022176, 0.000000, -0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2035, -249.628387, 1221.676879, 19.282167, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2036, -249.645431, 1219.500976, 19.269695, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(922, -248.376388, 1220.994750, 19.592195, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(922, -248.376388, 1212.260864, 19.592195, 0.000000, -0.100000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(922, -248.376388, 1212.260864, 19.592195, 0.000000, -0.100000, 90.000000, 0, 0, -1, 200.00, 200.00);
}