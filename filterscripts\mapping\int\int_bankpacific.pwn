CreateBankPacificInt()
{
    new STREAMER_TAG_OBJECT:pcxts;
    pcxts = CreateDynamicObject(18981, 1462.706176, -997.448242, 7.551933, 0.000000, 90.000022, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14537, "pdomebar", "club_floor2_sfwTEST", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1467.905883, -1002.657043, 10.269462, 0.000015, 0.000015, 44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1464.223876, -1006.337402, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1471.211547, -998.978698, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1472.212402, -998.978698, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1464.223876, -1006.947753, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1473.209228, -1007.932983, 10.289482, 0.000015, 0.000015, 44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1475.231201, -1005.962036, 7.788505, 0.000021, 90.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1471.230102, -1009.973083, 7.788505, 0.000000, 90.000022, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1475.232910, -998.978698, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1464.223876, -1009.979309, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1469.136230, -1004.283142, 13.140434, 89.999992, 137.455841, -92.455795, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14590, "mafcastopfoor", "cop_ceiling1", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1472.672119, -1007.818664, 13.140434, 89.999992, 137.455841, -92.455795, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14590, "mafcastopfoor", "cop_ceiling1", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1475.902954, -1001.477600, 13.140434, 89.999992, -90.000000, -89.999977, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14590, "mafcastopfoor", "cop_ceiling1", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1466.332641, -1011.048950, 13.140434, 89.999992, 179.999984, -89.999969, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14590, "mafcastopfoor", "cop_ceiling1", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1471.331909, -1011.048950, 13.140434, 89.999992, 179.999984, -89.999969, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14590, "mafcastopfoor", "cop_ceiling1", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1475.902954, -1006.468383, 13.140434, 89.999992, -90.000000, -89.999977, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14590, "mafcastopfoor", "cop_ceiling1", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1464.223876, -1008.338867, 12.287406, 89.999992, 90.000000, -89.999969, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1475.212890, -998.978698, 12.286796, 0.000000, 90.000022, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(14409, 1447.998046, -1007.407043, 10.504814, -0.000028, 0.000000, -89.999908, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14569, "traidman", "darkgrey_carpet_256", 0xFF686868);
    SetDynamicObjectMaterial(pcxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1473.210693, -994.177795, 13.140434, 89.999992, 179.999984, -89.999969, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14590, "mafcastopfoor", "cop_ceiling1", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1459.060668, -1008.328430, 13.140434, 89.999992, -90.000000, -89.999977, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14590, "mafcastopfoor", "cop_ceiling1", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1449.181030, -1009.958435, 10.239677, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1449.181030, -1004.868103, 10.239677, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1449.181030, -1009.958435, 15.202323, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1449.181030, -1004.868103, 15.202323, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1474.410522, -984.937744, 10.368095, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(14409, 1471.223510, -987.489562, 10.463188, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14569, "traidman", "darkgrey_carpet_256", 0xFF686868);
    SetDynamicObjectMaterial(pcxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1474.410522, -984.937744, 15.368339, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1469.857910, -989.656677, 11.138847, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1469.857910, -989.656677, 6.176933, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1474.410522, -989.656677, 6.176933, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1474.410522, -989.656677, 11.138847, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1475.231323, -988.926269, 10.408134, 0.000014, -179.999984, 89.999961, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1463.723388, -1006.337402, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1471.211547, -998.458435, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1459.674072, -1006.227783, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1453.183349, -1004.868103, 10.239677, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1459.377929, -989.656677, 8.638725, -0.000000, -89.999961, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1458.452148, -1004.465698, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1458.692382, -1005.226440, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1475.232910, -994.419067, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1474.232055, -994.419067, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1454.184326, -1009.969482, 10.239677, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1459.674072, -1009.939270, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1459.674072, -1008.939636, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(19325, 1475.197143, -996.904907, 10.276786, 89.999992, 90.000000, -89.999969, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0xFF686868);
    pcxts = CreateDynamicObject(19325, 1475.197143, -1001.426086, 10.276786, 89.999992, 90.000000, -89.999969, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0xFF686868);
    pcxts = CreateDynamicObject(19325, 1462.182617, -1009.947082, 10.276786, 89.999992, 178.299102, -88.299072, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0xFF686868);
    pcxts = CreateDynamicObject(19325, 1466.703857, -1009.947082, 10.276786, 89.999992, 178.299102, -88.299072, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0xFF686868);
    pcxts = CreateDynamicObject(18762, 1459.674072, -1005.227722, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1453.025756, -987.657287, 8.638725, 0.000028, -89.999984, 89.999877, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1450.176635, -987.657287, 8.638725, 0.000021, -89.999984, 89.999900, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1450.176635, -1001.908386, 8.638725, 0.000021, -89.999984, 89.999900, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1450.176635, -996.918945, 8.638725, 0.000021, -89.999984, 89.999900, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(8613, 1454.807128, -988.276916, 10.140068, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 5631, "apartmentalpha", "ws_railing1", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1456.378662, -987.657287, 8.638725, 0.000021, -89.999984, 89.999900, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1450.046508, -991.938171, 8.638725, 0.000021, -89.999984, 89.999900, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18981, 1462.706176, -997.448242, 18.211967, 0.000000, 90.000022, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 11301, "carshow_sfse", "ws_officy_ceiling", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1464.223876, -1006.337402, 15.247734, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1471.211547, -998.978698, 15.247734, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1472.212402, -998.978698, 15.247734, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1464.223876, -1006.947753, 15.247734, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1473.209228, -1007.932983, 15.251029, 0.000019, 0.000019, 44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1475.231201, -1005.962036, 12.750053, 0.000028, 90.000000, 89.999908, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1471.230102, -1009.973083, 12.750053, 0.000000, 90.000030, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1475.232910, -998.978698, 15.247734, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1464.223876, -1009.979309, 15.247734, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1464.223876, -1008.338867, 17.248954, 89.999992, 90.000015, -89.999961, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1475.212890, -998.978698, 17.248344, 0.000000, 90.000030, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1475.231323, -988.926269, 15.369682, 0.000021, -179.999984, 89.999938, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1463.723388, -1006.337402, 15.247734, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1471.211547, -998.458435, 15.247734, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1463.723388, -1006.447509, 15.247734, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1475.232910, -994.419067, 15.247734, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1474.232055, -994.419067, 15.247734, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1454.184326, -1009.969482, 15.201225, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1459.674072, -1009.969299, 15.247734, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1459.674072, -1009.468811, 15.247734, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(19325, 1475.197143, -996.904907, 16.219291, 89.999992, 90.000030, -89.999961, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0xFF686868);
    pcxts = CreateDynamicObject(19325, 1475.197143, -1001.426086, 16.219291, 89.999992, 90.000030, -89.999961, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0xFF686868);
    pcxts = CreateDynamicObject(19325, 1462.182617, -1009.947082, 16.219291, 89.999992, 179.574691, -89.574623, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0xFF686868);
    pcxts = CreateDynamicObject(19325, 1466.703857, -1009.947082, 16.219291, 89.999992, 179.574691, -89.574623, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0xFF686868);
    pcxts = CreateDynamicObject(18766, 1452.952270, -1004.868103, 15.202323, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1458.452148, -1004.465698, 15.209892, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1458.692382, -1005.226440, 15.209892, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1459.674072, -1005.227722, 15.209892, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1468.048583, -989.656677, 15.262626, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1459.377929, -989.656677, 12.821830, -0.000000, -89.999954, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1453.025756, -987.657287, 12.821830, 0.000035, -89.999984, 89.999855, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1450.176635, -987.657287, 12.821830, 0.000028, -89.999984, 89.999877, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1450.176635, -1001.908386, 12.821830, 0.000028, -89.999984, 89.999877, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1450.176635, -996.918945, 12.821830, 0.000028, -89.999984, 89.999877, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1456.378662, -987.657287, 12.821830, 0.000028, -89.999984, 89.999877, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1450.046508, -991.938171, 12.821830, 0.000028, -89.999984, 89.999877, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1450.015747, -985.656738, 8.468559, 0.000000, -90.000007, 179.999832, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1464.411254, -984.937744, 10.368095, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1464.411254, -984.937744, 15.368339, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1460.879028, -984.937744, 10.368095, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1460.879028, -984.937744, 15.368339, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1467.905883, -1002.657043, 18.779106, 0.000015, 0.000015, 44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1458.452148, -1000.446044, 10.286186, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1458.452148, -1000.446044, 15.209892, 0.000000, 0.000035, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1458.452148, -996.443298, 10.286186, 0.000000, 0.000035, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1458.452148, -996.443298, 15.209892, 0.000000, 0.000043, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1458.452148, -990.658813, 10.286186, 0.000000, 0.000043, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1458.452148, -990.658813, 15.209892, 0.000000, 0.000050, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1458.452148, -992.960815, 10.286186, 0.000000, 0.000050, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1458.452148, -992.960815, 15.209892, 0.000000, 0.000058, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1450.719238, -1004.465698, 10.286186, 0.000000, 0.000035, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1450.719238, -1004.465698, 15.209892, 0.000000, 0.000043, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1450.719238, -1000.446044, 10.286186, 0.000000, 0.000043, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1450.719238, -1000.446044, 15.209892, 0.000000, 0.000050, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1450.719238, -996.443298, 10.286186, 0.000000, 0.000050, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1450.719238, -996.443298, 15.209892, 0.000000, 0.000058, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1458.241943, -1001.507202, 8.546562, 0.000050, 90.000007, 89.999809, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1458.241943, -996.583496, 8.546562, 0.000050, 90.000015, 89.999809, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1450.178833, -992.960815, 10.286186, 0.000000, 0.000058, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1450.178833, -992.960815, 15.209892, 0.000000, 0.000066, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1458.241943, -995.953247, 8.546562, 0.000050, 90.000015, 89.999809, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(19325, 1458.359130, -1002.028991, 9.446830, 89.999992, 90.000000, -89.999969, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 19304, "pd_jail_door_top01", "pd_jail_door_top01", 0xFF2E1A0F);
    pcxts = CreateDynamicObject(19325, 1458.359130, -998.078979, 9.446830, 89.999992, 90.000000, -89.999969, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 19304, "pd_jail_door_top01", "pd_jail_door_top01", 0xFF2E1A0F);
    pcxts = CreateDynamicObject(19325, 1458.359130, -994.857421, 9.446830, 89.999992, 90.000000, -89.999969, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 19302, "pd_jail_door01", "pd_jail_door01", 0xFF2E1A0F);
    pcxts = CreateDynamicObject(1499, 1458.271118, -992.594116, 8.041923, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 3440, "airportpillar", "metalic_64", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 19303, "pd_jail_door02", "pd_jail_door02", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1456.430175, -1004.465698, 16.040702, 0.000012, -69.999946, -0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1456.430175, -1000.446044, 16.040702, 0.000012, -69.999946, -0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1456.430175, -996.443298, 16.040702, 0.000012, -69.999931, -0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1451.809204, -1004.465698, 16.681327, 0.000018, -94.199905, 0.000004, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1456.430175, -992.960815, 16.040702, 0.000012, -69.999916, -0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1450.719238, -1004.465698, 15.209892, 0.000000, 0.000043, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1450.719238, -1000.446044, 15.209892, 0.000000, 0.000050, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1450.719238, -996.443298, 15.209892, 0.000000, 0.000058, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1450.178833, -992.960815, 15.209892, 0.000000, 0.000066, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1451.809204, -1000.446044, 16.681327, 0.000018, -94.199905, 0.000004, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1451.809204, -996.443298, 16.681327, 0.000018, -94.199890, 0.000004, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1451.809204, -992.960815, 16.681327, 0.000018, -94.199874, 0.000004, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1462.221923, -1006.338012, 12.287406, 89.999992, 179.999984, -89.999969, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1459.674072, -1006.447998, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1463.720947, -1006.447509, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1457.671020, -1010.368591, 13.140434, 89.999992, 0.000026, -89.999961, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14590, "mafcastopfoor", "cop_ceiling1", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1456.560302, -1010.368591, 13.140434, 89.999992, 0.000026, -89.999961, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14590, "mafcastopfoor", "cop_ceiling1", 0x00000000);
    pcxts = CreateDynamicObject(1499, 1461.872192, -989.865417, 8.036918, 0.000000, 0.000043, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 3440, "airportpillar", "metalic_64", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 3044, "ciggarx", "CJ_KITCHDOOR", 0x00000000);
    pcxts = CreateDynamicObject(1499, 1464.872924, -989.845397, 8.036918, 0.000000, -0.000043, 179.999725, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 3440, "airportpillar", "metalic_64", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 3044, "ciggarx", "CJ_KITCHDOOR", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1466.397216, -989.656677, 15.262626, -0.000000, -179.999984, -0.000029, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(19325, 1464.807128, -989.898376, 12.590141, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(970, 1471.653930, -994.445373, 8.509941, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 5631, "apartmentalpha", "ws_railing1", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    pcxts = CreateDynamicObject(970, 1474.925170, -996.486267, 8.509941, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 5631, "apartmentalpha", "ws_railing1", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    pcxts = CreateDynamicObject(970, 1466.734252, -1009.666076, 8.509941, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 5631, "apartmentalpha", "ws_railing1", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    pcxts = CreateDynamicObject(970, 1461.892456, -1009.666076, 8.509941, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 5631, "apartmentalpha", "ws_railing1", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    pcxts = CreateDynamicObject(970, 1474.925170, -1001.506225, 8.509941, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 5631, "apartmentalpha", "ws_railing1", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1475.232910, -990.648193, 10.286186, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1474.232055, -990.648193, 10.286186, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(970, 1470.922973, -995.876037, 14.101127, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 5631, "apartmentalpha", "ws_railing1", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    pcxts = CreateDynamicObject(970, 1470.922973, -991.705871, 14.101127, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 5631, "apartmentalpha", "ws_railing1", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    pcxts = CreateDynamicObject(970, 1461.130981, -1006.045715, 14.101127, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 5631, "apartmentalpha", "ws_railing1", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    pcxts = CreateDynamicObject(970, 1469.178344, -1001.176879, 14.101127, -0.000024, -0.000024, -135.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 5631, "apartmentalpha", "ws_railing1", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    pcxts = CreateDynamicObject(970, 1466.230712, -1004.125305, 14.101127, -0.000024, -0.000024, -135.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 5631, "apartmentalpha", "ws_railing1", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    pcxts = CreateDynamicObject(19325, 1454.095581, -1008.686523, 15.623710, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1474.045043, -989.657287, 17.945610, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1474.045043, -998.967163, 18.942680, 89.999992, 180.000000, -89.999961, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1464.205444, -1009.356750, 18.942680, 89.999992, -90.000053, -89.999961, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19466, 1473.676635, -998.946777, 11.130790, 0.000028, 0.000000, 89.999908, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19466, 1464.207397, -1008.507019, 11.130790, 0.000000, -0.000028, 179.999816, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1469.546142, -1000.789794, 18.742607, 89.999992, 218.225967, -83.225967, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1465.743408, -1004.591003, 17.481498, -0.000022, 179.999954, -44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1459.705322, -1007.696289, 19.492851, 89.999992, -90.000053, -89.999961, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19466, 1459.656250, -1007.656250, 11.660453, 89.999992, -90.000038, -89.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19466, 1463.050292, -1006.397155, 11.130790, -0.000028, -0.000006, -89.999969, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1458.454711, -1000.663940, 16.840873, -0.000007, -0.000028, -0.000037, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1458.454711, -994.023925, 16.840873, -0.000007, -0.000028, -0.000037, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1454.869506, -996.443176, 18.343803, 5.600008, -0.000042, 89.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1448.260986, -996.443176, 18.992240, 5.600008, -0.000042, 89.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1454.869506, -992.961791, 18.343803, 5.600015, -0.000042, 89.999839, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1448.260986, -992.961791, 18.992240, 5.600015, -0.000042, 89.999839, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1454.869506, -1000.463012, 18.343803, 5.600022, -0.000042, 89.999816, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1448.260986, -1000.463012, 18.992240, 5.600022, -0.000042, 89.999816, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1454.869506, -1004.215026, 18.343803, 5.600029, -0.000042, 89.999794, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1448.260986, -1004.215026, 18.992240, 5.600029, -0.000042, 89.999794, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(18981, 1437.705444, -1008.385864, 18.211967, 0.000000, 90.000022, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 11301, "carshow_sfse", "ws_officy_ceiling", 0x00000000);
    pcxts = CreateDynamicObject(18981, 1431.976196, -1008.385864, 13.191947, 0.000000, 90.000022, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14537, "pdomebar", "club_floor2_sfwTEST", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1443.978881, -1000.351501, 15.312309, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1443.978881, -1014.460937, 15.312309, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1438.486816, -1012.399902, 15.312309, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1433.464111, -1006.900817, 15.312309, -0.000021, 0.000000, -89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1433.464111, -996.901611, 15.312309, -0.000021, 0.000000, -89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1438.933105, -995.861328, 15.312309, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18981, 1455.414184, -972.474914, 4.561820, 0.000000, 90.000022, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14537, "pdomebar", "club_floor2_sfwTEST", 0x00000000);
    pcxts = CreateDynamicObject(18981, 1462.706787, -972.665100, 13.034843, 0.000000, 90.000022, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14537, "pdomebar", "club_floor2_sfwTEST", 0x00000000);
    pcxts = CreateDynamicObject(18981, 1462.706787, -972.665100, 18.203056, 0.000000, 90.000022, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 11301, "carshow_sfse", "ws_officy_ceiling", 0x00000000);
    pcxts = CreateDynamicObject(19325, 1455.154418, -985.174499, 14.258476, 89.999992, 176.601181, -86.601173, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0xFF686868);
    pcxts = CreateDynamicObject(19325, 1455.154418, -985.174499, 20.900566, 89.999992, 176.601226, -86.601173, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0xFF686868);
    pcxts = CreateDynamicObject(18766, 1449.182861, -980.172729, 15.976127, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18981, 1437.718505, -972.665100, 13.034843, 0.000000, 90.000030, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14537, "pdomebar", "club_floor2_sfwTEST", 0x00000000);
    pcxts = CreateDynamicObject(18981, 1437.718505, -972.665100, 18.203056, 0.000000, 90.000030, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 11301, "carshow_sfse", "ws_officy_ceiling", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1454.674316, -975.672668, 15.976127, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1460.172241, -979.434509, 15.976127, -0.000021, 0.000000, -89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(14414, 1454.763549, -982.550476, 4.854057, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1453.025756, -982.657958, 8.508598, 0.000043, -89.999984, 89.999832, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1456.385131, -982.657958, 8.508598, 0.000043, -89.999984, 89.999832, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1456.385131, -982.657958, 8.508598, 0.000043, -89.999984, 89.999832, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1450.056030, -980.657653, 8.508598, 0.000007, -90.000022, 179.999710, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1445.066406, -980.657653, 8.638725, 0.000007, -90.000007, 179.999801, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1450.056030, -968.974304, 8.638725, 0.000007, -90.000015, 179.999755, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1445.066406, -968.974304, 8.638725, 0.000007, -90.000015, 179.999755, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1455.053955, -968.974304, 8.638725, 0.000007, -90.000015, 179.999755, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1457.055664, -971.973571, 8.638725, -0.000014, -89.999992, -90.000053, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1457.055664, -976.963256, 8.508598, -0.000028, -89.999992, -90.000007, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1457.055664, -977.673217, 8.508598, -0.000028, -89.999992, -90.000007, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1442.440795, -971.973571, 8.638725, -0.000021, -89.999992, -90.000030, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1442.440795, -976.963256, 8.638725, -0.000021, -89.999992, -90.000030, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1442.440795, -977.673217, 8.638725, -0.000021, -89.999992, -90.000030, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1449.182861, -980.172729, 15.976127, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18981, 1455.003295, -972.665100, 12.277763, 0.000000, 90.000022, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 11301, "carshow_sfse", "ws_officy_ceiling", 0x00000000);
    pcxts = CreateDynamicObject(19466, 1460.809692, -1006.397155, 11.130790, -0.000028, -0.000006, -89.999969, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1448.550781, -979.718200, 7.079765, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 19304, "pd_jail_door_top01", "pd_jail_door_top01", 0xFF686868);
    pcxts = CreateDynamicObject(19325, 1442.947021, -979.718200, 7.079765, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 19304, "pd_jail_door_top01", "pd_jail_door_top01", 0xFF686868);
    pcxts = CreateDynamicObject(19325, 1445.249023, -980.148620, 7.079765, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 19304, "pd_jail_door_top01", "pd_jail_door_top01", 0xFF686868);
    pcxts = CreateDynamicObject(19325, 1448.550781, -979.718200, 11.168388, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 19304, "pd_jail_door_top01", "pd_jail_door_top01", 0xFF686868);
    pcxts = CreateDynamicObject(19325, 1442.947021, -979.718200, 11.168388, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 19304, "pd_jail_door_top01", "pd_jail_door_top01", 0xFF686868);
    pcxts = CreateDynamicObject(19325, 1445.249023, -980.148620, 11.168388, 0.000028, 0.000000, 89.999908, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 19304, "pd_jail_door_top01", "pd_jail_door_top01", 0xFF686868);
    pcxts = CreateDynamicObject(19325, 1445.249023, -976.396362, 9.606864, 0.000028, 0.000000, 89.999908, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 19304, "pd_jail_door_top01", "pd_jail_door_top01", 0xFF686868);
    pcxts = CreateDynamicObject(11729, 1443.766113, -969.731689, 4.881645, 0.000000, 0.000043, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.766113, -969.731689, 6.761161, 0.000000, 0.000043, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1444.436157, -969.731689, 4.881645, 0.000000, 0.000050, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1444.436157, -969.731689, 6.761161, 0.000000, 0.000050, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1445.106811, -969.731689, 4.881645, 0.000000, 0.000066, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1445.106811, -969.731689, 6.761161, 0.000000, 0.000066, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1445.777465, -969.731689, 4.881645, 0.000000, 0.000073, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1445.777465, -969.731689, 6.761161, 0.000000, 0.000073, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1446.448120, -969.731689, 4.881645, 0.000000, 0.000050, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1446.448120, -969.731689, 6.761161, 0.000000, 0.000050, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1447.118164, -969.731689, 4.881645, 0.000000, 0.000058, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1447.118164, -969.731689, 6.761161, 0.000000, 0.000058, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1447.788818, -969.731689, 4.881645, 0.000000, 0.000073, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1447.788818, -969.731689, 6.761161, 0.000000, 0.000073, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -971.661254, 4.881645, 0.000035, 0.000007, 89.999778, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -971.661254, 6.761161, 0.000035, 0.000007, 89.999778, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -970.991210, 4.881645, 0.000035, 0.000014, 89.999778, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -970.991210, 6.761161, 0.000035, 0.000014, 89.999778, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -970.320556, 4.881645, 0.000035, 0.000029, 89.999778, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -970.320556, 6.761161, 0.000035, 0.000029, 89.999778, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -973.661193, 4.881645, 0.000043, 0.000007, 89.999755, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -973.661193, 6.761161, 0.000043, 0.000007, 89.999755, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -972.991149, 4.881645, 0.000043, 0.000014, 89.999755, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -972.991149, 6.761161, 0.000043, 0.000014, 89.999755, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -972.320495, 4.881645, 0.000043, 0.000029, 89.999755, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -972.320495, 6.761161, 0.000043, 0.000029, 89.999755, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -975.671813, 4.881645, 0.000050, 0.000007, 89.999732, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -975.671813, 6.761161, 0.000050, 0.000007, 89.999732, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -975.001770, 4.881645, 0.000050, 0.000014, 89.999732, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -975.001770, 6.761161, 0.000050, 0.000014, 89.999732, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -974.331115, 4.881645, 0.000050, 0.000029, 89.999732, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(11729, 1443.176147, -974.331115, 6.761161, 0.000050, 0.000029, 89.999732, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    pcxts = CreateDynamicObject(19325, 1443.728271, -976.397644, 7.079765, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 19304, "pd_jail_door_top01", "pd_jail_door_top01", 0xFF686868);
    pcxts = CreateDynamicObject(1499, 1448.554077, -976.462768, 5.039726, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF686868);
    SetDynamicObjectMaterial(pcxts, 1, 19302, "pd_jail_door01", "pd_jail_door01", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1452.406250, -980.446533, 7.463432, 0.000028, 0.000000, 89.999908, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1452.406250, -968.976440, 7.463432, 0.000028, 0.000000, 89.999908, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1452.406250, -980.446533, 9.992973, 0.000043, 0.000000, 89.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1452.406250, -968.976440, 9.992973, 0.000043, 0.000000, 89.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1452.406250, -978.335998, 9.992973, 0.000043, 0.000000, 89.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    pcxts = CreateDynamicObject(19922, 1445.766357, -978.378540, 5.041801, 0.000004, -0.000028, -179.699783, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    pcxts = CreateDynamicObject(19922, 1445.823242, -972.832275, 5.041801, 0.000021, -0.000000, 90.299926, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1450.024536, -989.658935, 5.515678, 0.000007, -90.000007, 179.999801, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(1499, 1455.451293, -989.523315, 8.041923, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 3440, "airportpillar", "metalic_64", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 19303, "pd_jail_door02", "pd_jail_door02", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1455.929687, -989.767944, 10.286186, 0.000000, 0.000072, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1455.929687, -989.767944, 15.209892, 0.000000, 0.000080, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1453.457763, -989.767944, 10.286186, 0.000000, 0.000080, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1453.457763, -989.767944, 15.209892, 0.000000, 0.000089, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(19325, 1455.154418, -989.577148, 14.448661, 89.999992, 176.601181, -86.601173, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18996, "mattextures", "safetymesh", 0xFF000000);
    pcxts = CreateDynamicObject(2184, 1439.515014, -1009.584716, 13.668022, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    pcxts = CreateDynamicObject(19894, 1438.461425, -1009.574584, 14.449638, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    pcxts = CreateDynamicObject(2185, 1439.576782, -1005.202636, 13.680717, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 3, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    pcxts = CreateDynamicObject(2185, 1435.684448, -1005.202636, 13.680717, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 3, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    pcxts = CreateDynamicObject(2185, 1439.576782, -1002.470153, 13.680717, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 3, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    pcxts = CreateDynamicObject(2185, 1435.684448, -1002.470153, 13.680717, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 3, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    pcxts = CreateDynamicObject(2185, 1439.576782, -999.837829, 13.680717, 0.000000, 0.000035, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 3, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    pcxts = CreateDynamicObject(2185, 1435.684448, -999.837829, 13.680717, 0.000000, 0.000035, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 3, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    pcxts = CreateDynamicObject(11245, 1440.663208, -1011.108825, 14.388236, 0.000015, -58.499980, 44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 14808, "lee_strip2", "Strip_Gold", 0x00000000);
    pcxts = CreateDynamicObject(11245, 1436.331054, -1011.108825, 14.388236, 0.000015, -58.500003, 135.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 14808, "lee_strip2", "Strip_Gold", 0x00000000);
    pcxts = CreateDynamicObject(1499, 1445.481079, -1008.880065, 13.671562, 0.000043, 0.000029, 89.999832, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 3440, "airportpillar", "metalic_64", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 3044, "ciggarx", "CJ_KITCHDOOR", 0x00000000);
    pcxts = CreateDynamicObject(1499, 1445.461059, -1005.879333, 13.671562, -0.000043, -0.000029, -89.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 3440, "airportpillar", "metalic_64", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 3044, "ciggarx", "CJ_KITCHDOOR", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1445.474609, -1009.368713, 15.639335, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1445.474609, -1005.387390, 15.639335, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(19325, 1445.532958, -1008.686523, 18.236259, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19325, 1449.504394, -985.287963, 18.044242, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(2164, 1438.086425, -996.365295, 13.666923, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    pcxts = CreateDynamicObject(2164, 1441.086425, -996.365295, 13.666923, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    pcxts = CreateDynamicObject(2164, 1435.086425, -996.365295, 13.666923, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    pcxts = CreateDynamicObject(2267, 1443.425903, -1003.865356, 15.997489, -0.000021, 0.000000, -89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 19173, "samppictures", "samppicture3", 0x00000000);
    pcxts = CreateDynamicObject(2267, 1443.425903, -998.252990, 15.997489, -0.000021, 0.000000, -89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 19173, "samppictures", "samppicture3", 0x00000000);
    pcxts = CreateDynamicObject(2267, 1434.004638, -998.253051, 15.997489, 0.000021, 0.000000, 89.999870, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 19173, "samppictures", "samppicture4", 0x00000000);
    pcxts = CreateDynamicObject(1789, 1450.290893, -979.849853, 5.606010, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    pcxts = CreateDynamicObject(2267, 1434.004638, -1003.865417, 15.997489, 0.000021, 0.000000, 89.999870, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 19173, "samppictures", "samppicture4", 0x00000000);
    pcxts = CreateDynamicObject(1789, 1451.581298, -973.219909, 5.606010, -0.000021, 0.000000, -89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 3440, "airportpillar", "metalic_64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    pcxts = CreateDynamicObject(2267, 1438.461791, -996.387207, 16.437919, 0.000004, 0.000035, -0.000118, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "white", 0xFF000000);
    SetDynamicObjectMaterial(pcxts, 1, 16434, "des_stwnsigns1", "des_banksign", 0x00000000);
    pcxts = CreateDynamicObject(14493, 1455.814575, -984.543945, 15.834403, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 8463, "vgseland", "triadcarpet2", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1460.172241, -976.523986, 15.976127, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1457.859985, -979.004760, 15.976127, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(18766, 1455.671264, -974.483581, 18.518608, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    pcxts = CreateDynamicObject(2270, 1453.910766, -976.669128, 15.059745, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    pcxts = CreateDynamicObject(19325, 1449.915039, -979.006835, 18.306205, -0.000021, 0.000000, -89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, -1, "none", "none", 0xFF000000);
    pcxts = CreateDynamicObject(19786, 1456.672607, -979.447143, 15.583305, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    pcxts = CreateDynamicObject(2283, 1459.643066, -981.611633, 16.199882, -0.000021, 0.000000, -89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 10412, "hotel1", "ws_vankhoffsign", 0x00000000);
    pcxts = CreateDynamicObject(2283, 1457.952026, -984.412109, 16.199882, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 17508, "barrio1_lae2", "gangsign3_LAe", 0x00000000);
    pcxts = CreateDynamicObject(1827, 1456.623901, -980.801086, 13.504814, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    pcxts = CreateDynamicObject(628, 1453.841552, -983.513916, 15.516899, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 17514, "landhub", "grasspatch_64HV", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 14477, "crlsbits", "CJ_PLANTPOT", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 3, 17848, "eastlstr2_lae2", "deadpalm01", 0x00000000);
    pcxts = CreateDynamicObject(2773, 1460.039428, -1003.770141, 8.566947, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14808, "lee_strip2", "Strip_Gold", 0x00000000);
    pcxts = CreateDynamicObject(2773, 1460.039428, -1001.179687, 8.566947, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14808, "lee_strip2", "Strip_Gold", 0x00000000);
    pcxts = CreateDynamicObject(2773, 1460.039428, -999.759460, 8.566947, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14808, "lee_strip2", "Strip_Gold", 0x00000000);
    pcxts = CreateDynamicObject(2773, 1460.039428, -997.168701, 8.566947, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14808, "lee_strip2", "Strip_Gold", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1466.564208, -1005.001037, 8.536796, 0.000019, 0.000019, 44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1470.234252, -1001.331298, 8.536796, 0.000019, 0.000019, 44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14650, "ab_trukstpc", "sa_wood08_128", 0x00000000);
    pcxts = CreateDynamicObject(2785, 1468.585327, -1003.347229, 8.871879, 0.000015, 0.000015, 44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 18646, "matcolours", "blue", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 2942, "kmb_atmx", "kmb_atm_sign", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 3, 4010, "lanblokb2", "bluewhitebuildwall2", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 5, 17547, "eastbeach4a_lae2", "bluestucco1", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1468.182128, -1003.382507, 10.538017, 89.999992, -137.455810, -87.544143, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1468.175048, -1003.375427, 10.216850, 89.999992, -137.455810, -87.544143, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    pcxts = CreateDynamicObject(1499, 1459.581542, -1006.940185, 8.039237, -0.000043, -0.000029, -89.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 3440, "airportpillar", "metalic_64", 0xFF2E1A0F);
    SetDynamicObjectMaterial(pcxts, 1, 3044, "ciggarx", "CJ_KITCHDOOR", 0x00000000);
    pcxts = CreateDynamicObject(11245, 1459.578125, -1004.472106, 10.459403, 0.000000, -70.499977, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 14808, "lee_strip2", "Strip_Gold", 0x00000000);
    pcxts = CreateDynamicObject(11245, 1459.578125, -990.631286, 10.459403, 0.000000, -70.499977, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 14808, "lee_strip2", "Strip_Gold", 0x00000000);
    pcxts = CreateDynamicObject(18762, 1471.272094, -994.419067, 10.286186, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    pcxts = CreateDynamicObject(2118, 1471.469726, -993.042724, 8.030937, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 16322, "a51_stores", "des_ghotwood1", 0xFF686868);
    SetDynamicObjectMaterial(pcxts, 1, 14415, "carter_block_2", "mp_gs_woodpanel1", 0x00000000);
    pcxts = CreateDynamicObject(2109, 1464.372802, -996.179748, 8.392265, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x90339999);
    pcxts = CreateDynamicObject(2117, 1464.383789, -999.616943, 8.031669, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x90339999);
    pcxts = CreateDynamicObject(2117, 1464.383789, -993.945312, 8.031669, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x90339999);
    pcxts = CreateDynamicObject(627, 1474.541503, -995.643554, 9.834892, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 654, "gta_tree_oldpine", "tree19Mi", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 14477, "crlsbits", "CJ_PLANTPOT", 0x00000000);
    pcxts = CreateDynamicObject(627, 1474.321289, -992.581481, 9.834892, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 654, "gta_tree_oldpine", "tree19Mi", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 14477, "crlsbits", "CJ_PLANTPOT", 0x00000000);
    pcxts = CreateDynamicObject(627, 1460.981689, -1009.185363, 9.834892, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 654, "gta_tree_oldpine", "tree19Mi", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 14477, "crlsbits", "CJ_PLANTPOT", 0x00000000);
    pcxts = CreateDynamicObject(2714, 1467.854736, -1003.837768, 9.462944, 0.000015, 0.000015, 44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(pcxts, 0, "CHASE", 130, "Arial", 80, 1, 0xFFFFFFFF, 0x00000000, 1);
    pcxts = CreateDynamicObject(2714, 1467.245849, -1004.446533, 9.462944, 0.000015, 0.000015, 44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(pcxts, 0, "CHASE", 130, "Arial", 80, 1, 0xFFFFFFFF, 0x00000000, 1);
    pcxts = CreateDynamicObject(2714, 1468.454833, -1003.237548, 9.462944, 0.000015, 0.000015, 44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(pcxts, 0, "CHASE", 130, "Arial", 80, 1, 0xFFFFFFFF, 0x00000000, 1);
    pcxts = CreateDynamicObject(2714, 1469.056152, -1002.636352, 9.462944, 0.000015, 0.000015, 44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(pcxts, 0, "CHASE", 130, "Arial", 80, 1, 0xFFFFFFFF, 0x00000000, 1);
    pcxts = CreateDynamicObject(2714, 1469.664428, -1002.028137, 9.462944, 0.000015, 0.000015, 44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(pcxts, 0, "CHASE", 130, "Arial", 80, 1, 0xFFFFFFFF, 0x00000000, 1);
    pcxts = CreateDynamicObject(1713, 1469.431396, -999.711303, 8.034233, -0.000015, -0.000015, -135.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    pcxts = CreateDynamicObject(1713, 1467.578613, -1001.564086, 8.034233, -0.000015, -0.000015, -135.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    pcxts = CreateDynamicObject(1713, 1465.718261, -1003.424133, 8.034233, -0.000015, -0.000015, -135.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    pcxts = CreateDynamicObject(627, 1470.228881, -998.893615, 9.834892, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 654, "gta_tree_oldpine", "tree19Mi", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 14477, "crlsbits", "CJ_PLANTPOT", 0x00000000);
    pcxts = CreateDynamicObject(2199, 1472.829833, -990.237609, 8.051933, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 2, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    pcxts = CreateDynamicObject(2199, 1463.838867, -1004.938964, 8.051933, -0.000021, 0.000000, -89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 2, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    pcxts = CreateDynamicObject(1508, 1472.969970, -1007.657592, 9.636650, -0.000015, 0.000015, -44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 3820, "boxhses_sfsx", "ws_wood_doors2", 0x00000000);
    pcxts = CreateDynamicObject(19482, 1468.757690, -1003.553894, 10.612358, -0.000015, 0.000015, -44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(pcxts, 0, "PACIFIC STANDARD", 130, "Times New Roman", 30, 1, 0xFF2F4F4F, 0x00000000, 1);
    pcxts = CreateDynamicObject(2257, 1463.339843, -990.170776, 14.391532, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 6336, "sunset02_law2", "bank01_LAw", 0x00000000);
    pcxts = CreateDynamicObject(2257, 1467.510131, -990.170776, 11.729545, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 8419, "vgsbldng1", "cityplansign01_256", 0x00000000);
    pcxts = CreateDynamicObject(19482, 1468.771850, -1003.539733, 10.382133, -0.000015, 0.000015, -44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(pcxts, 0, "PUBLIC DEPOSIT BANK", 130, "Times New Roman", 20, 1, 0xFF2F4F4F, 0x00000000, 1);
    pcxts = CreateDynamicObject(19482, 1467.696044, -1004.615112, 10.502250, -0.000015, 0.000015, -44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(pcxts, 0, "%", 130, "Wingdings", 40, 1, 0xFF000000, 0x00000000, 1);
    pcxts = CreateDynamicObject(19482, 1469.832397, -1002.479431, 10.502250, -0.000015, 0.000015, -44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(pcxts, 0, "%", 130, "Wingdings", 40, 1, 0xFF000000, 0x00000000, 1);
    pcxts = CreateDynamicObject(19482, 1467.343017, -1002.507995, 10.852592, -0.000015, 0.000015, -44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(pcxts, 0, "n", 130, "Webdings", 199, 1, 0xFF000000, 0x00000000, 1);
    pcxts = CreateDynamicObject(19482, 1467.343017, -1002.507995, 10.852592, -0.000015, 0.000015, -44.999984, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(pcxts, 0, "l", 130, "Webdings", 150, 1, 0xFF8B7765, 0x00000000, 1);
    pcxts = CreateDynamicObject(19482, 1467.335937, -1002.500915, 10.852592, -47.400009, 0.000021, -44.999958, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(pcxts, 0, "l", 130, "Webdings", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    pcxts = CreateDynamicObject(19482, 1467.127319, -1002.625427, 10.635429, -47.400009, 0.000021, -44.999958, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(pcxts, 0, "l", 130, "Arial", 50, 1, 0xFFFF0000, 0x00000000, 1);
    pcxts = CreateDynamicObject(19482, 1467.021118, -1002.759094, 10.480644, -47.400009, 0.000021, -44.999958, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(pcxts, 0, "l", 130, "Arial", 50, 1, 0xFFFF0000, 0x00000000, 1);
    pcxts = CreateDynamicObject(19482, 1467.321777, -1002.486755, 10.852592, -0.799990, 179.999984, 135.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(pcxts, 0, "CLOCK", 130, "Times New Roman", 20, 1, 0xFF000000, 0x00000000, 1);
    pcxts = CreateDynamicObject(2165, 1451.988769, -1003.504882, 8.047660, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 5, 1550, "cj_money_bags", "money_128", 0x00000000);
    pcxts = CreateDynamicObject(2165, 1451.988769, -1001.154724, 8.047660, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 5, 1550, "cj_money_bags", "money_128", 0x00000000);
    pcxts = CreateDynamicObject(2165, 1451.988769, -998.814331, 8.047660, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 5, 1550, "cj_money_bags", "money_128", 0x00000000);
    pcxts = CreateDynamicObject(2165, 1455.080322, -997.823425, 8.047660, -0.000028, 0.000000, -89.999877, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 5, 1550, "cj_money_bags", "money_128", 0x00000000);
    pcxts = CreateDynamicObject(2165, 1455.080322, -1000.173583, 8.047660, -0.000028, 0.000000, -89.999877, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 5, 1550, "cj_money_bags", "money_128", 0x00000000);
    pcxts = CreateDynamicObject(2165, 1455.080322, -1002.513977, 8.047660, -0.000028, 0.000000, -89.999877, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 5, 1550, "cj_money_bags", "money_128", 0x00000000);
    pcxts = CreateDynamicObject(627, 1451.470092, -994.802673, 9.834892, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 654, "gta_tree_oldpine", "tree19Mi", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 14477, "crlsbits", "CJ_PLANTPOT", 0x00000000);
    pcxts = CreateDynamicObject(627, 1458.571533, -1006.401977, 9.834892, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 654, "gta_tree_oldpine", "tree19Mi", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 14477, "crlsbits", "CJ_PLANTPOT", 0x00000000);
    pcxts = CreateDynamicObject(628, 1474.121948, -1004.604187, 10.017143, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 17514, "landhub", "grasspatch_64HV", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 14477, "crlsbits", "CJ_PLANTPOT", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 3, 17848, "eastlstr2_lae2", "deadpalm01", 0x00000000);
    pcxts = CreateDynamicObject(628, 1469.889770, -1008.843994, 10.017143, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 17514, "landhub", "grasspatch_64HV", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 14477, "crlsbits", "CJ_PLANTPOT", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 3, 17848, "eastlstr2_lae2", "deadpalm01", 0x00000000);
    pcxts = CreateDynamicObject(1713, 1473.087646, -1006.592468, 13.614921, -0.000015, -0.000015, -135.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    pcxts = CreateDynamicObject(1713, 1474.698486, -995.872619, 13.614921, -0.000021, 0.000000, -89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    pcxts = CreateDynamicObject(1713, 1462.779174, -1009.443420, 13.614921, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    pcxts = CreateDynamicObject(1713, 1454.596313, -1008.253479, 13.614921, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    pcxts = CreateDynamicObject(1713, 1457.415161, -988.123535, 8.023246, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    pcxts = CreateDynamicObject(1713, 1461.066406, -988.653991, 8.023246, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    pcxts = CreateDynamicObject(1708, 1465.764892, -990.658447, 8.051933, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    pcxts = CreateDynamicObject(1708, 1467.184692, -990.658447, 8.051933, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(pcxts, 2, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    pcxts = CreateDynamicObject(2257, 1474.704345, -987.257263, 16.051200, -0.000021, 0.000000, -89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 3083, "billbox", "Sprunk_postersign1", 0x00000000);
    pcxts = CreateDynamicObject(2257, 1459.985107, -985.476684, 11.041557, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 3715, "archlax", "arch_sign", 0x00000000);
    pcxts = CreateDynamicObject(2257, 1450.693725, -991.137634, 13.389457, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 14593, "papaerchaseoffice", "sign_restroom", 0x00000000);
    pcxts = CreateDynamicObject(2257, 1450.693725, -998.488708, 12.148612, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 3434, "skullsign", "vegasstripsign1_256", 0x00000000);
    pcxts = CreateDynamicObject(2257, 1450.693725, -1002.489257, 12.148612, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 7418, "vgnbballsign2", "bankofSA_law", 0x00000000);
    pcxts = CreateDynamicObject(2257, 1449.683349, -1009.428771, 15.080619, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(pcxts, 1, 14563, "triad_main", "sign_maintenance", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(1829, 1445.801757, -972.143432, 6.312065, 0.000014, -0.000028, 179.999740, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1829, 1445.801757, -973.613403, 6.312065, -0.000011, 0.000028, 0.000012, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1829, 1445.077514, -978.400024, 6.312065, -0.000014, -0.000014, -90.000015, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1829, 1446.547485, -978.400024, 6.312065, 0.000014, 0.000014, 89.999893, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1886, 1451.382690, -969.716857, 11.994804, 14.883669, -15.412958, -42.974720, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1886, 1451.389282, -979.906311, 11.997489, 14.883663, 15.412960, -137.025207, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2634, 1445.942626, -976.404296, 6.550224, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19999, 1438.474121, -1011.316467, 13.686210, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1671, 1436.511596, -1003.873229, 14.129081, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1671, 1436.511596, -1001.182006, 14.129081, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1671, 1440.373657, -1003.873229, 14.129081, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1671, 1440.373657, -1001.182006, 14.129081, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1671, 1436.511596, -998.501525, 14.129081, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1671, 1440.373657, -998.501525, 14.129081, 0.000000, 0.000035, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19624, 1439.348632, -1009.763061, 14.500419, -89.999992, 121.631492, 76.631500, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11705, 1437.768676, -1009.877624, 14.408744, 0.000015, 0.000015, 44.999984, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 1440.388427, -1004.916137, 14.511039, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 1440.388427, -1002.184753, 14.511039, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 1440.388427, -999.554138, 14.511039, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 1436.516723, -1004.916137, 14.511039, 0.000000, -0.000028, 179.999816, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 1436.516723, -1002.184753, 14.511039, 0.000000, -0.000028, 179.999816, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 1436.516723, -999.554138, 14.511039, 0.000000, -0.000028, 179.999816, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2186, 1443.006103, -997.686828, 13.636283, -0.000021, 0.000000, -89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2186, 1434.446044, -998.877258, 13.636283, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 1438.470214, -1011.862182, 16.241874, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 1445.814941, -972.859558, 6.943656, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 1445.794921, -978.400329, 6.943656, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1523, 1455.268920, -978.531677, 13.499320, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2525, 1459.171997, -977.750366, 13.524833, -0.000021, 0.000000, -89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2515, 1453.900024, -976.452880, 14.705375, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2814, 1456.613525, -980.876342, 13.987358, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2814, 1456.613525, -980.876342, 14.087455, 0.000015, -0.000015, 135.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11683, 1470.105224, -993.207824, 8.014579, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11683, 1470.105224, -991.877563, 8.014579, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11683, 1472.737670, -991.877624, 8.014579, -0.000021, 0.000000, -89.999900, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11683, 1472.737670, -993.207885, 8.014579, -0.000021, 0.000000, -89.999900, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1452.541748, -980.044860, 17.760917, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1450.622436, -974.914306, 11.821586, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1439.197753, -974.914306, 11.821586, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 1438.687255, -1004.144409, 17.761283, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2106, 1471.462402, -992.531799, 8.812553, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11728, 1454.828369, -969.451354, 6.861015, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1453.353393, -1002.892883, 8.048393, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1453.353393, -1000.502929, 8.048393, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1453.353393, -998.192382, 8.048393, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1456.655151, -1002.372558, 8.048393, 0.000028, 0.000000, 89.999908, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1456.655151, -994.652099, 8.048393, 0.000028, 0.000000, 89.999908, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1456.655151, -998.402587, 8.048393, 0.000028, 0.000000, 89.999908, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11728, 1455.920898, -990.207214, 9.913017, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1470.446899, -1005.127075, 12.549491, 0.000015, 0.000015, 44.999984, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1461.772827, -1008.381958, 12.440727, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1473.685424, -996.650329, 12.440727, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1470.446899, -1005.127075, 17.582206, 0.000019, 0.000019, 44.999984, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1461.772827, -1008.381958, 17.473442, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1473.685424, -996.650329, 17.473442, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14629, 1465.000000, -998.436096, 16.884696, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1473.730102, -987.236328, 17.576469, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1471.719970, -987.236328, 17.576469, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14804, 1464.370971, -996.336669, 9.791069, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1456.157104, -1007.357604, 17.586601, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1447.173095, -1007.357604, 17.586601, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1451.614013, -986.798095, 17.586601, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1454.695068, -986.798095, 17.586601, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1473.730102, -991.726562, 17.576469, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1472.809204, -992.497192, 12.496635, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1457.366577, -1007.347778, 12.496635, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14629, 1465.381225, -987.017761, 16.987113, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14804, 1464.390991, -996.036376, 9.791069, 0.000000, -0.000021, 179.999862, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2106, 1464.371337, -993.072265, 8.842582, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2106, 1464.371337, -993.812438, 8.842582, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2106, 1464.371337, -998.762878, 8.842582, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2106, 1464.371337, -999.503051, 8.842582, 0.000000, 0.000028, 0.000000, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2690, 1459.072753, -1008.904785, 9.289115, -0.000021, 0.000000, -89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2690, 1459.062744, -992.995727, 9.289115, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2690, 1450.820068, -992.995727, 11.249931, 0.000021, 0.000000, 89.999931, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1471.933715, -1003.640502, 12.549491, 0.000015, 0.000015, 44.999984, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 1468.953613, -1006.619262, 12.549491, 0.000015, 0.000015, 44.999984, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11710, 1472.890380, -1007.597900, 11.552543, 0.000015, 0.000015, 44.999984, 999, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11710, 1463.360229, -989.867553, 10.819389, 0.000000, 0.000021, 0.000000, 999, 1, -1, 200.00, 200.00);
}