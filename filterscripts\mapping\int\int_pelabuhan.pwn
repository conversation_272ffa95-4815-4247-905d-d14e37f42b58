CreatePelabuhanInt()
{
    new STREAMER_TAG_OBJECT:plbxts;
    plbxts = CreateDynamicObject(18981, 2750.929443, -2421.191650, 27.697509, 0.000000, 90.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2750.929443, -2396.221191, 27.697509, 0.000000, 90.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2769.414306, -2408.221679, 22.824951, 0.000029, 180.000015, 89.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2750.931152, -2383.230468, 23.702758, 0.000022, 180.000015, 89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 8395, "pyramid", "luxorwindow01_128", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2751.892089, -2434.199951, 35.695434, 0.000022, 180.000015, 89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2775.909179, -2421.191650, 27.697509, 0.000000, 90.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2763.927978, -2396.221191, 22.824951, 0.000000, 180.000015, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2776.890380, -2434.199951, 35.695434, 0.000022, 180.000015, 89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2788.891601, -2421.236572, 23.702758, 0.000000, 180.000015, 179.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 8395, "pyramid", "luxorwindow01_128", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2737.942382, -2395.231689, 35.695434, 0.000000, 180.000015, 179.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2737.942382, -2420.212402, 35.695434, 0.000000, 180.000015, 179.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2744.727050, -2427.034912, 35.695434, 0.000018, 180.000015, 44.999958, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2738.936035, -2383.239990, 24.690917, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2762.936279, -2383.239990, 24.690917, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2788.886474, -2409.228027, 24.690917, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2788.886474, -2433.202636, 24.690917, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2788.886474, -2421.241943, 28.172363, 0.000007, 90.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2750.936035, -2383.239990, 28.173095, 0.000000, 90.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2750.936035, -2383.239990, 36.684448, 0.000000, 90.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2775.949218, -2396.144531, 35.718383, 0.000000, 90.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2788.886474, -2421.221923, 36.693969, 0.000007, 90.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2765.949218, -2396.144531, 35.718383, 0.000000, 90.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2775.949218, -2406.144531, 35.718383, 0.000000, 90.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2765.949218, -2406.144531, 35.718383, 0.000000, 90.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2775.934082, -2383.230468, 48.667358, 0.000029, 180.000015, 89.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2788.891601, -2421.226562, 49.677856, 0.000000, 180.000015, 179.999816, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 9932, "nitelites", "sfnitewindows", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2750.981201, -2383.230468, 49.677856, 0.000029, 180.000015, 89.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 9932, "nitelites", "sfnitewindows", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2788.928222, -2396.221435, 48.667358, 0.000022, 180.000015, 179.999893, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2788.886474, -2396.229980, 36.693969, 0.000007, 90.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2775.907470, -2383.239990, 36.684448, 0.000000, 90.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2765.949218, -2396.144531, 34.717407, 0.000000, 90.000030, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2775.949218, -2406.144531, 34.717407, 0.000000, 90.000030, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2765.949218, -2406.144531, 34.717407, 0.000000, 90.000030, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2750.929443, -2421.191650, 48.580078, 0.000000, 90.000015, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2750.929443, -2396.221191, 48.580078, 0.000000, 90.000015, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2775.909179, -2421.191650, 48.580078, 0.000000, 90.000030, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2775.893066, -2396.221191, 48.580078, 0.000000, 90.000015, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2788.886474, -2408.243652, 49.650634, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2763.943115, -2383.242675, 49.650634, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2775.937500, -2383.239990, 47.695434, 0.000000, 90.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2788.886474, -2396.229980, 47.695556, 0.000007, 90.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2752.947509, -2396.230468, 35.714233, 0.000014, 90.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2752.947509, -2406.175048, 35.714233, 0.000014, 90.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2764.947753, -2419.135986, 35.714233, 0.000000, 89.999984, 179.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2775.961669, -2419.135986, 35.714233, 0.000000, 89.999984, 179.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2752.947509, -2396.230468, 34.713256, 0.000022, 90.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2752.947509, -2406.175048, 34.713256, 0.000022, 90.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2764.947753, -2419.135986, 34.713256, 0.000000, 89.999977, 179.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2775.961669, -2419.135986, 34.713256, 0.000000, 89.999977, 179.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2775.963378, -2396.144531, 34.717407, 0.000000, 90.000030, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2775.949218, -2396.194580, 27.698120, 0.000000, 90.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2812.922851, -2396.244628, 23.702758, 0.000000, 180.000015, 179.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2790.937988, -2394.739013, 27.690185, 89.999992, 180.000000, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2776.929199, -2383.223388, 23.702758, -0.000007, 180.000015, -90.000068, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2788.886474, -2384.228271, 24.690917, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2759.423339, -2408.617187, 30.075683, 0.000000, 90.000015, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2762.407714, -2408.618408, 20.602661, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2763.398681, -2408.618408, 19.222656, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2764.389648, -2408.618408, 18.021606, 0.000007, 0.000007, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(19482, 2768.832519, -2408.725341, 31.548217, -0.000022, 0.000000, -89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "PELA", 130, "Arial", 199, 1, 0xFF000000, 0x00000000, 1);
    plbxts = CreateDynamicObject(19482, 2774.122314, -2408.725341, 31.548217, -0.000022, 0.000000, -89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "BUHA", 130, "Arial", 199, 1, 0xFF000000, 0x00000000, 1);
    plbxts = CreateDynamicObject(19482, 2777.572265, -2408.725341, 31.548217, -0.000022, 0.000000, -89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "N", 130, "Arial", 199, 1, 0xFF000000, 0x00000000, 1);
    plbxts = CreateDynamicObject(19482, 2772.379882, -2408.725341, 30.127319, -0.000022, 0.000000, -89.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "BELAWAN", 130, "Arial", 120, 1, 0xFF000000, 0x00000000, 1);
    plbxts = CreateDynamicObject(19482, 2768.970703, -2408.735351, 30.136352, -0.000037, 0.000000, -89.999885, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "o", 130, "Webdings", 120, 1, 0xFF000000, 0x00000000, 1);
    plbxts = CreateDynamicObject(19482, 2775.791259, -2408.735351, 30.136352, -0.000022, -0.000000, 90.000068, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "o", 130, "Webdings", 120, 1, 0xFF000000, 0x00000000, 1);
    plbxts = CreateDynamicObject(19482, 2759.980224, -2409.195800, 31.466430, -0.000045, 0.000000, -89.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "o", 130, "Webdings", 199, 1, 0xFF000000, 0x00000000, 1);
    plbxts = CreateDynamicObject(19482, 2759.980224, -2409.185791, 31.006591, -0.000045, 0.000000, -89.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "m", 130, "Wingdings", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(10009, 2748.239501, -2388.650390, 32.996093, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(10009, 2783.490478, -2423.933105, 32.996093, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(970, 2780.130371, -2418.649169, 36.727172, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(970, 2775.959228, -2418.649169, 36.727172, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(970, 2771.789306, -2418.649169, 36.727172, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(970, 2767.619628, -2418.649169, 36.727172, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(970, 2763.459960, -2418.649169, 36.727172, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(970, 2759.289062, -2418.649169, 36.727172, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(970, 2755.117675, -2418.649169, 36.727172, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(970, 2753.469726, -2392.011230, 36.727172, 0.000022, 0.000007, 89.999900, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(970, 2753.469726, -2396.182373, 36.727172, 0.000022, 0.000007, 89.999900, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(970, 2753.469726, -2400.352294, 36.727172, 0.000022, 0.000007, 89.999900, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(970, 2753.469726, -2404.521972, 36.727172, 0.000022, 0.000007, 89.999900, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(970, 2753.469726, -2408.681640, 36.727172, 0.000022, 0.000007, 89.999900, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(970, 2753.469726, -2412.852539, 36.727172, 0.000022, 0.000007, 89.999900, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(970, 2753.469726, -2417.023925, 36.727172, 0.000022, 0.000007, 89.999900, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 6060, "shops2_law", "newall8-1blue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2748.957031, -2422.694091, 45.816406, 25.000000, -24.999984, -0.000003, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2747.801025, -2414.139648, 47.148071, 0.000005, -24.999958, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2747.801025, -2412.639648, 47.148071, 0.000005, -24.999958, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2767.945800, -2424.384765, 47.308227, 24.999998, 0.000044, -0.000014, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2769.445800, -2424.384765, 47.308227, 24.999998, 0.000044, -0.000014, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2777.945800, -2424.384765, 47.308227, 24.999998, 0.000044, -0.000014, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2779.445800, -2424.384765, 47.308227, 24.999998, 0.000044, -0.000014, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2747.801025, -2394.139648, 47.148071, 0.000005, -24.999958, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2747.801025, -2392.639648, 47.148071, 0.000005, -24.999958, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2757.945800, -2424.384765, 47.308227, 24.999998, 0.000044, -0.000014, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2759.445800, -2424.384765, 47.308227, 24.999998, 0.000044, -0.000014, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2747.801025, -2404.139648, 47.148071, 0.000005, -24.999958, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2747.801025, -2402.639648, 47.148071, 0.000005, -24.999958, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2782.889160, -2388.138671, 48.667358, 0.000027, 180.000015, 44.999931, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2788.886474, -2394.831054, 49.650634, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2788.886474, -2394.831054, 49.650634, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2777.305908, -2383.242675, 49.650634, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2750.936035, -2383.239990, 47.585571, 0.000000, 90.000015, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2788.886474, -2421.221923, 47.595092, 0.000014, 90.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2738.936035, -2383.239990, 34.640014, 0.000000, 0.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2788.886474, -2433.202636, 34.640014, 0.000000, 0.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2788.886474, -2396.229736, 33.733642, 0.000007, 90.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18763, 2751.452880, -2390.168212, 26.214477, 0.000015, 0.000015, 44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18763, 2751.658203, -2389.962890, 26.084350, 0.000015, 0.000015, 44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18763, 2751.898925, -2389.722167, 25.924194, 0.000015, 0.000015, 44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18763, 2752.139648, -2389.481445, 25.804077, 0.000015, 0.000015, 44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18763, 2784.967773, -2420.739013, 26.214477, 0.000034, 0.000009, 134.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18763, 2784.762451, -2420.534179, 26.084350, 0.000034, 0.000009, 134.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18763, 2784.521728, -2420.292968, 25.924194, 0.000034, 0.000009, 134.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18763, 2784.281250, -2420.052490, 25.804077, 0.000034, 0.000009, 134.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2785.247558, -2419.082519, 38.524658, 0.000000, 0.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2752.997314, -2386.901611, 38.524658, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2752.237304, -2391.865234, 27.623046, 0.000010, 0.000010, 44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2749.782714, -2389.410644, 27.623046, 0.000010, 0.000010, 44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2786.694824, -2419.932373, 27.623046, 0.000026, 0.000004, 134.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2784.249267, -2422.377929, 27.623046, 0.000026, 0.000004, 134.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2752.997314, -2390.401367, 38.524658, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2781.744628, -2419.082519, 38.524658, 0.000000, 0.000022, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 1975, "texttest", "kb_blue", 0x00000000);
    plbxts = CreateDynamicObject(19482, 2770.464599, -2383.752685, 42.169433, -0.000075, 0.000000, -89.999771, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "o", 130, "Webdings", 199, 1, 0xFF000000, 0x00000000, 1);
    plbxts = CreateDynamicObject(19482, 2770.464599, -2383.742675, 41.709594, -0.000075, 0.000000, -89.999771, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "m", 130, "Wingdings", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(19482, 2788.340820, -2401.649414, 42.169433, -0.000068, 0.000022, -179.999755, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "o", 130, "Webdings", 199, 1, 0xFF000000, 0x00000000, 1);
    plbxts = CreateDynamicObject(19482, 2788.350830, -2401.649414, 41.709594, -0.000068, 0.000022, -179.999755, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "m", 130, "Wingdings", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(18766, 2783.341308, -2388.697021, 45.583740, -0.000005, 0.000005, -44.999988, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(1508, 2749.890625, -2431.465332, 29.824707, 0.000005, 0.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14577, "casinovault01", "dts_elevator_door", 0x00000000);
    plbxts = CreateDynamicObject(1508, 2740.693115, -2422.267822, 29.824707, 0.000005, 0.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14577, "casinovault01", "dts_elevator_door", 0x00000000);
    plbxts = CreateDynamicObject(1508, 2745.300537, -2426.875244, 29.824707, 0.000005, 0.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14577, "casinovault01", "dts_elevator_door", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2748.426025, -2430.690673, 33.209472, 89.999992, 40.097309, -85.097282, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2741.412109, -2423.676757, 33.209472, 89.999992, 40.097309, -85.097282, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2748.426025, -2430.690673, 27.718383, 89.999992, 222.544189, -87.544143, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2741.368896, -2423.633544, 27.718383, 89.999992, 222.544189, -87.544143, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2755.491943, -2437.756591, 27.698364, 89.999992, 220.097305, -85.097282, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2734.302734, -2416.567382, 27.698364, 89.999992, 220.097305, -85.097282, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2755.490966, -2437.755615, 33.209472, 89.999992, 40.097309, -85.097282, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2734.348876, -2416.613525, 33.209472, 89.999992, 40.097309, -85.097282, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2749.966064, -2434.096191, 30.214233, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2738.044189, -2422.166992, 30.214233, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2752.018066, -2433.089843, 45.231811, -0.000010, 0.000010, -44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2739.042968, -2420.114746, 45.231811, -0.000010, 0.000010, -44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2748.681640, -2429.753417, 45.231811, -0.000010, 0.000010, -44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2742.408203, -2423.479980, 45.231811, -0.000010, 0.000010, -44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2745.562988, -2426.634765, 45.231811, -0.000010, 0.000010, -44.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2738.044189, -2388.744628, 30.214233, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2738.044189, -2388.744628, 35.213867, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2738.044189, -2388.744628, 40.203857, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2738.044189, -2388.744628, 45.203369, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2738.044189, -2388.744628, 50.203857, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2783.399414, -2434.096191, 30.214233, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2783.399414, -2434.096191, 35.214233, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2783.399414, -2434.096191, 40.193969, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2783.399414, -2434.096191, 45.194091, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2783.399414, -2434.096191, 50.193725, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 13603, "stad_tag", "Was_swr_wall_blue", 0x00000000);
    plbxts = CreateDynamicObject(2434, 2755.308837, -2406.678466, 28.020263, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2435, 2756.440185, -2406.845703, 28.024047, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2435, 2757.370361, -2406.845703, 28.024047, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2435, 2758.300781, -2406.845703, 28.024047, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2435, 2759.230712, -2406.845703, 28.024047, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2434, 2755.482177, -2401.845703, 28.020263, -0.000006, -0.000007, 179.999847, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2435, 2755.314941, -2402.977050, 28.024047, -0.000007, 0.000006, -90.000015, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2435, 2755.314941, -2403.907226, 28.024047, -0.000007, 0.000006, -90.000015, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2435, 2755.314941, -2404.837646, 28.024047, -0.000007, 0.000006, -90.000015, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2435, 2755.314941, -2405.767578, 28.024047, -0.000007, 0.000006, -90.000015, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2435, 2759.200683, -2401.843017, 28.024047, 0.000007, -0.000014, 179.999740, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2435, 2758.270507, -2401.843017, 28.024047, 0.000007, -0.000014, 179.999740, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2435, 2757.340087, -2401.843017, 28.024047, 0.000007, -0.000014, 179.999740, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2435, 2756.410156, -2401.843017, 28.024047, 0.000007, -0.000014, 179.999740, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2435, 2760.161621, -2406.845703, 28.024047, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2435, 2761.091796, -2406.845703, 28.024047, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2435, 2762.022216, -2406.845703, 28.024047, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2435, 2762.952148, -2406.845703, 28.024047, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(2595, 2755.347412, -2402.872314, 29.317138, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(2595, 2755.347412, -2405.913330, 29.317138, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(2595, 2755.347412, -2404.402099, 29.317138, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(19720, 2755.620361, -2404.360107, 33.474121, 0.000000, 83.499977, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 6869, "vegastemp1", "casinowall1_128", 0x00000000);
    plbxts = CreateDynamicObject(19786, 2754.355468, -2404.361816, 31.409790, 6.199991, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 6985, "vgnfremnt2", "striplightspinky_256", 0x00000000);
    plbxts = CreateDynamicObject(2662, 2754.184814, -2404.332519, 31.258789, 6.199989, 0.000007, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "ADA YANG BISA KAMI BANTU?", 130, "Times New Roman", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(2662, 2754.162597, -2404.332519, 31.467285, 6.199989, 0.000007, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "SELAMAT DATANG", 130, "Times New Roman", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(3533, 2753.472412, -2418.604980, 29.743652, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(3533, 2753.472412, -2384.709472, 29.743652, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(3533, 2787.422851, -2418.604980, 29.743652, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14623, "mafcasmain", "ab_tileStar", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(2611, 2759.036132, -2407.679199, 30.448730, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 3, 14608, "triad_big", "buddha_gold", 0x00000000);
    plbxts = CreateDynamicObject(2611, 2761.586669, -2407.679199, 30.448730, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 3, 14608, "triad_big", "buddha_gold", 0x00000000);
    plbxts = CreateDynamicObject(2484, 2760.337158, -2406.830566, 29.878784, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 18901, "matclothes", "bandanaflag", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 3, 14581, "ab_mafiasuitea", "cof_wood2", 0x00000000);
    plbxts = CreateDynamicObject(2164, 2763.401123, -2404.447265, 28.177490, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 18882, "hugebowls", "woodpanel1", 0x00000000);
    plbxts = CreateDynamicObject(2164, 2763.401123, -2402.686279, 28.177490, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 18882, "hugebowls", "woodpanel1", 0x00000000);
    plbxts = CreateDynamicObject(2162, 2760.721435, -2407.681152, 31.358520, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 18882, "hugebowls", "woodpanel1", 0x00000000);
    plbxts = CreateDynamicObject(948, 2763.171386, -2401.895263, 28.169677, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 11413, "fosterflowers", "starflower3", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(948, 2763.171386, -2406.058593, 28.169677, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 11413, "fosterflowers", "starflower3", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(2286, 2763.412353, -2403.944335, 31.880249, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(1806, 2757.544677, -2405.981201, 28.166503, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 18646, "matcolours", "grey-90-percent", 0x00000000);
    plbxts = CreateDynamicObject(1806, 2757.544677, -2402.729492, 28.166503, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 18646, "matcolours", "grey-90-percent", 0x00000000);
    plbxts = CreateDynamicObject(1806, 2757.544677, -2404.390380, 28.166503, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 18646, "matcolours", "grey-90-percent", 0x00000000);
    plbxts = CreateDynamicObject(2245, 2759.235839, -2401.867431, 29.298217, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(1723, 2772.396728, -2433.171386, 28.186889, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(1723, 2738.943847, -2401.771728, 28.186889, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(19482, 2781.539062, -2387.611328, 45.855102, -0.000010, -0.000010, -135.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "SELAMAT", 130, "Arial", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(19482, 2784.618896, -2390.691162, 45.855102, -0.000010, -0.000010, -135.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "DATANG", 130, "Arial", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(19482, 2782.020507, -2388.092773, 44.724121, -0.000010, -0.000010, -135.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "DI", 130, "Arial", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(19482, 2783.860839, -2389.933105, 44.724121, -0.000010, -0.000010, -135.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "LOS SANTOS", 130, "Arial", 60, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(1508, 2783.142333, -2388.902099, 37.871337, 0.000005, 0.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10023, "bigwhitesfe", "liftdoors_kb_256", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2782.650878, -2384.211425, 36.028808, 0.000005, 30.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2787.813476, -2389.374023, 36.028808, 0.000005, 30.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2782.002685, -2387.692138, 40.617309, 89.999992, 144.735610, -99.735618, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2784.335937, -2390.025390, 40.617309, 89.999992, 144.735610, -99.735618, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2782.961669, -2389.045166, 41.624633, 89.999992, -144.735610, -80.264381, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2782.961669, -2389.045166, 35.723876, 89.999992, -144.735610, -80.264381, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2779.433105, -2392.573730, 35.723876, 89.999992, -144.735610, -80.264381, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2775.897949, -2396.108886, 35.723876, 89.999992, -144.735610, -80.264381, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2772.368896, -2399.637939, 35.723876, 89.999992, -144.735610, -80.264381, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    plbxts = CreateDynamicObject(19482, 2754.251708, -2404.421875, 32.892578, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "LOKET", 130, "Arial", 100, 1, 0xFF000000, 0x00000000, 1);
    plbxts = CreateDynamicObject(18766, 2744.900390, -2420.102050, 27.718383, 89.999992, 222.544189, -87.544143, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2751.959716, -2427.161376, 27.718383, 89.999992, 222.544189, -87.544143, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    plbxts = CreateDynamicObject(19980, 2752.468017, -2414.146728, 35.747680, 0.000000, 179.999984, -90.000007, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 18646, "matcolours", "lightblue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 3, 18901, "matclothes", "darkblue", 0x00000000);
    plbxts = CreateDynamicObject(19980, 2752.478027, -2414.146728, 35.747680, 0.000000, -179.999984, 90.000007, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 18646, "matcolours", "lightblue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 3, 18901, "matclothes", "darkblue", 0x00000000);
    plbxts = CreateDynamicObject(19980, 2757.926757, -2419.617919, 35.747680, 0.000000, -179.999984, -0.000030, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 18646, "matcolours", "lightblue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 3, 18901, "matclothes", "darkblue", 0x00000000);
    plbxts = CreateDynamicObject(19980, 2757.926757, -2419.607910, 35.747680, 0.000000, 179.999984, 179.999984, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 18646, "matcolours", "lightblue", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 3, 18901, "matclothes", "darkblue", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2759.025878, -2434.227539, 27.698364, 89.999992, 220.097305, -85.097282, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2737.834228, -2413.035888, 27.698364, 89.999992, 220.097305, -85.097282, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14735, "newcrak", "carpet-tile", 0x00000000);
    plbxts = CreateDynamicObject(2714, 2752.414306, -2414.193115, 33.193725, -0.000029, 0.000000, -89.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "KEBERANGKATAN", 130, "Arial", 65, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(2714, 2752.414306, -2414.012939, 33.083618, -0.000029, 0.000000, -89.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "GATE DEPARTURE", 130, "Arial", 40, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(2714, 2752.414306, -2413.622558, 33.163696, -0.000029, 0.000000, -89.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "h", 130, "Wingdings 3", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(2714, 2757.964843, -2419.672851, 33.193725, -0.000029, 0.000045, 0.000061, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "KEBERANGKATAN", 130, "Arial", 65, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(2714, 2757.784667, -2419.672851, 33.083618, -0.000029, 0.000045, 0.000061, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "GATE DEPARTURE", 130, "Arial", 40, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(2714, 2757.394287, -2419.672851, 33.163696, -0.000029, 0.000045, 0.000061, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "h", 130, "Wingdings 3", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(18981, 2776.929199, -2383.223388, 23.702758, -0.000007, 180.000015, -90.000068, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2776.929199, -2383.223388, 23.702758, -0.000007, 180.000015, -90.000068, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2776.929199, -2391.442626, 23.702758, -0.000007, 180.000015, -90.000068, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2788.886474, -2384.228271, 24.690917, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2788.886474, -2392.428466, 24.690917, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2778.138183, -2391.216308, 23.702758, -0.000000, 180.000015, -0.000091, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    plbxts = CreateDynamicObject(1499, 2778.263183, -2406.741699, 28.187988, 0.000029, 0.000000, 89.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 19325, "lsmall_shops", "lsmall_window01", 0x00000000);
    plbxts = CreateDynamicObject(1499, 2778.243164, -2403.729003, 28.187988, -0.000029, 0.000000, -89.999908, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 19325, "lsmall_shops", "lsmall_window01", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2778.153076, -2402.770507, 33.180664, 0.000014, 180.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 19130, "matarrows", "green", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2778.141357, -2403.239501, 28.338256, 0.000000, 0.000029, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2778.141357, -2398.256347, 28.338256, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2778.141357, -2392.435302, 28.338256, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2778.141357, -2392.435302, 31.846923, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2778.093017, -2402.750488, 33.180664, 0.000014, 180.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 19130, "matarrows", "green", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2778.101318, -2403.239501, 28.338256, 0.000000, 0.000037, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2778.101318, -2398.256347, 28.338256, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2778.101318, -2392.435302, 28.338256, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    plbxts = CreateDynamicObject(18762, 2778.101318, -2392.435302, 31.846923, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    plbxts = CreateDynamicObject(2626, 2772.501464, -2405.921875, 28.694458, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 19092, "pompoms", "wood-64x64", 0x00000000);
    plbxts = CreateDynamicObject(19482, 2778.658691, -2402.793701, 32.413574, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "WALBERT", 130, "Arial", 80, 1, 0xFF000000, 0x00000000, 1);
    plbxts = CreateDynamicObject(18762, 2778.141357, -2407.223144, 28.338256, 0.000000, 0.000029, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 19267, "mapmarkers", "red-2", 0x00000000);
    plbxts = CreateDynamicObject(19482, 2777.587646, -2402.793701, 32.413574, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "WALBERT", 130, "Arial", 100, 1, 0xFF000000, 0x00000000, 1);
    plbxts = CreateDynamicObject(2599, 2779.102050, -2403.241455, 28.614746, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14636, "mafcas_signs", "sign_careful", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(2257, 2764.443603, -2404.386474, 31.807250, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 7088, "casinoshops1", "247sign1", 0x00000000);
    plbxts = CreateDynamicObject(2257, 2764.443603, -2399.805908, 31.807250, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 3467, "vegstreetsign", "streetsign2_256", 0x00000000);
    plbxts = CreateDynamicObject(2257, 2768.572021, -2391.972656, 31.807250, -0.000000, 0.000007, -0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 3083, "billbox", "Sprunk_postersign1", 0x00000000);
    plbxts = CreateDynamicObject(2257, 2772.922851, -2391.972656, 31.807250, -0.000000, 0.000007, -0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 3434, "skullsign", "vegasstripsign1_256", 0x00000000);
    plbxts = CreateDynamicObject(2784, 2761.933105, -2388.310546, 29.495239, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14652, "ab_trukstpa", "wood01", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 2, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 3, 14652, "ab_trukstpa", "wood01", 0x00000000);
    plbxts = CreateDynamicObject(2606, 2761.548095, -2388.284423, 30.621948, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 896, "underwater", "greyrockbig", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 16644, "a51_detailstuff", "a51_radardisp", 0x00000000);
    plbxts = CreateDynamicObject(1806, 2763.137451, -2388.277099, 28.166503, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14808, "lee_strip2", "strip_carpet2", 0x00000000);
    SetDynamicObjectMaterial(plbxts, 1, 18646, "matcolours", "grey-90-percent", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2790.937988, -2404.739257, 27.690185, 89.999992, 180.000000, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2795.931640, -2394.739013, 27.690185, 89.999992, 180.000015, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2795.931640, -2404.739257, 27.690185, 89.999992, 180.000015, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2800.916015, -2394.739013, 27.690185, 89.999992, 180.000030, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2800.916015, -2404.739257, 27.690185, 89.999992, 180.000030, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2801.941894, -2391.444091, 23.702758, -0.000007, 180.000015, -90.000068, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2801.891845, -2409.247558, 23.702758, -0.000007, 180.000015, -90.000068, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2805.915283, -2394.739013, 27.690185, 89.999992, 180.000015, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2805.915283, -2404.739257, 27.690185, 89.999992, 180.000015, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2810.908935, -2394.739013, 27.690185, 89.999992, 180.000030, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2810.908935, -2404.739257, 27.690185, 89.999992, 180.000030, -89.999961, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2800.929443, -2397.265625, 34.720458, 0.000000, 270.000000, 179.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2803.904052, -2407.741943, 23.702758, 0.000000, 180.000015, 179.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    plbxts = CreateDynamicObject(18981, 2792.917236, -2412.746826, 23.702758, 0.000000, 180.000015, 179.999862, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    plbxts = CreateDynamicObject(2626, 2808.432128, -2406.812744, 28.694458, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 19092, "pompoms", "wood-64x64", 0x00000000);
    plbxts = CreateDynamicObject(2626, 2770.501220, -2405.921875, 28.694458, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 19092, "pompoms", "wood-64x64", 0x00000000);
    plbxts = CreateDynamicObject(1723, 2796.417968, -2392.447509, 28.168212, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(1723, 2807.383544, -2392.447509, 28.168212, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(1723, 2791.909179, -2403.248535, 28.168212, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2792.909667, -2408.237060, 22.678955, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2803.901123, -2408.237060, 22.678955, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2803.901123, -2394.736083, 22.678955, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18980, 2792.909667, -2399.744628, 22.678955, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2788.868164, -2400.926757, 34.298583, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(19482, 2788.365966, -2400.732666, 33.049194, 0.000000, -0.000029, 179.999816, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "AVSTORE", 130, "Arial", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(19482, 2788.365966, -2400.732666, 32.528808, 0.000000, -0.000029, 179.999816, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "TERSEDIA BAJU DAN ELEKTRONIC HAPPY SHOPING", 130, "Arial", 20, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(2257, 2797.455078, -2391.972656, 31.807250, -0.000000, 0.000007, -0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 5406, "jeffers5a_lae", "lasuburbansgn1", 0x00000000);
    plbxts = CreateDynamicObject(2257, 2808.368408, -2391.972656, 31.807250, -0.000000, 0.000007, -0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 1, 1346, "roadside", "CJ_PHONESEXADD", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2798.143310, -2397.724853, 37.000244, 0.000000, 90.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(18766, 2808.396240, -2397.724853, 37.000244, 0.000000, 90.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(plbxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    plbxts = CreateDynamicObject(19482, 2798.124755, -2397.223632, 33.049194, 0.000007, -0.000021, 89.999855, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "CLOTHES SHOP", 130, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    plbxts = CreateDynamicObject(19482, 2808.378906, -2397.223632, 33.049194, 0.000007, -0.000021, 89.999855, 91, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(plbxts, 0, "ELECTRIC SHOP", 130, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(11710, 2745.253906, -2426.910400, 31.748413, -0.000005, 0.000005, -44.999988, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11710, 2740.645751, -2422.302246, 31.748413, -0.000005, 0.000005, -44.999988, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11710, 2749.840576, -2431.497070, 31.748413, -0.000005, 0.000005, -44.999988, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14629, 2743.527832, -2397.834960, 47.286743, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14629, 2758.636474, -2428.600097, 47.286743, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2749.199462, -2394.529052, 48.158691, 0.000000, 0.000029, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2760.199462, -2394.529052, 48.158691, 0.000000, 0.000029, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2760.199462, -2407.741210, 48.158691, 0.000000, 0.000029, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2749.199462, -2407.741210, 48.158691, 0.000000, 0.000029, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2770.696289, -2394.529052, 48.158691, 0.000000, 0.000037, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2770.696289, -2407.741210, 48.158691, 0.000000, 0.000037, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2760.199462, -2420.741210, 48.158691, 0.000000, 0.000029, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2781.259521, -2394.529052, 48.158691, 0.000000, 0.000045, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2781.259521, -2407.741210, 48.158691, 0.000000, 0.000045, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2770.696289, -2420.741210, 48.158691, 0.000000, 0.000037, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2781.259521, -2420.741210, 48.158691, 0.000000, 0.000045, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2749.199462, -2420.792968, 48.158691, 0.000000, 0.000029, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2750.724609, -2430.728515, 32.595214, -0.000021, 0.000021, -44.999984, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2746.132080, -2426.135986, 32.595214, -0.000021, 0.000021, -44.999984, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2741.515136, -2421.519042, 32.595214, -0.000021, 0.000021, -44.999984, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 2755.695800, -2402.892578, 29.094360, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 2755.695800, -2404.424072, 29.094360, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 2755.695800, -2405.935546, 29.094360, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2761.628906, -2393.399414, 34.173339, 0.000000, 270.000000, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2761.628906, -2403.381835, 34.173339, 0.000000, 270.000000, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2758.710693, -2411.727539, 34.173339, 0.000029, 270.000000, 89.999908, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2758.710693, -2410.727539, 34.173339, 0.000029, 270.000000, 89.999908, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2768.731933, -2410.727539, 34.173339, 0.000029, 270.000000, 89.999908, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2778.703613, -2410.727539, 34.173339, 0.000029, 270.000000, 89.999908, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2768.731933, -2411.727539, 34.173339, 0.000029, 270.000000, 89.999908, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2778.703613, -2411.727539, 34.173339, 0.000029, 270.000000, 89.999908, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2760.628906, -2393.399414, 34.173339, 0.000000, 270.000000, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1620, 2760.628906, -2403.381835, 34.173339, 0.000000, 270.000000, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2758.125000, -2393.383300, 33.949707, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2758.125000, -2404.264648, 33.949707, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2758.705566, -2414.406005, 33.949707, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2768.725830, -2414.406005, 33.949707, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2778.705810, -2414.406005, 33.949707, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2738.463378, -2401.738525, 34.020507, 0.000014, 0.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2738.463378, -2399.718505, 34.020507, 0.000014, 0.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2738.463378, -2399.718505, 36.120971, 0.000014, 0.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2738.463378, -2401.739746, 36.120971, 0.000014, 0.000000, 89.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2738.463378, -2401.738525, 38.221923, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2738.463378, -2399.718505, 38.221923, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2738.463378, -2399.718505, 40.322387, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2738.463378, -2401.739746, 40.322387, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2772.429687, -2433.671386, 34.020507, 0.000030, -0.000022, 179.999740, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2770.409667, -2433.671386, 34.020507, 0.000030, -0.000022, 179.999740, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2770.409667, -2433.671386, 36.120971, 0.000030, -0.000022, 179.999740, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2772.430908, -2433.671386, 36.120971, 0.000030, -0.000022, 179.999740, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2772.429687, -2433.671386, 38.221923, 0.000038, -0.000022, 179.999725, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2770.409667, -2433.671386, 38.221923, 0.000038, -0.000022, 179.999725, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2770.409667, -2433.671386, 40.322387, 0.000038, -0.000022, 179.999725, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 2772.430908, -2433.671386, 40.322387, 0.000038, -0.000022, 179.999725, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14805, 2773.925781, -2415.182373, 37.027343, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14805, 2763.515869, -2415.182373, 37.027343, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14805, 2756.903076, -2407.652587, 37.027343, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14805, 2756.903076, -2407.652587, 37.027343, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(14805, 2756.903076, -2398.310546, 37.027343, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2776.617675, -2410.673339, 36.732788, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2762.625732, -2410.673339, 36.732788, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2764.964599, -2410.673339, 36.732788, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2767.294189, -2410.673339, 36.732788, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2769.623291, -2410.673339, 36.732788, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2771.953613, -2410.673339, 36.732788, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2774.283203, -2410.673339, 36.732788, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2761.444580, -2409.333496, 36.732788, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2761.444580, -2406.994140, 36.732788, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2761.444580, -2404.664550, 36.732788, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2761.444580, -2402.334228, 36.732788, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2761.444580, -2400.004882, 36.732788, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2761.444580, -2397.675292, 36.732788, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2761.444580, -2395.295898, 36.732788, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2766.105957, -2384.113037, 36.901611, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2775.077636, -2384.113037, 36.901611, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2788.019042, -2397.013671, 36.901611, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2788.019042, -2405.935058, 36.901611, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2738.765380, -2414.502197, 28.861572, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2738.765380, -2409.021728, 28.861572, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2757.674804, -2433.327148, 28.861572, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2763.156250, -2433.327148, 28.861572, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2783.429931, -2433.327148, 28.861572, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2738.765380, -2388.750000, 28.861572, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2767.578857, -2409.103027, 28.861572, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2772.948242, -2409.103027, 28.861572, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2778.319091, -2409.103027, 28.861572, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(628, 2770.619628, -2384.366943, 38.182128, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(628, 2787.751464, -2401.447509, 38.182128, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(628, 2753.501464, -2432.968261, 30.176391, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(628, 2739.178466, -2418.617431, 30.176391, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2752.090820, -2397.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2752.090820, -2402.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2752.090820, -2407.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2751.271972, -2407.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2751.271972, -2402.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2751.271972, -2397.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2748.090820, -2397.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2748.090820, -2402.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2748.090820, -2407.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2747.271972, -2407.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2747.271972, -2402.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2747.271972, -2397.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2744.090820, -2397.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2744.090820, -2402.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2744.090820, -2407.571533, 28.682128, 0.000022, 0.000000, 89.999931, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2743.271972, -2407.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2743.271972, -2402.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2743.271972, -2397.571533, 28.682128, -0.000007, 0.000000, -89.999946, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2765.253173, -2420.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2770.253173, -2420.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2775.253173, -2420.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2775.253173, -2420.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2770.253173, -2420.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2765.253173, -2420.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2765.253173, -2424.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2770.253173, -2424.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2775.253173, -2424.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2775.253173, -2424.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2770.253173, -2424.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2765.253173, -2424.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2765.253173, -2428.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2770.253173, -2428.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2775.253173, -2428.023681, 28.682128, 0.000022, -0.000037, 179.999679, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2775.253173, -2428.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2770.253173, -2428.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2765.253173, -2428.842529, 28.682128, -0.000006, 0.000037, 0.000021, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11710, 2783.069091, -2388.966308, 39.838378, -0.000005, 0.000005, -44.999988, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2781.955810, -2390.079101, 40.820922, 0.000005, 0.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2781.127929, -2389.251220, 40.820922, 0.000005, 0.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19806, 2782.789550, -2390.912841, 40.820922, 0.000005, 0.000005, 44.999988, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2375, 2801.471923, -2408.713867, 28.119873, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2375, 2801.471923, -2408.713867, 30.579956, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2447, 2769.011718, -2405.771484, 28.169555, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1890, 2769.338867, -2401.985351, 28.160034, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1890, 2769.338867, -2398.025634, 28.160034, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1890, 2769.338867, -2393.973388, 28.160034, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1890, 2772.360351, -2401.985351, 28.160034, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1890, 2772.360351, -2398.025634, 28.160034, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1890, 2772.360351, -2393.973388, 28.160034, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2578, 2773.488525, -2407.617919, 30.943237, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2578, 2772.448242, -2407.617919, 30.943237, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2578, 2771.408447, -2407.617919, 30.943237, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2578, 2770.368408, -2407.617919, 30.943237, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2578, 2769.328613, -2407.617919, 30.943237, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2387, 2800.145019, -2404.387939, 28.184692, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1885, 2774.223876, -2405.802001, 28.179931, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2779, 2765.852783, -2407.204345, 28.176391, 0.000000, -0.000014, 179.999908, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2779, 2766.703613, -2407.204345, 28.176391, 0.000000, -0.000014, 179.999908, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2452, 2764.810791, -2393.073730, 28.097412, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2452, 2764.810791, -2394.655029, 28.097412, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2770.983886, -2399.899658, 34.275390, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2384.823974, 29.159545, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2385.323974, 29.159545, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2385.823974, 29.159545, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2386.323974, 29.159545, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2386.823974, 29.159545, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2387.323974, 29.159545, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2387.823974, 29.159545, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2388.474121, 29.159545, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2389.474121, 29.159545, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2390.474121, 29.159545, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 2761.952880, -2391.474121, 29.159545, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2760.728759, -2391.884033, 28.705322, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2760.728759, -2384.893310, 28.705322, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2760.728759, -2387.223388, 28.705322, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 2760.728759, -2389.553955, 28.705322, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(922, 2762.958496, -2396.892578, 29.051025, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(922, 2762.038330, -2396.892578, 29.051025, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(922, 2783.482177, -2392.420654, 29.051025, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2783.614257, -2399.899658, 34.275390, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2778.839111, -2400.053955, 28.872802, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2778.839111, -2394.693847, 28.872802, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2626, 2794.953125, -2406.082031, 28.694458, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2387, 2800.145019, -2406.239257, 28.184692, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2622, 2796.351806, -2406.139160, 28.947875, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2625, 2796.030517, -2408.315185, 29.700561, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19786, 2808.422851, -2408.757568, 32.581787, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19787, 2808.411865, -2408.762207, 31.217285, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2229, 2809.800048, -2407.199707, 28.170898, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2229, 2806.919189, -2407.199707, 28.170898, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2229, 2806.419189, -2407.199707, 28.170898, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2229, 2809.300048, -2407.199707, 28.170898, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19422, 2807.853027, -2406.830078, 29.228271, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19422, 2807.853027, -2406.619873, 29.228271, 0.000000, -0.000007, 179.999954, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18868, 2807.533447, -2406.675781, 29.203979, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18868, 2807.533447, -2406.865966, 29.203979, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18868, 2807.623535, -2406.865966, 29.203979, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18868, 2807.623535, -2406.675781, 29.203979, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19893, 2808.411865, -2406.745849, 29.219116, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1850, 2805.280273, -2403.833496, 28.120239, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1850, 2805.280273, -2400.833007, 28.120239, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1850, 2805.280273, -2397.842285, 28.120239, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1850, 2811.526123, -2398.342773, 28.120239, -0.000014, 0.000000, -89.999923, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1850, 2811.526123, -2401.343261, 28.120239, -0.000014, 0.000000, -89.999923, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1850, 2811.526123, -2404.333984, 28.120239, -0.000014, 0.000000, -89.999923, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1884, 2808.459716, -2402.217285, 28.140258, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1884, 2808.459716, -2398.125488, 28.140258, -0.000007, -0.000000, -89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2652, 2794.172851, -2406.086669, 29.711059, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2371, 2796.117919, -2402.338623, 28.159667, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2371, 2800.930908, -2402.338623, 28.159667, 0.000007, 0.000000, 89.999977, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2394, 2795.470458, -2401.960449, 28.891845, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2394, 2795.470458, -2402.621093, 28.891845, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2394, 2800.312011, -2402.621093, 28.891845, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(626, 2802.866455, -2395.749267, 30.181518, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(626, 2791.872802, -2400.759765, 30.181518, 0.000000, 0.000007, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2783.614257, -2399.899658, 34.275390, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2798.289550, -2399.899658, 34.275390, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(18075, 2808.300292, -2399.899658, 34.275390, 0.000000, 0.000014, 0.000000, 91, 1, -1, 200.00, 200.00);
}