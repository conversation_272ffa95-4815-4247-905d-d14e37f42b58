RemoveLumberjackBuilding(playerid)
{
    RemoveBuildingForPlayer(playerid, 18566, -2029.550, -2389.040, 30.187, 0.250);
    RemoveBuildingForPlayer(playerid, 18569, -2048.429, -2373.610, 30.710, 0.250);
    RemoveBuildingForPlayer(playerid, 18568, -2061.189, -2365.949, 30.328, 0.250);
    RemoveBuildingForPlayer(playerid, 18567, -2018.250, -2400.540, 30.578, 0.250);
}

CreateLumberjackExt()
{
    CreateDynamicObject(18609, -2058.038574, -2356.419677, 30.692094, 0.000000, 0.000000, 50.099983, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18609, -2053.319091, -2360.365478, 30.702095, 0.000000, 0.000000, 50.099983, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(13206, -2026.790039, -2391.563476, 29.536582, 0.000000, 0.000000, 135.499954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18609, -2050.171386, -2334.163574, 30.692090, 0.300000, 0.000000, 140.099975, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18609, -2055.258056, -2340.246826, 30.703609, 0.300000, 0.000000, 140.099975, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(13206, -2045.546997, -2329.757324, 29.546575, 0.000000, 0.000000, 135.499954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1463, -2042.750976, -2328.739990, 29.925067, 0.000000, 0.000000, -44.000011, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(941, -2044.963867, -2325.576416, 30.063026, 0.000000, 0.000000, -44.199996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(941, -2047.358764, -2323.248046, 30.063026, 0.000000, 0.000000, -44.199996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19468, -2044.146606, -2331.840087, 29.702947, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3403, -2053.934814, -2391.245849, 32.574169, 0.000000, 0.000000, 46.499992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3403, -2042.488525, -2402.107421, 32.654170, 0.000000, 0.000000, 46.499992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18609, -2052.074707, -2390.993408, 30.692092, 0.000000, 0.000000, 140.099975, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2991, -2056.884765, -2391.142822, 30.241651, 0.000000, 0.000000, 47.699977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3260, -2055.027343, -2389.156250, 30.326572, -52.800006, 0.000000, 137.299987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2991, -2045.064697, -2404.616210, 30.251651, 0.000000, 0.000000, 137.699981, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(684, -2038.409057, -2405.468994, 29.949022, 0.000000, 0.000000, -41.699974, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1456, -2044.167846, -2403.515136, 30.306114, -35.100006, 0.000000, 136.399963, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(937, -2042.198120, -2397.214355, 30.098608, 0.000000, 0.000000, 46.800003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3077, -2044.526245, -2399.406738, 29.635410, 0.000000, 0.000000, 45.300003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3077, -2049.480468, -2391.719238, 29.615409, 0.000000, 0.000000, 45.300003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(941, -2028.719604, -2384.801269, 30.103496, 0.000000, 0.000000, -46.099990, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(941, -2026.050170, -2387.574707, 30.103496, 0.000000, 0.000000, -46.099990, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(341, -2029.402465, -2384.430908, 30.647468, 90.999977, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(341, -2025.428344, -2388.712402, 30.572757, 90.999977, 98.799987, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1431, -2023.687866, -2389.784423, 30.180326, 0.000000, 0.000000, -48.599987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1463, -2021.980224, -2392.092041, 29.964168, 0.000000, 0.000000, 42.400001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1463, -2022.644287, -2394.433349, 29.964168, 0.000000, 0.000000, 42.400001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1368, -2031.096435, -2380.218505, 30.327629, -0.599999, 0.000000, -44.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -2020.881591, -2393.258056, 30.095010, 0.000000, 0.000000, -47.400012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(13206, -1991.705078, -2425.111816, 29.536582, 0.000000, 0.000000, 135.499954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(941, -1993.522705, -2418.418945, 30.093475, 0.000000, 0.000000, -46.099990, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(941, -1986.567260, -2425.646484, 30.093475, 0.000000, 0.000000, -46.099990, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1431, -1990.681030, -2420.889648, 30.150325, 0.000000, 0.000000, -46.399990, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1463, -1988.571777, -2423.645996, 29.944168, 0.000000, 0.000000, 133.699981, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2991, -1983.401733, -2428.949707, 30.221651, 0.000000, 0.000000, 135.299972, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1456, -1979.414550, -2431.497070, 30.123197, -127.400085, 0.000000, 136.399963, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1368, -2005.430541, -2405.420898, 30.296163, -0.599999, 0.000000, -44.700012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1368, -1965.411499, -2430.789794, 30.300165, -0.599999, 0.000000, -44.700012, 0, 0, -1, 200.00, 200.00); 
}