RemoveFortCarsonBuilding(playerid)
{
    //electronic FC
    RemoveBuildingForPlayer(playerid, 16064, -161.171, 1179.530, 22.492, 0.250);
    RemoveBuildingForPlayer(playerid, 16443, -161.171, 1179.530, 22.492, 0.250);
    RemoveBuildingForPlayer(playerid, 1345, -162.093, 1175.140, 19.539, 0.250);
    RemoveBuildingForPlayer(playerid, 1345, -149.820, 1164.109, 19.539, 0.250);
    RemoveBuildingForPlayer(playerid, 1345, -170.171, 1169.050, 19.539, 0.250);
    RemoveBuildingForPlayer(playerid, 1692, -174.242, 1177.900, 22.781, 0.250);
}

CreateFortCarsonExt()
{
    new STREAMER_TAG_OBJECT:FORTCARSONTXTA;
    //taman fc
    FORTCARSONTXTA = CreateDynamicObject(18765, -142.890960, 1163.600463, 16.285203, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 10765, "airportgnd_sfse", "desgreengrass", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18765, -142.890960, 1172.429199, 16.255203, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 10765, "airportgnd_sfse", "desgreengrass", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18766, -142.877700, 1159.157104, 18.837217, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 9515, "bigboxtemp1", "shingles1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -139.112228, 1167.745727, 16.310804, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -139.112228, 1169.595947, 16.310804, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -141.242233, 1169.595947, 16.310804, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18766, -148.327713, 1172.427246, 18.837217, 0.000000, 180.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 9515, "bigboxtemp1", "shingles1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18766, -148.367721, 1163.676025, 18.887182, 180.000000, 540.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 9515, "bigboxtemp1", "shingles1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -141.242233, 1167.735717, 16.310804, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -143.082183, 1167.745727, 16.310804, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -143.082183, 1169.595947, 16.310804, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -145.212188, 1169.595947, 16.310804, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -145.212188, 1167.735717, 16.310804, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -143.082183, 1163.565429, 16.310804, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -143.082183, 1165.415649, 16.310804, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -145.212188, 1165.415649, 16.310804, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -145.212188, 1163.555419, 16.310804, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -143.082183, 1160.284545, 16.310804, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -143.082183, 1162.134765, 16.310804, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -145.212188, 1162.134765, 16.310804, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -145.212188, 1160.274536, 16.310804, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -143.082183, 1171.165405, 16.310804, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -143.082183, 1173.015625, 16.310804, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -145.212188, 1173.015625, 16.310804, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -145.212188, 1171.155395, 16.310804, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -143.082183, 1174.605957, 16.310804, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -143.082183, 1176.456176, 16.310804, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -145.212188, 1176.456176, 16.310804, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -145.212188, 1174.595947, 16.310804, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(1280, -140.403915, 1160.701171, 19.146980, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(1280, -140.403915, 1163.061279, 19.146980, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(1280, -140.403915, 1166.482421, 19.146980, -0.000007, 0.000007, 89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(1280, -140.403915, 1164.122314, 19.146980, 0.000007, -0.000007, -90.000038, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(1280, -140.403915, 1170.571289, 19.146980, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(1280, -140.403915, 1172.931396, 19.146980, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(1280, -140.403915, 1176.352539, 19.146980, 0.000000, 0.000007, 89.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(1280, -140.403915, 1173.992431, 19.146980, 0.000000, -0.000007, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(2321, -141.136306, 1161.820800, 18.603088, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 8534, "tikimotel", "sa_wood03_128", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(2321, -141.136306, 1165.330688, 18.603088, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 8534, "tikimotel", "sa_wood03_128", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(2321, -141.136306, 1171.680908, 18.603088, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 8534, "tikimotel", "sa_wood03_128", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(2321, -141.136306, 1175.160766, 18.603088, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 8534, "tikimotel", "sa_wood03_128", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(638, -138.290298, 1160.387207, 19.339984, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(638, -138.290298, 1163.016723, 19.339984, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(638, -138.290298, 1165.606689, 19.339984, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(638, -138.290298, 1171.708007, 19.339984, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(638, -138.290298, 1174.337524, 19.339984, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(638, -138.290298, 1176.987548, 19.339984, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(1215, -138.270278, 1167.083129, 19.461515, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 14385, "trailerkb", "tr_wall2", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(1215, -138.270278, 1170.163696, 19.461515, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 14385, "trailerkb", "tr_wall2", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(1215, -141.990264, 1163.613159, 19.461515, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 14385, "trailerkb", "tr_wall2", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(1215, -141.990264, 1173.543212, 19.461515, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 14385, "trailerkb", "tr_wall2", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(2257, -140.360595, 1159.671875, 20.145376, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 3603, "bevmans01_la", "pavepat2_128", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(2270, -141.622390, 1160.159667, 20.380640, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(2270, -141.622390, 1160.159667, 19.260639, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(2270, -139.072341, 1160.159667, 19.260639, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(2270, -139.072372, 1160.159667, 20.380640, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -146.504852, 1162.147338, 19.164167, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 9515, "bigboxtemp1", "shingles1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(19428, -147.804595, 1162.639160, 19.584476, 0.000000, 270.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(19428, -147.804595, 1166.139892, 19.584476, 0.000000, 270.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(19428, -147.804595, 1169.639770, 19.584476, 0.000000, 270.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(19428, -147.804595, 1173.149536, 19.584476, 0.000000, 270.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(19428, -147.804595, 1176.648803, 19.584476, 0.000000, 270.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(19428, -147.804595, 1160.399414, 19.574476, 0.000000, 270.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 13691, "bevcunto2_lahills", "crazypave", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -146.504852, 1167.156860, 19.164167, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 9515, "bigboxtemp1", "shingles1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -146.504852, 1172.156738, 19.164167, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 9515, "bigboxtemp1", "shingles1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -146.504852, 1175.146484, 17.174148, 180.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 9515, "bigboxtemp1", "shingles1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -146.504852, 1176.126708, 17.174148, 180.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 9515, "bigboxtemp1", "shingles1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(18762, -146.504852, 1177.126708, 17.174148, 180.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 9515, "bigboxtemp1", "shingles1", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(638, -147.439315, 1160.917236, 19.935983, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 4830, "airport2", "bevflower2", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(638, -147.439315, 1163.616943, 19.935983, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 4830, "airport2", "bevflower2", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(638, -147.439315, 1166.217163, 19.935983, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 4830, "airport2", "bevflower2", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(638, -147.439315, 1168.957397, 19.935983, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 4830, "airport2", "bevflower2", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(638, -147.439315, 1171.808105, 19.935983, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 4830, "airport2", "bevflower2", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(638, -147.439315, 1174.858154, 19.935983, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 1, 4830, "airport2", "bevflower2", 0x00000000);
    FORTCARSONTXTA = CreateDynamicObject(19482, -145.108749, 1177.378417, 20.938787, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(FORTCARSONTXTA, 0, "0", 130, "Arial", 199, 0, 0xFF161717, 0x00000000, 1);
    FORTCARSONTXTA = CreateDynamicObject(19482, -143.388748, 1177.378417, 20.938787, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(FORTCARSONTXTA, 0, "0", 130, "Arial", 199, 0, 0xFF161717, 0x00000000, 1);
    FORTCARSONTXTA = CreateDynamicObject(19482, -144.238723, 1177.378417, 20.378784, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(FORTCARSONTXTA, 0, "0", 130, "Arial", 199, 0, 0xFF161717, 0x00000000, 1);
    FORTCARSONTXTA = CreateDynamicObject(19482, -143.388748, 1177.378417, 21.598802, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(FORTCARSONTXTA, 0, ".", 130, "Arial", 199, 0, 0xFF161717, 0x00000000, 1);
    FORTCARSONTXTA = CreateDynamicObject(19482, -143.388748, 1177.378417, 21.448801, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(FORTCARSONTXTA, 0, ".", 130, "Arial", 199, 0, 0xFF161717, 0x00000000, 1);
    FORTCARSONTXTA = CreateDynamicObject(19482, -145.128768, 1177.378417, 21.448801, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(FORTCARSONTXTA, 0, ".", 130, "Arial", 199, 0, 0xFF161717, 0x00000000, 1);
    FORTCARSONTXTA = CreateDynamicObject(19482, -145.128768, 1177.378417, 21.598804, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(FORTCARSONTXTA, 0, ".", 130, "Arial", 199, 0, 0xFF161717, 0x00000000, 1);
    FORTCARSONTXTA = CreateDynamicObject(19482, -140.808700, 1177.378417, 20.378784, 180.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(FORTCARSONTXTA, 0, "have you ever been in love?", 130, "Georgia", 30, 0, 0xFF161717, 0x00000000, 1);
    FORTCARSONTXTA = CreateDynamicObject(19482, -140.808700, 1177.388427, 20.378784, 180.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(FORTCARSONTXTA, 0, "have you ever been in love?", 130, "Georgia", 30, 0, 0xFF161717, 0x00000000, 1);
    FORTCARSONTXTA = CreateDynamicObject(19482, -140.808700, 1177.398437, 20.378784, 180.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(FORTCARSONTXTA, 0, "have you ever been in love?", 130, "Georgia", 30, 0, 0xFF161717, 0x00000000, 1);
    FORTCARSONTXTA = CreateDynamicObject(19482, -140.808700, 1177.408447, 20.378784, 180.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(FORTCARSONTXTA, 0, "have you ever been in love?", 130, "Georgia", 30, 0, 0xFF161717, 0x00000000, 1);
    FORTCARSONTXTA = CreateDynamicObject(19482, -140.808700, 1177.398437, 20.378784, 180.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(FORTCARSONTXTA, 0, "have you ever been in love?", 130, "Georgia", 30, 0, 0xFF161717, 0x00000000, 1);
    FORTCARSONTXTA = CreateDynamicObject(19482, -140.808700, 1177.388427, 20.378784, 180.000000, 180.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(FORTCARSONTXTA, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(FORTCARSONTXTA, 0, "have you ever been in love?", 130, "Georgia", 30, 0, 0xFF161717, 0x00000000, 1);
}