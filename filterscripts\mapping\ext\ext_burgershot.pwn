RemoveBurgershotBuilding(playerid)
{
    //PDC
    RemoveBuildingForPlayer(playerid, 2631, 654.351, -1869.570, 4.500, 0.250);
    RemoveBuildingForPlayer(playerid, 2629, 653.929, -1864.085, 4.445, 0.250);
    RemoveBuildingForPlayer(playerid, 2630, 659.273, -1864.203, 4.445, 0.250);
    RemoveBuildingForPlayer(playerid, 1255, 658.281, -1879.468, 3.773, 0.250);
    RemoveBuildingForPlayer(playerid, 2632, 659.992, -1869.632, 4.500, 0.250);
    RemoveBuildingForPlayer(playerid, 1255, 660.351, -1879.468, 3.773, 0.250);
    RemoveBuildingForPlayer(playerid, 1255, 672.695, -1879.468, 3.773, 0.250);
    RemoveBuildingForPlayer(playerid, 6066, 673.179, -1867.195, 6.734, 0.250);
    RemoveBuildingForPlayer(playerid, 1255, 674.765, -1879.468, 3.773, 0.250);
    RemoveBuildingForPlayer(playerid, 621, 652.234, -1828.898, 3.515, 0.250);
    RemoveBuildingForPlayer(playerid, 647, 654.125, -1830.546, 6.593, 0.250);
    RemoveBuildingForPlayer(playerid, 647, 658.296, -1829.468, 6.414, 0.250);
    RemoveBuildingForPlayer(playerid, 712, 657.625, -1834.695, 13.992, 0.250);
    RemoveBuildingForPlayer(playerid, 1281, 660.273, -1840.687, 5.820, 0.250);
    RemoveBuildingForPlayer(playerid, 1281, 663.101, -1834.937, 5.820, 0.250);
    RemoveBuildingForPlayer(playerid, 1231, 648.242, -1874.640, 7.156, 0.250);

    //+arivena
    RemoveBuildingForPlayer(playerid, 16143, -324.148, 1302.229, 52.664, 0.250);
    RemoveBuildingForPlayer(playerid, 16751, -324.148, 1302.229, 52.664, 0.250);
    RemoveBuildingForPlayer(playerid, 16144, -324.148, 1302.229, 52.664, 0.250);
    RemoveBuildingForPlayer(playerid, 16751, -324.148, 1302.229, 52.664, 0.250);
}

CreateBurgershotExt()
{
    new STREAMER_TAG_OBJECT: brgstxt;

    //texas
    brgstxt = CreateDynamicObject(18762, 2698.167480, 743.798034, 12.203192, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2698.167480, 741.798034, 15.203188, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2698.167480, 736.797790, 15.203188, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2697.605468, 744.100036, 12.263191, 89.999992, 173.225997, -83.225967, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2697.605468, 744.100036, 13.104192, 89.999992, 173.225997, -83.225967, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2698.355712, 741.510192, 15.503192, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2698.355712, 736.930114, 15.503192, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2700.166992, 733.837585, 15.203188, 89.999992, -6.774020, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2698.355712, 735.779479, 15.503192, 0.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2700.135009, 734.028503, 15.503192, 0.000022, -89.999984, 89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2705.166992, 733.837585, 15.203188, 89.999992, -6.774020, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2704.995117, 734.029479, 15.503192, 0.000022, -89.999984, 89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 2709.982666, 733.526550, 16.213224, 0.000029, 89.999977, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 2709.982666, 734.147155, 16.213224, 0.000029, 89.999977, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2697.857421, 743.647155, 9.833188, 89.999992, 263.225921, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2700.166992, 733.837585, 9.833187, 89.999992, -6.774045, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2705.046875, 733.837585, 9.833187, 89.999992, -6.774045, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2697.857421, 735.837585, 9.833188, 89.999992, 263.225921, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    brgstxt = CreateDynamicObject(14394, 2697.270996, 739.874938, 9.493190, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(970, 2697.925537, 743.304138, 10.833201, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(970, 2697.925537, 736.103454, 10.833201, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(970, 2700.005859, 734.023620, 10.833201, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(970, 2704.175781, 734.023620, 10.833201, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(970, 2708.326171, 734.023620, 10.833201, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2698.387695, 744.568298, 12.333188, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2698.387695, 744.968688, 12.333188, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2697.857421, 746.497741, 9.833188, 89.999992, 263.225921, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    brgstxt = CreateDynamicObject(975, 2698.140136, 747.156188, 9.523199, 0.000022, 270.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 13694, "lahillstxd1a", "glasswindow2_256", 0xFFFFFFFF);
    SetDynamicObjectMaterial(brgstxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(975, 2698.140136, 745.595886, 9.523199, 0.000022, 270.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(brgstxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(975, 2698.140136, 745.595886, 8.993210, 0.000022, 270.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(brgstxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(975, 2698.140136, 745.595886, 8.283211, 0.000022, 270.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(brgstxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(975, 2698.140136, 747.156188, 5.953193, 0.000022, 270.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(brgstxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 2698.187988, 748.769958, 14.463202, -0.000022, 179.999984, -89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(14394, 2697.270996, 752.934753, 9.493190, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2698.185058, 750.379577, 12.243172, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10763, "airport1_sfse", "ws_airportdoors1", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2698.185058, 750.379577, 8.743172, 0.000000, 179.999984, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10763, "airport1_sfse", "ws_airportdoors1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 2698.187988, 751.980407, 14.463202, -0.000022, 179.999984, -89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(975, 2698.140136, 753.608093, 9.523199, -0.000022, 270.000000, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 13694, "lahillstxd1a", "glasswindow2_256", 0xFFFFFFFF);
    SetDynamicObjectMaterial(brgstxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(975, 2698.140136, 755.168395, 9.523199, -0.000022, 270.000000, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(brgstxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(975, 2698.140136, 755.168395, 8.993210, -0.000022, 270.000000, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(brgstxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(975, 2698.140136, 755.168395, 8.283211, -0.000022, 270.000000, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(brgstxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(975, 2698.140136, 753.608093, 5.953193, -0.000022, 270.000000, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(brgstxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2698.387695, 755.778503, 12.333188, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2698.387695, 756.459167, 12.333188, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2697.857421, 754.237976, 9.833188, 89.999992, 263.225921, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2697.857421, 754.659362, 9.833188, 89.999992, 263.225921, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-50-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2698.387695, 746.748229, 14.453182, 0.000022, 90.000015, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2698.387695, 751.707458, 14.453182, 0.000022, 90.000015, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2698.387695, 754.458679, 14.453182, 0.000022, 90.000015, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(2707, 2702.009765, 752.858581, 11.253190, -0.000022, 112.599952, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x90FFFFFF);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2697.827148, 746.788269, 15.013179, 0.000022, 90.000015, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2697.827148, 751.767517, 15.013179, 0.000022, 90.000015, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2697.827148, 755.007507, 15.013179, 0.000022, 90.000015, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2700.828613, 757.007995, 15.013179, 0.000007, 89.999984, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2705.828613, 757.007995, 15.013179, 0.000007, 89.999984, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2710.828613, 757.007995, 15.013179, 0.000007, 89.999984, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2715.828613, 757.007995, 15.013179, 0.000007, 89.999984, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 2722.817871, 756.459167, 12.333188, 0.000007, -0.000014, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 2727.318359, 750.979919, 12.333188, -0.000022, 0.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 2727.318359, 741.150329, 12.333188, -0.000022, 0.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 2703.857421, 756.459167, 12.333188, 0.000007, -0.000014, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 2712.817871, 756.459167, 12.333188, 0.000007, -0.000014, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 2727.318359, 738.539489, 12.333188, -0.000022, 0.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 2716.429199, 734.039978, 12.333188, -0.000007, 0.000029, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2698.042968, 739.495056, 17.422281, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "brnstucco1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2698.041992, 738.444274, 17.422281, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "brnstucco1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2702.922363, 733.716735, 17.422281, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "brnstucco1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 2716.260742, 734.758239, 10.719136, -0.000022, 179.999984, -89.999763, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 2716.251953, 734.758239, 10.719136, 0.000022, 0.000036, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 2721.838867, 734.039978, 12.333188, -0.000007, 0.000029, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2702.943359, 744.223571, 17.422281, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "brnstucco1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2712.573242, 744.223571, 17.422281, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "brnstucco1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2722.193115, 744.224548, 17.423278, -0.000022, -0.000014, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "brnstucco1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 2716.260742, 737.968200, 10.719136, -0.000022, 179.999984, -89.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 2716.251953, 737.968200, 10.719136, 0.000022, 0.000044, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2716.264892, 736.371765, 14.729139, 0.000020, -0.000022, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10755, "airportrminl_sfse", "ws_airportwin2", 0x00000000);
    brgstxt = CreateDynamicObject(19426, 2711.254394, 737.977478, 12.049118, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, 2708.532714, 737.977478, 12.049118, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2698.403320, 749.124938, 16.572267, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2698.403320, 751.676208, 16.572267, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2703.132812, 756.427185, 16.572267, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2712.552734, 756.427185, 16.572267, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2711.503173, 748.717956, 12.049127, 0.000007, -0.000014, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19325, 2710.008300, 737.980407, 10.369134, 89.999992, 173.225997, -83.225967, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    brgstxt = CreateDynamicObject(19866, 2710.221679, 737.976989, 10.319142, 0.000022, 0.000014, 89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2713.663085, 737.978210, 10.320141, 0.000022, 0.000014, 89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2712.535156, 737.976501, 14.759119, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2700.081787, 739.152282, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2703.581787, 739.152282, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2707.081787, 739.152282, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2710.581787, 739.152282, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2714.081787, 739.152282, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2715.132324, 739.152282, 10.232197, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2709.017578, 744.429626, 15.213191, 89.999992, -6.773986, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2705.236328, 744.429626, 15.213191, 89.999992, -6.774045, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2704.836425, 744.429626, 15.213191, 89.999992, -6.774045, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2700.976074, 744.429626, 15.213191, 89.999992, -6.774045, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2700.229980, 743.997985, 12.049131, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19383, 2703.439941, 743.997985, 12.049131, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2714.754150, 737.979431, 14.759119, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19383, 2706.647949, 743.997985, 12.049131, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2709.857421, 743.997985, 12.049131, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2709.474609, 737.958435, 11.939151, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2710.504882, 737.958435, 11.939151, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2703.450195, 743.997985, 15.549130, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2722.723144, 744.226501, 17.424280, -0.000022, -0.000014, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "brnstucco1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2727.450683, 739.325622, 17.424280, -0.000007, 0.000014, -0.000105, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "brnstucco1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2722.683593, 733.717712, 17.422281, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "brnstucco1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2711.506103, 751.158142, 12.049127, 0.000007, -0.000014, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2707.826660, 737.889099, 12.740146, 89.999992, 264.132141, -84.132133, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2707.826660, 743.171081, 12.740146, 89.999992, 264.132141, -84.132133, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2715.769287, 740.543640, 10.819144, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2711.504150, 748.717956, 15.549127, 0.000007, -0.000014, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2707.827636, 740.129333, 12.740146, 89.999992, 264.132141, -84.132133, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2707.820556, 738.642028, 8.569136, 89.999992, 83.225975, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2707.839111, 738.642028, 8.569136, 89.999992, 83.225975, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19325, 2707.818359, 742.211853, 11.719141, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    brgstxt = CreateDynamicObject(19353, 2707.813476, 742.300231, 9.309139, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2707.812500, 741.729675, 9.310137, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2700.081787, 739.152282, 15.083215, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2703.581787, 739.152282, 15.083215, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2707.081787, 739.152282, 15.083215, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2710.581787, 739.152282, 15.083215, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2714.081787, 739.152282, 15.083215, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2715.132324, 739.152282, 15.082217, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2707.853515, 742.300231, 9.309139, 0.000014, -0.000022, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2707.852539, 741.729675, 9.310137, 0.000014, -0.000022, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2707.820312, 742.300231, 14.919153, 0.000029, -0.000022, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2707.819335, 741.729675, 14.919153, 0.000029, -0.000022, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2707.841552, 742.300231, 14.919153, 0.000036, -0.000022, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2707.840576, 741.729675, 14.919153, 0.000036, -0.000022, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2707.831542, 740.870056, 12.740147, 89.999992, 83.225975, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2707.831542, 743.920104, 12.740147, 89.999992, 83.225975, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2707.817382, 740.812683, 12.049138, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2707.817382, 743.053894, 12.049138, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2707.817382, 742.033386, 10.999124, 89.999992, 83.226013, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2707.817382, 742.033386, 13.039133, 89.999992, 83.226013, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2707.817382, 741.912780, 12.049138, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1560, 2707.834472, 740.140808, 12.799118, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(1560, 2707.833496, 740.140808, 13.929112, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2707.826660, 738.639099, 16.600151, 89.999992, 264.132141, -84.132133, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2707.825683, 739.387390, 16.600151, 89.999992, 264.132141, -84.132133, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2707.828613, 740.128112, 16.598154, 89.999992, 83.225975, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2707.828613, 739.377380, 16.598154, 89.999992, 83.225975, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2707.832763, 738.638610, 12.588154, 89.999992, 83.225975, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2709.334960, 737.976501, 14.759119, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2709.474609, 737.958435, 11.009140, 89.999992, 173.225997, -83.225967, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19426, 2715.344726, 737.977478, 12.049118, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19325, 2714.098388, 737.980407, 10.369134, 89.999992, 173.225997, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x90FFFFFF);
    brgstxt = CreateDynamicObject(1897, 2709.474609, 737.958435, 12.889147, 89.999992, 173.225997, -83.225967, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2713.365722, 737.958435, 11.939151, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2712.164550, 737.958435, 12.889147, 89.999992, 173.225997, -83.225967, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2713.515136, 737.958435, 12.889147, 89.999992, 173.225997, -83.225967, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2712.164550, 737.958435, 11.015126, 89.999992, 173.225997, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2713.515136, 737.958435, 11.015126, 89.999992, 173.225997, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2712.185058, 737.958435, 11.939151, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2714.605468, 737.958435, 11.939151, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2711.506103, 751.158142, 15.549127, 0.000007, -0.000014, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2709.857421, 743.998962, 15.549127, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2715.768310, 741.444030, 10.820142, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2715.598144, 741.664245, 10.140140, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2715.598144, 740.563659, 10.140140, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2716.767578, 743.444030, 8.820139, 0.000022, -179.999984, 89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2717.767578, 743.444030, 8.820139, 0.000022, -179.999984, 89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2716.577636, 743.665710, 8.140132, 0.000000, 179.999984, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2717.508300, 743.665710, 8.140132, 0.000000, 179.999984, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2717.768554, 743.665710, 8.140132, 0.000000, 179.999984, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2710.581787, 748.782409, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2714.081787, 748.782409, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2714.081787, 748.782409, 15.083219, 0.000020, 89.999977, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2710.581787, 752.013122, 10.232198, 0.000029, 89.999977, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2714.081787, 752.013122, 10.232198, 0.000029, 89.999977, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2714.081787, 752.013122, 15.082221, 0.000036, 89.999977, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2717.581787, 739.152282, 15.083219, 0.000020, 89.999977, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2717.581787, 739.152282, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2717.581787, 748.782409, 15.083219, 0.000020, 89.999977, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2714.081787, 748.782409, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2717.581787, 748.782409, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2717.581787, 752.013122, 15.082221, 0.000036, 89.999977, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2714.081787, 752.013122, 10.232198, 0.000029, 89.999977, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2717.581787, 752.013122, 10.232198, 0.000029, 89.999977, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2721.081787, 739.152282, 15.083219, 0.000020, 89.999977, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2717.581787, 739.152282, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2721.081787, 739.152282, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2724.581787, 739.152282, 15.083219, 0.000020, 89.999977, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2717.581787, 748.782409, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2721.081787, 748.782409, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2725.632324, 739.152282, 15.082221, 0.000020, 89.999977, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2717.581787, 752.013122, 10.232198, 0.000029, 89.999977, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2721.081787, 752.013122, 10.232198, 0.000029, 89.999977, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2721.081787, 748.782409, 15.083219, 0.000020, 89.999977, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2721.081787, 739.152282, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2724.581787, 739.152282, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2725.632324, 739.152282, 10.232197, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2721.081787, 748.782409, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2724.581787, 748.782409, 10.233201, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2725.632324, 748.782409, 10.232197, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2721.081787, 752.013122, 10.232198, 0.000029, 89.999977, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2724.581787, 752.013122, 10.232198, 0.000029, 89.999977, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2725.632324, 752.013122, 10.231199, 0.000029, 89.999977, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2724.581787, 748.782409, 15.083219, 0.000020, 89.999977, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2725.632324, 748.782409, 15.082221, 0.000020, 89.999977, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2721.081787, 752.013122, 15.082221, 0.000036, 89.999977, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2724.581787, 752.013122, 15.082221, 0.000036, 89.999977, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2725.632324, 752.013122, 15.081217, 0.000036, 89.999977, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2718.357421, 744.167907, 12.599144, 89.999992, 83.226013, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2718.357421, 743.667663, 12.599144, 89.999992, 83.226013, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 2720.072265, 743.749206, 10.719136, -0.000022, 179.999984, -89.999626, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 2720.063476, 743.749206, 10.719136, 0.000022, 0.000058, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2722.648437, 743.749694, 10.539138, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2722.648437, 743.749694, 9.799141, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2724.428710, 743.750671, 10.539138, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2724.428710, 743.750671, 9.799141, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2721.774414, 743.752624, 13.009140, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2724.974121, 743.752624, 13.009140, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2725.233398, 743.756530, 13.009140, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2724.974121, 743.752624, 16.509141, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2725.233398, 743.756530, 16.509141, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2721.774414, 743.752624, 16.509141, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, 2719.189453, 743.725036, 14.689137, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2717.547119, 743.447448, 12.599144, 89.999992, 173.225997, -83.225967, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3820, "boxhses_sfsx", "stonewall_la", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2716.797363, 743.447448, 12.599144, 89.999992, 173.225997, -83.225967, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3820, "boxhses_sfsx", "stonewall_la", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 2716.892089, 743.449157, 15.129137, -0.000007, 179.999984, 0.000319, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2715.769287, 740.523620, 14.089142, 89.999992, 263.226013, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2715.768310, 741.444030, 14.090146, 89.999992, 263.225921, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2715.769287, 740.523620, 15.089142, 89.999992, 263.226013, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2715.768310, 741.444030, 15.090146, 89.999992, 263.225921, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2716.767578, 743.444030, 16.090133, 0.000022, -179.999984, 89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2717.767578, 743.444030, 16.090133, 0.000022, -179.999984, 89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2715.883544, 740.934265, 13.869134, 0.000007, 89.999977, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2715.883544, 740.934265, 14.059136, 0.000007, 89.999977, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2717.910644, 737.964538, 12.029143, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19383, 2720.070800, 742.034851, 12.029143, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2720.069824, 739.515319, 9.539133, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2718.556152, 737.965515, 12.029143, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2720.069824, 739.515319, 14.179141, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2717.910644, 737.964538, 15.529141, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2718.561035, 737.965515, 15.529141, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2720.070800, 742.034851, 15.529141, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2720.097167, 738.196472, 11.869136, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2720.097167, 740.487243, 11.869136, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2720.097167, 739.236755, 11.229130, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1897, 2720.097167, 739.236755, 12.489135, -89.999992, 263.225952, 83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(2655, 2720.181152, 740.830017, 12.169135, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2813, "gb_books01", "GB_novels01", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2716.273437, 734.084899, 12.639149, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2726.885742, 739.264099, 9.539140, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2726.905761, 739.264099, 13.039139, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2726.906738, 739.264099, 14.139142, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2721.995849, 734.473083, 9.539140, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2721.155029, 734.474060, 9.539140, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2721.155029, 734.474060, 13.039139, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2722.064941, 734.476013, 13.039139, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2721.155029, 734.474060, 16.539140, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2722.064941, 734.476013, 16.539140, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2515, 2726.583984, 738.899353, 11.351137, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(2643, 2725.020996, 743.446960, 13.089138, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19926, 2719.582763, 739.005065, 10.319137, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3922, "bistro", "Marble2", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19926, 2719.581787, 739.485290, 10.320137, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3922, "bistro", "Marble2", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1968, 2709.326171, 741.876892, 10.839135, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1731, "cj_lighting", "CJ_WICKER1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14668, "711c", "gun_ceiling1128", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2716.416015, 756.008728, 12.049127, -0.000022, 0.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2722.016601, 756.009460, 12.049127, -0.000022, 0.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2726.846923, 748.648620, 12.049127, -0.000007, 0.000029, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2726.845947, 751.139831, 12.049127, -0.000007, 0.000029, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2716.416015, 756.008728, 15.549127, -0.000022, 0.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2722.016601, 756.009460, 15.549127, -0.000022, 0.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2726.846923, 748.648620, 15.549127, -0.000007, 0.000029, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2726.845947, 751.139831, 15.549127, -0.000007, 0.000029, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19934, 2720.391113, 744.214050, 10.123137, 0.000014, -0.000022, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19934, 2720.391113, 744.954284, 10.123137, 0.000014, -0.000022, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19934, 2720.391113, 745.693786, 10.123137, 0.000014, -0.000022, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19934, 2720.391113, 746.433288, 10.123137, 0.000014, -0.000022, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19934, 2720.391113, 747.173522, 10.123137, 0.000014, -0.000022, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19934, 2720.391113, 747.904235, 10.123137, 0.000014, -0.000022, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19940, 2720.396484, 744.825866, 11.749155, 0.000022, -0.000022, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19940, 2720.396484, 746.816345, 11.749155, 0.000022, -0.000022, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19940, 2720.395507, 747.276794, 11.750155, 0.000022, -0.000022, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19940, 2720.396484, 744.825866, 12.649168, 0.000022, -0.000022, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19940, 2720.396484, 746.816345, 12.649168, 0.000022, -0.000022, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19940, 2720.395507, 747.276794, 12.650168, 0.000022, -0.000022, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19940, 2720.396484, 744.825866, 13.359170, 0.000029, -0.000022, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19940, 2720.396484, 746.816345, 13.359170, 0.000029, -0.000022, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19940, 2720.395507, 747.276794, 13.360173, 0.000029, -0.000022, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(2247, 2720.247558, 747.764831, 12.209136, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2247, 2720.247558, 745.054626, 12.209136, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2247, 2720.247558, 746.414733, 12.209136, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14387, "dr_gsnew", "mp_flowerbush", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2247, 2720.247558, 747.284606, 13.809136, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14387, "dr_gsnew", "mp_flowerbush", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2247, 2720.247558, 745.444030, 13.809136, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2808, 2726.318847, 745.582458, 10.889137, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18200, "w_town2cs_t", "mottled_creme_64HV", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6040, "law_cnrtplaz", "creamshop1_LAe", 0x00000000);
    brgstxt = CreateDynamicObject(2808, 2726.318847, 747.752624, 10.889137, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18200, "w_town2cs_t", "mottled_creme_64HV", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6040, "law_cnrtplaz", "creamshop1_LAe", 0x00000000);
    brgstxt = CreateDynamicObject(2808, 2726.318847, 749.923767, 10.889137, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18200, "w_town2cs_t", "mottled_creme_64HV", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6040, "law_cnrtplaz", "creamshop1_LAe", 0x00000000);
    brgstxt = CreateDynamicObject(2808, 2726.318847, 752.094665, 10.889137, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18200, "w_town2cs_t", "mottled_creme_64HV", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6040, "law_cnrtplaz", "creamshop1_LAe", 0x00000000);
    brgstxt = CreateDynamicObject(2808, 2726.318847, 754.254577, 10.889137, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18200, "w_town2cs_t", "mottled_creme_64HV", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6040, "law_cnrtplaz", "creamshop1_LAe", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2726.820800, 749.967956, 12.939146, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    brgstxt = CreateDynamicObject(2115, 2724.556640, 753.735778, 10.319137, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2115, 2724.556640, 751.586120, 10.319137, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2115, 2724.556640, 749.415466, 10.319137, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2115, 2724.556640, 747.255065, 10.319137, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2115, 2724.556640, 745.055114, 10.319137, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1968, 2712.676757, 746.457458, 10.839135, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1731, "cj_lighting", "CJ_WICKER1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14668, "711c", "gun_ceiling1128", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1968, 2712.676757, 753.917175, 10.839135, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1731, "cj_lighting", "CJ_WICKER1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14668, "711c", "gun_ceiling1128", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1968, 2712.676757, 750.356872, 10.839135, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1731, "cj_lighting", "CJ_WICKER1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14668, "711c", "gun_ceiling1128", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1968, 2715.656738, 752.186950, 10.839135, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1731, "cj_lighting", "CJ_WICKER1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14668, "711c", "gun_ceiling1128", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1968, 2715.656738, 748.386657, 10.839135, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1731, "cj_lighting", "CJ_WICKER1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14668, "711c", "gun_ceiling1128", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2709.632324, 748.784851, 10.253198, 0.000007, 89.999977, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15055, "svlamid", "AH_flroortile3", 0x00000000);
    brgstxt = CreateDynamicObject(1968, 2718.317626, 753.917175, 10.839135, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1731, "cj_lighting", "CJ_WICKER1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14668, "711c", "gun_ceiling1128", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1968, 2718.317626, 750.356872, 10.839135, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1731, "cj_lighting", "CJ_WICKER1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14668, "711c", "gun_ceiling1128", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2257, 2711.615234, 746.354187, 12.829131, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 17503, "furniture_lae2", "clukmenu1_LAe2", 0x00000000);
    brgstxt = CreateDynamicObject(2257, 2711.615234, 750.254577, 12.829131, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 2430, "cj_burg_sign", "CJ_BS_MENU2", 0x00000000);
    brgstxt = CreateDynamicObject(2257, 2711.615234, 753.925231, 12.829131, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 2645, "cj_piz_sign", "CJ_PIZZA_MENU1", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2719.027832, 755.957946, 13.339154, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2719.027832, 755.957946, 9.839155, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2723.618164, 755.957946, 13.339154, -0.000022, 179.999984, -89.999763, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2723.618164, 755.957946, 9.839155, -0.000022, 179.999984, -89.999763, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2714.478759, 755.957946, 13.339154, -0.000022, 179.999984, -89.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2714.478759, 755.957946, 9.839155, -0.000022, 179.999984, -89.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(2643, 2714.469726, 755.885192, 11.969156, -0.000022, 0.000022, 0.000075, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(3053, 2714.491210, 756.074890, 11.979145, 89.999992, 263.226013, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(2643, 2719.039306, 755.885192, 11.969156, -0.000029, 0.000022, 0.000105, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(3053, 2719.061035, 756.074890, 11.979145, 89.999992, 263.226043, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(2643, 2723.689697, 755.885192, 11.969156, -0.000037, 0.000022, 0.000121, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(3053, 2723.711425, 756.074890, 11.979145, 89.999992, 263.226043, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(2261, 2714.489501, 755.275329, 11.669136, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 2767, "cb_details", "wrapfood_cb", 0x00000000);
    brgstxt = CreateDynamicObject(2261, 2719.060058, 755.275329, 11.669136, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 2767, "cb_details", "wrapfood_cb", 0x00000000);
    brgstxt = CreateDynamicObject(2261, 2723.710937, 755.275329, 11.669136, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 2767, "cb_details", "wrapfood_cb", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2708.617675, 744.429626, 15.213191, 89.999992, -6.773986, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2704.757324, 744.429626, 15.213191, 89.999992, -6.773986, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2713.331298, 748.782409, 15.084219, 0.000029, 89.999977, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2713.331298, 752.013122, 15.083222, 0.000044, 89.999977, 179.999649, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2709.632324, 751.155212, 10.252200, 0.000007, 89.999977, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15055, "svlamid", "AH_flroortile3", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2706.132324, 748.784851, 10.253198, 0.000007, 89.999977, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15055, "svlamid", "AH_flroortile3", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2706.132324, 751.155212, 10.252200, 0.000007, 89.999977, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15055, "svlamid", "AH_flroortile3", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2702.632324, 748.784851, 10.253198, 0.000007, 89.999977, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15055, "svlamid", "AH_flroortile3", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2702.632324, 751.155212, 10.252200, 0.000007, 89.999977, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15055, "svlamid", "AH_flroortile3", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2699.913085, 748.784851, 10.252200, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15055, "svlamid", "AH_flroortile3", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2699.913085, 751.155212, 10.251197, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15055, "svlamid", "AH_flroortile3", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2709.632324, 748.784851, 14.753218, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14789, "ab_sfgymmain", "gun_ceiling2_128", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2709.632324, 751.155212, 14.752220, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14789, "ab_sfgymmain", "gun_ceiling2_128", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2706.132324, 748.784851, 14.753218, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14789, "ab_sfgymmain", "gun_ceiling2_128", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2706.132324, 751.155212, 14.752220, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14789, "ab_sfgymmain", "gun_ceiling2_128", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2702.632324, 748.784851, 14.753218, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14789, "ab_sfgymmain", "gun_ceiling2_128", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2702.632324, 751.155212, 14.752220, 0.000014, 89.999977, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14789, "ab_sfgymmain", "gun_ceiling2_128", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2699.913085, 748.784851, 14.752220, 0.000022, 89.999977, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14789, "ab_sfgymmain", "gun_ceiling2_128", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2699.913085, 751.155212, 14.751217, 0.000022, 89.999977, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14789, "ab_sfgymmain", "gun_ceiling2_128", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2706.596191, 756.028259, 12.049127, -0.000022, 0.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2703.664550, 756.031188, 12.049127, -0.000022, 0.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2706.596191, 756.028259, 15.549127, -0.000022, 0.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2703.664550, 756.031188, 15.549127, -0.000022, 0.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2705.052734, 748.868103, 12.049127, 0.000007, -0.000014, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2705.051757, 751.139099, 12.049127, 0.000007, -0.000014, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2705.052734, 748.868103, 15.549127, 0.000007, -0.000014, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2705.051757, 751.139099, 15.549127, 0.000007, -0.000014, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14847, "mp_policesf", "mp_cop_marble", 0x00000000);
    brgstxt = CreateDynamicObject(2207, 2702.872070, 753.334167, 10.339138, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(2208, 2701.968505, 749.816345, 10.209136, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(2208, 2701.967529, 748.214782, 10.210135, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(2207, 2701.020751, 747.324645, 10.339138, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19426, 2701.488281, 750.693054, 10.978141, 0.000022, 90.000015, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3922, "bistro", "Marble2", 0x00000000);
    brgstxt = CreateDynamicObject(19426, 2701.489257, 750.012390, 10.979144, 0.000022, 90.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3922, "bistro", "Marble2", 0x00000000);
    brgstxt = CreateDynamicObject(19426, 2702.508300, 750.693054, 10.978141, 0.000022, 90.000022, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3922, "bistro", "Marble2", 0x00000000);
    brgstxt = CreateDynamicObject(19426, 2702.509277, 750.012390, 10.979144, 0.000022, 90.000015, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3922, "bistro", "Marble2", 0x00000000);
    brgstxt = CreateDynamicObject(2789, 2702.010742, 756.025329, 12.556508, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2813, "gb_books01", "GB_novels05", 0x00000000);
    brgstxt = CreateDynamicObject(2247, 2709.156494, 753.529968, 11.539145, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2707.271728, 756.004577, 12.479126, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14488, "dogsgym", "AH_stolewindow", 0x00000000);
    brgstxt = CreateDynamicObject(2115, 2709.132812, 746.193298, 10.339137, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 2707.921386, 744.175720, 10.339138, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(2247, 2710.080566, 746.227966, 11.559137, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1761, 2705.622558, 747.778991, 10.339138, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    brgstxt = CreateDynamicObject(1817, 2707.614746, 748.348815, 10.339138, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2717.222167, 733.716735, 17.422281, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "brnstucco1", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2720.828613, 757.007995, 15.013179, 0.000007, 89.999984, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2725.828613, 757.007995, 15.013179, 0.000007, 89.999984, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2727.828613, 754.988220, 15.014182, -0.000022, 90.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2727.828613, 749.988220, 15.014182, -0.000022, 90.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2727.828613, 744.988464, 15.014182, -0.000022, 90.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2727.828613, 739.988464, 15.014182, -0.000022, 90.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2727.827636, 735.398864, 15.015180, -0.000022, 90.000000, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2724.837158, 733.397888, 15.015180, -0.000007, 90.000030, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2719.837158, 733.397888, 15.015180, -0.000007, 90.000030, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2714.977294, 733.397888, 15.015180, -0.000007, 90.000030, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2727.447753, 738.446716, 17.424280, -0.000007, 0.000014, -0.000105, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "brnstucco1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2721.873046, 756.427185, 16.572267, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2722.533691, 756.427185, 16.572267, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2727.262207, 751.547302, 16.572267, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2727.262207, 749.116149, 16.572267, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2727.450683, 739.498229, 13.924279, -0.000014, 0.000014, -0.000091, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "brnstucco1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2722.683593, 733.717712, 13.922282, -0.000022, -0.000014, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "brnstucco1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2717.222167, 733.716735, 13.922282, -0.000022, -0.000014, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "brnstucco1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2727.447753, 738.446716, 13.924279, -0.000014, 0.000014, -0.000091, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "brnstucco1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2727.390625, 739.498229, 13.924279, -0.000014, 0.000014, -0.000091, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "brnstucco1", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2697.833984, 735.678405, 19.582223, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2697.833984, 740.678405, 19.582223, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2697.833007, 742.339538, 19.583223, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2700.832519, 744.340270, 19.583223, 89.999992, -6.774020, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2727.474121, 735.678405, 19.582223, 89.999992, 263.225921, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2727.474121, 740.678405, 19.582223, 89.999992, 263.225921, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2727.473144, 742.339538, 19.583223, 89.999992, 263.225921, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2705.832519, 744.340270, 19.583223, 89.999992, -6.774020, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2710.832519, 744.340270, 19.583223, 89.999992, -6.774020, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2715.832519, 744.340270, 19.583223, 89.999992, -6.774020, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2720.832519, 744.340270, 19.583223, 89.999992, -6.774020, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2725.471191, 744.340270, 19.584222, 89.999992, -6.774020, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2700.832519, 733.677673, 19.584222, 89.999992, -6.774045, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2705.832519, 733.677673, 19.584222, 89.999992, -6.774045, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2710.832519, 733.677673, 19.584222, 89.999992, -6.774045, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2715.832519, 733.677673, 19.584222, 89.999992, -6.774045, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2720.832519, 733.677673, 19.584222, 89.999992, -6.774045, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2725.471191, 733.677673, 19.585222, 89.999992, -6.774045, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 2709.982666, 733.526550, 6.213224, 0.000029, 89.999977, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 2709.982666, 734.147155, 6.213224, 0.000029, 89.999977, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 5168, "lashops6_las2", "yellow2_128", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2698.642578, 754.129821, 18.003221, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2698.642578, 749.129821, 18.003221, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2698.642578, 744.130065, 18.003221, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2701.641601, 756.128845, 18.003221, 89.999992, -6.774020, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2706.641601, 756.128845, 18.003221, 89.999992, -6.774020, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2711.641601, 756.128845, 18.003221, 89.999992, -6.774020, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2716.641601, 756.128845, 18.003221, 89.999992, -6.774020, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2721.641601, 756.128845, 18.003221, 89.999992, -6.774020, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2724.961425, 756.129821, 18.004219, 89.999992, -6.774020, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2726.973632, 754.130798, 18.004219, 89.999992, 263.225921, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2726.973632, 749.130798, 18.004219, 89.999992, 263.225921, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2726.973632, 744.131042, 18.004219, 89.999992, 263.225921, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19377, 2702.930908, 738.978454, 19.407197, 0.000022, 90.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19377, 2712.560058, 738.978454, 19.407197, 0.000022, 90.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19377, 2722.190429, 738.978454, 19.407197, 0.000022, 90.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19377, 2722.190429, 749.549255, 17.677202, 0.000022, 90.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19377, 2712.561523, 749.549255, 17.677202, 0.000022, 90.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19377, 2702.991210, 749.549255, 17.676202, 0.000022, 90.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19377, 2722.190429, 750.469665, 17.676202, 0.000022, 90.000015, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19377, 2712.561523, 750.469665, 17.676202, 0.000022, 90.000015, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19377, 2702.991210, 750.469665, 17.675203, 0.000022, 90.000015, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(3053, 2698.095703, 743.263366, 17.404447, -0.000037, 90.000022, 0.000121, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(2643, 2697.907714, 743.280212, 17.377996, -0.000022, -0.000037, -90.000099, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    brgstxt = CreateDynamicObject(1822, 2698.288330, 742.745056, 17.904727, -89.999992, 6.774096, 96.773963, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(2662, 2697.762451, 743.827331, 17.298234, -0.000022, -9.400066, -90.000282, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "T", 130, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2697.761474, 743.521911, 17.348859, -0.000022, -9.400066, -90.000282, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "E", 130, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2697.760498, 743.264587, 17.391149, -0.000022, -9.400080, -90.000373, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "X", 130, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2697.761474, 743.352233, 17.202009, -0.000022, 20.599929, -90.000373, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "I", 130, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2697.761474, 743.228454, 17.529710, -0.000022, 20.599929, -90.000373, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "I", 130, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2697.759521, 742.958923, 17.441984, -0.000022, -9.400066, -90.000282, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "A", 130, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2697.758544, 742.722106, 17.481218, -0.000022, -9.400066, -90.000282, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "S", 130, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(19482, 2697.941406, 740.588562, 17.388113, 0.000022, -0.000022, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "TEXAS", 130, "Arial", 110, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(19482, 2697.941406, 736.307800, 17.388113, 0.000022, -0.000022, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "CHICKEN", 130, "Arial", 110, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(19866, 2698.043701, 740.202575, 17.193201, 0.000014, -0.000022, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2698.043701, 736.332214, 17.193201, 0.000014, -0.000022, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2698.043701, 740.202575, 16.783193, 0.000022, -0.000022, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2698.043701, 736.332214, 16.783193, 0.000022, -0.000022, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19426, 2724.705810, 743.820739, 13.390883, 89.999992, 173.226013, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14706, "labig2int2", "wood14S", 0x00000000);
    brgstxt = CreateDynamicObject(3053, 2725.023925, 743.658874, 13.150238, -0.000036, 89.999977, -90.000099, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(2643, 2725.040771, 743.846862, 13.123785, 0.000022, -0.000036, 179.999588, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    brgstxt = CreateDynamicObject(1822, 2724.505615, 743.466247, 13.650519, -89.999992, -261.808746, 98.191146, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(2662, 2725.587890, 743.992126, 13.044024, 0.000022, -9.400067, 179.999404, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "T", 130, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2725.282226, 743.993103, 13.094652, 0.000022, -9.400067, 179.999404, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "E", 130, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2725.025146, 743.994079, 13.136939, 0.000022, -9.400079, 179.999313, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "X", 130, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2725.112792, 743.993103, 12.947796, 0.000022, 20.599929, 179.999313, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "I", 130, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2724.988769, 743.993103, 13.275503, 0.000022, 20.599929, 179.999313, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "I", 130, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2724.719482, 743.995056, 13.187773, 0.000022, -9.400067, 179.999404, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "A", 130, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2724.482666, 743.996032, 13.227007, 0.000022, -9.400067, 179.999404, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "S", 130, "Arial", 190, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(19426, 2724.704833, 743.819030, 12.790870, 89.999992, 173.226013, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14706, "labig2int2", "wood14S", 0x00000000);
    brgstxt = CreateDynamicObject(19426, 2722.591796, 743.822937, 13.390883, 89.999992, 173.226028, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14706, "labig2int2", "wood14S", 0x00000000);
    brgstxt = CreateDynamicObject(19426, 2722.590820, 743.823913, 12.790870, 89.999992, 173.226028, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14706, "labig2int2", "wood14S", 0x00000000);
    brgstxt = CreateDynamicObject(2662, 2723.170410, 743.981140, 13.789145, 0.000037, -0.000022, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "WE BRING", 130, "Arial", 110, 1, 0xFF000000, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2721.939453, 743.981140, 13.789145, 0.000037, -0.000022, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "THE", 130, "Arial", 110, 1, 0xFF000000, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2723.310546, 743.981140, 13.429145, 0.000051, -0.000022, 179.999633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "FLAVO", 130, "Arial", 130, 1, 0xFF000000, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2722.150146, 743.981140, 13.429145, 0.000051, -0.000022, 179.999633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "RFUL", 130, "Arial", 130, 1, 0xFF000000, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2723.321044, 743.981140, 13.039141, 0.000051, -0.000022, 179.999633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "LEGEN", 130, "Arial", 130, 1, 0xFF000000, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2722.101318, 743.981140, 13.039141, 0.000051, -0.000022, 179.999633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "DARY", 130, "Arial", 130, 1, 0xFF000000, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2723.290527, 743.981140, 12.649147, 0.000045, -0.000022, 179.999649, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "TASTE OF", 130, "Arial", 90, 1, 0xFF000000, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2722.110107, 743.981140, 12.649147, 0.000045, -0.000022, 179.999649, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "TEXAS", 130, "Arial", 90, 1, 0xFF000000, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2723.390625, 743.981140, 12.319149, 0.000045, -0.000022, 179.999649, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "TO THE", 130, "Arial", 100, 1, 0xFF000000, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2662, 2722.200195, 743.981140, 12.319149, 0.000045, -0.000022, 179.999649, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "WORLD", 130, "Arial", 100, 1, 0xFF000000, 0x00000000, 1);
    brgstxt = CreateDynamicObject(2643, 2725.020019, 743.567077, 13.090140, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(1822, 2724.518066, 743.841735, 13.629134, -0.000022, 89.999992, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4981, "wiresetc2_las", "lasunionclk", 0x00000000);
    brgstxt = CreateDynamicObject(18981, 2709.859130, 721.093933, 9.400313, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2721.861816, 733.044494, 10.600310, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13691, "bevcunto2_lahills", "stonewall3_la", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2721.861816, 709.092895, 10.600310, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13691, "bevcunto2_lahills", "stonewall3_la", 0x00000000);
    brgstxt = CreateDynamicObject(18980, 2721.862792, 721.864196, 10.050300, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13691, "bevcunto2_lahills", "stonewall3_la", 0x00000000);
    brgstxt = CreateDynamicObject(18980, 2709.843017, 709.084533, 10.050300, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13691, "bevcunto2_lahills", "stonewall3_la", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 2697.848876, 709.092895, 10.600310, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13691, "bevcunto2_lahills", "stonewall3_la", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2703.419677, 709.117065, 11.073307, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6487, "councl_law2", "lanlabra1_M", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2713.049560, 709.117065, 11.073307, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6487, "councl_law2", "lanlabra1_M", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2719.453369, 709.117065, 11.073307, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6487, "councl_law2", "lanlabra1_M", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2720.901855, 709.177429, 10.500320, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2697.938476, 709.177429, 10.500320, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2721.888671, 714.720947, 11.073307, 0.000014, -0.000022, 179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6487, "councl_law2", "lanlabra1_M", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2721.888671, 724.350830, 11.073307, 0.000014, -0.000022, 179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6487, "councl_law2", "lanlabra1_M", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 2721.888671, 730.754638, 11.073307, 0.000014, -0.000022, 179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6487, "councl_law2", "lanlabra1_M", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2721.872802, 709.177429, 10.500320, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, 2721.872802, 732.157165, 10.500320, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2719.595947, 725.887329, 9.820311, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4595, "crparkgm_lan2", "sl_dtcparklines1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 2719.595947, 716.427185, 9.821311, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4595, "crparkgm_lan2", "sl_dtcparklines1", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(1719, 2701.940429, 752.695251, 11.146759, -17.399997, -0.000023, 179.999786, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2451, 2720.812500, 738.827575, 10.119134, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11706, 2720.526855, 740.793640, 10.319132, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2417, 2726.391113, 743.186218, 10.319137, -0.000022, -0.000014, -89.999961, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2417, 2726.391113, 742.225769, 10.319137, -0.000022, -0.000014, -89.999961, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2417, 2726.391113, 741.275573, 10.319137, -0.000022, -0.000014, -89.999961, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2131, 2726.327636, 737.665466, 10.319137, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19927, 2726.386230, 739.408386, 10.319137, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19622, 2726.690429, 735.400329, 11.012380, 8.700015, -0.199992, 89.999893, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19924, 2721.203613, 739.325866, 14.069143, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2163, 2726.791015, 742.509704, 12.609142, -0.000022, -0.000022, -90.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2163, 2726.791015, 740.739929, 12.609142, -0.000022, -0.000022, -90.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2163, 2726.791992, 741.699645, 13.529148, -0.000022, 179.999984, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2163, 2726.791992, 739.928649, 13.530146, -0.000022, 179.999984, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2417, 2726.381835, 743.246276, 10.318139, -0.000014, 0.000022, 0.000060, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2417, 2725.421875, 743.246276, 10.318139, -0.000014, 0.000022, 0.000060, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2417, 2724.461425, 743.246276, 10.318139, -0.000014, 0.000022, 0.000060, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1235, 2723.474609, 743.156677, 10.829138, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2256, 2722.009765, 743.643005, 12.829141, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2453, 2715.810058, 738.592468, 11.669140, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, 2717.050048, 738.516540, 10.319137, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, 2717.050048, 738.516540, 11.709138, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11706, 2719.726562, 740.863708, 10.319132, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2066, 2717.899414, 738.548767, 10.309139, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2258, 2719.111572, 738.062438, 12.219142, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1514, 2715.843261, 739.659851, 11.549135, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1514, 2715.843261, 742.220153, 11.549135, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 2720.376953, 748.113220, 12.239139, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 2720.400390, 743.983337, 12.239139, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 2720.400390, 746.143493, 12.239139, 0.000014, -0.000022, 179.999740, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 2720.376953, 745.952575, 12.239139, -0.000014, 0.000022, 0.000060, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2310, 2723.349365, 754.742614, 10.778135, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2310, 2723.349365, 753.772399, 10.778135, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2310, 2723.349365, 752.592712, 10.778135, 0.000014, -0.000022, 179.999740, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2310, 2723.349365, 751.622253, 10.778135, 0.000014, -0.000022, 179.999740, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2310, 2723.349365, 750.422546, 10.778135, 0.000022, -0.000022, 179.999725, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2310, 2723.349365, 749.452331, 10.778135, 0.000022, -0.000022, 179.999725, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2310, 2723.349365, 748.341735, 10.778135, 0.000029, -0.000022, 179.999694, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2310, 2723.349365, 747.371520, 10.778135, 0.000029, -0.000022, 179.999694, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2310, 2723.349365, 746.150329, 10.778135, 0.000037, -0.000022, 179.999679, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2310, 2723.349365, 745.180114, 10.778135, 0.000037, -0.000022, 179.999679, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2657, 2711.622802, 748.304382, 12.849142, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2657, 2711.622802, 752.074645, 12.849142, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2431, 2715.242675, 741.723083, 13.889141, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 2704.457519, 752.136169, 10.339138, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 2704.457519, 748.586364, 10.339138, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 2704.457519, 750.306579, 10.339138, -0.000022, -0.000007, -89.999916, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 2699.444824, 748.586364, 10.339138, 0.000022, 0.000014, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 2699.444824, 752.136169, 10.339138, 0.000022, 0.000014, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 2699.444824, 750.416198, 10.339138, 0.000022, 0.000014, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 2701.985351, 745.784362, 10.338135, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2608, 2700.815917, 744.296569, 12.198135, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, 2699.456054, 744.600036, 10.338135, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, 2704.556152, 744.273376, 10.339138, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2184, 2707.374511, 753.366638, 10.339138, -0.000014, 0.000022, 0.000060, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 2708.437500, 755.587585, 10.338135, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19893, 2708.492919, 753.639587, 11.119132, 0.000014, -0.000022, 179.199752, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 2709.625976, 744.635192, 10.339138, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2256, 2710.288085, 755.924255, 12.549137, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, 2710.936767, 755.407897, 10.339138, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2608, 2709.765380, 744.319763, 12.859135, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19893, 2709.557373, 746.014099, 11.139140, -0.000007, 0.000022, 0.000029, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, 2705.633300, 744.581970, 10.339138, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14455, 2711.246582, 747.952575, 11.869136, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 2709.470947, 751.651306, 10.339138, 0.000000, -0.000024, -159.899703, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 2707.102050, 751.711120, 10.339138, 0.000014, -0.000018, 159.800033, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1828, 2708.285644, 753.418884, 10.339138, 0.000012, -0.000019, 166.399795, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19174, 2705.168945, 752.863464, 12.679145, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 2721.206054, 743.854675, 13.079140, -0.000014, 0.000022, 0.000060, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 2726.046875, 743.854675, 13.079140, -0.000014, 0.000022, 0.000060, 0, 0, -1, 200.00, 200.00);

    //PDC
    brgstxt = CreateDynamicObject(19353, 669.873107, -1878.106811, 4.982500, -89.999992, 360.000000, 89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18763, 667.232177, -1879.540161, 3.759114, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 664.703063, -1879.787597, 4.972498, -89.999992, 273.398864, 93.398757, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 664.703063, -1883.287353, 4.972498, 89.999992, 453.398803, -93.398765, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 671.543090, -1879.787597, 4.972498, -89.999992, 271.700988, 91.700851, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 671.543090, -1883.287353, 4.972498, 89.999992, 451.700927, -91.700859, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 669.873107, -1884.953247, 4.982500, -89.999992, 360.000030, 89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 666.373229, -1884.953247, 4.982499, 89.999992, 706.605773, -76.605758, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18763, 667.232177, -1883.353149, 3.759114, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18763, 667.232177, -1881.431640, 3.759114, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18763, 669.972473, -1880.670898, 3.759114, 0.000029, 90.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18763, 669.972473, -1882.362060, 3.759114, 0.000029, 90.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 664.765197, -1884.966796, 4.669227, 0.000000, 179.999984, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 671.525024, -1884.966796, 4.669227, 0.000000, 179.999984, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 671.525024, -1878.086059, 4.479225, 0.000000, 179.999984, -179.999755, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 664.734985, -1878.086059, 4.479225, 0.000000, 179.999984, -179.999755, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 667.936706, -1879.618164, 9.238602, 86.700035, 180.000686, -0.000638, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1453, "break_farm", "cj_hay2", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 667.936462, -1884.602661, 9.526007, 86.700035, 180.000686, -0.000638, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1453, "break_farm", "cj_hay2", 0x00000000);
    brgstxt = CreateDynamicObject(3041, 672.057006, -1879.971435, 5.729115, 0.000029, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3041, 672.067138, -1883.113525, 5.719113, 0.000029, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3041, 664.716613, -1879.971435, 5.729115, 0.000037, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3041, 664.726623, -1883.113525, 5.719114, 0.000037, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3041, 666.580383, -1884.439819, 5.749114, 0.000014, -0.000037, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3041, 669.722473, -1884.429809, 5.739115, 0.000014, -0.000037, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3041, 669.722473, -1878.108154, 5.739115, 0.000014, -0.000037, 179.999694, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 667.936706, -1879.619384, 9.218634, 86.700035, 180.000686, -0.000623, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 667.936462, -1884.603881, 9.506039, 86.700035, 180.000686, -0.000623, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 670.446105, -1877.603393, 9.112440, -3.299963, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 665.456176, -1877.603393, 9.112440, -3.299963, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 663.391357, -1879.607055, 9.227968, 0.000035, 93.299972, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 663.391357, -1884.588623, 9.515203, 0.000035, 93.299972, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 665.416625, -1886.624877, 9.632608, 3.299978, 89.999954, 179.999435, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 670.406494, -1886.624877, 9.632608, 3.299978, 89.999954, 179.999435, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 672.462158, -1884.601684, 9.515954, -0.000006, 86.699996, -90.000183, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 672.462158, -1879.620239, 9.228727, -0.000006, 86.699996, -90.000183, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 663.176757, -1875.554321, 4.570003, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 653.177673, -1875.554321, 3.989995, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 650.037353, -1873.064697, 3.999995, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 650.037353, -1863.064575, 3.999995, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 673.177307, -1875.554321, 3.989995, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 665.726196, -1880.553955, 4.570001, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 661.396484, -1883.053833, 3.769999, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 665.726928, -1885.543457, 4.580004, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 672.516174, -1885.543457, 2.989996, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 672.516174, -1880.543090, 2.989996, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(19849, 649.001281, -1863.838500, 3.659995, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 4, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 13364, "cetown3cs_t", "ws_sandstone2", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 665.726928, -1890.532958, 4.580004, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 665.726928, -1895.522949, 4.580004, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 655.756958, -1895.522949, 4.580004, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 645.767333, -1895.522949, 4.580004, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 635.787658, -1895.522949, 4.580004, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 633.286804, -1893.034423, 4.600004, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 633.286804, -1883.045410, 4.600004, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 633.286804, -1873.065917, 4.600004, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 633.286804, -1863.075439, 4.600004, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 633.286804, -1853.076660, 4.600004, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 640.766784, -1890.542968, 4.580004, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 650.766662, -1890.562988, 4.580004, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 659.776489, -1890.552978, 4.119999, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 650.757141, -1890.532958, 3.950000, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 640.767456, -1890.562988, 3.950000, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 635.058166, -1883.072387, 3.950000, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 635.068176, -1873.102539, 3.950000, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 642.548034, -1875.554321, 3.989995, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 663.176757, -1875.554321, 3.579999, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 662.236938, -1883.053833, 4.130002, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 660.595947, -1883.053833, 3.470000, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 659.436157, -1889.401977, 3.779999, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 665.726928, -1900.025634, 3.579997, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 655.756958, -1900.025634, 3.579997, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 645.767333, -1900.025634, 3.579997, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 635.787658, -1900.025634, 3.579997, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 665.726928, -1904.609130, 2.569998, 89.999992, 89.999992, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 655.756958, -1904.609130, 2.569998, 89.999992, 89.999992, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 645.767333, -1904.609130, 2.569998, 89.999992, 89.999992, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 635.787658, -1904.609130, 2.569998, 89.999992, 89.999992, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 670.235839, -1890.033569, 2.419996, 180.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 670.225830, -1897.514770, 1.569998, 180.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 631.294616, -1897.514770, 1.599997, 180.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 631.294616, -1887.514526, 1.619997, 180.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 631.294616, -1877.514160, 1.599997, 180.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 631.294616, -1867.523193, 1.589998, 180.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 670.675781, -1885.126953, 4.669227, 0.000000, 179.999984, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 670.675781, -1897.837036, 4.669227, 0.000000, 179.999984, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 670.675781, -1891.926391, 4.669227, 0.000000, 179.999984, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 661.795288, -1898.206542, 4.669228, 0.000000, 179.999984, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 647.654663, -1898.216674, 4.669229, 0.000000, 179.999984, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 631.004516, -1898.216674, 4.669229, 0.000000, 179.999984, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 631.004516, -1887.956787, 4.669229, 0.000000, 179.999984, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 635.574951, -1887.956787, 4.669229, 0.000000, 179.999984, -179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 631.004516, -1878.017456, 4.669229, 0.000000, 179.999984, -179.999755, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 635.574951, -1878.017456, 4.669229, 0.000000, 179.999984, -179.999755, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 631.004516, -1873.056640, 4.669229, 0.000000, 179.999984, -179.999710, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 635.574951, -1873.056640, 4.669229, 0.000000, 179.999984, -179.999710, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 665.867065, -1899.125488, 10.240041, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0xFF909090);
    brgstxt = CreateDynamicObject(18766, 665.867065, -1894.136840, 10.240041, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0xFF909090);
    brgstxt = CreateDynamicObject(18766, 655.887084, -1899.126464, 10.240041, 89.999992, 89.999992, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0xFF909090);
    brgstxt = CreateDynamicObject(18766, 655.887084, -1894.136840, 10.240041, 89.999992, 89.999992, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0xFF909090);
    brgstxt = CreateDynamicObject(18766, 645.926757, -1899.126464, 10.240041, 89.999992, 90.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0xFF909090);
    brgstxt = CreateDynamicObject(18766, 645.926757, -1894.136840, 10.240041, 89.999992, 90.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0xFF909090);
    brgstxt = CreateDynamicObject(18766, 635.946228, -1899.126464, 10.240041, 89.999992, 90.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0xFF909090);
    brgstxt = CreateDynamicObject(18766, 635.946228, -1894.136840, 10.240041, 89.999992, 90.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0xFF909090);
    brgstxt = CreateDynamicObject(18766, 633.444335, -1886.630859, 10.240041, 89.999992, 90.000015, 0.000037, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0xFF909090);
    brgstxt = CreateDynamicObject(18766, 633.444335, -1876.639404, 10.240041, 89.999992, 90.000015, 0.000037, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0xFF909090);
    brgstxt = CreateDynamicObject(18766, 665.897094, -1894.166870, 10.250041, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19325, "lsmall_shops", "lsmall_window01", 0xFFFFFFFF);
    brgstxt = CreateDynamicObject(18766, 665.877075, -1899.136474, 10.250041, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19325, "lsmall_shops", "lsmall_window01", 0xFFFFFFFF);
    brgstxt = CreateDynamicObject(18766, 655.897338, -1894.156860, 10.250041, 89.999992, 89.999992, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19325, "lsmall_shops", "lsmall_window01", 0xFFFFFFFF);
    brgstxt = CreateDynamicObject(18766, 655.907348, -1899.156494, 10.250041, 89.999992, 89.999992, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19325, "lsmall_shops", "lsmall_window01", 0xFFFFFFFF);
    brgstxt = CreateDynamicObject(18766, 645.898132, -1894.156860, 10.250041, 89.999992, 90.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19325, "lsmall_shops", "lsmall_window01", 0xFFFFFFFF);
    brgstxt = CreateDynamicObject(18766, 645.908142, -1899.156494, 10.250041, 89.999992, 90.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19325, "lsmall_shops", "lsmall_window01", 0xFFFFFFFF);
    brgstxt = CreateDynamicObject(18766, 635.899597, -1894.156860, 10.250041, 89.999992, 90.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19325, "lsmall_shops", "lsmall_window01", 0xFFFFFFFF);
    brgstxt = CreateDynamicObject(18766, 635.909606, -1899.156494, 10.250041, 89.999992, 90.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19325, "lsmall_shops", "lsmall_window01", 0xFFFFFFFF);
    brgstxt = CreateDynamicObject(18766, 633.439819, -1886.666503, 10.250041, 89.999992, 135.000000, -44.999965, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19325, "lsmall_shops", "lsmall_window01", 0xFFFFFFFF);
    brgstxt = CreateDynamicObject(18766, 633.439819, -1876.683715, 10.250041, 89.999992, 90.000000, 0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19325, "lsmall_shops", "lsmall_window01", 0xFFFFFFFF);
    brgstxt = CreateDynamicObject(11689, 658.661621, -1891.996826, 4.609999, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 651.617248, -1890.542968, 4.560002, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(1706, 657.149536, -1898.563842, 4.079997, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    brgstxt = CreateDynamicObject(1706, 653.209655, -1898.563842, 4.079997, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    brgstxt = CreateDynamicObject(1706, 649.169128, -1898.563842, 4.079997, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    brgstxt = CreateDynamicObject(1706, 645.060546, -1898.563842, 4.079997, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    brgstxt = CreateDynamicObject(1706, 641.120666, -1898.563842, 4.079997, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    brgstxt = CreateDynamicObject(1706, 637.080139, -1898.563842, 4.079997, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    brgstxt = CreateDynamicObject(936, 653.659545, -1896.432373, 5.239998, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4828, "airport3_las", "sanpedock7", 0x00000000);
    brgstxt = CreateDynamicObject(936, 651.840026, -1895.572509, 5.250000, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4828, "airport3_las", "sanpedock7", 0x00000000);
    brgstxt = CreateDynamicObject(936, 653.639526, -1895.572509, 5.250000, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4828, "airport3_las", "sanpedock7", 0x00000000);
    brgstxt = CreateDynamicObject(936, 643.138977, -1896.432373, 5.239998, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4828, "airport3_las", "sanpedock7", 0x00000000);
    brgstxt = CreateDynamicObject(936, 651.869812, -1896.432373, 5.239998, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4828, "airport3_las", "sanpedock7", 0x00000000);
    brgstxt = CreateDynamicObject(936, 641.319458, -1895.572509, 5.250000, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4828, "airport3_las", "sanpedock7", 0x00000000);
    brgstxt = CreateDynamicObject(936, 643.118957, -1895.572509, 5.250000, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4828, "airport3_las", "sanpedock7", 0x00000000);
    brgstxt = CreateDynamicObject(936, 641.349243, -1896.432373, 5.239998, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4828, "airport3_las", "sanpedock7", 0x00000000);
    brgstxt = CreateDynamicObject(936, 646.709777, -1905.661132, 3.339993, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4828, "airport3_las", "sanpedock7", 0x00000000);
    brgstxt = CreateDynamicObject(936, 644.890258, -1904.801269, 3.349993, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4828, "airport3_las", "sanpedock7", 0x00000000);
    brgstxt = CreateDynamicObject(936, 646.689758, -1904.801269, 3.349993, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4828, "airport3_las", "sanpedock7", 0x00000000);
    brgstxt = CreateDynamicObject(936, 644.920043, -1905.661132, 3.339993, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4828, "airport3_las", "sanpedock7", 0x00000000);
    brgstxt = CreateDynamicObject(936, 652.690002, -1905.661132, 3.339993, 0.000000, -0.000022, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4828, "airport3_las", "sanpedock7", 0x00000000);
    brgstxt = CreateDynamicObject(936, 650.870483, -1904.801269, 3.349993, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4828, "airport3_las", "sanpedock7", 0x00000000);
    brgstxt = CreateDynamicObject(936, 652.669982, -1904.801269, 3.349993, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4828, "airport3_las", "sanpedock7", 0x00000000);
    brgstxt = CreateDynamicObject(936, 650.900268, -1905.661132, 3.339993, 0.000000, -0.000022, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4828, "airport3_las", "sanpedock7", 0x00000000);
    brgstxt = CreateDynamicObject(1706, 657.149536, -1903.023437, 3.059998, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    brgstxt = CreateDynamicObject(1706, 639.238647, -1903.023437, 3.059998, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "ws_whiteplaster_btm", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 4010, "lanblokb2", "bluewhitebuild1", 0x00000000);
    brgstxt = CreateDynamicObject(14394, 666.346740, -1898.825805, 4.249997, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(14394, 666.346740, -1903.348999, 3.249996, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 662.102905, -1904.377929, 2.321496, 0.000000, 57.600055, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 670.203918, -1904.377929, 2.321496, 0.000000, 57.600055, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 670.203918, -1899.857543, 3.318298, 0.000000, 57.600055, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 662.113464, -1899.857543, 3.318298, 0.000000, 57.600055, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(14387, 633.403503, -1899.185180, 4.099997, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 631.293273, -1899.879028, 3.352071, 0.000000, 57.600055, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 635.303222, -1899.898925, 3.327553, 0.000000, 57.600055, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 631.293273, -1904.360839, 2.332211, 0.000000, 57.600055, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 635.293579, -1904.360839, 2.332211, 0.000000, 57.600055, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(14387, 633.403503, -1903.647583, 3.079998, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(717, 652.187316, -1891.105468, 5.120003, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(717, 647.887512, -1890.144653, 5.120003, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(717, 653.227478, -1887.184326, 3.739996, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(717, 659.507263, -1876.816040, 5.120003, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 662.666259, -1875.504272, 4.230000, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 670.585449, -1887.046020, 5.540005, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 670.585449, -1890.495971, 5.540005, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 670.585449, -1893.945922, 5.540005, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 670.585449, -1895.995117, 5.540005, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 670.585449, -1899.357543, 4.792520, 25.500026, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 670.585449, -1903.504272, 3.912369, 25.500026, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 670.575439, -1901.304687, 4.609998, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 670.575439, -1905.375854, 3.609996, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 630.945922, -1874.954223, 5.540005, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 630.945922, -1878.414428, 5.540005, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 630.945922, -1881.845214, 5.540005, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 630.945922, -1885.294311, 5.540005, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 630.945922, -1888.735107, 5.540005, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 630.945922, -1892.195068, 5.540005, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 630.945922, -1896.115844, 5.540005, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 630.945922, -1900.856811, 4.629999, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 630.944396, -1899.159545, 4.842514, 25.500026, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 630.945922, -1893.925903, 5.540005, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 630.944396, -1903.869750, 3.937074, 25.500026, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 630.945922, -1905.366699, 3.589998, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(14387, 636.873901, -1875.584716, 4.089997, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 637.619384, -1877.602783, 3.342202, 0.000000, 57.600055, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 637.619384, -1873.511108, 3.342202, 0.000000, 57.600055, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 635.068176, -1863.122924, 3.950000, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 637.445617, -1873.133789, 5.010001, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 640.895141, -1873.133789, 5.010001, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 644.334716, -1873.133789, 5.010001, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 645.815063, -1873.133789, 5.010001, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 663.794860, -1863.070800, 9.335136, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 656.055236, -1863.051391, 9.335136, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18765, 669.333068, -1867.384765, 2.575148, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 674.215759, -1866.932983, 7.065143, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18765, 659.342224, -1867.384765, 3.025146, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18765, 655.432189, -1867.384765, 3.015146, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 660.495239, -1872.584716, 3.035145, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 655.135559, -1872.574707, 3.055145, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 660.495239, -1862.443603, 3.035145, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 655.135559, -1862.433593, 3.055145, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 650.635742, -1867.604736, 3.045145, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 670.485168, -1862.433593, 3.055145, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18765, 659.212097, -1853.633056, 2.615148, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(18765, 649.882324, -1823.635375, 2.575144, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(18765, 649.302490, -1853.633056, 2.585148, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(18765, 641.402038, -1853.633056, 2.595148, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(18765, 650.881774, -1843.634155, 2.585144, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(18765, 641.402038, -1843.634155, 2.595148, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(18765, 649.882324, -1833.633789, 2.585144, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(18765, 641.402038, -1833.623779, 2.595148, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19912, 663.170837, -1863.038818, 7.248373, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14668, "711c", "cj_white_wall2", 0x80FFFFFF);
    brgstxt = CreateDynamicObject(18765, 641.402038, -1823.630859, 2.595148, 0.000000, 0.000050, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(18765, 649.222961, -1863.622802, 2.575146, 0.000000, 0.000059, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(18765, 641.402038, -1863.622802, 2.565148, 0.000000, 0.000059, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(18765, 659.222778, -1856.972412, 2.595148, 0.000000, 0.000075, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19912, 655.501586, -1863.038818, 9.938387, 0.000000, 270.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14668, "711c", "cj_white_wall2", 0x80FFFFFF);
    brgstxt = CreateDynamicObject(18765, 659.214233, -1843.634155, 2.595148, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19912, 653.721618, -1863.058837, 9.898386, 0.000000, 270.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14668, "711c", "cj_white_wall2", 0x80FFFFFF);
    brgstxt = CreateDynamicObject(18765, 659.214233, -1833.633789, 2.595148, 0.000000, 0.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19912, 664.021301, -1863.048828, 9.838386, 0.000000, 270.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14668, "711c", "cj_white_wall2", 0x80FFFFFF);
    brgstxt = CreateDynamicObject(18765, 659.214233, -1823.640869, 2.585148, 0.000000, 0.000059, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(2491, 651.232543, -1858.270996, 3.425135, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 8037, "vgssmulticarprk", "vgsSstonewall01", 0xFFFFFFFF);
    brgstxt = CreateDynamicObject(18766, 634.367309, -1863.075439, 4.520001, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 634.367309, -1853.076660, 4.520001, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 675.175903, -1866.922973, 3.065145, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(14394, 659.915893, -1861.106323, 4.679999, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 634.517211, -1849.086425, 4.570003, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 669.865478, -1879.402832, 4.590003, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 669.865478, -1874.403442, 4.590003, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 664.324462, -1863.061279, 9.335136, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 655.534301, -1863.050659, 9.335136, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(8613, 646.636291, -1867.533325, 6.169984, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 651.245666, -1867.463500, 7.065143, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 669.865478, -1880.602783, 3.660001, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 656.024230, -1863.050659, 5.615119, 360.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 656.024230, -1863.050659, 9.215129, 540.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 668.824951, -1863.050659, 9.215129, 540.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 668.824951, -1863.050659, 5.645115, 720.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 673.765319, -1863.050659, 5.645115, 720.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 673.765319, -1863.050659, 9.215129, 900.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 651.864624, -1863.051391, 9.335136, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 673.605285, -1863.051391, 9.335136, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(14394, 665.416442, -1868.146728, 4.679998, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6284, "bev_law2", "beachwalkway", 0x00000000);
    brgstxt = CreateDynamicObject(14394, 665.416442, -1866.805786, 4.679998, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(2455, 663.994934, -1871.429443, 5.502696, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "ws_airpt_concrete", 0x00000000);
    brgstxt = CreateDynamicObject(3041, 659.990295, -1872.049682, 5.749114, 0.000014, -0.000045, 179.999649, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(2455, 663.994934, -1870.499633, 5.502696, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "ws_airpt_concrete", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 660.452026, -1871.982543, 4.982499, 89.999992, 713.212646, -83.212585, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 663.941772, -1871.982543, 4.982499, 89.999992, 713.212646, -83.212585, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(3041, 663.390319, -1872.049682, 5.749114, 0.000014, -0.000045, 179.999649, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 665.571166, -1872.179199, 7.065155, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 673.221618, -1872.179199, 7.065155, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 663.695617, -1863.050659, 9.215129, 540.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 658.725708, -1863.050659, 9.215129, 540.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 672.201354, -1863.046020, 9.815176, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 667.201416, -1863.046020, 9.815176, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 662.201538, -1863.046020, 9.815176, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 657.212463, -1863.046020, 9.815176, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 653.252258, -1863.066040, 9.825177, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 655.726074, -1871.954467, 7.055141, 0.000000, 0.000007, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(2455, 663.994934, -1869.569213, 5.502696, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "ws_airpt_concrete", 0x00000000);
    brgstxt = CreateDynamicObject(2454, 664.011230, -1868.650512, 5.516802, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "ws_airpt_concrete", 0x00000000);
    brgstxt = CreateDynamicObject(2455, 662.934875, -1868.508544, 5.502696, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "ws_airpt_concrete", 0x00000000);
    brgstxt = CreateDynamicObject(2455, 662.005249, -1868.508544, 5.502696, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "ws_airpt_concrete", 0x00000000);
    brgstxt = CreateDynamicObject(2455, 661.084899, -1868.508544, 5.502696, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "ws_airpt_concrete", 0x00000000);
    brgstxt = CreateDynamicObject(2455, 660.175354, -1868.508544, 5.502696, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "ws_airpt_concrete", 0x00000000);
    brgstxt = CreateDynamicObject(2455, 659.245727, -1868.508544, 5.502696, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "ws_airpt_concrete", 0x00000000);
    brgstxt = CreateDynamicObject(2455, 658.325378, -1868.508544, 5.502696, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "ws_airpt_concrete", 0x00000000);
    brgstxt = CreateDynamicObject(19927, 655.337463, -1871.031127, 5.475146, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(19926, 657.260314, -1871.029296, 5.475145, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(2455, 655.555664, -1868.508544, 5.502696, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "ws_airpt_concrete", 0x00000000);
    brgstxt = CreateDynamicObject(2455, 654.624877, -1868.508544, 5.502696, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "ws_airpt_concrete", 0x00000000);
    brgstxt = CreateDynamicObject(2455, 653.705200, -1868.508544, 5.502696, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "ws_airpt_concrete", 0x00000000);
    brgstxt = CreateDynamicObject(3041, 659.990295, -1871.579833, 5.739112, 0.000014, -0.000045, 179.999649, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(2457, 657.391906, -1868.509277, 5.495147, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 6284, "bev_law2", "beachwalkway", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 10765, "airportgnd_sfse", "ws_airpt_concrete", 0x00000000);
    brgstxt = CreateDynamicObject(19926, 653.421020, -1871.029296, 5.475145, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(19926, 652.681030, -1871.009277, 5.465145, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(1499, 653.260498, -1868.453979, 4.015127, 0.000007, 0.000000, 179.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(19815, 664.613098, -1881.538330, 5.830007, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(19815, 667.903625, -1885.068847, 5.830007, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(19124, 660.859008, -1892.691894, 9.440016, 180.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(19125, 656.889404, -1892.691894, 9.420019, 180.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(19126, 650.908081, -1892.691894, 9.430020, 180.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(19127, 646.957458, -1892.691894, 9.420015, 180.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(19123, 642.926513, -1892.691894, 9.460020, 180.000000, 540.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(19122, 638.906372, -1892.691894, 9.450015, 180.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 657.681030, -1877.549682, 6.985154, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 651.430664, -1877.549682, 6.985154, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 655.856201, -1875.535034, 9.085150, 90.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 655.856201, -1870.544921, 9.075151, 90.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(2073, 650.984130, -1858.492187, 5.570139, -0.000000, 179.999984, -179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 8037, "vgssmulticarprk", "vgsSstonewall01", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(2086, 650.968017, -1858.538330, 5.690151, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 8037, "vgssmulticarprk", "vgsSstonewall01", 0x00000000);
    brgstxt = CreateDynamicObject(2086, 650.968017, -1858.538330, 6.420154, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 8037, "vgssmulticarprk", "vgsSstonewall01", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 655.756103, -1866.052978, 9.815155, 90.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18996, "mattextures", "sampwhite", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 665.755676, -1866.052978, 9.815155, 90.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18996, "mattextures", "sampwhite", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 668.715942, -1866.052978, 9.815155, 90.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18996, "mattextures", "sampwhite", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 668.715942, -1869.194458, 9.815155, 90.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18996, "mattextures", "sampwhite", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 665.855834, -1870.544921, 9.075151, 90.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 669.748107, -1870.534912, 9.085150, 90.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(19445, 659.949279, -1863.039184, 9.694648, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 664.824890, -1863.061279, 9.335136, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 655.053833, -1863.061279, 9.335136, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(2913, 650.921752, -1858.519287, 5.740142, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 8037, "vgssmulticarprk", "vgsSstonewall01", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 654.473327, -1863.061279, 9.335136, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(1568, 651.015258, -1858.512695, 3.170130, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 8037, "vgssmulticarprk", "vgsSstonewall01", 0xFFFFFFFF);
    SetDynamicObjectMaterial(brgstxt, 1, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    brgstxt = CreateDynamicObject(18880, 665.315307, -1863.061279, 9.335136, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 7, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 8, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 10, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 657.500671, -1862.065429, 9.285167, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 662.500488, -1862.065429, 9.285167, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 662.270202, -1863.036621, 10.275172, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 657.479858, -1863.036621, 10.275172, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 657.479858, -1863.036621, 11.265171, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 662.269531, -1863.036621, 11.265171, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 664.346923, -1862.050537, 6.345149, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 655.457031, -1862.050537, 6.345149, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18996, "mattextures", "sampblack", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 651.464233, -1877.486938, 8.889232, 0.000000, 179.999984, -179.999755, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 657.754333, -1877.486938, 8.889232, 0.000000, 179.999984, -179.999755, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 651.414184, -1869.597045, 8.889232, 0.000000, 179.999984, -179.999755, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 655.856201, -1875.535034, 13.245160, 90.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 655.856201, -1870.545532, 13.235158, 90.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 655.736083, -1868.564208, 11.255120, 0.000000, 0.000007, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 660.395812, -1873.015869, 11.235118, 0.000000, 0.000007, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 645.856384, -1875.535034, 9.085150, 90.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 645.876403, -1871.682373, 9.045149, 90.000000, 180.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 641.360473, -1877.549682, 6.985154, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 641.360473, -1869.679321, 6.055144, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 641.353454, -1877.486938, 8.889232, 0.000000, 179.999984, -179.999755, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(3875, 641.353454, -1869.656982, 8.889232, 0.000000, 179.999984, -179.999755, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19297, "matlights", "invisible", 0x00000000);
    brgstxt = CreateDynamicObject(18766, 638.367004, -1873.502441, 9.045149, 90.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(19929, 640.468078, -1873.610839, 9.592022, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18996, "mattextures", "sampblack", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 6284, "bev_law2", "pierwin08b_law", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 636.369812, -1869.679321, 6.055144, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 642.714599, -1869.642944, 10.080002, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 647.224731, -1869.642944, 10.080002, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 644.924560, -1869.642944, 10.080002, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 642.693969, -1877.633544, 10.080002, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 646.133666, -1877.633544, 10.080002, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 649.593017, -1877.633544, 10.080002, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 653.313110, -1877.633544, 10.080002, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 656.763000, -1877.633544, 10.080002, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(3850, 658.513244, -1877.633544, 10.080002, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3615, "beachhut", "asanmonbhut2", 0x00000000);
    brgstxt = CreateDynamicObject(19353, 661.372131, -1862.576660, 10.825149, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "Putri Deli", 100, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(19353, 658.252075, -1862.616699, 10.825164, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "Beach Club", 100, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(18762, 651.252380, -1865.677124, 9.835177, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1408, "break_fence1", "CJ_W_wood", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 652.489929, -1859.810302, 3.959589, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 8037, "vgssmulticarprk", "vgsSstonewall01", 0x00000000);
    brgstxt = CreateDynamicObject(11744, 650.994079, -1858.103393, 6.626007, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14629, "ab_chande", "ab_goldpipe", 0x00000000);
    brgstxt = CreateDynamicObject(11744, 650.994079, -1858.103393, 6.656007, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2895, "law_coffinfl", "hot_flowers1", 0x00000000);
    brgstxt = CreateDynamicObject(11744, 650.994079, -1858.103393, 6.636007, 0.000000, 0.000000, -48.100013, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 4830, "airport2", "bevflower2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 652.489929, -1858.820800, 3.959589, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 8037, "vgssmulticarprk", "vgsSstonewall01", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 652.489929, -1857.821533, 3.959589, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 8037, "vgssmulticarprk", "vgsSstonewall01", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 651.489807, -1859.810302, 3.959589, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 8037, "vgssmulticarprk", "vgsSstonewall01", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 650.489501, -1859.810302, 3.959589, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 8037, "vgssmulticarprk", "vgsSstonewall01", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 649.489746, -1859.810302, 3.959589, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 8037, "vgssmulticarprk", "vgsSstonewall01", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 649.489746, -1858.810302, 3.959589, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 8037, "vgssmulticarprk", "vgsSstonewall01", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 649.489746, -1857.820434, 3.959589, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 8037, "vgssmulticarprk", "vgsSstonewall01", 0x00000000);
    brgstxt = CreateDynamicObject(1568, 652.535522, -1859.853881, 3.440123, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 8037, "vgssmulticarprk", "vgsSstonewall01", 0xFFFFFFFF);
    SetDynamicObjectMaterial(brgstxt, 1, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    brgstxt = CreateDynamicObject(1568, 649.484680, -1859.853881, 3.450123, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 8037, "vgssmulticarprk", "vgsSstonewall01", 0xFFFFFFFF);
    SetDynamicObjectMaterial(brgstxt, 1, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 16639, "a51_labs", "dam_terazzo", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 637.308776, -1842.678833, 6.396628, 89.999992, 1593.909179, -63.909324, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 637.438659, -1842.678833, 6.526630, 89.999992, 1593.909179, -63.909324, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 637.578796, -1842.678833, 6.666635, 89.999992, 1593.909179, -63.909324, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 637.698913, -1842.678833, 6.806638, 89.999992, 1593.909179, -63.909324, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 640.119812, -1841.149047, 6.946638, 89.999992, 1773.902954, -63.903083, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 639.989929, -1841.149047, 7.076640, 89.999992, 1773.902954, -63.903083, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 639.849792, -1841.149047, 7.216643, 89.999992, 1773.902954, -63.903083, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 639.729675, -1841.149047, 7.356647, 89.999992, 1773.902954, -63.903083, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 639.849548, -1841.149047, 7.496652, 89.999992, 1773.906127, -63.906196, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 639.989929, -1841.149047, 7.626654, 89.999992, 1773.906127, -63.906196, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 639.628112, -1841.149047, 6.136668, 89.999992, 1773.906127, -63.906196, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 639.628112, -1841.149047, 6.266671, 89.999992, 1773.906127, -63.906196, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 639.467956, -1841.149047, 5.996666, 89.999992, 1773.906127, -63.906196, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 639.628112, -1841.149047, 5.866663, 89.999992, 1773.906127, -63.906196, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 637.906921, -1841.173706, 5.476091, 0.000006, 1079.149414, -89.149093, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 637.766784, -1841.171020, 5.335947, 0.000006, 1079.149414, -89.149093, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 639.798278, -1841.149047, 5.736660, 89.999992, 1773.906127, -63.906196, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 638.187194, -1841.160278, 5.756073, 0.000006, 1079.149414, -89.149093, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 638.328063, -1841.155395, 5.896056, 0.000006, 1079.149414, -89.149093, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 638.047058, -1841.166625, 5.626083, 0.000006, 1079.149414, -89.149093, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 639.798278, -1841.149047, 5.596658, 89.999992, 1773.906127, -63.906196, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 638.748718, -1841.162475, 6.466454, 0.000006, 1079.149414, -89.149093, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 638.608093, -1841.119750, 6.255733, 0.000006, 1079.149414, -89.149093, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 639.354675, -1841.615356, 5.473148, -0.000000, -0.000009, 179.999603, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18081, "cj_barb", "CJ_TILES_5", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 639.344665, -1842.225708, 5.493147, -0.000000, -0.000009, 179.999603, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18081, "cj_barb", "CJ_TILES_5", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 638.467956, -1841.138549, 6.055895, 0.000006, 1079.149414, -89.149093, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 639.608093, -1841.149047, 5.466654, 89.999992, 1773.906127, -63.906196, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 639.407897, -1841.149047, 5.346652, 89.999992, 1773.906127, -63.906196, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 639.207702, -1841.149047, 5.226648, 89.999992, 1773.906127, -63.906196, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 639.017517, -1841.149047, 5.086645, 89.999992, 1773.906127, -63.906196, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 639.725036, -1841.625366, 5.463149, -0.000000, 0.000005, 179.999603, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18081, "cj_barb", "CJ_TILES_5", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 639.715026, -1842.235717, 5.453148, -0.000000, 0.000005, 179.999603, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18081, "cj_barb", "CJ_TILES_5", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 663.341186, -1841.119750, 6.396628, 89.999992, 1747.848388, -37.848556, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 663.211303, -1841.119750, 6.526630, 89.999992, 1747.848388, -37.848556, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 663.071166, -1841.119750, 6.666635, 89.999992, 1747.848388, -37.848556, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 662.951049, -1841.119750, 6.806638, 89.999992, 1747.848388, -37.848556, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 660.530151, -1842.649536, 6.946638, 89.999992, 1927.846435, -37.846569, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 660.660034, -1842.649536, 7.076640, 89.999992, 1927.846435, -37.846569, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 660.800170, -1842.649536, 7.216643, 89.999992, 1927.846435, -37.846569, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 660.920288, -1842.649536, 7.356647, 89.999992, 1927.846435, -37.846569, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 660.800415, -1842.649536, 7.496652, 89.999992, 1927.847412, -37.847564, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 660.660034, -1842.649536, 7.626654, 89.999992, 1927.847412, -37.847564, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 661.021850, -1842.649536, 6.136668, 89.999992, 1927.847412, -37.847564, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 661.021850, -1842.649536, 6.266671, 89.999992, 1927.847412, -37.847564, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 661.182006, -1842.649536, 5.996666, 89.999992, 1927.847412, -37.847564, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 661.021850, -1842.649536, 5.866663, 89.999992, 1927.847412, -37.847564, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 662.743041, -1842.624877, 5.476091, -0.000000, 1079.149414, 90.850868, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 662.883178, -1842.627563, 5.335947, -0.000000, 1079.149414, 90.850868, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 660.851684, -1842.649536, 5.736660, 89.999992, 1927.847412, -37.847564, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 662.462768, -1842.638305, 5.756073, -0.000000, 1079.149414, 90.850868, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 662.321899, -1842.643188, 5.896056, -0.000000, 1079.149414, 90.850868, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 662.602905, -1842.631958, 5.626083, -0.000000, 1079.149414, 90.850868, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 660.851684, -1842.649536, 5.596658, 89.999992, 1927.847412, -37.847564, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 661.901245, -1842.636108, 6.466454, -0.000000, 1079.149414, 90.850868, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 662.041870, -1842.678833, 6.255733, -0.000000, 1079.149414, 90.850868, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 661.295288, -1842.183227, 5.473148, -0.000007, -0.000016, -0.000434, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18081, "cj_barb", "CJ_TILES_5", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 661.305297, -1841.572875, 5.493147, -0.000007, -0.000016, -0.000434, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18081, "cj_barb", "CJ_TILES_5", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 662.182006, -1842.660034, 6.055895, -0.000000, 1079.149414, 90.850868, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 661.041870, -1842.649536, 5.466654, 89.999992, 1927.847412, -37.847564, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 661.242065, -1842.649536, 5.346652, 89.999992, 1927.847412, -37.847564, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 661.442260, -1842.649536, 5.226648, 89.999992, 1927.847412, -37.847564, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(1504, 661.632446, -1842.649536, 5.086645, 89.999992, 1927.847412, -37.847564, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 660.924926, -1842.173217, 5.463149, -0.000007, -0.000001, -0.000434, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18081, "cj_barb", "CJ_TILES_5", 0x00000000);
    brgstxt = CreateDynamicObject(18762, 660.934936, -1841.562866, 5.453148, -0.000007, -0.000001, -0.000434, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18081, "cj_barb", "CJ_TILES_5", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(1813, 657.198669, -1901.229370, 4.079997, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1813, 653.257751, -1901.229370, 4.079997, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1813, 649.176574, -1901.229370, 4.079997, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1813, 645.109680, -1901.229370, 4.079997, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1813, 641.168762, -1901.229370, 4.079997, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1813, 637.087585, -1901.229370, 4.079997, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1820, 652.228393, -1896.584838, 5.700006, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1820, 641.707824, -1896.584838, 5.700006, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1820, 645.278625, -1905.813598, 3.799999, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1820, 651.258850, -1905.813598, 3.799999, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1813, 657.198669, -1905.688964, 3.059998, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1813, 639.287780, -1905.688964, 3.059998, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1255, 655.450195, -1909.375366, 2.392867, 0.000000, 3.299998, -101.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1255, 653.045959, -1908.903198, 2.312999, 0.000000, 0.000000, -70.900001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1255, 643.462707, -1909.361206, 2.213495, -0.000007, 8.099994, -100.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1255, 641.050048, -1908.888549, 2.194000, -0.000007, 7.400001, -70.899986, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(642, 642.293273, -1904.885253, 4.389989, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(642, 648.792724, -1904.885253, 4.389989, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(642, 655.292541, -1904.885253, 4.389989, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3505, 672.094299, -1891.112182, 2.410001, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3505, 637.465209, -1890.131225, 5.080004, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3505, 659.534912, -1876.660766, 5.080004, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3505, 672.094299, -1898.522338, 2.070002, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3505, 679.394653, -1876.660766, 3.350001, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3505, 679.394653, -1886.870727, 2.860001, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3505, 629.623718, -1898.522338, 1.990002, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3505, 629.623718, -1888.602172, 2.510000, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3505, 629.623718, -1873.501708, 2.510000, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1542, 660.224121, -1871.413940, 7.085155, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2500, 660.784179, -1871.844604, 6.655151, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11719, 654.774597, -1870.976684, 6.365149, 0.000000, 0.000000, 154.299987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19830, 659.506103, -1871.242309, 6.595152, 0.000000, 0.000000, -176.499862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2416, 656.869567, -1871.262817, 5.475145, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2151, 652.600219, -1871.305297, 5.305142, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2151, 653.970275, -1871.305297, 5.305142, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1514, 660.220825, -1868.424438, 6.765154, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2437, 661.774841, -1871.422363, 6.535147, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1541, 665.403503, -1883.317382, 6.849123, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1545, 665.368164, -1879.151733, 6.859117, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11686, 671.001586, -1880.580444, 5.259112, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11686, 671.011596, -1882.461303, 5.249113, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19820, 671.141540, -1879.894287, 6.329115, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19820, 671.141540, -1880.104492, 6.329115, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19820, 671.141540, -1880.294677, 6.329115, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19823, 671.154968, -1882.447265, 6.359125, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19823, 671.154968, -1882.667480, 6.359125, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19823, 671.154968, -1882.897583, 6.359125, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1667, 667.973022, -1884.609252, 6.709121, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1667, 667.113159, -1884.609252, 6.709121, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1667, 666.392822, -1884.609252, 6.709121, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1551, 664.399902, -1881.359619, 6.829120, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1551, 664.399902, -1881.069335, 6.829120, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1551, 664.399902, -1880.449584, 6.829120, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 661.276794, -1878.350097, 5.029996, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 661.276794, -1887.432250, 5.039997, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 656.676391, -1877.871337, 4.449996, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 654.716064, -1877.871337, 4.449996, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 652.615722, -1877.871337, 4.459997, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 650.635498, -1877.871337, 4.459997, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 648.445312, -1877.871337, 4.459997, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 646.064697, -1877.871337, 4.459997, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 643.024047, -1877.871337, 4.459997, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 640.033630, -1877.871337, 4.459997, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 649.656921, -1888.072875, 4.559989, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 644.776306, -1888.072875, 4.559989, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 639.185119, -1888.072875, 4.559989, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19279, 662.931152, -1878.231811, 9.801357, -52.399982, 4.899998, 96.899993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3872, 657.298889, -1878.418212, 5.070001, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2229, 664.925659, -1884.743652, 9.280020, 180.000000, 0.000000, 55.899974, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2229, 664.501525, -1877.929565, 9.020015, 180.000000, 0.000000, 114.100151, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1716, 662.851013, -1866.812133, 5.515147, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2027, 666.656372, -1863.983642, 5.632698, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2027, 669.446899, -1863.983642, 5.632698, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2027, 672.456909, -1863.983642, 5.632698, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19608, 638.799560, -1873.593383, 9.545149, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19893, 640.553649, -1873.584228, 10.512034, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2227, 640.367797, -1874.508789, 9.672025, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14820, 640.462463, -1873.590454, 10.552024, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19128, 642.826293, -1875.641235, 9.525150, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19128, 642.826293, -1871.671020, 9.525150, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19128, 646.767150, -1875.641235, 9.525150, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19128, 646.767150, -1871.671020, 9.525150, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14651, 657.428833, -1875.302368, 11.705163, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14651, 657.428833, -1871.341674, 11.705163, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(338, 656.247985, -1874.229248, 10.645155, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(338, 656.247985, -1874.389404, 10.645155, 0.399998, 93.199966, -72.600006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(338, 656.937866, -1869.808959, 10.641930, 0.399998, 93.199966, -72.600006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(338, 656.699340, -1869.883300, 10.640187, 0.399998, 93.199966, -87.600059, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 656.389709, -1871.000122, 13.255173, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 656.360168, -1874.980224, 13.255173, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1825, 653.167663, -1876.160644, 9.555459, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1825, 650.087219, -1876.160644, 9.555459, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(334, 651.034423, -1858.522827, 6.423959, 0.000000, 59.399978, 90.000000, 0, 0, -1, 200.00, 200.00); 

    
    //+arivena
    brgstxt = CreateDynamicObject(18981, -300.771087, 1304.141357, 40.861827, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -288.770385, 1298.647827, 50.860992, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -288.770385, 1288.649291, 50.860992, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -333.950531, 1298.647827, 50.860992, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -333.950531, 1288.649291, 50.860992, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -288.770385, 1288.649291, 45.860992, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -333.950531, 1288.649291, 45.860992, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -333.950531, 1298.649291, 45.860992, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18981, -321.951263, 1304.142333, 40.862827, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -328.680572, 1284.149169, 50.860992, 0.000022, 0.000000, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -319.100585, 1284.149169, 50.860992, 0.000022, 0.000000, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -309.210479, 1284.149169, 50.860992, 0.000022, 0.000000, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -299.660583, 1284.149169, 50.860992, 0.000022, 0.000000, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -294.130584, 1284.149169, 50.860992, 0.000022, 0.000000, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -328.680572, 1284.149169, 45.860992, 0.000022, 0.000000, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -319.100585, 1284.149169, 45.860992, 0.000022, 0.000000, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -309.210479, 1284.149169, 45.860992, 0.000022, 0.000000, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -299.660583, 1284.149169, 45.860992, 0.000022, 0.000000, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -294.130584, 1284.149169, 45.860992, 0.000022, 0.000000, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -328.790374, 1302.017822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -319.160369, 1302.017822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -309.530364, 1302.017822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -299.900360, 1302.017822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -293.590209, 1302.017822, 53.263664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -328.790374, 1298.517822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -319.160369, 1298.517822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -309.530364, 1298.517822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -299.900360, 1298.517822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -293.590209, 1298.517822, 53.263664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -328.790374, 1295.017822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -319.160369, 1295.017822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -309.530364, 1295.017822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -299.900360, 1295.017822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -293.590209, 1295.017822, 53.263664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -313.322570, 1301.000000, 53.279605, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -319.160369, 1291.517822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -309.530364, 1291.517822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -299.900360, 1291.517822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -293.590209, 1291.517822, 53.263664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -303.692565, 1301.000000, 53.279605, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -319.160369, 1288.017822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -309.530364, 1288.017822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -299.900360, 1288.017822, 53.262664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -293.590209, 1288.017822, 53.263664, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -294.802520, 1301.000000, 53.280605, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -319.160369, 1286.316406, 53.261665, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -309.530364, 1286.316406, 53.261665, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -299.900360, 1286.316406, 53.261665, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -293.590209, 1286.316406, 53.262664, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -294.130584, 1287.108886, 43.862037, 90.000022, 0.000000, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -304.130584, 1287.108886, 43.862037, 90.000022, 0.000000, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -314.130584, 1287.108886, 43.862037, 90.000022, 0.000000, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -324.130584, 1287.108886, 43.862037, 90.000022, 0.000000, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -328.490661, 1287.108886, 43.862037, 90.000022, 0.000000, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -314.130584, 1291.988403, 43.862037, 89.999992, -161.565048, -18.435037, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -324.130584, 1291.988403, 43.862037, 89.999992, -161.565048, -18.435037, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -328.490661, 1291.988403, 43.862037, 89.999992, -161.565048, -18.435037, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -312.441131, 1303.441040, 53.702804, 89.999992, 90.000076, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -309.271209, 1303.441040, 53.702804, 89.999992, 90.000076, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -310.791137, 1302.690673, 56.282775, -0.000052, 270.000000, -89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -312.898803, 1303.093139, 57.296154, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -308.868804, 1303.093139, 57.326133, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -310.791137, 1302.690673, 57.272796, -0.000060, 270.000000, -89.999824, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -315.462463, 1303.080322, 53.359756, 0.000000, 90.000076, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -308.252593, 1303.079345, 55.859767, 0.000000, 0.000075, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -310.652496, 1303.079345, 57.859752, 0.000000, 90.000068, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -308.640502, 1303.575317, 55.039546, 89.999992, 180.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -316.012451, 1303.130371, 52.600749, 0.000000, 90.000076, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18980, -304.179687, 1303.119873, 53.048618, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -289.884460, 1299.199951, 53.048618, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18980, -318.609558, 1303.119873, 53.048618, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(1301, -331.402465, 1301.707885, 53.550243, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -332.854339, 1299.199951, 53.048618, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -332.854339, 1294.330444, 53.048618, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -332.854339, 1289.510253, 53.048618, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -332.854339, 1287.410156, 53.048618, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(1301, -291.332458, 1301.707885, 53.550243, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -289.884460, 1294.330444, 53.048618, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -289.884460, 1289.510253, 53.048618, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -289.884460, 1287.410156, 53.048618, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18980, -302.779785, 1285.410278, 53.048618, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18980, -320.439544, 1285.410278, 53.048618, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -310.652496, 1302.949218, 53.209770, 0.000000, 90.000068, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -291.481689, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -290.898101, 1303.219360, 57.611709, -0.100002, 179.999984, 160.299972, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -290.411346, 1302.969116, 57.611610, -0.100000, 179.999984, 144.799972, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -290.001831, 1302.520263, 57.611331, -0.099999, 179.999984, 122.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.786865, 1301.997802, 57.611148, -0.099997, 179.999984, 102.399955, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1301.417480, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -292.081695, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -292.681701, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -293.281707, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -293.881713, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -294.481719, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -295.081726, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -295.681732, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -296.281738, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -296.881744, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -297.481750, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -298.081756, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -298.681762, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -299.281768, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -299.881774, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -300.481781, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -301.081787, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -301.681793, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -302.281799, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -302.881805, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -303.481811, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -304.081817, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -304.681823, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -305.281829, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -305.881835, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -306.481842, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -307.081848, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -307.681854, 1303.302124, 57.611835, 0.000000, 179.999984, -179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1301.570556, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -332.963775, 1302.154174, 57.611709, -0.100019, 179.999984, -109.699974, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -332.713531, 1302.640869, 57.611610, -0.100010, 179.999984, -125.199943, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -332.264678, 1303.050415, 57.611331, -0.099997, 179.999984, -148.000030, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -331.742218, 1303.265380, 57.611148, -0.099987, 179.999984, -167.599899, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -331.161895, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -330.561889, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -329.961883, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -329.361877, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -328.761871, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -328.161865, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -327.561859, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -326.961853, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -326.361846, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -325.761840, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -325.161834, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -324.561828, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -323.961822, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -323.361816, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -322.761810, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -322.161804, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -321.561798, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -320.961791, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -320.361785, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -319.761779, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -319.161773, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -318.561767, 1303.305419, 57.610122, -0.100028, 179.999938, -90.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1300.970581, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1300.370605, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1299.770629, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1299.170654, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1298.570678, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1297.970703, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1297.370727, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1296.770751, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1296.170776, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1295.570800, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1294.970825, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1294.370849, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1293.770874, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1293.170898, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1292.570922, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1291.970947, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1291.370971, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1290.770996, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1290.171020, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1289.571044, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1288.971069, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1288.371093, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1287.771118, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1287.171142, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1286.571166, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -333.046539, 1285.971191, 57.611835, -0.000022, 179.999984, -89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -322.938476, 1301.068969, 53.258594, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 12844, "cos_liquorstore", "b_wtilesreflect", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1300.817504, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1300.217529, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1299.617553, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1299.017578, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1298.417602, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1297.817626, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1297.217651, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1296.617675, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1296.017700, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1295.417724, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1294.817749, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1294.217773, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1293.617797, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1293.017822, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1292.417846, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1291.817871, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1291.217895, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1290.617919, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1290.017944, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1289.417968, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1288.817993, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1288.218017, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1287.618041, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1287.018066, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -289.746826, 1286.418090, 57.610122, -0.100005, 179.999969, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18882, "hugebowls", "woodpanel1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -322.938476, 1301.088989, 53.288589, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 12844, "cos_liquorstore", "b_wtilesreflect", 0x00000000);
    brgstxt = CreateDynamicObject(1301, -331.402465, 1301.707885, 57.560256, 0.000000, 360.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18980, -304.179687, 1303.119873, 58.078582, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18980, -318.609558, 1303.119873, 58.078582, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(1301, -291.342712, 1301.707885, 57.560256, 0.000000, 360.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(1301, -291.342712, 1301.707885, 58.620262, 0.000000, 540.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(1301, -291.341705, 1301.708862, 58.820251, 0.000000, 540.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(1301, -331.402465, 1301.707885, 58.610282, 0.000000, 540.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(1301, -331.402465, 1301.707885, 58.820270, 0.000000, 540.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -289.884460, 1299.199951, 58.060596, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -332.854339, 1299.199951, 58.060596, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -332.854339, 1294.330444, 58.060596, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -332.854339, 1289.510253, 58.060596, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -332.854339, 1287.410156, 58.060596, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -289.884460, 1294.330444, 58.060596, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -289.884460, 1289.510253, 58.060596, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -289.884460, 1287.410156, 58.060596, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18980, -302.779785, 1285.410278, 58.060577, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18980, -320.439544, 1285.410278, 58.060577, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -295.115570, 1300.898559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -304.745574, 1300.898559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -314.375579, 1300.898559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -324.005584, 1300.898559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -327.615600, 1300.899536, 58.139591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -295.115570, 1297.398559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -304.745574, 1297.398559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -314.375579, 1297.398559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -324.005584, 1297.398559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -327.615600, 1297.399536, 58.139591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -295.115570, 1293.898559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -304.745574, 1293.898559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -314.375579, 1293.898559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -324.005584, 1293.898559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -327.615600, 1293.899536, 58.139591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -295.115570, 1290.398559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -304.745574, 1290.398559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -314.375579, 1290.398559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -324.005584, 1290.398559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -327.615600, 1290.399536, 58.139591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -295.115570, 1286.898559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -304.745574, 1286.898559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -314.375579, 1286.898559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -324.005584, 1286.898559, 58.138591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -327.615600, 1286.899536, 58.139591, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19379, -295.115570, 1297.398925, 58.141590, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19379, -304.745574, 1297.398925, 58.141590, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19379, -314.375579, 1297.398925, 58.141590, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19379, -324.005584, 1297.398925, 58.141590, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19379, -327.715576, 1297.398925, 58.142589, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19379, -295.115570, 1290.807495, 58.142589, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19379, -304.745574, 1290.807495, 58.142589, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19379, -314.375579, 1290.807495, 58.142589, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19379, -324.005584, 1290.807495, 58.142589, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(19379, -327.715576, 1290.807495, 58.143589, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3881, "apsecurity_sfxrf", "ws_rooftarmac2", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -328.262390, 1285.501953, 55.588588, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -318.262390, 1285.501953, 55.588588, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -313.262390, 1285.501953, 55.588588, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -308.187744, 1285.851684, 55.288589, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -307.341644, 1285.443725, 56.138618, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -304.101623, 1285.443725, 56.138618, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -303.287750, 1285.851684, 55.288589, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -305.817687, 1285.851684, 56.158569, 180.000000, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -305.817687, 1285.851684, 57.498542, 180.000000, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -302.712493, 1285.501953, 55.588588, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -289.952484, 1285.501953, 55.588588, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -290.537750, 1285.851684, 55.288589, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -292.937896, 1285.851684, 57.498542, 180.000000, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -295.367797, 1285.851684, 55.288589, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -292.077880, 1285.851684, 55.288589, 89.999992, 89.999992, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -293.747924, 1285.851684, 55.288589, 89.999992, 89.999992, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -292.937896, 1285.851684, 53.618560, 180.000000, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -297.387725, 1285.851684, 55.288589, 89.999992, 89.999992, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -299.787872, 1285.851684, 57.498542, 0.000000, 269.999969, 90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -302.217773, 1285.851684, 55.288589, 89.999992, 89.999992, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -298.927856, 1285.851684, 55.288589, 89.999992, 90.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -300.597900, 1285.851684, 55.288589, 89.999992, 90.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -299.787872, 1285.851684, 53.618560, 0.000000, 269.999969, 90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -295.952453, 1285.501953, 55.588588, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -296.802581, 1285.502929, 55.588588, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19912, -290.590270, 1285.541992, 55.846527, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x80FFFFFF);
    SetDynamicObjectMaterial(brgstxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 5, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -316.012451, 1303.060302, 52.600749, 0.000000, 90.000076, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(2434, -314.696014, 1301.322753, 53.288597, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(2435, -315.816009, 1301.486816, 53.288597, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(2435, -316.746002, 1301.486816, 53.288597, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(2435, -317.675964, 1301.486816, 53.288597, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(2367, -314.808929, 1299.199218, 53.302608, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    brgstxt = CreateDynamicObject(2367, -314.808929, 1297.099243, 53.302608, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    brgstxt = CreateDynamicObject(2367, -314.807922, 1296.080078, 53.303607, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    brgstxt = CreateDynamicObject(2434, -314.861114, 1294.968505, 53.288597, 0.000014, 0.000000, 359.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(2435, -315.791137, 1294.968505, 53.288597, 0.000014, 0.000000, 359.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(1897, -318.218750, 1297.397827, 54.385604, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -318.209197, 1301.319580, 56.288616, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.209197, 1298.920043, 52.688674, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.208190, 1297.930541, 52.689674, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -318.208190, 1295.520263, 56.279647, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.207183, 1295.892700, 55.946712, -63.499988, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.207183, 1300.912475, 55.946712, -63.499988, -0.000017, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -318.209197, 1301.319580, 52.788616, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -318.208190, 1295.520263, 52.779647, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.206176, 1298.160766, 56.819675, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.205169, 1298.160766, 57.259674, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1897, -318.218750, 1299.607177, 54.385604, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(1897, -318.218750, 1299.767333, 55.348583, 180.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(1897, -318.218750, 1297.257080, 55.348583, 180.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(1897, -318.218750, 1298.348022, 55.885597, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(1897, -318.218750, 1297.690917, 55.801284, 116.300056, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(1897, -318.218750, 1299.397949, 55.647865, 62.700038, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(1742, -318.256744, 1295.577514, 56.008541, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    brgstxt = CreateDynamicObject(1742, -318.255737, 1294.897216, 56.009540, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    brgstxt = CreateDynamicObject(1742, -318.258758, 1295.576538, 54.374565, 0.000029, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    brgstxt = CreateDynamicObject(1742, -318.257751, 1294.896240, 54.375564, 0.000029, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    brgstxt = CreateDynamicObject(19938, -317.895233, 1297.193969, 54.408588, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19940, -317.897247, 1298.413940, 54.408588, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(2766, -318.084259, 1297.372558, 56.913921, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 2645, "cj_piz_sign", "CJ_PIZZA_MEN1", 0x00000000);
    brgstxt = CreateDynamicObject(2766, -318.084259, 1299.343505, 56.913921, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 2645, "cj_piz_sign", "CJ_PIZZA_MEN2", 0x00000000);
    brgstxt = CreateDynamicObject(2270, -317.625610, 1298.383300, 56.798603, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 2430, "cj_burg_sign", "CJ_BS_MENU1", 0x00000000);
    brgstxt = CreateDynamicObject(19938, -317.897247, 1299.623413, 54.408588, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19938, -317.895233, 1297.193969, 54.378593, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19940, -317.897247, 1298.413940, 54.378593, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19938, -317.897247, 1299.623413, 54.378593, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -318.208068, 1290.822998, 52.878578, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -316.535888, 1291.648559, 56.288585, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -316.535888, 1291.648559, 52.788585, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -314.725830, 1291.648559, 56.288585, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -314.725830, 1291.648559, 52.788585, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -313.208099, 1290.828857, 52.788585, 0.000007, -0.000007, 179.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -313.208099, 1290.828857, 56.288585, 0.000007, -0.000007, 179.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -315.640167, 1290.818725, 54.088584, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(19383, -318.209075, 1288.192871, 55.068595, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -318.208068, 1290.822998, 56.378578, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.207061, 1286.493164, 55.068595, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.206054, 1288.903564, 57.278598, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.205047, 1287.553344, 57.278598, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1499, -318.204742, 1287.446289, 53.308605, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10763, "airport1_sfse", "ws_airportdoors1", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.208038, 1294.083374, 57.238594, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.207031, 1294.083374, 56.628597, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -315.640167, 1290.598388, 54.089584, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(2515, -317.282409, 1291.267456, 54.318595, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(2515, -314.042419, 1291.267456, 54.318595, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(2515, -315.692352, 1291.267456, 54.318595, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(3337, -316.631591, 1291.618286, 52.338619, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(3337, -314.761627, 1291.619262, 52.338619, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(19327, -316.587280, 1291.487548, 55.438583, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    brgstxt = CreateDynamicObject(19327, -314.837310, 1291.488525, 55.438583, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -314.935852, 1291.624145, 56.328571, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18368, "cs_mountaintop", "des_woodrails", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -316.525970, 1291.625122, 56.328571, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18368, "cs_mountaintop", "des_woodrails", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -314.352447, 1285.948364, 56.328586, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -314.352447, 1285.948364, 52.828586, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -313.072448, 1285.947387, 52.828586, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -313.072448, 1285.947387, 56.328586, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15041, "bigsfsave", "ah_ceilpan1", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -318.120910, 1286.741699, 54.708599, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -318.120910, 1289.661865, 54.708599, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -318.119903, 1290.822143, 54.708599, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -318.119903, 1286.741699, 54.458621, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -318.119903, 1289.661865, 54.458621, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -318.118896, 1290.822143, 54.458621, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    brgstxt = CreateDynamicObject(19174, -314.393615, 1286.031982, 56.248607, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2254, "picture_frame_clip", "CJ_PAINTING15", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -319.891784, 1292.272583, 56.338581, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -323.101806, 1292.272583, 56.338581, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -325.502838, 1292.281372, 56.338581, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -319.891784, 1292.274536, 55.078639, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -323.101806, 1292.274536, 55.078639, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -325.500823, 1292.282348, 55.078639, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -326.221740, 1290.766235, 53.008678, 0.000014, -0.000014, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -326.219726, 1290.765258, 56.308620, 0.000014, -0.000007, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -326.219726, 1287.561645, 53.008678, 0.000014, -0.000014, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -326.217712, 1287.561645, 56.308620, 0.000014, -0.000007, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -324.602661, 1287.111206, 56.338581, 0.000000, 0.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -321.392639, 1287.111206, 56.338581, 0.000000, 0.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.991607, 1287.102416, 56.338581, 0.000000, 0.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -324.602661, 1287.109252, 55.078639, 0.000007, 0.000000, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -321.392639, 1287.109252, 55.078639, 0.000007, 0.000000, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.993621, 1287.101440, 55.078639, 0.000007, 0.000000, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.350036, 1291.389038, 54.588634, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.350036, 1290.369018, 54.588634, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -319.048980, 1289.646362, 53.838607, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(3089, -319.009246, 1289.746459, 54.678585, 0.000000, 0.000000, 76.099998, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(2525, -319.592712, 1291.622802, 53.348602, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14651, "ab_trukstpd", "bbar_poollamp1", 0x00000000);
    brgstxt = CreateDynamicObject(19828, -319.795501, 1292.179321, 54.628593, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19828, -319.635498, 1292.179321, 54.628593, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -320.750030, 1291.389038, 54.588634, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -320.750030, 1290.369018, 54.588634, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -321.448974, 1289.646362, 53.838607, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(3089, -321.409240, 1289.746459, 54.678585, 0.000007, 0.000000, 76.099975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(2525, -321.992706, 1291.622802, 53.348602, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14651, "ab_trukstpd", "bbar_poollamp1", 0x00000000);
    brgstxt = CreateDynamicObject(19828, -322.195495, 1292.179321, 54.628593, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19828, -322.035491, 1292.179321, 54.628593, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -323.120056, 1291.389038, 54.588634, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -323.120056, 1290.369018, 54.588634, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -323.819000, 1289.646362, 53.838607, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(3089, -323.779266, 1289.746459, 54.678585, 0.000014, 0.000003, 76.099967, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(2525, -324.362731, 1291.622802, 53.348602, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14651, "ab_trukstpd", "bbar_poollamp1", 0x00000000);
    brgstxt = CreateDynamicObject(19828, -324.565521, 1292.179321, 54.628593, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19828, -324.405517, 1292.179321, 54.628593, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -325.460144, 1291.389038, 54.588634, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -325.460144, 1290.369018, 54.588634, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -313.322570, 1297.500000, 53.279605, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -303.692565, 1297.500000, 53.279605, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -294.802520, 1297.500000, 53.280605, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -313.322570, 1294.000000, 53.279605, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -303.692565, 1294.000000, 53.279605, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -294.802520, 1294.000000, 53.280605, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -313.322570, 1290.500000, 53.279605, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -303.692565, 1290.500000, 53.279605, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -294.802520, 1290.500000, 53.280605, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -313.322570, 1287.448120, 53.278606, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -303.692565, 1287.448120, 53.278606, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -294.802520, 1287.448120, 53.279605, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(2643, -305.314239, 1294.242309, 53.805557, 270.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "orange", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -302.748779, 1294.273315, 53.785541, 0.000000, 270.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "orange", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -302.748779, 1294.925903, 53.785541, 0.000000, 270.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "orange", 0x00000000);
    brgstxt = CreateDynamicObject(2643, -297.274383, 1294.242309, 53.805557, -89.999992, 270.000000, 89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "orange", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -299.908813, 1294.273315, 53.785541, 0.000007, 270.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "orange", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -299.908813, 1294.925903, 53.785541, 0.000007, 270.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "orange", 0x00000000);
    brgstxt = CreateDynamicObject(3498, -304.991180, 1294.199218, 50.405570, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3595, "dingbat01_la", "bambowal1_LA", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 3595, "dingbat01_la", "bambowal1_LA", 0x00000000);
    brgstxt = CreateDynamicObject(3498, -297.651184, 1294.199218, 50.405570, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3595, "dingbat01_la", "bambowal1_LA", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 3595, "dingbat01_la", "bambowal1_LA", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -303.319702, 1294.003784, 53.174564, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3595, "dingbat01_la", "bambowal1_LA", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -300.119689, 1294.003784, 53.174564, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3595, "dingbat01_la", "bambowal1_LA", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -299.319702, 1294.005737, 53.173564, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3595, "dingbat01_la", "bambowal1_LA", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -303.319702, 1294.402221, 53.174564, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3595, "dingbat01_la", "bambowal1_LA", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -300.119689, 1294.402221, 53.174564, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3595, "dingbat01_la", "bambowal1_LA", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -299.319702, 1294.404174, 53.173564, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3595, "dingbat01_la", "bambowal1_LA", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -300.400909, 1294.205932, 54.158538, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 5397, "barrio1_lae", "dirtgaz64b", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -302.300994, 1294.205932, 54.159538, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 5397, "barrio1_lae", "dirtgaz64b", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -302.838806, 1294.583618, 54.335548, 0.000022, 270.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6985, "vgnfremnt2", "striplightsorange_256", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -299.817840, 1294.584594, 54.336547, 0.000022, 270.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 6985, "vgnfremnt2", "striplightsorange_256", 0x00000000);
    brgstxt = CreateDynamicObject(2001, -304.496643, 1294.165283, 54.403736, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(633, -304.550537, 1294.197509, 55.409397, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 4003, "cityhall_tr_lan", "planta256", 0x00000000);
    brgstxt = CreateDynamicObject(2001, -301.416595, 1294.165283, 54.403736, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(633, -301.470489, 1294.197509, 55.409397, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 4003, "cityhall_tr_lan", "planta256", 0x00000000);
    brgstxt = CreateDynamicObject(2001, -298.236480, 1294.165283, 54.403736, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(633, -298.290374, 1294.197509, 55.409397, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 4003, "cityhall_tr_lan", "planta256", 0x00000000);
    brgstxt = CreateDynamicObject(2808, -305.650726, 1302.125976, 53.995567, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2808, -303.570800, 1302.125976, 53.995567, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2808, -302.750762, 1301.492309, 53.996566, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2808, -306.470489, 1301.492309, 53.996566, -0.000022, 0.000000, -89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -306.247741, 1301.222900, 53.235565, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -306.247741, 1300.352661, 53.235565, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -306.246734, 1300.132568, 53.236564, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -302.997406, 1301.222900, 53.235565, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -302.997406, 1300.352661, 53.235565, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -302.996398, 1300.132568, 53.236564, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -303.857330, 1301.923583, 53.235565, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -305.357421, 1301.923583, 53.235565, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -304.557403, 1301.924560, 53.237564, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(1819, -305.086242, 1299.895507, 53.365543, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3922, "bistro", "Marble2", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(1575, -302.978912, 1301.936279, 54.078460, -106.999992, 0.000000, -48.000034, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1575, -304.775909, 1302.097412, 54.072074, -106.999992, 0.000000, -1.200029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2808, -299.460845, 1302.125976, 53.995567, 0.000000, -0.000029, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2808, -297.380920, 1302.125976, 53.995567, 0.000000, -0.000029, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2808, -296.560882, 1301.492309, 53.996566, 0.000037, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2808, -300.280609, 1301.492309, 53.996566, -0.000037, 0.000000, -89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -300.057861, 1301.222900, 53.235565, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -300.057861, 1300.352661, 53.235565, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -300.056854, 1300.132568, 53.236564, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -296.807525, 1301.222900, 53.235565, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -296.807525, 1300.352661, 53.235565, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -296.806518, 1300.132568, 53.236564, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -297.667449, 1301.923583, 53.235565, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -299.167541, 1301.923583, 53.235565, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -298.367523, 1301.924560, 53.237564, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(1819, -298.896362, 1299.895507, 53.365543, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3922, "bistro", "Marble2", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(1575, -296.789031, 1301.936279, 54.078460, -72.999977, 179.999954, 131.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1575, -298.586029, 1302.097412, 54.072074, -72.999992, 179.999938, 178.799880, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2808, -298.720245, 1286.734619, 53.995567, 0.000000, -0.000022, -0.000151, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2808, -300.800170, 1286.734619, 53.995567, 0.000000, -0.000022, -0.000151, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2808, -301.620208, 1287.368286, 53.996566, 0.000029, 0.000000, -90.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2808, -297.900482, 1287.368286, 53.996566, -0.000029, 0.000000, 90.000030, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -298.123229, 1287.637695, 53.235565, 0.000000, 0.000007, 179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -298.123229, 1288.507934, 53.235565, 0.000000, 0.000007, 179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -298.124237, 1288.728027, 53.236564, 0.000000, 0.000014, 179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -301.373565, 1287.637695, 53.235565, 0.000000, 0.000014, 179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -301.373565, 1288.507934, 53.235565, 0.000000, 0.000014, 179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -301.374572, 1288.728027, 53.236564, 0.000000, 0.000022, 179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -300.513641, 1286.937011, 53.235565, 0.000000, 0.000022, 179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -299.013549, 1286.937011, 53.235565, 0.000000, 0.000022, 179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -299.813568, 1286.936035, 53.237564, 0.000000, 0.000022, 179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(1819, -299.284729, 1288.965087, 53.365543, 0.000000, 0.000007, 179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3922, "bistro", "Marble2", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(1575, -301.392059, 1286.924316, 54.078460, -72.999977, 179.999969, -48.000038, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1575, -299.595062, 1286.763183, 54.072074, -72.999992, 179.999969, -1.200072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2808, -291.930603, 1286.734619, 53.995567, 0.000000, -0.000014, -0.000151, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2808, -294.010528, 1286.734619, 53.995567, 0.000000, -0.000014, -0.000151, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2808, -294.830566, 1287.368286, 53.996566, 0.000022, 0.000000, -90.000038, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2808, -291.110839, 1287.368286, 53.996566, -0.000022, 0.000000, 90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3306, "cunte_house1", "des_ntwnwall1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -291.333587, 1287.637695, 53.235565, 0.000000, 0.000000, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -291.333587, 1288.507934, 53.235565, 0.000000, 0.000000, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -291.334594, 1288.728027, 53.236564, 0.000000, 0.000007, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -294.583923, 1287.637695, 53.235565, 0.000000, 0.000007, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -294.583923, 1288.507934, 53.235565, 0.000000, 0.000007, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -294.584930, 1288.728027, 53.236564, 0.000000, 0.000014, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -293.723999, 1286.937011, 53.235565, 0.000000, 0.000014, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -292.223907, 1286.937011, 53.235565, 0.000000, 0.000014, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2293, -293.023925, 1286.936035, 53.237564, 0.000000, 0.000014, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(1819, -292.495086, 1288.965087, 53.365543, 0.000000, 0.000000, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3922, "bistro", "Marble2", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(1575, -294.602416, 1286.924316, 54.078460, -72.999977, 179.999984, -48.000022, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1575, -292.805419, 1286.763183, 54.072074, -72.999992, 180.000000, -1.200047, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -294.223754, 1301.894165, 54.526523, 89.999992, 270.000000, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -294.222747, 1301.674072, 54.527523, 89.999992, 270.000000, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -294.223754, 1301.894165, 53.406581, 89.999992, 270.000000, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -294.222747, 1301.674072, 53.407581, 89.999992, 270.000000, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -293.742919, 1301.856567, 54.516529, 0.000000, 179.999984, -179.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(11710, -294.459167, 1301.787841, 54.616542, 90.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19638, "fruitcrates1", "oranges1", 0x00000000);
    brgstxt = CreateDynamicObject(2240, -301.522918, 1302.015136, 53.855525, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2240, -307.592712, 1302.015136, 53.855525, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2240, -302.672698, 1286.524658, 53.855525, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2240, -296.332702, 1286.524658, 53.855525, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19604, -289.892883, 1297.849853, 52.906551, 270.000000, 720.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 16646, "a51_alpha", "waterdirty256", 0x00000000);
    brgstxt = CreateDynamicObject(19604, -289.892883, 1292.880493, 52.906551, 270.000000, 720.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 16646, "a51_alpha", "waterdirty256", 0x00000000);
    brgstxt = CreateDynamicObject(19604, -289.892883, 1287.880493, 52.906551, 270.000000, 720.000000, 990.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 16646, "a51_alpha", "waterdirty256", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -302.157714, 1285.851684, 55.288589, 89.999992, 89.999992, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -290.577758, 1285.851684, 55.288589, 89.999992, 89.999992, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19604, -332.892852, 1297.759765, 52.906551, 270.000000, 720.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 16646, "a51_alpha", "waterdirty256", 0x00000000);
    brgstxt = CreateDynamicObject(19604, -305.302612, 1303.170166, 52.906551, 270.000000, 720.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 16646, "a51_alpha", "waterdirty256", 0x00000000);
    brgstxt = CreateDynamicObject(19604, -300.302581, 1303.170166, 52.906551, 270.000000, 720.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 16646, "a51_alpha", "waterdirty256", 0x00000000);
    brgstxt = CreateDynamicObject(19604, -295.312805, 1303.170166, 52.906551, 270.000000, 720.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 16646, "a51_alpha", "waterdirty256", 0x00000000);
    brgstxt = CreateDynamicObject(19604, -332.892852, 1292.759887, 52.906551, 270.000000, 720.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 16646, "a51_alpha", "waterdirty256", 0x00000000);
    brgstxt = CreateDynamicObject(19604, -332.892852, 1287.769653, 52.906551, 270.000000, 720.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 16646, "a51_alpha", "waterdirty256", 0x00000000);
    brgstxt = CreateDynamicObject(19604, -327.352874, 1303.159423, 52.906551, 270.000000, 720.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 16646, "a51_alpha", "waterdirty256", 0x00000000);
    brgstxt = CreateDynamicObject(19604, -322.352935, 1303.159423, 52.906551, 270.000000, 720.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 16646, "a51_alpha", "waterdirty256", 0x00000000);
    brgstxt = CreateDynamicObject(19604, -317.371978, 1303.159423, 52.906551, 270.000000, 720.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 16646, "a51_alpha", "waterdirty256", 0x00000000);
    brgstxt = CreateDynamicObject(2724, -291.575469, 1298.352661, 53.916545, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2724, -292.895507, 1297.062255, 53.916545, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2724, -291.575469, 1295.762207, 53.916545, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(1825, -291.508758, 1297.045654, 53.366542, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(2724, -291.575469, 1293.431762, 53.916545, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2724, -292.895507, 1292.141357, 53.916545, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2724, -291.575469, 1290.841308, 53.916545, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(1825, -291.508758, 1292.124755, 53.366542, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(1827, -305.292755, 1300.213623, 58.045547, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(1827, -294.702972, 1300.213623, 58.045547, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(1827, -300.152954, 1300.213623, 58.045547, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(1827, -305.292755, 1288.142822, 58.045547, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(1827, -294.702972, 1288.142822, 58.045547, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(1827, -300.152954, 1288.142822, 58.045547, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(2073, -308.240844, 1300.686157, 57.775524, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(2073, -312.220672, 1300.686157, 57.845516, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(2073, -308.240844, 1293.896118, 57.775524, 0.000000, 0.000052, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(2073, -312.220672, 1293.896118, 57.845516, 0.000000, 0.000082, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(1827, -292.772949, 1293.973388, 58.045547, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(1734, -298.203155, 1293.901367, 58.057331, 0.000000, 0.000052, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(1734, -298.953155, 1292.821533, 58.057331, 0.000000, 0.000052, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(1734, -298.203155, 1295.301513, 58.057331, 0.000000, 0.000052, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(2073, -298.680908, 1294.606201, 57.355522, 0.000000, 0.000052, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(2073, -299.210906, 1293.636474, 57.355522, 0.000000, 0.000052, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(1734, -299.943084, 1294.351562, 58.057331, 0.000000, 0.000052, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10398, "countryclub_sfs", "hc_wall2", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -313.778747, 1296.157348, 56.797149, 89.999992, 178.299148, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -313.778747, 1297.537353, 56.797149, 89.999992, 178.299148, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(2073, -313.879394, 1295.255615, 57.285522, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -313.778747, 1298.957397, 56.797149, 89.999992, 178.299163, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -313.778747, 1300.317260, 56.797149, 89.999992, 178.299163, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -313.863220, 1301.203735, 56.797149, 89.999992, 207.660064, -87.660072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -314.184387, 1301.527832, 56.798149, 89.999992, 237.550781, -87.550781, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -314.624694, 1301.646850, 56.799148, 89.999992, 267.459686, -87.459648, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -313.863220, 1295.300903, 56.799148, -89.999992, 211.156600, 91.156532, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -314.184387, 1294.976806, 56.798149, -89.999992, 241.112213, 91.112083, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -314.624694, 1294.857910, 56.797149, -89.999992, 271.069305, 91.069198, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -315.048889, 1294.877441, 56.797149, 89.999992, 267.979522, -87.979476, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -315.488189, 1294.877441, 56.797149, 89.999992, 264.929290, -84.929252, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -315.048828, 1301.607543, 56.797149, 89.999992, 265.836730, -85.836669, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -315.488189, 1301.607543, 56.797149, 89.999992, 266.236083, -86.235992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -316.780181, 1296.157348, 56.799148, 89.999992, 176.308975, -86.308921, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -316.780181, 1297.537353, 56.799148, 89.999992, 176.308975, -86.308921, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(2073, -316.679412, 1295.255615, 57.285522, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -316.780181, 1298.957397, 56.799148, 89.999992, 175.110610, -85.110557, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(18066, -316.780181, 1300.317260, 56.799148, 89.999992, 175.110610, -85.110557, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -316.681793, 1301.203735, 56.799148, -89.999992, 33.793304, 93.793243, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -316.360626, 1301.527832, 56.798149, -89.999992, 63.756290, 93.756248, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -315.920318, 1301.646850, 56.797149, -89.999992, 94.064704, 94.064689, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -316.681793, 1295.300903, 56.797149, 89.999992, 746.116210, -86.116203, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -316.360626, 1294.976806, 56.798149, 89.999992, 776.054504, -86.054428, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -315.920318, 1294.857910, 56.799148, 89.999992, 447.201416, -87.201423, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 1239, "icons6", "lyellow32", 0x00000000);
    brgstxt = CreateDynamicObject(2073, -313.879394, 1301.207031, 57.285522, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(2073, -316.679412, 1301.207031, 57.285522, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(2066, -312.629882, 1290.462280, 53.365543, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(2066, -312.629882, 1291.302856, 53.365543, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -313.074981, 1290.450439, 55.015567, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 16640, "a51", "a51_cardreader", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -313.074981, 1291.290893, 55.015567, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 16640, "a51", "a51_cardreader", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -304.074920, 1283.192260, 53.298301, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(970, -308.602600, 1282.711425, 53.893714, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(970, -308.602600, 1280.612792, 53.893714, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(970, -306.522674, 1278.492187, 53.893714, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(970, -302.372772, 1278.492187, 53.893714, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(970, -298.222778, 1278.492187, 53.893714, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(970, -294.072662, 1278.492187, 53.893714, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(970, -291.982604, 1278.492187, 53.893714, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(970, -289.902770, 1282.711425, 53.893714, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(970, -289.902770, 1280.612792, 53.893714, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -294.444946, 1283.192260, 53.298301, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -304.073913, 1279.861450, 53.299301, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -294.443939, 1279.861450, 53.299301, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14785, "gen_offtrackint", "otb_rooftile1", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -294.885009, 1283.192260, 57.538333, 0.000029, 90.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3595, "dingbat01_la", "bambowal1_LA", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -301.305023, 1283.192260, 57.538333, 0.000029, 90.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3595, "dingbat01_la", "bambowal1_LA", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -294.884002, 1280.532348, 57.539333, 0.000045, 90.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3595, "dingbat01_la", "bambowal1_LA", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -301.304016, 1280.532348, 57.539333, 0.000045, 90.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 3595, "dingbat01_la", "bambowal1_LA", 0x00000000);
    brgstxt = CreateDynamicObject(19087, -301.997009, 1281.782348, 57.998611, -80.999984, 0.000048, 0.000048, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19087, -301.997009, 1284.093750, 58.364669, -80.999984, 0.000048, 0.000048, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19087, -301.997009, 1285.022583, 58.511707, -80.999984, 0.000048, 0.000048, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19087, -290.647033, 1281.782348, 57.998611, -80.999984, 0.000096, 0.000096, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19087, -290.647033, 1284.093750, 58.364669, -80.999984, 0.000096, 0.000096, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19087, -290.647033, 1285.022583, 58.511707, -80.999984, 0.000096, 0.000096, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19087, -296.566986, 1281.782348, 57.998611, -80.999984, 0.000146, 0.000144, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19087, -296.566986, 1284.093750, 58.364669, -80.999984, 0.000146, 0.000144, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19087, -296.566986, 1285.022583, 58.511707, -80.999984, 0.000146, 0.000144, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(1762, -301.441802, 1283.456909, 53.384239, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1762, -301.440795, 1282.296386, 53.385238, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1817, -299.224151, 1282.932006, 53.385238, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    brgstxt = CreateDynamicObject(1762, -297.990722, 1283.277099, 53.384239, -0.000007, 0.000000, -89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1762, -297.991729, 1284.437622, 53.385238, -0.000007, 0.000000, -89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1762, -294.801849, 1283.456909, 53.384239, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1762, -294.800842, 1282.296386, 53.385238, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1817, -292.584197, 1282.932006, 53.385238, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    brgstxt = CreateDynamicObject(1762, -291.350769, 1283.277099, 53.384239, -0.000014, 0.000000, -89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(1762, -291.351776, 1284.437622, 53.385238, -0.000014, 0.000000, -89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 13007, "sw_bankint", "woodfloor1", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2240, -296.392761, 1284.125366, 53.855525, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2707, -301.539794, 1283.532958, 57.395244, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    SetDynamicObjectMaterial(brgstxt, 1, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(2707, -290.989837, 1283.532958, 57.395244, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    SetDynamicObjectMaterial(brgstxt, 1, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(2707, -296.249847, 1283.532958, 57.395244, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    SetDynamicObjectMaterial(brgstxt, 1, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(2707, -298.919769, 1283.532958, 57.395244, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    SetDynamicObjectMaterial(brgstxt, 1, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(2707, -293.359771, 1283.532958, 57.395244, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x80FFFFFF);
    SetDynamicObjectMaterial(brgstxt, 1, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -305.896789, 1278.597167, 54.295223, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -300.896789, 1278.597167, 54.295223, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -295.896789, 1278.597167, 54.295223, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -292.516845, 1278.598144, 54.296222, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(970, -306.302673, 1279.282958, 53.833721, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(970, -302.372772, 1279.282958, 53.833721, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(970, -298.222778, 1279.282958, 53.833721, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(970, -294.072662, 1279.282958, 53.833721, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(970, -292.102630, 1279.282958, 53.833721, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(970, -306.302673, 1278.662353, 53.833721, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(970, -302.372772, 1278.662353, 53.833721, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(970, -298.222778, 1278.662353, 53.833721, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(970, -294.072662, 1278.662353, 53.833721, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(970, -292.102630, 1278.662353, 53.833721, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(2125, -307.296508, 1280.375610, 53.724243, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 14846, "genintintpoliceb", "coptop_blue", 0x00000000);
    brgstxt = CreateDynamicObject(2125, -305.316650, 1280.375610, 53.724243, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 14846, "genintintpoliceb", "coptop_blue", 0x00000000);
    brgstxt = CreateDynamicObject(2125, -303.406616, 1280.375610, 53.724243, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 14846, "genintintpoliceb", "coptop_blue", 0x00000000);
    brgstxt = CreateDynamicObject(2125, -301.236724, 1280.375610, 53.724243, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 14846, "genintintpoliceb", "coptop_blue", 0x00000000);
    brgstxt = CreateDynamicObject(2125, -299.066864, 1280.375610, 53.724243, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 14846, "genintintpoliceb", "coptop_blue", 0x00000000);
    brgstxt = CreateDynamicObject(2125, -296.957000, 1280.375610, 53.724243, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 14846, "genintintpoliceb", "coptop_blue", 0x00000000);
    brgstxt = CreateDynamicObject(2125, -294.997039, 1280.375610, 53.724243, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 14846, "genintintpoliceb", "coptop_blue", 0x00000000);
    brgstxt = CreateDynamicObject(2125, -292.957153, 1280.375610, 53.724243, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 14846, "genintintpoliceb", "coptop_blue", 0x00000000);
    brgstxt = CreateDynamicObject(2125, -290.837097, 1280.375610, 53.724243, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 14846, "genintintpoliceb", "coptop_blue", 0x00000000);
    brgstxt = CreateDynamicObject(19923, -319.036224, 1298.443969, 53.348602, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10763, "airport1_sfse", "ws_airportdoors1", 0x00000000);
    brgstxt = CreateDynamicObject(2133, -319.435394, 1297.066040, 53.277614, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2134, -319.435394, 1299.856201, 53.277614, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2133, -319.435394, 1300.856323, 53.277614, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -299.825866, 1283.223022, 53.884231, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(2685, -299.862243, 1283.237304, 54.004280, 0.000000, 90.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2685, -299.792266, 1283.217285, 54.004280, 0.000000, 90.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -293.165924, 1283.223022, 53.884231, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(2685, -293.202301, 1283.237304, 54.004280, -0.000007, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2685, -293.132324, 1283.217285, 54.004280, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -293.165924, 1288.403808, 53.884231, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(2685, -293.202301, 1288.418090, 54.004280, -0.000014, 90.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2685, -293.132324, 1288.398071, 54.004280, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -300.025909, 1288.403808, 53.884231, 89.999992, 179.999984, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(2685, -300.062286, 1288.418090, 54.004280, -0.000022, 90.000000, -89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2685, -299.992309, 1288.398071, 54.004280, 0.000022, 90.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -298.615753, 1300.444335, 53.884231, 89.999992, 180.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(2685, -298.652130, 1300.458618, 54.004280, -0.000029, 90.000000, -89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2685, -298.582153, 1300.438598, 54.004280, 0.000029, 90.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(11711, -304.765808, 1300.444335, 53.884231, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(2685, -304.802185, 1300.458618, 54.004280, -0.000037, 90.000000, -89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2685, -304.732208, 1300.438598, 54.004280, 0.000037, 90.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(3875, -307.583984, 1284.609252, 48.269165, 0.000000, 132.200027, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(3875, -290.413940, 1284.609252, 48.269165, 0.000000, 132.200027, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(3875, -298.693847, 1284.609252, 48.269165, 0.000000, 132.200027, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 4, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(14394, -310.837280, 1305.312988, 52.484580, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -306.821807, 1305.288452, 50.774162, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -314.871795, 1305.518676, 50.774162, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -314.871795, 1304.778076, 50.774162, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -310.532958, 1302.624145, 53.005546, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -310.813018, 1302.624145, 53.005546, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(2133, -319.435394, 1301.846435, 53.277614, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2133, -319.434387, 1296.435791, 53.276615, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.923004, 1294.099121, 54.448589, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.923004, 1296.079467, 54.448589, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.923004, 1302.410522, 54.448589, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.923004, 1301.260864, 54.448589, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.923004, 1300.751342, 56.248580, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.923004, 1297.370849, 56.248580, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.923004, 1295.760009, 56.248580, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.923004, 1294.099121, 55.918525, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.923004, 1296.079467, 55.918525, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.923004, 1302.410522, 55.918525, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.923004, 1301.260864, 55.918525, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.923004, 1300.751342, 57.718517, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.923004, 1297.370849, 57.718517, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -318.923004, 1295.760009, 57.718517, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -319.603027, 1300.631591, 56.978530, 90.000007, 180.000000, 179.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -319.603027, 1297.131835, 56.978530, 90.000007, 180.000000, 179.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -319.603027, 1295.901489, 56.978530, 90.000007, 180.000000, 179.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(2715, -319.542419, 1301.811279, 55.528591, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14771, "int_brothelint3", "Bow_bar_cooler_lwr", 0x00000000);
    brgstxt = CreateDynamicObject(2715, -319.542419, 1301.811279, 54.238594, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14771, "int_brothelint3", "Bow_bar_cooler_lwr", 0x00000000);
    brgstxt = CreateDynamicObject(2133, -318.785369, 1297.066040, 53.276615, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2134, -318.785369, 1299.856201, 53.276615, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2133, -318.785369, 1300.856323, 53.276615, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2133, -318.784362, 1296.435791, 53.275615, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2427, -318.390045, 1296.588867, 54.338615, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 1209, "vend", "veding1_64", 0x00000000);
    brgstxt = CreateDynamicObject(1897, -318.246704, 1300.916503, 55.288597, 180.000000, 450.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2766, -319.043609, 1300.313476, 54.368618, 270.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(2154, -323.288055, 1302.363647, 53.348602, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2154, -324.638000, 1302.363647, 53.348602, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2154, -331.998046, 1295.993286, 53.348602, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -325.183166, 1302.677246, 52.708614, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -327.473144, 1302.677246, 52.708614, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -332.263092, 1301.158203, 52.708614, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -332.263092, 1298.108276, 52.708614, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -332.263092, 1295.178466, 52.708614, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -332.263092, 1294.988281, 52.708614, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -332.263092, 1294.187622, 54.128601, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -332.263092, 1294.767700, 54.128601, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -321.490814, 1290.606201, 53.278610, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15034, "genhotelsave", "bathtile05_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -321.490814, 1288.894653, 53.277610, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15034, "genhotelsave", "bathtile05_int", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -332.263092, 1289.838867, 52.708614, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -332.600402, 1290.216186, 50.838607, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -322.938476, 1294.108398, 53.278594, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 12844, "cos_liquorstore", "b_wtilesreflect", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -322.938476, 1297.598388, 53.278594, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 12844, "cos_liquorstore", "b_wtilesreflect", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -322.938476, 1301.068969, 53.258594, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 12844, "cos_liquorstore", "b_wtilesreflect", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -327.558593, 1294.108398, 53.277595, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 12844, "cos_liquorstore", "b_wtilesreflect", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -327.558593, 1297.598388, 53.277595, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 12844, "cos_liquorstore", "b_wtilesreflect", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -327.558593, 1301.068969, 53.287593, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 12844, "cos_liquorstore", "b_wtilesreflect", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -327.558593, 1292.428222, 51.597587, 0.000007, 180.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -317.928619, 1292.428222, 51.597587, 0.000007, 180.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -327.558593, 1285.906738, 51.597587, 0.000014, 180.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -317.928619, 1285.906738, 51.597587, 0.000014, 180.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -327.558593, 1292.428222, 48.097587, 0.000007, 180.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -317.928619, 1292.428222, 48.097587, 0.000007, 180.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -327.558593, 1285.906738, 48.097587, 0.000014, 180.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -317.928619, 1285.906738, 48.097587, 0.000014, 180.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -332.600402, 1290.216186, 46.628616, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -326.277313, 1288.120483, 51.293418, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -326.279327, 1290.219726, 51.285419, 270.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 3, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -321.524688, 1287.716552, 51.340816, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14789, "ab_sfgymmain", "gun_ceiling2_128", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -321.524688, 1291.196411, 51.340816, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14789, "ab_sfgymmain", "gun_ceiling2_128", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -321.058685, 1290.588134, 51.597587, 0.000007, 180.000000, 179.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -321.058685, 1290.588134, 48.097587, 0.000007, 180.000000, 179.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -331.314025, 1290.879516, 52.355976, 0.000041, 58.799987, 89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -331.314025, 1287.885375, 50.542854, 0.000041, 58.799987, 89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -328.641052, 1286.750488, 49.915996, 0.000041, 58.799964, 179.999633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -325.646911, 1286.750488, 48.102874, 0.000041, 58.799964, 179.999633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -330.949554, 1287.868041, 48.422462, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -330.949554, 1286.897827, 48.422462, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -330.949554, 1285.897705, 48.422462, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -331.929565, 1285.897705, 48.422462, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -331.929565, 1286.887573, 48.422462, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -331.929565, 1287.887939, 48.422462, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -328.641052, 1287.541137, 49.916996, 0.000041, 58.799957, 179.999588, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -325.646911, 1287.541137, 48.103874, 0.000041, 58.799957, 179.999588, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -330.834075, 1290.879516, 52.356975, 0.000041, 58.799987, 89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -330.834075, 1287.885375, 50.543853, 0.000041, 58.799987, 89.999870, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -330.479553, 1287.868041, 48.423461, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -330.479553, 1286.868041, 48.423461, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -330.479553, 1285.868041, 48.423461, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19445, -326.782928, 1288.333251, 46.965789, 31.200000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -329.992950, 1291.815185, 51.097919, 31.200000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -329.991943, 1290.463378, 50.279426, 31.200000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -330.002960, 1288.308593, 51.998687, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -329.990936, 1290.133178, 49.159400, 90.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -329.990936, 1290.133178, 45.959400, 90.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -329.990936, 1293.633178, 49.159400, 90.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -329.990936, 1293.633178, 45.959400, 90.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -328.642974, 1288.334228, 45.265792, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -327.393096, 1288.335205, 45.265792, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(19379, -326.207550, 1290.661254, 47.189395, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 12844, "cos_liquorstore", "b_wtilesreflect", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -327.472656, 1292.291137, 52.626693, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    brgstxt = CreateDynamicObject(19087, -330.033294, 1290.501342, 53.224044, -58.700004, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19087, -330.033294, 1292.244506, 54.283885, -58.700004, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19087, -329.924041, 1288.343750, 51.785102, -58.699954, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19087, -327.873260, 1288.343750, 50.538257, -58.699954, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19087, -326.403533, 1288.343750, 49.644653, -58.699954, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18880, -323.952972, 1288.308593, 51.448703, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19087, -325.950653, 1288.343750, 49.369308, -58.699954, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19325, -326.654235, 1292.294555, 52.613533, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(970, -330.040405, 1290.414672, 52.609130, 0.000000, -31.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(970, -327.988220, 1288.297973, 49.931797, 0.000000, -31.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(970, -325.913879, 1288.297973, 48.685401, 0.000000, -31.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(2029, -327.775360, 1289.906127, 47.275333, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(1735, -329.454193, 1290.422241, 47.275341, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0xFFFF859A);
    brgstxt = CreateDynamicObject(2766, -327.759704, 1290.062866, 48.085319, 270.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(2766, -327.759704, 1290.772338, 48.085319, 270.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(2609, -329.919006, 1289.453735, 48.725395, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(2609, -329.919006, 1289.003906, 48.725395, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(2609, -329.919006, 1289.453735, 47.305385, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(2609, -329.919006, 1289.003906, 47.305385, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 2, 3922, "bistro", "Marble", 0x00000000);
    brgstxt = CreateDynamicObject(1827, -330.297760, 1290.605590, 50.525325, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 4981, "wiresetc2_las", "lasunionclk", 0x00000000);
    brgstxt = CreateDynamicObject(1827, -330.317779, 1290.605590, 50.525325, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(3053, -329.931243, 1290.623046, 50.525352, 0.000000, 90.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -321.095031, 1285.722167, 49.005325, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -321.095031, 1292.571777, 49.005325, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -321.095031, 1285.722167, 49.595298, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19353, -321.095031, 1292.571777, 49.595298, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(1761, -321.635925, 1290.151855, 47.275333, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 15034, "genhotelsave", "bathtile05_int", 0x00000000);
    brgstxt = CreateDynamicObject(2255, -321.651580, 1290.057128, 49.205326, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14420, "dr_gsbits", "mp_apt1_pic2", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -325.406860, 1293.086059, 48.795307, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -322.546905, 1293.086059, 48.795307, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -323.956970, 1293.086059, 48.795307, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -324.666931, 1293.086059, 48.795307, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19866, -323.216918, 1293.086059, 48.795307, 89.999992, 89.999992, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -324.147735, 1293.622802, 48.165332, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(19426, -323.737701, 1293.622802, 48.165332, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(18981, -321.961273, 1296.142456, 43.482814, 0.000022, 90.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18981, -300.731323, 1296.142456, 43.482814, 0.000022, 90.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(2774, -332.877136, 1284.727172, 30.554176, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(2774, -290.047241, 1284.727172, 30.554176, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(2774, -311.437347, 1284.727172, 30.554176, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 2, 18646, "matcolours", "grey-80-percent", 0x00000000);
    brgstxt = CreateDynamicObject(19482, -316.024719, 1303.644042, 55.572914, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "SRI MERSING", 130, "Arial", 70, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(19482, -316.004821, 1303.644042, 54.972938, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(brgstxt, 0, "MASAKAN MELAYU - NUSANTARA", 130, "Arial", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    brgstxt = CreateDynamicObject(1716, -315.254608, 1299.243408, 54.310489, 0.000045, -0.000007, 179.999710, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 5150, "wiresetc_las2", "ganggraf01_LA_m", 0xFFFFFFFF);
    SetDynamicObjectMaterial(brgstxt, 1, -1, "none", "none", 0xFF303030);
    brgstxt = CreateDynamicObject(2266, -315.489593, 1299.558349, 54.420597, 0.000012, -0.000045, -90.000289, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    brgstxt = CreateDynamicObject(2266, -314.488739, 1299.538330, 54.420597, -0.000012, 0.000045, 89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(brgstxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    brgstxt = CreateDynamicObject(2247, -314.838104, 1295.330322, 54.665538, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    brgstxt = CreateDynamicObject(2248, -317.625305, 1301.421020, 54.315547, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    brgstxt = CreateDynamicObject(2248, -316.685333, 1301.421020, 54.315547, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    brgstxt = CreateDynamicObject(14793, -326.186859, 1297.776611, 57.964530, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 19480, "signsurf", "sign", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -299.654663, 1295.044433, 53.196823, 89.999992, -353.284454, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -299.360534, 1294.998657, 53.196823, 89.999992, -403.284454, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -299.480438, 1295.085449, 53.196823, 89.999992, -278.284484, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -300.002716, 1295.030639, 53.196823, 89.999992, -557.484497, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -300.147369, 1294.717041, 53.196823, 89.999992, -467.484527, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -300.258056, 1294.883056, 53.196823, 89.999992, -434.184661, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -300.290405, 1295.223754, 53.196823, 89.999992, -497.284576, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -300.761352, 1295.050048, 53.196823, 89.999992, -557.784606, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -301.125152, 1295.067016, 53.196823, 89.999992, -527.784545, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -301.351837, 1295.066040, 53.196823, 89.999992, -578.084533, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -301.668029, 1295.115234, 53.196823, 89.999992, -557.784545, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -301.973663, 1294.830932, 53.196823, 89.999992, -467.784576, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -301.947937, 1295.139892, 53.196823, 89.999992, -467.784576, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -301.932403, 1295.439575, 53.196823, 89.999992, -467.784576, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -302.546722, 1295.161254, 53.196823, 89.999992, -557.784545, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -302.732086, 1295.152221, 53.196823, 89.999992, -526.184570, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -302.951385, 1295.123657, 53.196823, 89.999992, -565.784545, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -303.568115, 1295.283813, 53.196823, 89.999992, -353.284454, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -303.273986, 1295.238037, 53.196823, 89.999992, -403.284454, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(1881, -303.393920, 1295.324829, 53.196823, 89.999992, -278.284484, -165.215469, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -322.146362, 1305.131591, 49.836193, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -327.956298, 1305.131591, 49.836193, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18766, -300.176330, 1305.131591, 50.386192, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -326.365020, 1284.772583, 52.846691, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -331.174743, 1284.772583, 52.847690, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -333.724792, 1287.751831, 52.847690, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(18762, -333.724792, 1292.751708, 52.847690, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    brgstxt = CreateDynamicObject(2047, -285.400939, 1317.648681, 60.703891, 0.000044, 0.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1975, "texttest", "kb_red", 0x00000000);
    brgstxt = CreateDynamicObject(2047, -285.400939, 1317.648681, 60.703891, 0.000012, 0.000000, -90.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 1975, "texttest", "kb_red", 0x00000000);
    brgstxt = CreateDynamicObject(2047, -285.401947, 1317.648681, 59.853897, 0.000012, 0.000000, -90.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    brgstxt = CreateDynamicObject(2047, -285.400939, 1317.648681, 59.873893, 0.000044, 0.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(brgstxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(19279, -309.655944, 1303.859375, 58.034236, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, -303.569213, 1293.915771, 54.752288, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, -301.429229, 1293.915771, 54.752288, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, -299.159301, 1293.915771, 54.752288, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, -299.709289, 1293.915771, 54.752288, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, -301.879272, 1293.915771, 54.752288, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, -303.149322, 1293.915771, 54.752288, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, -299.159301, 1294.456054, 54.752288, 0.000000, -0.000014, 179.999801, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, -301.299285, 1294.456054, 54.752288, 0.000000, -0.000014, 179.999801, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, -303.569213, 1294.456054, 54.752288, 0.000000, -0.000014, 179.999801, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, -303.019226, 1294.456054, 54.752288, 0.000000, -0.000014, 179.999801, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, -300.849243, 1294.456054, 54.752288, 0.000000, -0.000014, 179.999801, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, -299.579193, 1294.456054, 54.752288, 0.000000, -0.000014, 179.999801, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(857, -303.478332, 1294.144653, 55.147647, 0.000014, 0.000001, 80.599952, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(862, -302.774841, 1294.213989, 54.846057, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(857, -300.038177, 1294.144653, 55.147647, 0.000029, 0.000003, 80.599906, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(862, -299.334686, 1294.213989, 54.846057, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11722, -293.752105, 1301.923339, 54.686538, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11723, -293.820526, 1301.692749, 54.696540, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, -301.505462, 1301.991455, 53.655532, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, -307.575256, 1301.991455, 53.655532, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, -302.655242, 1286.500976, 53.655532, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, -296.315246, 1286.500976, 53.655532, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1776, -291.498840, 1301.721069, 54.506538, 0.000000, 0.000000, -41.499996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, -308.233154, 1300.691406, 58.057331, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, -312.212982, 1300.691406, 58.127323, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, -308.233154, 1293.901367, 58.057331, 0.000000, 0.000052, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, -312.212982, 1293.901367, 58.127323, 0.000000, 0.000082, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, -303.305541, 1286.198974, 56.035533, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, -308.165496, 1286.198974, 56.035533, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, -308.895568, 1302.429565, 56.055534, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, -312.885589, 1302.429565, 56.055534, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2010, -296.375305, 1284.101684, 53.655532, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1670, -290.994537, 1278.995239, 54.415222, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1670, -295.274597, 1278.995239, 54.415222, 0.000000, 0.000000, 20.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1670, -299.214508, 1278.995239, 54.415222, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1670, -303.404418, 1278.995239, 54.415222, 0.000000, 0.000000, 27.300003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1670, -307.354431, 1278.995239, 54.415222, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2682, -299.658386, 1283.711791, 54.034236, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2682, -293.058258, 1283.711791, 54.034236, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2682, -292.704772, 1288.363403, 54.016532, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2682, -299.564758, 1288.363403, 54.016532, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2682, -298.154602, 1300.403930, 54.016532, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2682, -304.304656, 1300.403930, 54.016532, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2682, -291.644775, 1291.983398, 54.416515, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2682, -291.774810, 1296.853271, 54.416519, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2131, -319.448272, 1295.539550, 53.698600, -0.000029, 0.000000, -89.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2131, -319.448272, 1294.504882, 53.858604, 179.999969, 0.000000, 450.000091, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11718, -319.361907, 1298.273071, 54.328590, 0.000000, 0.000000, -88.199989, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11715, -318.363433, 1301.055175, 55.098598, 270.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11716, -318.363433, 1299.895019, 55.098598, 270.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11715, -318.363433, 1300.015136, 55.178592, 270.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11716, -318.363433, 1300.215332, 55.078594, 270.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11715, -318.363433, 1300.895874, 55.078594, 270.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11716, -318.363433, 1300.725830, 55.078594, 270.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11715, -318.363433, 1300.495727, 55.128597, 270.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11715, -318.363433, 1300.345581, 55.128597, 270.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2134, -320.939758, 1302.116699, 53.348602, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11723, -318.598663, 1301.061645, 54.458595, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11722, -318.438690, 1300.941528, 54.458595, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2133, -321.939880, 1302.116699, 53.348602, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2133, -325.639770, 1302.116699, 53.348602, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2133, -326.639801, 1302.116699, 53.348602, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2134, -327.619689, 1302.116699, 53.348602, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2134, -328.619537, 1302.116699, 53.348602, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2134, -320.939758, 1302.116699, 52.368591, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2133, -321.939880, 1302.116699, 52.368591, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2133, -325.639770, 1302.116699, 52.368591, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2133, -326.639801, 1302.116699, 52.368591, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2134, -327.619689, 1302.116699, 52.368591, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2134, -328.619537, 1302.116699, 52.368591, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2131, -331.732208, 1293.983276, 53.258609, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2134, -331.729522, 1297.366821, 53.348602, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2134, -331.729522, 1298.356933, 53.348602, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2134, -331.729522, 1299.126953, 53.349601, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2341, -331.730163, 1302.106567, 53.348602, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2134, -329.609527, 1302.116699, 53.348602, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2134, -330.579559, 1302.116699, 53.348602, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2134, -330.739532, 1302.116699, 53.349601, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2133, -331.729522, 1300.097290, 53.348602, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2133, -331.729522, 1301.087402, 53.348602, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2133, -331.729522, 1301.107421, 53.349601, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14726, -327.016876, 1293.446655, 51.474597, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2612, -328.594543, 1288.459960, 48.975322, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2255, -321.651580, 1288.266723, 49.205326, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3812, -321.062530, 1289.266967, 50.775321, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2141, -321.385772, 1291.649169, 47.275333, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19786, -323.928771, 1292.401977, 49.405342, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2855, -323.952575, 1292.081054, 48.245315, 0.000000, 0.000000, 85.900001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2256, -327.962615, 1292.311279, 49.735340, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, -326.195983, 1290.423706, 47.275333, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2222, -316.009979, 1294.996093, 54.405551, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2223, -315.469970, 1294.996093, 54.405551, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1514, -314.842620, 1297.138061, 54.575534, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2222, -317.920013, 1297.847167, 54.485553, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2222, -317.920013, 1298.987060, 54.485553, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2453, -315.701416, 1301.424316, 54.725547, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11705, -314.817779, 1301.233032, 54.305553, 0.000000, 0.000000, -25.500003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19279, -311.925903, 1303.859375, 58.034236, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19279, -292.545806, 1303.859375, 58.034236, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19279, -304.205810, 1303.859375, 58.034236, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19279, -316.375915, 1303.859375, 58.034236, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19279, -330.065643, 1303.859375, 58.034236, 180.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19279, -333.485656, 1299.178588, 58.034236, 180.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19279, -333.485656, 1286.178100, 58.034236, 180.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19279, -289.175750, 1286.178100, 58.034236, -0.000007, -179.999984, -90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19279, -289.175750, 1299.178588, 58.034236, -0.000007, -179.999984, -90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, -323.113952, 1288.784545, 51.335323, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -289.887359, 1303.240112, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -333.147369, 1303.240112, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -331.177429, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -318.757690, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -324.877807, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -322.017822, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -328.128021, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -326.518066, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -329.568054, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -323.508117, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -320.518035, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -307.367980, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -291.238067, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -299.098052, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -295.088012, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -292.958099, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -296.978088, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -303.328186, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -301.218200, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -305.298156, 1303.900268, 53.006813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, -291.619232, 1305.078247, 53.335571, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, -296.989196, 1305.078247, 53.335571, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, -302.319244, 1305.078247, 53.335571, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, -319.359252, 1305.078247, 52.995578, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, -324.729187, 1305.078247, 52.995578, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, -330.089141, 1305.078247, 52.995578, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16101, -285.430725, 1316.679321, 50.282070, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19820, -317.982025, 1294.780883, 54.389972, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19821, -318.008728, 1295.452758, 55.152366, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19822, -317.952850, 1295.455932, 54.391529, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19823, -317.981597, 1296.196777, 54.402893, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1951, -317.979827, 1296.184936, 55.352630, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1544, -317.987884, 1294.781494, 55.152275, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1543, -317.971038, 1296.162109, 56.021217, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1486, -317.962005, 1294.757324, 56.175338, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19823, -317.981597, 1295.476318, 56.022865, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19820, -317.982025, 1296.181030, 56.419975, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1544, -317.987884, 1296.181640, 57.222278, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19821, -318.008728, 1294.772094, 57.222301, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19822, -317.952850, 1294.775268, 56.401473, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1951, -317.979827, 1295.484741, 56.572601, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1543, -317.971038, 1295.461914, 57.241188, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
}