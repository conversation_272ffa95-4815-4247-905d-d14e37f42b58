name: nightly-release

on:
  push

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
        with:
          submodules: true

      - name: Create zip
        run: |
          cd ..
          zip ${{ github.event.repository.name }}/${{ github.event.repository.name }}-nightly.zip ${{ github.event.repository.name }} -r
          
      - uses: pyTooling/Actions/releaser@r0
        with:
          token: ${{ secrets.GITHUB_TOKEN }}
          tag: nightly
          files: ${{ github.event.repository.name }}-nightly.zip
