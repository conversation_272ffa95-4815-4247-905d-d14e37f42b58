RemoveFederalBuilding(playerid)
{
    RemoveBuildingForPlayer(playerid, 16203, 199.344, 1943.79, 18.2031, 250.0); 	// Land
	RemoveBuildingForPlayer(playerid, 16590, 199.344, 1943.79, 18.2031, 250.0); 	// Land LOD
	RemoveBuildingForPlayer(playerid, 16323, 199.336, 1943.88, 18.2031, 250.0); 	// Buildings
    RemoveBuildingForPlayer(playerid, 16619, 199.336, 1943.88, 18.2031, 250.0); 	// Buildings LOD
    RemoveBuildingForPlayer(playerid, 1697, 228.797, 1835.34, 23.2344, 250.0); 		// Solar Panels (they poke through the roof inside)
    RemoveBuildingForPlayer(playerid, 16094, 191.141, 1870.04, 21.4766, 250.0); 	// Outer Fence
}

CreateFederalExt()
{
    new STREAMER_TAG_OBJECT: fdrlxt;

    CreateDynamicObject(11692, 199.344, 1943.79, 18.2031, 0.0, 0.0, 0.0, 0, 0, -1, 900.00, 900.00, -1); 
    CreateDynamicObject(19312, 191.141, 1870.04, 21.4766, 0.0, 0.0, 0.0, 0, 0, -1, 900.00, 900.00, -1); 
    CreateDynamicObject(19905, 206.798950, 1931.643432, 16.450595, 0.0, 0.0, 0.0, 0, 0, -1, 900.00, 900.00, -1); 
    CreateDynamicObject(19905, 188.208908, 1835.033569, 16.450595, 0.0, 0.0, 0.0, 0, 0, -1, 900.00, 900.00, -1); 
    CreateDynamicObject(19905, 230.378875, 1835.033569, 16.450595, 0.0, 0.0, 0.0, 0, 0, -1, 900.00, 900.00, -1); 
    CreateDynamicObject(19907, 142.013977, 1902.538085, 17.633581, 0.0, 0.0, 270.0, 0, 0, -1, 900.00, 900.00, -1); 
    CreateDynamicObject(19907, 146.854003, 1846.008056, 16.533580, 0.0, 0.0, 0.0, 0, 0, -1, 900.00, 900.00, -1); 
    CreateDynamicObject(19909, 137.900390, 1875.024291, 16.836734, 0.0, 0.0, 270.0, 0, 0, -1, 900.00, 900.00, -1); 
    CreateDynamicObject(19909, 118.170387, 1875.184326, 16.846735, 0.0, 0.0, 0.0, 0, 0, -1, 900.00, 900.00, -1); 

    //heliped & banner
    fdrlxt = CreateDynamicObject(18765, 280.758758, 2057.226074, 15.564107, 0.000000, 0.000000, 0.000000, 0, 0, -1, 900.00, 900.00, -1);  
    SetDynamicObjectMaterial(fdrlxt, 0, 13364, "cetown3cs_t", "ws_sandstone2", 0x00000000);
    fdrlxt = CreateDynamicObject(19482, 144.553573, 1941.392089, 23.551887, 0.000000, 0.000000, 91.199989, 0, 0, -1, 900.00, 900.00, -1);  
    SetDynamicObjectMaterialText(fdrlxt, 0, "LAPAS\nTANJUNG GUSTA", 90, "Calibri", 35, 1, 0xFFFF0000, 0xFFFFFFFF, 1);
    fdrlxt = CreateDynamicObject(19482, 144.526916, 1941.231689, 23.551887, 0.000000, 0.000000, 271.199981, 0, 0, -1, 900.00, 900.00, -1);  
    SetDynamicObjectMaterialText(fdrlxt, 0, "LAPAS\nTANJUNG GUSTA", 90, "Calibri", 35, 1, 0xFFFF0000, 0xFFFFFFFF, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(3934, 280.881378, 2057.397705, 18.064523, 0.000000, 0.000000, 0.000000, 0, 0, -1, 900.00, 900.00, -1);  
    CreateDynamicObject(8572, 278.652435, 2063.125732, 16.796823, 0.000000, 0.000000, 180.000000, 0, 0, -1, 900.00, 900.00, -1);  
    CreateDynamicObject(19123, 285.549591, 2062.025878, 18.050153, 0.000000, 0.000000, 0.000000, 0, 0, -1, 900.00, 900.00, -1);  
    CreateDynamicObject(19124, 285.519042, 2052.388183, 18.045560, 0.000000, 0.000000, 0.000000, 0, 0, -1, 900.00, 900.00, -1);  
    CreateDynamicObject(2949, 284.125518, 2038.714111, 16.638675, 0.000000, 0.000000, 270.000000, 0, 0, -1, 900.00, 900.00, -1);  
}