new Text:InventTD[10],
	Text:InventLineTD[20],
    PlayerText:BoxItem[MAX_PLAYERS][20],
	PlayerText:PrevMod[MAX_PLAYERS][20],
    PlayerText:NameItem[MAX_PLAYERS][20],
	PlayerText:QuantItem[MAX_PLAYERS][20],
	PlayerText:InventWeightTD[MAX_PLAYERS][4];

CreateInventTD()
{
	// InventTD[0] = TextDrawCreate(132.500000, 108.000000, "ld_dual:white");
	// TextDrawFont(InventTD[0], 4);
	// TextDrawLetterSize(InventTD[0], 0.600000, 2.000000);
	// TextDrawTextSize(InventTD[0], 231.100000, 262.000000);
	// TextDrawSetOutline(InventTD[0], true);
	// TextDrawSetShadow(InventTD[0], false);
	// TextDrawAlignment(InventTD[0], true);
	// TextDrawColor(InventTD[0], 0x9696968c);
	// TextDrawBackgroundColor(InventTD[0], 255);
	// TextDrawBoxColor(InventTD[0], 50);
	// TextDrawUseBox(InventTD[0], true);
	// TextDrawSetProportional(InventTD[0], true);
	// TextDrawSetSelectable(InventTD[0], false);

	InventTD[0] = TextDrawCreate(139.500000, 122.000000, "ld_dual:white");
	TextDrawFont(InventTD[0], 4);
	TextDrawLetterSize(InventTD[0], 0.600000, 2.000000);
	TextDrawTextSize(InventTD[0], 218.600006, 3.500000);
	TextDrawSetOutline(InventTD[0], true);
	TextDrawSetShadow(InventTD[0], false);
	TextDrawAlignment(InventTD[0], true);
	TextDrawColor(InventTD[0], 0x3030308c);
	TextDrawBackgroundColor(InventTD[0], 255);
	TextDrawBoxColor(InventTD[0], 50);
	TextDrawUseBox(InventTD[0], true);
	TextDrawSetProportional(InventTD[0], true);
	TextDrawSetSelectable(InventTD[0], false);

	// InventTD[2] = TextDrawCreate(385.500000, 173.000000, "ld_dual:white");
	// TextDrawFont(InventTD[2], 4);
	// TextDrawLetterSize(InventTD[2], 0.600000, 2.000000);
	// TextDrawTextSize(InventTD[2], 74.600000, 140.500000);
	// TextDrawSetOutline(InventTD[2], true);
	// TextDrawSetShadow(InventTD[2], false);
	// TextDrawAlignment(InventTD[2], true);
	// TextDrawColor(InventTD[2], 0x9696968c);
	// TextDrawBackgroundColor(InventTD[2], 255);
	// TextDrawBoxColor(InventTD[2], 50);
	// TextDrawUseBox(InventTD[2], true);
	// TextDrawSetProportional(InventTD[2], true);
	// TextDrawSetSelectable(InventTD[2], false);

	InventTD[1] = TextDrawCreate(393.500000, 177.500000, "ld_dual:white");
	TextDrawFont(InventTD[1], 4);
	TextDrawLetterSize(InventTD[1], 0.600000, 2.000000);
	TextDrawTextSize(InventTD[1], 58.600000, 23.000000);
	TextDrawSetOutline(InventTD[1], true);
	TextDrawSetShadow(InventTD[1], false);
	TextDrawAlignment(InventTD[1], true);
	TextDrawColor(InventTD[1], 0x00000066);
	TextDrawBackgroundColor(InventTD[1], 255);
	TextDrawBoxColor(InventTD[1], 50);
	TextDrawUseBox(InventTD[1], true);
	TextDrawSetProportional(InventTD[1], true);
	TextDrawSetSelectable(InventTD[1], false);

	InventTD[2] = TextDrawCreate(393.500000, 204.500000, "ld_dual:white");
	TextDrawFont(InventTD[2], 4);
	TextDrawLetterSize(InventTD[2], 0.600000, 2.000000);
	TextDrawTextSize(InventTD[2], 58.600000, 23.000000);
	TextDrawSetOutline(InventTD[2], true);
	TextDrawSetShadow(InventTD[2], false);
	TextDrawAlignment(InventTD[2], true);
	TextDrawColor(InventTD[2], 0x00000066);
	TextDrawBackgroundColor(InventTD[2], 255);
	TextDrawBoxColor(InventTD[2], 50);
	TextDrawUseBox(InventTD[2], true);
	TextDrawSetProportional(InventTD[2], true);
	TextDrawSetSelectable(InventTD[2], false);

	InventTD[3] = TextDrawCreate(415.000000, 209.000000, "Use");
	TextDrawFont(InventTD[3], true);
	TextDrawLetterSize(InventTD[3], 0.250000, 1.500000);
	TextDrawTextSize(InventTD[3], 449.000000, 14.000000);
	TextDrawSetOutline(InventTD[3], false);
	TextDrawSetShadow(InventTD[3], false);
	TextDrawAlignment(InventTD[3], true);
	TextDrawColor(InventTD[3], 0xffffffe6);
	TextDrawBackgroundColor(InventTD[3], 255);
	TextDrawBoxColor(InventTD[3], 50);
	TextDrawUseBox(InventTD[3], false);
	TextDrawSetProportional(InventTD[3], true);
	TextDrawSetSelectable(InventTD[3], true);

	InventTD[4] = TextDrawCreate(393.500000, 231.500000, "ld_dual:white");
	TextDrawFont(InventTD[4], 4);
	TextDrawLetterSize(InventTD[4], 0.600000, 2.000000);
	TextDrawTextSize(InventTD[4], 58.600000, 23.000000);
	TextDrawSetOutline(InventTD[4], true);
	TextDrawSetShadow(InventTD[4], false);
	TextDrawAlignment(InventTD[4], true);
	TextDrawColor(InventTD[4], 0x00000066);
	TextDrawBackgroundColor(InventTD[4], 255);
	TextDrawBoxColor(InventTD[4], 50);
	TextDrawUseBox(InventTD[4], true);
	TextDrawSetProportional(InventTD[4], true);
	TextDrawSetSelectable(InventTD[4], false);

	InventTD[5] = TextDrawCreate(414.000000, 236.000000, "Give");
	TextDrawFont(InventTD[5], true);
	TextDrawLetterSize(InventTD[5], 0.250000, 1.500000);
	TextDrawTextSize(InventTD[5], 449.000000, 14.000000);
	TextDrawSetOutline(InventTD[5], false);
	TextDrawSetShadow(InventTD[5], false);
	TextDrawAlignment(InventTD[5], true);
	TextDrawColor(InventTD[5], 0xffffffe6);
	TextDrawBackgroundColor(InventTD[5], 255);
	TextDrawBoxColor(InventTD[5], 50);
	TextDrawUseBox(InventTD[5], false);
	TextDrawSetProportional(InventTD[5], true);
	TextDrawSetSelectable(InventTD[5], true);

	InventTD[6] = TextDrawCreate(393.500000, 258.500000, "ld_dual:white");
	TextDrawFont(InventTD[6], 4);
	TextDrawLetterSize(InventTD[6], 0.600000, 2.000000);
	TextDrawTextSize(InventTD[6], 58.600000, 23.000000);
	TextDrawSetOutline(InventTD[6], true);
	TextDrawSetShadow(InventTD[6], false);
	TextDrawAlignment(InventTD[6], true);
	TextDrawColor(InventTD[6], 0x00000066);
	TextDrawBackgroundColor(InventTD[6], 255);
	TextDrawBoxColor(InventTD[6], 50);
	TextDrawUseBox(InventTD[6], true);
	TextDrawSetProportional(InventTD[6], true);
	TextDrawSetSelectable(InventTD[6], false);

	InventTD[7] = TextDrawCreate(414.000000, 262.000000, "Drop");
	TextDrawFont(InventTD[7], true);
	TextDrawLetterSize(InventTD[7], 0.250000, 1.500000);
	TextDrawTextSize(InventTD[7], 449.000000, 14.000000);
	TextDrawSetOutline(InventTD[7], false);
	TextDrawSetShadow(InventTD[7], false);
	TextDrawAlignment(InventTD[7], true);
	TextDrawColor(InventTD[7], 0xffffffe6);
	TextDrawBackgroundColor(InventTD[7], 255);
	TextDrawBoxColor(InventTD[7], 50);
	TextDrawUseBox(InventTD[7], false);
	TextDrawSetProportional(InventTD[7], true);
	TextDrawSetSelectable(InventTD[7], true);

	InventTD[8] = TextDrawCreate(393.500000, 285.500000, "ld_dual:white");
	TextDrawFont(InventTD[8], 4);
	TextDrawLetterSize(InventTD[8], 0.600000, 2.000000);
	TextDrawTextSize(InventTD[8], 58.600000, 23.000000);
	TextDrawSetOutline(InventTD[8], true);
	TextDrawSetShadow(InventTD[8], false);
	TextDrawAlignment(InventTD[8], true);
	TextDrawColor(InventTD[8], 0x00000066);
	TextDrawBackgroundColor(InventTD[8], 255);
	TextDrawBoxColor(InventTD[8], 50);
	TextDrawUseBox(InventTD[8], true);
	TextDrawSetProportional(InventTD[8], true);
	TextDrawSetSelectable(InventTD[8], false);

	InventTD[9] = TextDrawCreate(413.000000, 289.000000, "Tutup");
	TextDrawFont(InventTD[9], true);
	TextDrawLetterSize(InventTD[9], 0.250000, 1.500000);
	TextDrawTextSize(InventTD[9], 449.000000, 14.000000);
	TextDrawSetOutline(InventTD[9], false);
	TextDrawSetShadow(InventTD[9], false);
	TextDrawAlignment(InventTD[9], true);
	TextDrawColor(InventTD[9], 0xffffffe6);
	TextDrawBackgroundColor(InventTD[9], 255);
	TextDrawBoxColor(InventTD[9], 50);
	TextDrawUseBox(InventTD[9], false);
	TextDrawSetProportional(InventTD[9], true);
	TextDrawSetSelectable(InventTD[9], true);
}

CreateInventLine()
{
	InventLineTD[0] = TextDrawCreate(139.500000, 179.000000, "ld_dual:white");
	TextDrawFont(InventLineTD[0], 4);
	TextDrawLetterSize(InventLineTD[0], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[0], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[0], true);
	TextDrawSetShadow(InventLineTD[0], false);
	TextDrawAlignment(InventLineTD[0], true);
	TextDrawColor(InventLineTD[0], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[0], 255);
	TextDrawBoxColor(InventLineTD[0], 50);
	TextDrawUseBox(InventLineTD[0], true);
	TextDrawSetProportional(InventLineTD[0], true);
	TextDrawSetSelectable(InventLineTD[0], false);

	InventLineTD[1] = TextDrawCreate(183.500000, 179.000000, "ld_dual:white");
	TextDrawFont(InventLineTD[1], 4);
	TextDrawLetterSize(InventLineTD[1], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[1], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[1], true);
	TextDrawSetShadow(InventLineTD[1], false);
	TextDrawAlignment(InventLineTD[1], true);
	TextDrawColor(InventLineTD[1], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[1], 255);
	TextDrawBoxColor(InventLineTD[1], 50);
	TextDrawUseBox(InventLineTD[1], true);
	TextDrawSetProportional(InventLineTD[1], true);
	TextDrawSetSelectable(InventLineTD[1], false);

	InventLineTD[2] = TextDrawCreate(227.500000, 179.000000, "ld_dual:white");
	TextDrawFont(InventLineTD[2], 4);
	TextDrawLetterSize(InventLineTD[2], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[2], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[2], true);
	TextDrawSetShadow(InventLineTD[2], false);
	TextDrawAlignment(InventLineTD[2], true);
	TextDrawColor(InventLineTD[2], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[2], 255);
	TextDrawBoxColor(InventLineTD[2], 50);
	TextDrawUseBox(InventLineTD[2], true);
	TextDrawSetProportional(InventLineTD[2], true);
	TextDrawSetSelectable(InventLineTD[2], false);

	InventLineTD[3] = TextDrawCreate(271.500000, 179.000000, "ld_dual:white");
	TextDrawFont(InventLineTD[3], 4);
	TextDrawLetterSize(InventLineTD[3], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[3], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[3], true);
	TextDrawSetShadow(InventLineTD[3], false);
	TextDrawAlignment(InventLineTD[3], true);
	TextDrawColor(InventLineTD[3], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[3], 255);
	TextDrawBoxColor(InventLineTD[3], 50);
	TextDrawUseBox(InventLineTD[3], true);
	TextDrawSetProportional(InventLineTD[3], true);
	TextDrawSetSelectable(InventLineTD[3], false);

	InventLineTD[4] = TextDrawCreate(315.500000, 179.000000, "ld_dual:white");
	TextDrawFont(InventLineTD[4], 4);
	TextDrawLetterSize(InventLineTD[4], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[4], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[4], true);
	TextDrawSetShadow(InventLineTD[4], false);
	TextDrawAlignment(InventLineTD[4], true);
	TextDrawColor(InventLineTD[4], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[4], 255);
	TextDrawBoxColor(InventLineTD[4], 50);
	TextDrawUseBox(InventLineTD[4], true);
	TextDrawSetProportional(InventLineTD[4], true);
	TextDrawSetSelectable(InventLineTD[4], false);

	InventLineTD[5] = TextDrawCreate(139.500000, 237.600006, "ld_dual:white");
	TextDrawFont(InventLineTD[5], 4);
	TextDrawLetterSize(InventLineTD[5], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[5], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[5], true);
	TextDrawSetShadow(InventLineTD[5], false);
	TextDrawAlignment(InventLineTD[5], true);
	TextDrawColor(InventLineTD[5], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[5], 255);
	TextDrawBoxColor(InventLineTD[5], 50);
	TextDrawUseBox(InventLineTD[5], true);
	TextDrawSetProportional(InventLineTD[5], true);
	TextDrawSetSelectable(InventLineTD[5], false);

	InventLineTD[6] = TextDrawCreate(183.500000, 237.600006, "ld_dual:white");
	TextDrawFont(InventLineTD[6], 4);
	TextDrawLetterSize(InventLineTD[6], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[6], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[6], true);
	TextDrawSetShadow(InventLineTD[6], false);
	TextDrawAlignment(InventLineTD[6], true);
	TextDrawColor(InventLineTD[6], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[6], 255);
	TextDrawBoxColor(InventLineTD[6], 50);
	TextDrawUseBox(InventLineTD[6], true);
	TextDrawSetProportional(InventLineTD[6], true);
	TextDrawSetSelectable(InventLineTD[6], false);

	InventLineTD[7] = TextDrawCreate(227.500000, 237.600006, "ld_dual:white");
	TextDrawFont(InventLineTD[7], 4);
	TextDrawLetterSize(InventLineTD[7], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[7], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[7], true);
	TextDrawSetShadow(InventLineTD[7], false);
	TextDrawAlignment(InventLineTD[7], true);
	TextDrawColor(InventLineTD[7], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[7], 255);
	TextDrawBoxColor(InventLineTD[7], 50);
	TextDrawUseBox(InventLineTD[7], true);
	TextDrawSetProportional(InventLineTD[7], true);
	TextDrawSetSelectable(InventLineTD[7], false);

	InventLineTD[8] = TextDrawCreate(271.500000, 237.600006, "ld_dual:white");
	TextDrawFont(InventLineTD[8], 4);
	TextDrawLetterSize(InventLineTD[8], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[8], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[8], true);
	TextDrawSetShadow(InventLineTD[8], false);
	TextDrawAlignment(InventLineTD[8], true);
	TextDrawColor(InventLineTD[8], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[8], 255);
	TextDrawBoxColor(InventLineTD[8], 50);
	TextDrawUseBox(InventLineTD[8], true);
	TextDrawSetProportional(InventLineTD[8], true);
	TextDrawSetSelectable(InventLineTD[8], false);

	InventLineTD[9] = TextDrawCreate(315.500000, 237.600006, "ld_dual:white");
	TextDrawFont(InventLineTD[9], 4);
	TextDrawLetterSize(InventLineTD[9], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[9], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[9], true);
	TextDrawSetShadow(InventLineTD[9], false);
	TextDrawAlignment(InventLineTD[9], true);
	TextDrawColor(InventLineTD[9], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[9], 255);
	TextDrawBoxColor(InventLineTD[9], 50);
	TextDrawUseBox(InventLineTD[9], true);
	TextDrawSetProportional(InventLineTD[9], true);
	TextDrawSetSelectable(InventLineTD[9], false);

	InventLineTD[10] = TextDrawCreate(139.500000, 296.600006, "ld_dual:white");
	TextDrawFont(InventLineTD[10], 4);
	TextDrawLetterSize(InventLineTD[10], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[10], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[10], true);
	TextDrawSetShadow(InventLineTD[10], false);
	TextDrawAlignment(InventLineTD[10], true);
	TextDrawColor(InventLineTD[10], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[10], 255);
	TextDrawBoxColor(InventLineTD[10], 50);
	TextDrawUseBox(InventLineTD[10], true);
	TextDrawSetProportional(InventLineTD[10], true);
	TextDrawSetSelectable(InventLineTD[10], false);

	InventLineTD[11] = TextDrawCreate(183.500000, 296.600006, "ld_dual:white");
	TextDrawFont(InventLineTD[11], 4);
	TextDrawLetterSize(InventLineTD[11], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[11], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[11], true);
	TextDrawSetShadow(InventLineTD[11], false);
	TextDrawAlignment(InventLineTD[11], true);
	TextDrawColor(InventLineTD[11], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[11], 255);
	TextDrawBoxColor(InventLineTD[11], 50);
	TextDrawUseBox(InventLineTD[11], true);
	TextDrawSetProportional(InventLineTD[11], true);
	TextDrawSetSelectable(InventLineTD[11], false);

	InventLineTD[12] = TextDrawCreate(227.500000, 296.600006, "ld_dual:white");
	TextDrawFont(InventLineTD[12], 4);
	TextDrawLetterSize(InventLineTD[12], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[12], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[12], true);
	TextDrawSetShadow(InventLineTD[12], false);
	TextDrawAlignment(InventLineTD[12], true);
	TextDrawColor(InventLineTD[12], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[12], 255);
	TextDrawBoxColor(InventLineTD[12], 50);
	TextDrawUseBox(InventLineTD[12], true);
	TextDrawSetProportional(InventLineTD[12], true);
	TextDrawSetSelectable(InventLineTD[12], false);

	InventLineTD[13] = TextDrawCreate(271.500000, 296.600006, "ld_dual:white");
	TextDrawFont(InventLineTD[13], 4);
	TextDrawLetterSize(InventLineTD[13], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[13], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[13], true);
	TextDrawSetShadow(InventLineTD[13], false);
	TextDrawAlignment(InventLineTD[13], true);
	TextDrawColor(InventLineTD[13], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[13], 255);
	TextDrawBoxColor(InventLineTD[13], 50);
	TextDrawUseBox(InventLineTD[13], true);
	TextDrawSetProportional(InventLineTD[13], true);
	TextDrawSetSelectable(InventLineTD[13], false);

	InventLineTD[14] = TextDrawCreate(315.500000, 296.600006, "ld_dual:white");
	TextDrawFont(InventLineTD[14], 4);
	TextDrawLetterSize(InventLineTD[14], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[14], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[14], true);
	TextDrawSetShadow(InventLineTD[14], false);
	TextDrawAlignment(InventLineTD[14], true);
	TextDrawColor(InventLineTD[14], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[14], 255);
	TextDrawBoxColor(InventLineTD[14], 50);
	TextDrawUseBox(InventLineTD[14], true);
	TextDrawSetProportional(InventLineTD[14], true);
	TextDrawSetSelectable(InventLineTD[14], false);

	InventLineTD[15] = TextDrawCreate(139.500000, 356.000000, "ld_dual:white");
	TextDrawFont(InventLineTD[15], 4);
	TextDrawLetterSize(InventLineTD[15], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[15], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[15], true);
	TextDrawSetShadow(InventLineTD[15], false);
	TextDrawAlignment(InventLineTD[15], true);
	TextDrawColor(InventLineTD[15], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[15], 255);
	TextDrawBoxColor(InventLineTD[15], 50);
	TextDrawUseBox(InventLineTD[15], true);
	TextDrawSetProportional(InventLineTD[15], true);
	TextDrawSetSelectable(InventLineTD[15], false);

	InventLineTD[16] = TextDrawCreate(183.500000, 356.000000, "ld_dual:white");
	TextDrawFont(InventLineTD[16], 4);
	TextDrawLetterSize(InventLineTD[16], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[16], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[16], true);
	TextDrawSetShadow(InventLineTD[16], false);
	TextDrawAlignment(InventLineTD[16], true);
	TextDrawColor(InventLineTD[16], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[16], 255);
	TextDrawBoxColor(InventLineTD[16], 50);
	TextDrawUseBox(InventLineTD[16], true);
	TextDrawSetProportional(InventLineTD[16], true);
	TextDrawSetSelectable(InventLineTD[16], false);

	InventLineTD[17] = TextDrawCreate(227.500000, 356.000000, "ld_dual:white");
	TextDrawFont(InventLineTD[17], 4);
	TextDrawLetterSize(InventLineTD[17], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[17], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[17], true);
	TextDrawSetShadow(InventLineTD[17], false);
	TextDrawAlignment(InventLineTD[17], true);
	TextDrawColor(InventLineTD[17], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[17], 255);
	TextDrawBoxColor(InventLineTD[17], 50);
	TextDrawUseBox(InventLineTD[17], true);
	TextDrawSetProportional(InventLineTD[17], true);
	TextDrawSetSelectable(InventLineTD[17], false);

	InventLineTD[18] = TextDrawCreate(271.500000, 356.000000, "ld_dual:white");
	TextDrawFont(InventLineTD[18], 4);
	TextDrawLetterSize(InventLineTD[18], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[18], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[18], true);
	TextDrawSetShadow(InventLineTD[18], false);
	TextDrawAlignment(InventLineTD[18], true);
	TextDrawColor(InventLineTD[18], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[18], 255);
	TextDrawBoxColor(InventLineTD[18], 50);
	TextDrawUseBox(InventLineTD[18], true);
	TextDrawSetProportional(InventLineTD[18], true);
	TextDrawSetSelectable(InventLineTD[18], false);

	InventLineTD[19] = TextDrawCreate(315.500000, 356.000000, "ld_dual:white");
	TextDrawFont(InventLineTD[19], 4);
	TextDrawLetterSize(InventLineTD[19], 0.600000, 2.000000);
	TextDrawTextSize(InventLineTD[19], 42.600006, 3.500000);
	TextDrawSetOutline(InventLineTD[19], true);
	TextDrawSetShadow(InventLineTD[19], false);
	TextDrawAlignment(InventLineTD[19], true);
	TextDrawColor(InventLineTD[19], 0xff91a4cc);
	TextDrawBackgroundColor(InventLineTD[19], 255);
	TextDrawBoxColor(InventLineTD[19], 50);
	TextDrawUseBox(InventLineTD[19], true);
	TextDrawSetProportional(InventLineTD[19], true);
	TextDrawSetSelectable(InventLineTD[19], false);
}

CreateInventWeightTD(playerid)
{
	InventWeightTD[playerid][0] = CreatePlayerTextDraw(playerid, 139.000000, 112.000000, "Adhipati Tanjung"); //name
	PlayerTextDrawFont(playerid, InventWeightTD[playerid][0], true);
	PlayerTextDrawLetterSize(playerid, InventWeightTD[playerid][0], 0.187500, 1.050000);
	PlayerTextDrawTextSize(playerid, InventWeightTD[playerid][0], 259.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, InventWeightTD[playerid][0], false);
	PlayerTextDrawSetShadow(playerid, InventWeightTD[playerid][0], false);
	PlayerTextDrawAlignment(playerid, InventWeightTD[playerid][0], true);
	PlayerTextDrawColor(playerid, InventWeightTD[playerid][0], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, InventWeightTD[playerid][0], 255);
	PlayerTextDrawBoxColor(playerid, InventWeightTD[playerid][0], 50);
	PlayerTextDrawUseBox(playerid, InventWeightTD[playerid][0], false);
	PlayerTextDrawSetProportional(playerid, InventWeightTD[playerid][0], true);
	PlayerTextDrawSetSelectable(playerid, InventWeightTD[playerid][0], false);

	InventWeightTD[playerid][1] = CreatePlayerTextDraw(playerid, 139.500000, 122.000000, "ld_dual:white"); //progress bar weight
	PlayerTextDrawFont(playerid, InventWeightTD[playerid][1], 4);
	PlayerTextDrawLetterSize(playerid, InventWeightTD[playerid][1], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, InventWeightTD[playerid][1], 132.600006, 3.500000);
	PlayerTextDrawSetOutline(playerid, InventWeightTD[playerid][1], true);
	PlayerTextDrawSetShadow(playerid, InventWeightTD[playerid][1], false);
	PlayerTextDrawAlignment(playerid, InventWeightTD[playerid][1], true);
	PlayerTextDrawColor(playerid, InventWeightTD[playerid][1], 0xff91a4cc);
	PlayerTextDrawBackgroundColor(playerid, InventWeightTD[playerid][1], 255);
	PlayerTextDrawBoxColor(playerid, InventWeightTD[playerid][1], 50);
	PlayerTextDrawUseBox(playerid, InventWeightTD[playerid][1], true);
	PlayerTextDrawSetProportional(playerid, InventWeightTD[playerid][1], true);
	PlayerTextDrawSetSelectable(playerid, InventWeightTD[playerid][1], false);

	InventWeightTD[playerid][2] = CreatePlayerTextDraw(playerid, 356.000000, 113.500000, "10,5/50"); //weight
	PlayerTextDrawFont(playerid, InventWeightTD[playerid][2], true);
	PlayerTextDrawLetterSize(playerid, InventWeightTD[playerid][2], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, InventWeightTD[playerid][2], 600.000000, 17.000000);
	PlayerTextDrawSetOutline(playerid, InventWeightTD[playerid][2], false);
	PlayerTextDrawSetShadow(playerid, InventWeightTD[playerid][2], false);
	PlayerTextDrawAlignment(playerid, InventWeightTD[playerid][2], 3);
	PlayerTextDrawColor(playerid, InventWeightTD[playerid][2], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, InventWeightTD[playerid][2], 255);
	PlayerTextDrawBoxColor(playerid, InventWeightTD[playerid][2], 50);
	PlayerTextDrawUseBox(playerid, InventWeightTD[playerid][2], false);
	PlayerTextDrawSetProportional(playerid, InventWeightTD[playerid][2], true);
	PlayerTextDrawSetSelectable(playerid, InventWeightTD[playerid][2], false);

	InventWeightTD[playerid][3] = CreatePlayerTextDraw(playerid, 409.000000, 182.000000, "Ammount");
	PlayerTextDrawFont(playerid, InventWeightTD[playerid][3], true);
	PlayerTextDrawLetterSize(playerid, InventWeightTD[playerid][3], 0.250000, 1.500000);
	PlayerTextDrawTextSize(playerid, InventWeightTD[playerid][3], 449.000000, 14.000000);
	PlayerTextDrawSetOutline(playerid, InventWeightTD[playerid][3], false);
	PlayerTextDrawSetShadow(playerid, InventWeightTD[playerid][3], false);
	PlayerTextDrawAlignment(playerid, InventWeightTD[playerid][3], true);
	PlayerTextDrawColor(playerid, InventWeightTD[playerid][3], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, InventWeightTD[playerid][3], 255);
	PlayerTextDrawBoxColor(playerid, InventWeightTD[playerid][3], 50);
	PlayerTextDrawUseBox(playerid, InventWeightTD[playerid][3], false);
	PlayerTextDrawSetProportional(playerid, InventWeightTD[playerid][3], true);
	PlayerTextDrawSetSelectable(playerid, InventWeightTD[playerid][3], true);
}

CreateInventBoxItemTD(playerid)
{
	BoxItem[playerid][0] = CreatePlayerTextDraw(playerid, 139.500000, 125.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][0], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][0], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][0], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][0], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][0], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][0], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][0], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][0], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][0], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][0], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][0], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][0], true);

	BoxItem[playerid][1] = CreatePlayerTextDraw(playerid, 183.500000, 125.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][1], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][1], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][1], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][1], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][1], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][1], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][1], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][1], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][1], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][1], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][1], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][1], true);

	BoxItem[playerid][2] = CreatePlayerTextDraw(playerid, 227.500000, 125.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][2], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][2], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][2], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][2], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][2], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][2], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][2], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][2], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][2], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][2], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][2], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][2], true);

	BoxItem[playerid][3] = CreatePlayerTextDraw(playerid, 271.500000, 125.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][3], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][3], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][3], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][3], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][3], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][3], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][3], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][3], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][3], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][3], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][3], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][3], true);

	BoxItem[playerid][4] = CreatePlayerTextDraw(playerid, 315.500000, 125.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][4], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][4], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][4], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][4], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][4], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][4], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][4], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][4], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][4], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][4], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][4], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][4], true);

	BoxItem[playerid][5] = CreatePlayerTextDraw(playerid, 139.500000, 184.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][5], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][5], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][5], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][5], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][5], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][5], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][5], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][5], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][5], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][5], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][5], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][5], true);

	BoxItem[playerid][6] = CreatePlayerTextDraw(playerid, 183.500000, 184.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][6], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][6], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][6], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][6], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][6], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][6], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][6], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][6], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][6], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][6], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][6], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][6], true);

	BoxItem[playerid][7] = CreatePlayerTextDraw(playerid, 227.500000, 184.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][7], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][7], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][7], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][7], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][7], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][7], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][7], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][7], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][7], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][7], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][7], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][7], true);

	BoxItem[playerid][8] = CreatePlayerTextDraw(playerid, 271.500000, 184.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][8], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][8], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][8], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][8], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][8], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][8], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][8], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][8], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][8], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][8], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][8], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][8], true);

	BoxItem[playerid][9] = CreatePlayerTextDraw(playerid, 315.500000, 184.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][9], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][9], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][9], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][9], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][9], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][9], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][9], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][9], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][9], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][9], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][9], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][9], true);

	BoxItem[playerid][10] = CreatePlayerTextDraw(playerid, 139.500000, 243.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][10], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][10], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][10], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][10], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][10], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][10], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][10], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][10], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][10], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][10], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][10], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][10], true);

	BoxItem[playerid][11] = CreatePlayerTextDraw(playerid, 183.500000, 243.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][11], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][11], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][11], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][11], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][11], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][11], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][11], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][11], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][11], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][11], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][11], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][11], true);

	BoxItem[playerid][12] = CreatePlayerTextDraw(playerid, 227.500000, 243.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][12], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][12], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][12], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][12], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][12], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][12], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][12], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][12], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][12], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][12], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][12], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][12], true);

	BoxItem[playerid][13] = CreatePlayerTextDraw(playerid, 271.500000, 243.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][13], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][13], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][13], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][13], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][13], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][13], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][13], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][13], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][13], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][13], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][13], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][13], true);

	BoxItem[playerid][14] = CreatePlayerTextDraw(playerid, 315.500000, 243.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][14], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][14], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][14], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][14], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][14], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][14], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][14], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][14], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][14], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][14], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][14], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][14], true);

	BoxItem[playerid][15] = CreatePlayerTextDraw(playerid, 139.500000, 302.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][15], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][15], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][15], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][15], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][15], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][15], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][15], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][15], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][15], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][15], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][15], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][15], true);

	BoxItem[playerid][16] = CreatePlayerTextDraw(playerid, 183.500000, 302.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][16], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][16], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][16], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][16], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][16], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][16], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][16], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][16], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][16], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][16], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][16], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][16], true);

	BoxItem[playerid][17] = CreatePlayerTextDraw(playerid, 227.500000, 302.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][17], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][17], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][17], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][17], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][17], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][17], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][17], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][17], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][17], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][17], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][17], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][17], true);

	BoxItem[playerid][18] = CreatePlayerTextDraw(playerid, 271.500000, 302.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][18], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][18], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][18], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][18], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][18], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][18], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][18], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][18], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][18], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][18], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][18], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][18], true);

	BoxItem[playerid][19] = CreatePlayerTextDraw(playerid, 315.500000, 302.500000, "ld_dual:white");
	PlayerTextDrawFont(playerid, BoxItem[playerid][19], 4);
	PlayerTextDrawLetterSize(playerid, BoxItem[playerid][19], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, BoxItem[playerid][19], 42.600006, 53.500000);
	PlayerTextDrawSetOutline(playerid, BoxItem[playerid][19], true);
	PlayerTextDrawSetShadow(playerid, BoxItem[playerid][19], false);
	PlayerTextDrawAlignment(playerid, BoxItem[playerid][19], true);
	PlayerTextDrawColor(playerid, BoxItem[playerid][19], 0x00000066);
	PlayerTextDrawBackgroundColor(playerid, BoxItem[playerid][19], 0x00000066);
	PlayerTextDrawBoxColor(playerid, BoxItem[playerid][19], 50);
	PlayerTextDrawUseBox(playerid, BoxItem[playerid][19], true);
	PlayerTextDrawSetProportional(playerid, BoxItem[playerid][19], true);
	PlayerTextDrawSetSelectable(playerid, BoxItem[playerid][19], true);
}

CreateInventNameItemTD(playerid)
{
    NameItem[playerid][0] = CreatePlayerTextDraw(playerid, 142.000000, 126.500000, "Ransel");
	PlayerTextDrawFont(playerid, NameItem[playerid][0], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][0], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][0], 182.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][0], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][0], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][0], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][0], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][0], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][0], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][0], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][0], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][0], false);

	NameItem[playerid][1] = CreatePlayerTextDraw(playerid, 186.000000, 126.500000, "Ayam Kemas");
	PlayerTextDrawFont(playerid, NameItem[playerid][1], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][1], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][1], 226.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][1], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][1], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][1], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][1], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][1], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][1], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][1], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][1], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][1], false);

	NameItem[playerid][2] = CreatePlayerTextDraw(playerid, 230.000000, 126.500000, "Yarn");
	PlayerTextDrawFont(playerid, NameItem[playerid][2], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][2], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][2], 270.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][2], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][2], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][2], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][2], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][2], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][2], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][2], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][2], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][2], false);

	NameItem[playerid][3] = CreatePlayerTextDraw(playerid, 274.000000, 126.500000, "Chili");
	PlayerTextDrawFont(playerid, NameItem[playerid][3], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][3], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][3], 314.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][3], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][3], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][3], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][3], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][3], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][3], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][3], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][3], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][3], false);

	NameItem[playerid][4] = CreatePlayerTextDraw(playerid, 318.000000, 126.500000, "Sauce");
	PlayerTextDrawFont(playerid, NameItem[playerid][4], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][4], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][4], 357.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][4], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][4], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][4], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][4], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][4], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][4], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][4], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][4], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][4], false);

	NameItem[playerid][5] = CreatePlayerTextDraw(playerid, 142.000000, 184.500000, "Clothes");
	PlayerTextDrawFont(playerid, NameItem[playerid][5], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][5], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][5], 182.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][5], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][5], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][5], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][5], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][5], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][5], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][5], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][5], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][5], false);

	NameItem[playerid][6] = CreatePlayerTextDraw(playerid, 186.000000, 184.500000, "Kopi");
	PlayerTextDrawFont(playerid, NameItem[playerid][6], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][6], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][6], 226.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][6], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][6], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][6], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][6], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][6], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][6], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][6], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][6], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][6], false);

	NameItem[playerid][7] = CreatePlayerTextDraw(playerid, 230.000000, 184.500000, "Jerigen");
	PlayerTextDrawFont(playerid, NameItem[playerid][7], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][7], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][7], 270.000000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][7], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][7], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][7], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][7], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][7], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][7], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][7], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][7], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][7], false);

	NameItem[playerid][8] = CreatePlayerTextDraw(playerid, 274.000000, 184.500000, "Pancingan");
	PlayerTextDrawFont(playerid, NameItem[playerid][8], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][8], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][8], 314.000000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][8], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][8], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][8], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][8], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][8], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][8], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][8], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][8], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][8], false);

	NameItem[playerid][9] = CreatePlayerTextDraw(playerid, 318.000000, 184.500000, "Pil Stres");
	PlayerTextDrawFont(playerid, NameItem[playerid][9], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][9], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][9], 357.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][9], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][9], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][9], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][9], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][9], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][9], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][9], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][9], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][9], false);

	NameItem[playerid][10] = CreatePlayerTextDraw(playerid, 142.000000, 243.500000, "Daging");
	PlayerTextDrawFont(playerid, NameItem[playerid][10], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][10], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][10], 182.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][10], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][10], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][10], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][10], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][10], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][10], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][10], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][10], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][10], false);

	NameItem[playerid][11] = CreatePlayerTextDraw(playerid, 186.000000, 243.500000, "Marijuana");
	PlayerTextDrawFont(playerid, NameItem[playerid][11], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][11], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][11], 226.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][11], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][11], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][11], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][11], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][11], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][11], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][11], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][11], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][11], false);

	NameItem[playerid][12] = CreatePlayerTextDraw(playerid, 230.000000, 243.500000, "Kunci Letter T");
	PlayerTextDrawFont(playerid, NameItem[playerid][12], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][12], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][12], 270.000000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][12], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][12], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][12], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][12], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][12], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][12], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][12], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][12], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][12], false);

	NameItem[playerid][13] = CreatePlayerTextDraw(playerid, 274.000000, 243.500000, "Sampah Makanan");
	PlayerTextDrawFont(playerid, NameItem[playerid][13], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][13], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][13], 314.000000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][13], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][13], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][13], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][13], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][13], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][13], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][13], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][13], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][13], false);

	NameItem[playerid][14] = CreatePlayerTextDraw(playerid, 318.000000, 243.500000, "Nasi Goreng");
	PlayerTextDrawFont(playerid, NameItem[playerid][14], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][14], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][14], 357.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][14], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][14], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][14], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][14], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][14], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][14], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][14], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][14], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][14], false);

	NameItem[playerid][15] = CreatePlayerTextDraw(playerid, 142.000000, 302.500000, "Es Teh");
	PlayerTextDrawFont(playerid, NameItem[playerid][15], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][15], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][15], 182.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][15], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][15], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][15], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][15], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][15], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][15], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][15], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][15], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][15], false);

	NameItem[playerid][16] = CreatePlayerTextDraw(playerid, 186.000000, 302.500000, "Perban");
	PlayerTextDrawFont(playerid, NameItem[playerid][16], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][16], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][16], 226.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][16], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][16], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][16], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][16], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][16], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][16], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][16], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][16], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][16], false);

	NameItem[playerid][17] = CreatePlayerTextDraw(playerid, 230.000000, 302.500000, "Toolkit");
	PlayerTextDrawFont(playerid, NameItem[playerid][17], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][17], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][17], 269.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][17], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][17], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][17], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][17], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][17], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][17], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][17], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][17], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][17], false);

	NameItem[playerid][18] = CreatePlayerTextDraw(playerid, 274.000000, 302.500000, "Batu");
	PlayerTextDrawFont(playerid, NameItem[playerid][18], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][18], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][18], 314.000000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][18], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][18], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][18], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][18], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][18], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][18], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][18], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][18], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][18], false);

	NameItem[playerid][19] = CreatePlayerTextDraw(playerid, 318.000000, 302.500000, "Log");
	PlayerTextDrawFont(playerid, NameItem[playerid][19], true);
	PlayerTextDrawLetterSize(playerid, NameItem[playerid][19], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, NameItem[playerid][19], 357.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, NameItem[playerid][19], false);
	PlayerTextDrawSetShadow(playerid, NameItem[playerid][19], false);
	PlayerTextDrawAlignment(playerid, NameItem[playerid][19], true);
	PlayerTextDrawColor(playerid, NameItem[playerid][19], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, NameItem[playerid][19], 255);
	PlayerTextDrawBoxColor(playerid, NameItem[playerid][19], 50);
	PlayerTextDrawUseBox(playerid, NameItem[playerid][19], false);
	PlayerTextDrawSetProportional(playerid, NameItem[playerid][19], true);
	PlayerTextDrawSetSelectable(playerid, NameItem[playerid][19], false);
}

CreateInventPrevModTD(playerid)
{
    PrevMod[playerid][0] = CreatePlayerTextDraw(playerid, 140.000000, 126.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][0], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][0], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][0], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][0], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][0], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][0], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][0], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][0], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][0], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][0], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][0], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][0], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][0], 3026);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][0], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][0], 1, 1);

	PrevMod[playerid][1] = CreatePlayerTextDraw(playerid, 183.500000, 126.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][1], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][1], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][1], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][1], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][1], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][1], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][1], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][1], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][1], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][1], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][1], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][1], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][1], 2768);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][1], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][1], 1, 1);

	PrevMod[playerid][2] = CreatePlayerTextDraw(playerid, 227.000000, 126.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][2], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][2], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][2], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][2], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][2], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][2], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][2], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][2], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][2], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][2], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][2], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][2], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][2], 1880);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][2], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][2], 1, 1);

	PrevMod[playerid][3] = CreatePlayerTextDraw(playerid, 271.000000, 126.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][3], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][3], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][3], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][3], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][3], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][3], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][3], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][3], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][3], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][3], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][3], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][3], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][3], 2253);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][3], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][3], 1, 1);

	PrevMod[playerid][4] = CreatePlayerTextDraw(playerid, 315.000000, 126.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][4], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][4], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][4], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][4], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][4], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][4], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][4], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][4], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][4], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][4], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][4], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][4], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][4], 11722);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][4], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][4], 1, 1);

	PrevMod[playerid][5] = CreatePlayerTextDraw(playerid, 140.000000, 185.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][5], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][5], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][5], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][5], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][5], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][5], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][5], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][5], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][5], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][5], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][5], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][5], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][5], 2399);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][5], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][5], 1, 1);

	PrevMod[playerid][6] = CreatePlayerTextDraw(playerid, 183.500000, 185.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][6], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][6], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][6], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][6], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][6], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][6], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][6], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][6], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][6], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][6], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][6], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][6], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][6], 19835);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][6], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][6], 1, 1);

	PrevMod[playerid][7] = CreatePlayerTextDraw(playerid, 227.000000, 185.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][7], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][7], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][7], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][7], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][7], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][7], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][7], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][7], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][7], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][7], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][7], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][7], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][7], 1650);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][7], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][7], 1, 1);

	PrevMod[playerid][8] = CreatePlayerTextDraw(playerid, 271.000000, 185.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][8], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][8], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][8], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][8], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][8], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][8], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][8], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][8], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][8], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][8], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][8], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][8], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][8], 18632);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][8], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][8], 1, 1);

	PrevMod[playerid][9] = CreatePlayerTextDraw(playerid, 315.000000, 185.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][9], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][9], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][9], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][9], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][9], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][9], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][9], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][9], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][9], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][9], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][9], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][9], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][9], 1241);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][9], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][9], 1, 1);

	PrevMod[playerid][10] = CreatePlayerTextDraw(playerid, 140.000000, 244.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][10], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][10], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][10], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][10], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][10], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][10], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][10], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][10], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][10], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][10], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][10], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][10], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][10], 2806);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][10], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][10], 1, 1);

	PrevMod[playerid][11] = CreatePlayerTextDraw(playerid, 183.500000, 244.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][11], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][11], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][11], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][11], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][11], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][11], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][11], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][11], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][11], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][11], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][11], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][11], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][11], 1578);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][11], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][11], 1, 1);

	PrevMod[playerid][12] = CreatePlayerTextDraw(playerid, 227.000000, 244.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][12], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][12], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][12], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][12], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][12], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][12], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][12], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][12], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][12], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][12], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][12], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][12], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][12], 18633);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][12], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][12], 1, 1);

	PrevMod[playerid][13] = CreatePlayerTextDraw(playerid, 271.000000, 244.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][13], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][13], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][13], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][13], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][13], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][13], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][13], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][13], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][13], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][13], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][13], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][13], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][13], 2840);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][13], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][13], 1, 1);

	PrevMod[playerid][14] = CreatePlayerTextDraw(playerid, 315.000000, 244.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][14], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][14], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][14], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][14], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][14], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][14], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][14], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][14], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][14], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][14], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][14], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][14], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][14], 2355);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][14], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][14], 1, 1);

	PrevMod[playerid][15] = CreatePlayerTextDraw(playerid, 140.000000, 303.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][15], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][15], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][15], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][15], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][15], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][15], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][15], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][15], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][15], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][15], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][15], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][15], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][15], 1546);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][15], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][15], 1, 1);

	PrevMod[playerid][16] = CreatePlayerTextDraw(playerid, 183.500000, 303.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][16], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][16], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][16], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][16], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][16], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][16], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][16], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][16], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][16], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][16], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][16], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][16], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][16], 11736);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][16], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][16], 1, 1);

	PrevMod[playerid][17] = CreatePlayerTextDraw(playerid, 227.000000, 303.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][17], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][17], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][17], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][17], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][17], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][17], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][17], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][17], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][17], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][17], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][17], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][17], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][17], 19918);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][17], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][17], 1, 1);

	PrevMod[playerid][18] = CreatePlayerTextDraw(playerid, 271.000000, 303.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][18], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][18], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][18], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][18], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][18], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][18], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][18], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][18], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][18], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][18], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][18], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][18], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][18], 3930);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][18], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][18], 1, 1);

	PrevMod[playerid][19] = CreatePlayerTextDraw(playerid, 315.000000, 303.000000, "Preview_Model");
	PlayerTextDrawFont(playerid, PrevMod[playerid][19], 5);
	PlayerTextDrawLetterSize(playerid, PrevMod[playerid][19], 0.600000, 2.000000);
	PlayerTextDrawTextSize(playerid, PrevMod[playerid][19], 42.500000, 56.500000);
	PlayerTextDrawSetOutline(playerid, PrevMod[playerid][19], false);
	PlayerTextDrawSetShadow(playerid, PrevMod[playerid][19], false);
	PlayerTextDrawAlignment(playerid, PrevMod[playerid][19], true);
	PlayerTextDrawColor(playerid, PrevMod[playerid][19], -1);
	PlayerTextDrawBackgroundColor(playerid, PrevMod[playerid][19], false);
	PlayerTextDrawBoxColor(playerid, PrevMod[playerid][19], 255);
	PlayerTextDrawUseBox(playerid, PrevMod[playerid][19], false);
	PlayerTextDrawSetProportional(playerid, PrevMod[playerid][19], true);
	PlayerTextDrawSetSelectable(playerid, PrevMod[playerid][19], true);
	PlayerTextDrawSetPreviewModel(playerid, PrevMod[playerid][19], 831);
	PlayerTextDrawSetPreviewRot(playerid, PrevMod[playerid][19], -10.000000, 0.000000, -20.000000, 1.200000);
	PlayerTextDrawSetPreviewVehCol(playerid, PrevMod[playerid][19], 1, 1);
}

CreateInventQuantTD(playerid)
{
	QuantItem[playerid][0] = CreatePlayerTextDraw(playerid, 142.000000, 167.500000, "1x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][0], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][0], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][0], 182.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][0], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][0], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][0], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][0], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][0], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][0], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][0], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][0], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][0], false);

	QuantItem[playerid][1] = CreatePlayerTextDraw(playerid, 186.000000, 167.500000, "20x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][1], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][1], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][1], 226.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][1], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][1], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][1], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][1], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][1], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][1], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][1], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][1], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][1], false);

	QuantItem[playerid][2] = CreatePlayerTextDraw(playerid, 230.000000, 167.500000, "30x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][2], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][2], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][2], 270.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][2], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][2], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][2], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][2], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][2], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][2], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][2], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][2], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][2], false);

	QuantItem[playerid][3] = CreatePlayerTextDraw(playerid, 274.000000, 167.500000, "15x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][3], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][3], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][3], 314.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][3], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][3], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][3], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][3], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][3], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][3], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][3], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][3], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][3], false);

	QuantItem[playerid][4] = CreatePlayerTextDraw(playerid, 318.000000, 167.500000, "5x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][4], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][4], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][4], 357.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][4], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][4], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][4], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][4], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][4], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][4], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][4], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][4], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][4], false);

	QuantItem[playerid][5] = CreatePlayerTextDraw(playerid, 142.000000, 227.500000, "10000x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][5], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][5], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][5], 182.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][5], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][5], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][5], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][5], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][5], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][5], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][5], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][5], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][5], false);

	QuantItem[playerid][6] = CreatePlayerTextDraw(playerid, 186.000000, 227.500000, "800x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][6], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][6], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][6], 226.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][6], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][6], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][6], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][6], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][6], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][6], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][6], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][6], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][6], false);

	QuantItem[playerid][7] = CreatePlayerTextDraw(playerid, 230.000000, 227.500000, "90x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][7], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][7], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][7], 270.000000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][7], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][7], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][7], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][7], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][7], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][7], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][7], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][7], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][7], false);

	QuantItem[playerid][8] = CreatePlayerTextDraw(playerid, 274.000000, 227.500000, "190x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][8], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][8], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][8], 314.000000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][8], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][8], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][8], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][8], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][8], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][8], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][8], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][8], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][8], false);

	QuantItem[playerid][9] = CreatePlayerTextDraw(playerid, 318.000000, 227.500000, "89x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][9], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][9], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][9], 357.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][9], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][9], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][9], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][9], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][9], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][9], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][9], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][9], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][9], false);

	QuantItem[playerid][10] = CreatePlayerTextDraw(playerid, 142.000000, 284.500000, "80x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][10], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][10], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][10], 182.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][10], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][10], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][10], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][10], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][10], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][10], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][10], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][10], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][10], false);

	QuantItem[playerid][11] = CreatePlayerTextDraw(playerid, 186.000000, 284.500000, "150x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][11], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][11], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][11], 226.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][11], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][11], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][11], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][11], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][11], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][11], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][11], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][11], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][11], false);

	QuantItem[playerid][12] = CreatePlayerTextDraw(playerid, 230.000000, 284.500000, "69x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][12], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][12], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][12], 270.000000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][12], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][12], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][12], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][12], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][12], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][12], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][12], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][12], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][12], false);

	QuantItem[playerid][13] = CreatePlayerTextDraw(playerid, 274.000000, 284.500000, "16x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][13], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][13], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][13], 314.000000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][13], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][13], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][13], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][13], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][13], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][13], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][13], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][13], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][13], false);

	QuantItem[playerid][14] = CreatePlayerTextDraw(playerid, 318.000000, 284.500000, "99x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][14], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][14], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][14], 357.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][14], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][14], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][14], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][14], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][14], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][14], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][14], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][14], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][14], false);

	QuantItem[playerid][15] = CreatePlayerTextDraw(playerid, 142.000000, 343.500000, "10x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][15], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][15], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][15], 182.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][15], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][15], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][15], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][15], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][15], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][15], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][15], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][15], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][15], false);

	QuantItem[playerid][16] = CreatePlayerTextDraw(playerid, 186.000000, 343.500000, "77x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][16], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][16], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][16], 226.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][16], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][16], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][16], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][16], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][16], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][16], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][16], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][16], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][16], false);

	QuantItem[playerid][17] = CreatePlayerTextDraw(playerid, 230.000000, 343.500000, "88x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][17], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][17], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][17], 269.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][17], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][17], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][17], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][17], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][17], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][17], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][17], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][17], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][17], false);

	QuantItem[playerid][18] = CreatePlayerTextDraw(playerid, 274.000000, 343.500000, "100x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][18], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][18], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][18], 314.000000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][18], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][18], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][18], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][18], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][18], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][18], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][18], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][18], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][18], false);

	QuantItem[playerid][19] = CreatePlayerTextDraw(playerid, 318.000000, 343.500000, "91x");
	PlayerTextDrawFont(playerid, QuantItem[playerid][19], true);
	PlayerTextDrawLetterSize(playerid, QuantItem[playerid][19], 0.158333, 0.850000);
	PlayerTextDrawTextSize(playerid, QuantItem[playerid][19], 357.500000, 17.000000);
	PlayerTextDrawSetOutline(playerid, QuantItem[playerid][19], false);
	PlayerTextDrawSetShadow(playerid, QuantItem[playerid][19], false);
	PlayerTextDrawAlignment(playerid, QuantItem[playerid][19], true);
	PlayerTextDrawColor(playerid, QuantItem[playerid][19], 0xffffffe6);
	PlayerTextDrawBackgroundColor(playerid, QuantItem[playerid][19], 255);
	PlayerTextDrawBoxColor(playerid, QuantItem[playerid][19], 50);
	PlayerTextDrawUseBox(playerid, QuantItem[playerid][19], false);
	PlayerTextDrawSetProportional(playerid, QuantItem[playerid][19], true);
	PlayerTextDrawSetSelectable(playerid, QuantItem[playerid][19], false);
}

ShowInventoryTD(playerid)
{
	for(new x; x < 10; x++)
	{
		TextDrawShowForPlayer(playerid, InventTD[x]);
	}

	for(new x; x < 20; x++)
	{
		PlayerTextDrawShow(playerid, BoxItem[playerid][x]);
	}
}

HideInventoryTD(playerid)
{
	for(new x; x < 10; x++)
	{
		TextDrawHideForPlayer(playerid, InventTD[x]);
	}
	for(new x; x < 20; x++)
	{
		PlayerTextDrawHide(playerid, BoxItem[playerid][x]);
		PlayerTextDrawHide(playerid, PrevMod[playerid][x]);
		PlayerTextDrawHide(playerid, NameItem[playerid][x]);
		PlayerTextDrawHide(playerid, QuantItem[playerid][x]);
		TextDrawHideForPlayer(playerid, InventLineTD[x]);
	}
	PlayerTextDrawHide(playerid, InventWeightTD[playerid][0]);
	PlayerTextDrawHide(playerid, InventWeightTD[playerid][1]);
	PlayerTextDrawHide(playerid, InventWeightTD[playerid][2]);
	PlayerTextDrawHide(playerid, InventWeightTD[playerid][3]);
}