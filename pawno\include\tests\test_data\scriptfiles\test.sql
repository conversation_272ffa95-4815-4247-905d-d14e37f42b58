CREATE TABLE `test` (
  `number` int(11) NOT NULL AUTO_INCREMENT,
  `text` varchar(45) DEFAULT NULL,
  `float` float DEFAULT NULL,
  PRIMARY KEY (`number`)
) ENGINE=InnoDB AUTO_INCREMENT=100 DEFAULT CHARSET=utf8;

INSERT INTO `test` VALUES 
  (1, 'asdf', 3.142341),
  (2, 'asdf', 3.142341),
  (3, 'asdf', 3.142341),
  (4, 'asdf', 3.142341),
  (5, 'asdf', 3.142341),
  (6, 'asdf', 3.142341),
  (7, 'asdf', 3.142341),
  (8, 'asdf', 3.142341),
  (9, 'asdf', 3.142341),
  (10, 'asdf', 3.142341);

CREATE TABLE `test2` (
  `id` INT NOT NULL AUTO_INCREMENT,
  `text` VARCHAR(45) NULL DEFAULT NULL,
  PRIMARY KEY (`id`));

INSERT INTO `test2` VALUES ();
INSERT INTO `test2` (`text`) VALUE ('asdf');
