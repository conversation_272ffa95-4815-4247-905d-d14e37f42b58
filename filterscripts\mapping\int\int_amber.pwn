CreateAmberInt()
{
    new STREAMER_TAG_OBJECT:asdwtx;
    asdwtx = CreateDynamicObject(18981, 1477.957153, -2163.787841, -12.173122, 0.000000, 270.000000, -179.999938, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14847, "mp_policesf", "mp_cop_vinyl", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1478.600708, -2176.197753, -9.962856, -0.000007, 720.000000, -89.999954, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10763, "airport1_sfse", "ws_airportdoors1", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1483.499755, -2178.719726, -9.962856, 0.000000, -0.000014, 179.999908, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1473.709228, -2178.719726, -9.962856, 0.000000, -0.000014, 179.999908, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1478.559814, -2175.638671, -8.303188, 0.000007, -89.999984, 89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1488.179321, -2175.638671, -8.303188, 0.000007, -89.999984, 89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1488.339111, -2173.996826, -10.033045, 0.000007, 0.000014, 89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1486.918701, -2172.007324, -10.033045, 0.000000, 360.000000, 179.999893, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1488.010498, -2173.996826, -6.543787, 0.000014, 0.000014, 89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1478.450439, -2173.996826, -6.543787, 0.000014, 0.000014, 89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1486.918701, -2172.007324, -6.533045, 0.000000, 360.000000, 179.999893, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1478.577636, -2167.154052, -10.033045, -0.000051, 360.000000, -89.999855, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1478.577636, -2167.154052, -6.533045, -0.000051, 360.000000, -89.999855, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1488.126708, -2167.154052, -10.033045, -0.000059, 360.000000, -89.999832, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1488.126708, -2167.154052, -6.533045, -0.000059, 360.000000, -89.999832, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1468.800781, -2173.998779, -9.962856, -0.000014, 0.000000, -89.999954, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1468.840820, -2173.998779, -6.532924, -0.000014, 0.000000, -89.999954, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(18981, 1476.445678, -2179.709228, -5.983180, 0.000000, 270.000000, -179.999938, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 3922, "bistro", "marblekb_256128", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1465.667846, -2169.164550, -10.033045, 0.000000, 360.000000, 179.999847, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1465.667846, -2169.164550, -6.533045, 0.000000, 360.000000, 179.999847, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1468.968383, -2167.154052, -6.533045, -0.000051, 360.000000, -89.999855, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19935, 1481.407714, -2169.396484, -11.776515, -0.000023, 0.000014, -68.200012, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1736, "cj_ammo", "CJ_Black_metal", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19937, 1479.302856, -2169.622070, -11.774378, -0.000027, 0.000007, -88.199874, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1736, "cj_ammo", "CJ_Black_metal", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19935, 1477.951293, -2169.508056, -11.776515, -0.000028, -0.000003, -108.199951, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1736, "cj_ammo", "CJ_Black_metal", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19934, 1477.910766, -2169.779541, -11.415369, -0.000028, 0.000003, -108.199951, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16322, "a51_stores", "steel64", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19937, 1480.228393, -2169.817138, -11.417017, -0.000025, 0.000014, -88.199874, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16322, "a51_stores", "steel64", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19934, 1481.457031, -2169.663085, -11.414394, -0.000019, 0.000018, -68.200012, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16322, "a51_stores", "steel64", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19937, 1479.144165, -2169.853271, -11.415064, -0.000025, 0.000014, -88.199874, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16322, "a51_stores", "steel64", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19934, 1477.209228, -2169.549072, -11.415369, -0.000028, 0.000003, -108.199951, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16322, "a51_stores", "steel64", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19935, 1477.066894, -2169.217285, -11.777491, -0.000028, -0.000003, -108.199951, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1736, "cj_ammo", "CJ_Black_metal", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(2697, 1477.436523, -2169.865478, -11.381374, -0.000003, 90.000030, -18.200063, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10932, "station_sfse", "neon", 0x00000000);
    asdwtx = CreateDynamicObject(19934, 1477.143920, -2169.529541, -11.417385, -0.000028, 0.000003, -108.199951, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16322, "a51_stores", "steel64", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(2697, 1478.904785, -2170.080566, -11.381374, 0.000007, 90.000030, 1.800006, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10932, "station_sfse", "neon", 0x00000000);
    asdwtx = CreateDynamicObject(2697, 1480.406250, -2170.035644, -11.381374, 0.000007, 90.000030, 1.800006, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10932, "station_sfse", "neon", 0x00000000);
    asdwtx = CreateDynamicObject(19937, 1480.102416, -2169.597167, -11.776392, -0.000027, 0.000007, -88.199874, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1736, "cj_ammo", "CJ_Black_metal", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(2697, 1481.858276, -2169.732910, -11.381374, 0.000014, 90.000022, 21.800020, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10932, "station_sfse", "neon", 0x00000000);
    asdwtx = CreateDynamicObject(19935, 1482.264770, -2169.053710, -11.776515, -0.000023, 0.000014, -68.200012, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1736, "cj_ammo", "CJ_Black_metal", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19934, 1482.142578, -2169.386474, -11.414394, -0.000019, 0.000018, -68.200012, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16322, "a51_stores", "steel64", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(2714, 1479.088012, -2170.092773, -10.884914, 0.000018, 0.000018, 1.799940, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterialText(asdwtx, 0, "________________________________________________________________________________________________________________________________", 130, "Arial", 40, 0, 0xFFFFFFFF, 0x00000000, 1);
    asdwtx = CreateDynamicObject(2714, 1479.088378, -2170.092041, -10.924831, 0.000018, 0.000018, 1.799940, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterialText(asdwtx, 0, "RECEPTION", 130, "Impact", 130, 0, 0xFFFFFFFF, 0x00000000, 0);
    asdwtx = CreateDynamicObject(2714, 1479.761230, -2170.071289, -11.181728, 0.000018, 0.000018, 1.799940, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterialText(asdwtx, 0, "INFORMATION", 130, "Impact", 90, 0, 0xFFFFFFFF, 0x00000000, 0);
    asdwtx = CreateDynamicObject(2714, 1480.601074, -2170.045898, -10.821743, 0.000018, 0.000018, 1.799940, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterialText(asdwtx, 0, "i", 90, "Fixedsys", 76, 0, 0xFF171717, 0x00000000, 0);
    asdwtx = CreateDynamicObject(2714, 1480.501464, -2170.048095, -10.791712, 0.000018, 0.000018, 1.799940, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterialText(asdwtx, 0, "n", 130, "Webdings", 180, 0, 0xFFFFFFFF, 0x00000000, 0);
    asdwtx = CreateDynamicObject(19445, 1469.044921, -2169.145751, -12.522060, 0.000004, 0.000014, -0.000068, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(19445, 1464.293457, -2173.956787, -12.524073, 0.000014, -0.000004, 89.999862, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(19445, 1467.214111, -2164.415771, -15.614101, 89.999992, 166.631423, -76.631523, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(19445, 1467.315429, -2169.205810, -11.022121, 0.000004, 90.000015, -0.000068, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 4829, "airport_las", "Grass_128HV", 0x00000000);
    asdwtx = CreateDynamicObject(2751, 1469.035888, -2166.054687, -10.741419, -0.000014, 0.000041, -89.999992, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1560, "7_11_door", "CJ_CHROME2", 0x00000000);
    asdwtx = CreateDynamicObject(2751, 1469.035888, -2166.054687, -10.643337, -0.000014, 0.000041, -89.999992, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(2751, 1469.035888, -2166.054687, -10.545130, -0.000014, 0.000041, -89.999992, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(2751, 1469.035888, -2167.745605, -10.741419, -0.000014, 0.000048, -89.999992, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1560, "7_11_door", "CJ_CHROME2", 0x00000000);
    asdwtx = CreateDynamicObject(2751, 1469.035888, -2167.745605, -10.643337, -0.000014, 0.000048, -89.999992, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(2751, 1469.035888, -2167.745605, -10.545130, -0.000014, 0.000048, -89.999992, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(2751, 1469.035888, -2169.576416, -10.741419, -0.000014, 0.000066, -89.999992, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1560, "7_11_door", "CJ_CHROME2", 0x00000000);
    asdwtx = CreateDynamicObject(2751, 1469.035888, -2169.576416, -10.643337, -0.000014, 0.000066, -89.999992, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(2751, 1469.035888, -2169.576416, -10.545130, -0.000014, 0.000066, -89.999992, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(2751, 1469.035888, -2171.687500, -10.741419, -0.000014, 0.000074, -89.999992, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1560, "7_11_door", "CJ_CHROME2", 0x00000000);
    asdwtx = CreateDynamicObject(2751, 1469.035888, -2171.687500, -10.643337, -0.000014, 0.000074, -89.999992, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(2751, 1469.035888, -2171.687500, -10.545130, -0.000014, 0.000074, -89.999992, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1465.667846, -2159.556396, -10.033045, 0.000000, 360.000000, 179.999755, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(2751, 1469.035888, -2164.474365, -10.741419, -0.000014, 0.000034, -89.999992, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1560, "7_11_door", "CJ_CHROME2", 0x00000000);
    asdwtx = CreateDynamicObject(2751, 1469.035888, -2164.474365, -10.643337, -0.000014, 0.000034, -89.999992, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(2751, 1469.035888, -2164.474365, -10.545130, -0.000014, 0.000034, -89.999992, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19325, 1486.824829, -2170.624267, -8.905117, 89.999992, -111.624649, -68.375419, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1465.667846, -2159.556396, -6.533045, 0.000000, 360.000000, 179.999755, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1465.667846, -2150.010986, -10.033045, 0.000000, 360.000000, 179.999710, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1465.667846, -2150.010986, -6.533045, 0.000000, 360.000000, 179.999710, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(2311, 1486.464721, -2171.472656, -11.702236, 0.000014, 0.000000, 89.999954, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2257, 1486.812988, -2170.558349, -8.223232, -0.000014, 0.000000, -89.999954, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 4981, "wiresetc2_las", "lasunionclk", 0x00000000);
    asdwtx = CreateDynamicObject(19482, 1479.952880, -2167.279052, -8.602259, -0.000014, 0.000000, -89.999954, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(asdwtx, 0, "UBER", 140, "Lucida Sans Unicode", 130, 1, 0xFFFFFFFF, 0x00000000, 1);
    asdwtx = CreateDynamicObject(19325, 1479.954589, -2167.263671, -8.905117, 89.999992, -90.013122, 0.013059, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(2643, 1465.785644, -2172.562744, -8.682642, 0.000044, 0.000000, 89.999816, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-93-percent", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19482, 1465.773559, -2169.130126, -8.452114, 0.000000, 0.000014, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(asdwtx, 0, "UBER", 140, "Lucida Sans Unicode", 130, 1, 0xFF000000, 0x00000000, 1);
    asdwtx = CreateDynamicObject(2247, 1482.527832, -2168.959716, -10.349514, 0.000019, 0.000000, 114.299888, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 3902, "libertyhi3", "glass2_64", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1478.740722, -2169.853759, -10.003993, 11.199892, 0.000039, 1.599678, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1478.700561, -2169.854492, -10.003993, 11.199892, 0.000039, 1.599678, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1478.740234, -2169.840087, -10.072596, 11.199892, 0.000039, 1.599678, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1478.700195, -2169.840820, -10.072657, 11.199892, 0.000039, 1.599678, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1478.720458, -2169.848876, -10.023708, 11.199892, 0.000039, 1.599678, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    asdwtx = CreateDynamicObject(19475, 1478.696166, -2169.872070, -10.019556, -0.000022, 371.199829, -88.399971, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0xF0000000);
    SetDynamicObjectMaterialText(asdwtx, 0, "n", 140, "Wingdings", 90, 0, 0xFF404040, 0x00000000, 1);
    asdwtx = CreateDynamicObject(19475, 1478.745971, -2169.870849, -10.019556, -0.000022, 371.199829, -88.399971, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0xF0000000);
    SetDynamicObjectMaterialText(asdwtx, 0, "n", 140, "Wingdings", 90, 0, 0xFF404040, 0x00000000, 1);
    asdwtx = CreateDynamicObject(18641, 1478.589111, -2169.885009, -10.235926, -0.000022, 156.300003, -88.399971, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0xFFFFFFFF);
    SetDynamicObjectMaterial(asdwtx, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(19483, 1465.886230, -2172.853271, -8.602808, 0.000012, 0.000044, -0.000080, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(asdwtx, 0, "A", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    asdwtx = CreateDynamicObject(19560, 1478.728393, -2169.887451, -10.377895, 0.000000, 179.999984, -178.399917, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0x00000000);
    asdwtx = CreateDynamicObject(19483, 1465.886230, -2172.362792, -8.592800, 0.000012, 0.000044, -0.000080, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(asdwtx, 0, "M", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    asdwtx = CreateDynamicObject(19475, 1478.696044, -2169.863769, -10.061733, -0.000022, 371.199829, -88.399971, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0xF0000000);
    SetDynamicObjectMaterialText(asdwtx, 0, "n", 140, "Wingdings", 90, 0, 0xFF404040, 0x00000000, 1);
    asdwtx = CreateDynamicObject(19475, 1478.745849, -2169.862548, -10.061733, -0.000022, 371.199829, -88.399971, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0xF0000000);
    SetDynamicObjectMaterialText(asdwtx, 0, "n", 140, "Wingdings", 90, 0, 0xFF404040, 0x00000000, 1);
    asdwtx = CreateDynamicObject(19182, 1478.721801, -2169.895263, -9.811611, -0.000022, 101.199890, -88.399971, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1478.700683, -2169.855468, -10.003993, 11.199892, 0.000039, 1.599678, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0x00000000);
    asdwtx = CreateDynamicObject(18641, 1478.869750, -2169.876953, -10.235926, -0.000022, 156.300003, -88.399971, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0xFFFFFFFF);
    SetDynamicObjectMaterial(asdwtx, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1478.700195, -2169.841308, -10.072657, 11.199892, 0.000039, 1.599678, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0x00000000);
    asdwtx = CreateDynamicObject(19897, 1478.881835, -2169.836669, -10.170313, -0.000022, -78.699928, -88.399971, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18882, "hugebowls", "wallwhite2top", 0x00000000);
    asdwtx = CreateDynamicObject(19897, 1478.781860, -2169.839355, -10.170313, -0.000022, -78.699928, -88.399971, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18882, "hugebowls", "wallwhite2top", 0x00000000);
    asdwtx = CreateDynamicObject(19897, 1478.671997, -2169.842529, -10.170313, -0.000022, -78.699928, -88.399971, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18882, "hugebowls", "wallwhite2top", 0x00000000);
    asdwtx = CreateDynamicObject(19897, 1478.562133, -2169.845458, -10.170313, -0.000022, -78.699928, -88.399971, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18882, "hugebowls", "wallwhite2top", 0x00000000);
    asdwtx = CreateDynamicObject(19475, 1478.815673, -2169.842773, -10.020533, 0.000022, 348.800292, 91.599906, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    asdwtx = CreateDynamicObject(19478, 1478.495971, -2169.870361, -9.924404, 0.000022, 348.800292, 91.599906, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    asdwtx = CreateDynamicObject(19478, 1478.494873, -2169.832275, -10.110804, 0.000022, 348.800292, 91.599906, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1480.472045, -2169.853759, -10.003993, 11.199892, 0.000048, 1.599676, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1480.431884, -2169.854492, -10.003993, 11.199892, 0.000048, 1.599676, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1480.471557, -2169.840087, -10.072596, 11.199892, 0.000048, 1.599676, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1480.431518, -2169.840820, -10.072657, 11.199892, 0.000048, 1.599676, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1480.451782, -2169.848876, -10.023708, 11.199892, 0.000048, 1.599676, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    asdwtx = CreateDynamicObject(19475, 1480.427490, -2169.872070, -10.019556, -0.000029, 371.199829, -88.399948, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0xF0000000);
    SetDynamicObjectMaterialText(asdwtx, 0, "n", 140, "Wingdings", 90, 0, 0xFF404040, 0x00000000, 1);
    asdwtx = CreateDynamicObject(19475, 1480.477294, -2169.870849, -10.019556, -0.000029, 371.199829, -88.399948, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0xF0000000);
    SetDynamicObjectMaterialText(asdwtx, 0, "n", 140, "Wingdings", 90, 0, 0xFF404040, 0x00000000, 1);
    asdwtx = CreateDynamicObject(18641, 1480.320434, -2169.885009, -10.235926, -0.000029, 156.300003, -88.399948, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0xFFFFFFFF);
    SetDynamicObjectMaterial(asdwtx, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(19560, 1480.459716, -2169.887451, -10.377895, 0.000000, 179.999984, -178.399871, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0x00000000);
    asdwtx = CreateDynamicObject(19475, 1480.427368, -2169.863769, -10.061733, -0.000029, 371.199829, -88.399948, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0xF0000000);
    SetDynamicObjectMaterialText(asdwtx, 0, "n", 140, "Wingdings", 90, 0, 0xFF404040, 0x00000000, 1);
    asdwtx = CreateDynamicObject(19475, 1480.477172, -2169.862548, -10.061733, -0.000029, 371.199829, -88.399948, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0xF0000000);
    SetDynamicObjectMaterialText(asdwtx, 0, "n", 140, "Wingdings", 90, 0, 0xFF404040, 0x00000000, 1);
    asdwtx = CreateDynamicObject(19182, 1480.453125, -2169.895263, -9.811611, -0.000029, 101.199890, -88.399948, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1480.432006, -2169.855468, -10.003993, 11.199892, 0.000048, 1.599676, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0x00000000);
    asdwtx = CreateDynamicObject(18641, 1480.601074, -2169.876953, -10.235926, -0.000029, 156.300003, -88.399948, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0xFFFFFFFF);
    SetDynamicObjectMaterial(asdwtx, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1480.431518, -2169.841308, -10.072657, 11.199892, 0.000048, 1.599676, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19513, "whitephone", "whitephone", 0x00000000);
    asdwtx = CreateDynamicObject(19897, 1480.613159, -2169.836669, -10.170313, -0.000029, -78.699928, -88.399948, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18882, "hugebowls", "wallwhite2top", 0x00000000);
    asdwtx = CreateDynamicObject(19897, 1480.513183, -2169.839355, -10.170313, -0.000029, -78.699928, -88.399948, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18882, "hugebowls", "wallwhite2top", 0x00000000);
    asdwtx = CreateDynamicObject(19897, 1480.403320, -2169.842529, -10.170313, -0.000029, -78.699928, -88.399948, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18882, "hugebowls", "wallwhite2top", 0x00000000);
    asdwtx = CreateDynamicObject(19897, 1480.293457, -2169.845458, -10.170313, -0.000029, -78.699928, -88.399948, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18882, "hugebowls", "wallwhite2top", 0x00000000);
    asdwtx = CreateDynamicObject(19475, 1480.546997, -2169.842773, -10.020533, 0.000029, 348.800292, 91.599884, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    asdwtx = CreateDynamicObject(19478, 1480.227294, -2169.870361, -9.924404, 0.000029, 348.800292, 91.599884, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    asdwtx = CreateDynamicObject(19478, 1480.226196, -2169.832275, -10.110804, 0.000029, 348.800292, 91.599884, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    asdwtx = CreateDynamicObject(2662, 1480.561157, -2169.802734, -10.047634, -11.299997, -0.000014, 179.999908, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(asdwtx, 0, "UBER", 130, "Franklin Gothic Medium", 30, 0, 0xFFFFFFFF, 0x00000000, 1);
    asdwtx = CreateDynamicObject(2662, 1478.849975, -2169.802734, -10.047634, -11.299997, -0.000014, 179.999908, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(asdwtx, 0, "UBER", 130, "Franklin Gothic Medium", 30, 0, 0xFFFFFFFF, 0x00000000, 1);
    asdwtx = CreateDynamicObject(2247, 1486.514770, -2170.555419, -10.759609, -0.000007, 0.000006, -90.900024, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 3902, "libertyhi3", "glass2_64", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1490.398803, -2169.748291, -10.033045, 0.000000, 360.000000, 179.999710, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1490.398803, -2163.957519, -6.533045, 0.000000, 360.000000, 179.999710, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1500.793457, -2155.754394, -9.962856, -0.000014, 360.000000, -0.000136, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1490.398803, -2154.412109, -6.533045, 0.000000, 360.000000, 179.999664, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(18981, 1477.957153, -2138.817382, -12.173122, 0.000000, 270.000000, -179.999938, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14847, "mp_policesf", "mp_cop_vinyl", 0x00000000);
    asdwtx = CreateDynamicObject(19380, 1480.440795, -2146.633544, -8.393337, 0.000000, 270.000000, -179.999938, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 3922, "bistro", "marblekb_256128", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1470.565551, -2151.394042, -10.033045, -0.000022, 360.000000, -90.000114, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1470.585571, -2151.394042, -6.533045, -0.000022, 360.000000, -90.000114, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(18981, 1478.246826, -2154.699462, -2.693264, 0.000000, 270.000000, -179.999938, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 3922, "bistro", "marblekb_256128", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1472.511108, -2153.156982, -7.803187, 0.000007, 270.000000, 89.999870, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14847, "mp_policesf", "mp_cop_vinyl", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1489.393432, -2151.394042, -9.992945, -0.000029, 360.000000, -90.000091, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1489.363403, -2151.394042, -6.533045, -0.000029, 360.000000, -90.000091, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(3361, 1466.701049, -2156.430175, -9.788599, -0.000014, 0.000000, -89.999954, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14847, "mp_policesf", "mp_cop_vinyl", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 18646, "matcolours", "grey-70-percent", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 4830, "airport2", "sanairtex3", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 18646, "matcolours", "grey-70-percent", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1472.501098, -2153.177001, -7.813198, 0.000007, 270.000000, 89.999870, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1482.090820, -2153.156982, -7.803187, 0.000014, 270.000000, 89.999847, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14847, "mp_policesf", "mp_cop_vinyl", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1482.080810, -2153.177001, -7.813198, 0.000014, 270.000000, 89.999847, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1491.718994, -2153.156982, -7.803186, 0.000022, 270.000000, 89.999824, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14847, "mp_policesf", "mp_cop_vinyl", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1491.708984, -2153.177001, -7.813198, 0.000022, 270.000000, 89.999824, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1478.577636, -2167.154052, -3.093287, -0.000081, 360.000000, -89.999763, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1488.126708, -2167.154052, -3.093287, -0.000089, 360.000000, -89.999740, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1465.667846, -2169.164550, -3.093286, 0.000000, 360.000000, 179.999664, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1468.968383, -2167.154052, -3.031887, -0.000074, 360.000000, -89.999786, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1465.667846, -2159.556396, -3.093287, 0.000000, 360.000000, 179.999572, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1465.667846, -2150.010986, -3.061916, 0.000000, 360.000000, 179.999572, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1490.398803, -2163.957519, -3.093286, 0.000000, 360.000000, 179.999526, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1490.398803, -2154.412109, -3.093287, 0.000000, 360.000000, 179.999481, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1480.110961, -2151.394042, -2.731594, -0.000044, 360.000000, -90.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1470.585571, -2151.394042, -3.093287, -0.000050, 360.000000, -90.000022, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1489.363403, -2151.394042, -3.031887, -0.000051, 360.000000, -90.000022, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(18981, 1502.927490, -2163.787841, -12.173122, 0.000000, 270.000000, -179.999938, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14847, "mp_policesf", "mp_cop_vinyl", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1475.297363, -2146.551269, -10.033045, 0.000000, 360.000000, 179.999710, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1480.187377, -2144.998779, -10.033045, -0.000022, 360.000000, -90.000114, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1489.727294, -2144.998779, -10.033045, -0.000022, 360.000000, -90.000114, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1500.793457, -2165.363037, -9.993189, -0.000014, 360.000000, -0.000136, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19380, 1490.930419, -2146.633544, -8.393337, 0.000000, 270.000000, -179.999938, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 3922, "bistro", "marblekb_256128", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1490.398803, -2147.864501, -10.033045, 0.000000, 360.000000, 179.999664, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1499.003173, -2151.395263, -9.972805, -0.000029, 360.000000, -90.000091, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1495.162963, -2165.016357, -9.782801, -0.000029, 360.000000, -90.000091, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1504.783081, -2165.016357, -10.033045, -0.000029, 360.000000, -90.000091, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(18981, 1502.865966, -2163.564941, -7.773281, 0.000000, 270.000000, -179.999938, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 3922, "bistro", "marblekb_256128", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1490.339721, -2165.041503, -14.833033, -0.000007, -89.999969, -89.999977, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 4830, "airport2", "sanairtex3", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1490.339721, -2162.011230, -14.833033, -0.000007, -89.999969, -89.999977, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 4830, "airport2", "sanairtex3", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1490.339721, -2158.981689, -14.833033, -0.000007, -89.999969, -89.999977, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 4830, "airport2", "sanairtex3", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(19395, 1490.413696, -2154.281982, -9.992275, 0.000000, 0.000014, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(1499, 1490.403564, -2155.019531, -11.673122, 0.000014, 0.000000, 89.999954, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 1649, "wglass", "carshowwin2", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1484.679321, -2151.430908, -17.113613, 0.000000, 270.000000, -0.000029, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 4830, "airport2", "sanairtex3", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(19380, 1480.440795, -2146.633544, -8.203150, 0.000000, 270.000000, -179.999847, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    asdwtx = CreateDynamicObject(19380, 1490.930419, -2146.633544, -8.203150, 0.000000, 270.000000, -179.999847, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    asdwtx = CreateDynamicObject(19380, 1480.440795, -2146.633544, -8.012966, 0.000000, 270.000000, -179.999801, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    asdwtx = CreateDynamicObject(19380, 1490.930419, -2146.633544, -8.012966, 0.000000, 270.000000, -179.999801, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    asdwtx = CreateDynamicObject(19380, 1480.440795, -2146.633544, -7.842800, 0.000000, 270.000000, -179.999664, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    asdwtx = CreateDynamicObject(19380, 1490.930419, -2146.633544, -7.842800, 0.000000, 270.000000, -179.999664, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1484.639282, -2151.470947, -7.663173, 0.000000, 270.000000, -0.000029, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 4830, "airport2", "sanairtex3", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1478.468383, -2151.430908, -17.113613, 0.000000, 270.000000, -0.000029, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 4830, "airport2", "sanairtex3", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1478.509277, -2151.470947, -7.663173, 0.000000, 270.000000, -0.000029, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 4830, "airport2", "sanairtex3", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(19380, 1480.020385, -2146.633544, -7.802761, 0.000000, 270.000000, -179.999618, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14847, "mp_policesf", "mp_cop_vinyl", 0x00000000);
    asdwtx = CreateDynamicObject(19380, 1490.510009, -2146.633544, -7.802761, 0.000000, 270.000000, -179.999618, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14847, "mp_policesf", "mp_cop_vinyl", 0x00000000);
    asdwtx = CreateDynamicObject(19395, 1479.962768, -2151.359619, -9.992275, 0.000014, 0.000000, 89.999954, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(1499, 1480.702636, -2151.348144, -11.743189, 0.000000, -0.000014, 179.999908, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 1649, "wglass", "carshowwin2", 0x00000000);
    asdwtx = CreateDynamicObject(19395, 1479.962768, -2151.359619, -5.992031, 0.000014, 0.000000, 89.999954, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(1499, 1480.702636, -2151.348144, -7.742579, 0.000000, -0.000014, 179.999908, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 1649, "wglass", "carshowwin2", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1475.317260, -2146.541259, -6.533045, 0.000000, 360.000000, 179.999710, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1475.317260, -2146.541259, -3.033229, 0.000000, 360.000000, 179.999710, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1487.604858, -2146.541259, -6.533045, -0.000007, 360.000000, -0.000188, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1487.604858, -2146.541259, -3.033229, -0.000007, 360.000000, -0.000188, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1482.774902, -2142.257812, -6.533045, 0.000014, 360.000000, 89.999710, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1482.774902, -2142.257812, -3.033229, 0.000014, 360.000000, 89.999710, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1473.164428, -2142.257812, -6.533045, 0.000022, 360.000000, 89.999687, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1473.164428, -2142.257812, -3.033229, 0.000022, 360.000000, 89.999687, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19935, 1467.979614, -2154.420898, -7.798914, 0.000014, -0.000037, 179.999694, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1469.158081, -2154.844726, -7.013088, -0.000037, 270.000000, -89.999855, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1471.148559, -2154.844726, -7.013088, -0.000037, 270.000000, -89.999855, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    asdwtx = CreateDynamicObject(19935, 1472.369384, -2154.420898, -7.798914, 0.000014, -0.000045, 179.999649, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1473.547851, -2154.844726, -7.013088, -0.000045, 270.000000, -89.999832, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1475.538330, -2154.844726, -7.013088, -0.000045, 270.000000, -89.999832, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    asdwtx = CreateDynamicObject(19935, 1476.858886, -2154.420898, -7.798914, 0.000014, -0.000052, 179.999603, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1478.037353, -2154.844726, -7.013088, -0.000052, 270.000000, -89.999809, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1480.027832, -2154.844726, -7.013088, -0.000052, 270.000000, -89.999809, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    asdwtx = CreateDynamicObject(19935, 1481.190063, -2154.420898, -7.798914, 0.000014, -0.000059, 179.999557, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1482.368530, -2154.844726, -7.013088, -0.000059, 270.000000, -89.999786, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1484.359008, -2154.844726, -7.013088, -0.000059, 270.000000, -89.999786, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    asdwtx = CreateDynamicObject(19935, 1485.529907, -2154.420898, -7.798914, 0.000014, -0.000068, 179.999511, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1486.708374, -2154.844726, -7.013088, -0.000068, 270.000000, -89.999763, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1488.698852, -2154.844726, -7.013088, -0.000068, 270.000000, -89.999763, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    asdwtx = CreateDynamicObject(19935, 1489.919555, -2154.420898, -7.798914, 0.000014, -0.000068, 179.999511, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19935, 1490.349121, -2154.420898, -7.798914, 0.000014, -0.000068, 179.999511, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(8326, 1472.471923, -2153.561767, -3.360987, 0.100014, 90.000000, 89.999954, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18835, "mickytextures", "whiteforletters", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(8326, 1485.911621, -2153.451416, -3.084193, 0.100014, 270.000000, 89.999954, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18835, "mickytextures", "whiteforletters", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(8326, 1472.471923, -2165.073730, -3.360987, 0.100022, 90.000000, 89.999931, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18835, "mickytextures", "whiteforletters", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(8326, 1485.911621, -2164.971191, -3.084193, 0.100022, 270.000000, 89.999931, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18835, "mickytextures", "whiteforletters", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1473.098266, -2152.530517, -3.266871, 89.999992, 90.000053, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16377, "des_byofficeint", "ufo_pics2", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 1340, "foodkarts", "dogcart06", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1464.389160, -2152.530517, -3.266871, 89.999992, 90.000038, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16377, "des_byofficeint", "ufo_pics2", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 1340, "foodkarts", "dogcart06", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1464.379150, -2149.500976, -3.266871, 89.999992, 90.000038, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16377, "des_byofficeint", "ufo_pics2", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 1340, "foodkarts", "dogcart06", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1473.088256, -2149.500976, -3.266871, 89.999992, 90.000053, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16377, "des_byofficeint", "ufo_pics2", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 1340, "foodkarts", "dogcart06", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1481.767578, -2152.530517, -3.266871, 89.999992, 90.000068, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16377, "des_byofficeint", "ufo_pics2", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 1340, "foodkarts", "dogcart06", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1481.757568, -2149.500976, -3.266871, 89.999992, 90.000068, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16377, "des_byofficeint", "ufo_pics2", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 1340, "foodkarts", "dogcart06", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1481.758911, -2162.898437, -3.267116, 89.999992, 90.000022, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16377, "des_byofficeint", "ufo_pics2", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 1340, "foodkarts", "dogcart06", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1481.758911, -2165.917724, -3.267116, 89.999992, 90.000022, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16377, "des_byofficeint", "ufo_pics2", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 1340, "foodkarts", "dogcart06", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1473.049804, -2162.910400, -3.267116, 89.999992, 90.000038, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16377, "des_byofficeint", "ufo_pics2", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 1340, "foodkarts", "dogcart06", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1475.849731, -2162.947998, -1.972560, -0.000014, 360.000000, -90.000137, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1464.359252, -2162.928466, -3.267116, 89.999992, 90.000053, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16377, "des_byofficeint", "ufo_pics2", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 1340, "foodkarts", "dogcart06", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1466.230346, -2162.947998, -1.972560, -0.000014, 360.000000, -90.000137, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1466.230346, -2155.665039, -1.972560, -0.000014, 360.000000, -90.000137, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1475.870117, -2155.665039, -1.972560, -0.000014, 360.000000, -90.000137, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1475.870117, -2155.665039, -1.972560, -0.000014, 360.000000, -90.000137, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1485.509765, -2155.665039, -1.972560, -0.000014, 360.000000, -90.000137, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1481.670166, -2160.991455, -3.193018, -89.999992, 76.398292, 76.398254, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 14560, "triad_bar", "triad_decor1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1472.970581, -2160.991455, -3.193018, -89.999992, 76.398292, 76.398254, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 14560, "triad_bar", "triad_decor1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(19380, 1480.440795, -2146.511474, -3.272181, 0.000000, 270.000000, -179.999618, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 3922, "bistro", "marblekb_256128", 0x00000000);
    asdwtx = CreateDynamicObject(19380, 1490.940307, -2146.511474, -3.272181, 0.000000, 270.000000, -179.999618, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 3922, "bistro", "marblekb_256128", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1473.049804, -2165.929687, -3.267116, 89.999992, 90.000038, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16377, "des_byofficeint", "ufo_pics2", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 1340, "foodkarts", "dogcart06", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1464.359252, -2165.928222, -3.267116, 89.999992, 90.000053, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 16377, "des_byofficeint", "ufo_pics2", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 1340, "foodkarts", "dogcart06", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1485.479858, -2162.947998, -1.972560, -0.000014, 360.000000, -90.000137, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1495.099975, -2162.947998, -1.972560, -0.000014, 360.000000, -90.000137, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(976, 1464.260498, -2160.991455, -3.193017, -89.999992, 76.398292, 76.398254, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 14560, "triad_bar", "triad_decor1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(19426, 1489.939819, -2145.531250, -7.726953, 0.000036, 0.000000, 89.999885, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(1233, 1488.150878, -2145.420898, -8.097010, 0.000014, 359.999969, 89.999977, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 14668, "711c", "cj_white_wall2", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(19938, 1488.710571, -2145.458740, -10.366419, 0.000000, 0.000029, 0.999997, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1488.703002, -2145.324707, -12.399990, 0.000000, 0.000036, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19938, 1488.710571, -2145.458740, -9.786463, 0.000000, 0.000029, 0.999997, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1485.568359, -2145.324707, -12.399990, 0.000000, 0.000036, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19938, 1488.710571, -2145.458740, -10.916468, 0.000000, 0.000029, 0.999997, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1485.558471, -2145.324707, -8.409998, 0.000012, 179.999969, -0.000012, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19426, 1489.119506, -2144.817382, -10.287014, 0.000000, 0.000036, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1488.963012, -2145.657714, -10.420008, 89.999992, 90.000030, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19426, 1490.230102, -2144.817382, -10.287014, 0.000000, 0.000036, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19426, 1489.939819, -2145.531250, -13.106960, 0.000036, 0.000000, 89.999885, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1490.390869, -2145.657226, -11.159998, 89.999992, 90.000030, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1490.390869, -2145.657226, -10.180019, 89.999992, 90.000030, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1490.390869, -2145.657226, -9.190029, 89.999992, 90.000030, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1489.791015, -2145.657226, -11.610012, 0.000029, -89.999984, 89.999916, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1488.302612, -2145.657714, -11.609951, 0.000029, -89.999984, 89.999916, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1486.801513, -2145.657226, -11.610012, 0.000029, -89.999984, 89.999916, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1486.499633, -2145.657714, -10.420008, 89.999992, 90.000030, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1488.692871, -2145.247802, -10.420008, 89.999992, 90.000030, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19354, 1483.729248, -2145.154296, -10.924892, 0.000022, 0.000000, 89.999931, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1489.791015, -2145.657226, -9.219997, 0.000044, -89.999984, 89.999870, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1488.791381, -2145.657226, -9.219997, 0.000044, -89.999984, 89.999870, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1487.791137, -2145.657226, -9.219997, 0.000044, -89.999984, 89.999870, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1486.790527, -2145.657226, -9.219997, 0.000044, -89.999984, 89.999870, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19922, 1488.682373, -2145.324707, -8.409998, 0.000012, 179.999969, -0.000012, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19426, 1489.889038, -2145.529785, -10.287014, 0.000036, 0.000000, 89.999885, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    asdwtx = CreateDynamicObject(19922, 1491.043334, -2145.324707, -10.079981, 0.000000, 89.999961, 179.999771, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1489.943969, -2144.963378, -9.920008, 0.000000, 270.000000, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1490.944580, -2144.963378, -9.920008, 0.000000, 270.000000, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1490.314331, -2144.963378, -12.170008, 0.000000, 360.000000, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1490.314331, -2144.963378, -8.659998, 0.000000, 540.000000, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19354, 1486.369628, -2144.063476, -11.075037, 0.000007, -0.000014, 179.999877, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19922, 1487.223754, -2144.934570, -12.170008, 0.000000, 360.000000, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(2259, 1487.593261, -2145.724121, -11.860012, 0.000000, 0.000036, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0xFFFFFFFF);
    SetDynamicObjectMaterial(asdwtx, 1, 18067, "intclothes_acc", "mp_cloth_vic", 0x00000000);
    asdwtx = CreateDynamicObject(19354, 1486.938354, -2145.154296, -10.944911, 0.000022, 0.000000, 89.999931, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19922, 1487.223754, -2144.934570, -8.670008, 0.000000, 540.000000, 179.999801, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19352, 1487.098144, -2145.424072, -11.379969, 0.000000, -0.000036, 179.999771, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18901, "matclothes", "bandanacloth3", 0x00000000);
    asdwtx = CreateDynamicObject(19922, 1484.204223, -2144.934570, -12.170008, 0.000000, 360.000000, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1489.513916, -2144.934570, -10.669947, 0.000000, 270.000000, -0.000029, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19939, 1487.181152, -2145.247314, -11.540003, 0.000029, -89.999984, 89.999916, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1488.181030, -2145.247314, -11.540003, 0.000029, -89.999984, 89.999916, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1489.171508, -2145.247314, -11.540003, 0.000029, -89.999984, 89.999916, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1487.181152, -2145.247314, -9.300013, 0.000036, -89.999984, 89.999893, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1488.181030, -2145.247314, -9.300074, 0.000036, -89.999984, 89.999893, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1489.171508, -2145.247314, -9.300074, 0.000036, -89.999984, 89.999893, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1486.555541, -2145.247314, -8.874903, 89.999992, -269.149261, -90.850593, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1488.685913, -2145.247314, -8.874903, 89.999992, -269.574523, -90.425300, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19805, 1487.800781, -2145.215576, -11.959986, 0.000000, 0.000044, -0.000029, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 6094, "canalsg_law", "ws_sheetwood_clean", 0xFFFFFFFF);
    SetDynamicObjectMaterial(asdwtx, 1, 14479, "skuzzy_motelmain", "mp_CJ_Laminate1", 0x00000000);
    asdwtx = CreateDynamicObject(19805, 1487.800781, -2145.215576, -8.880030, 0.000000, 0.000044, -0.000029, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 6094, "canalsg_law", "ws_sheetwood_clean", 0xFFFFFFFF);
    SetDynamicObjectMaterial(asdwtx, 1, 14479, "skuzzy_motelmain", "mp_CJ_Laminate1", 0x00000000);
    asdwtx = CreateDynamicObject(19805, 1489.600952, -2145.215576, -10.130030, 0.000000, 0.000044, -0.000029, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 6094, "canalsg_law", "ws_sheetwood_clean", 0xFFFFFFFF);
    SetDynamicObjectMaterial(asdwtx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19805, 1489.600952, -2145.215576, -11.370020, 0.000000, 0.000044, -0.000029, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 6094, "canalsg_law", "ws_sheetwood_clean", 0xFFFFFFFF);
    SetDynamicObjectMaterial(asdwtx, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(18980, 1485.463378, -2145.878173, -8.658289, 89.999992, 179.999984, -89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1485.801879, -2145.657226, -11.610012, 0.000029, -89.999984, 89.999916, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(1233, 1487.054199, -2145.419921, -9.639980, 89.999992, 180.000015, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 14668, "711c", "cj_white_wall2", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(3032, 1488.151123, -2145.247070, -8.726833, 0.000000, 720.000000, -179.999801, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14561, "triad_neon", "lightalp1a", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(2394, 1487.194580, -2145.421142, -9.990015, 0.000000, 0.000036, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 2, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(2253, 1488.951171, -2145.880859, -11.350002, 0.000036, 0.000000, 89.999885, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1355, "break_s_bins", "dirt64b", 0xFFFFFFFF);
    SetDynamicObjectMaterial(asdwtx, 1, 19598, "sfbuilding1", "darkwood1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 801, "gta_proc_ferns", "veg_bush2", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 801, "gta_proc_ferns", "veg_bushgrn", 0x00000000);
    asdwtx = CreateDynamicObject(2394, 1484.634399, -2145.421142, -9.990015, 0.000000, 0.000036, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 2, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1484.811401, -2145.657226, -11.610012, 0.000029, -89.999984, 89.999916, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(3032, 1488.152099, -2145.245117, -8.734827, 0.000000, 360.000000, 0.000091, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14561, "triad_neon", "lightalp1a", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1482.438232, -2145.324707, -12.399990, 0.000000, 0.000036, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1479.307495, -2145.324707, -12.399990, 0.000000, 0.000036, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1476.187866, -2145.324707, -12.399990, 0.000000, 0.000036, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19815, 1467.329833, -2151.545410, -9.161037, 0.000022, -0.000037, 179.999679, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0xFFCCCCCC);
    asdwtx = CreateDynamicObject(19815, 1469.740966, -2151.558105, -8.679347, 0.000014, 28.799961, 179.999633, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0xFFCCCCCC);
    asdwtx = CreateDynamicObject(19815, 1470.925781, -2151.558105, -8.028957, 0.000014, 28.799961, 179.999633, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0xFFCCCCCC);
    asdwtx = CreateDynamicObject(19815, 1470.204345, -2152.906738, -8.424036, 0.000009, 28.799961, 179.999664, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0xFFCCCCCC);
    asdwtx = CreateDynamicObject(19922, 1481.104858, -2144.934570, -12.170008, 0.000000, 360.000000, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1477.975341, -2144.934570, -12.170008, 0.000000, 360.000000, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1474.845458, -2144.934570, -12.170008, 0.000000, 360.000000, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19354, 1477.319335, -2145.154296, -10.954859, 0.000022, 0.000000, 89.999931, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19354, 1480.529418, -2145.154296, -10.954920, 0.000022, 0.000000, 89.999931, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19354, 1474.109375, -2145.154296, -10.954494, 0.000022, 0.000000, 89.999931, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19354, 1486.629882, -2144.063476, -11.075037, 0.000007, -0.000014, 179.999877, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19922, 1482.428466, -2145.324707, -8.409998, 0.000012, 179.999969, -0.000012, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1479.308715, -2145.324707, -8.409998, 0.000012, 179.999969, -0.000012, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1476.188964, -2145.324707, -8.409998, 0.000012, 179.999969, -0.000012, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19354, 1485.739624, -2144.003173, -10.804531, 0.000007, -0.000014, 179.999877, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1485.800781, -2145.657226, -9.219997, 0.000044, -89.999984, 89.999870, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1484.800292, -2145.657226, -9.219997, 0.000044, -89.999984, 89.999870, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1483.809936, -2145.657226, -9.219997, 0.000044, -89.999984, 89.999870, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1482.858276, -2145.657226, -9.219997, 0.000051, -89.999984, 89.999847, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1481.857788, -2145.657226, -9.219997, 0.000051, -89.999984, 89.999847, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1480.867431, -2145.657226, -9.219997, 0.000051, -89.999984, 89.999847, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1479.877685, -2145.657226, -9.219997, 0.000059, -89.999984, 89.999824, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1478.877197, -2145.657226, -9.219997, 0.000059, -89.999984, 89.999824, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1477.886840, -2145.657226, -9.219997, 0.000059, -89.999984, 89.999824, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1476.895263, -2145.657226, -9.219997, 0.000067, -89.999984, 89.999801, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1475.904907, -2145.657226, -9.219997, 0.000067, -89.999984, 89.999801, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19354, 1475.468505, -2144.363525, -10.684412, 0.000007, -0.000014, 179.999877, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1483.881225, -2145.657226, -11.610012, 0.000037, -89.999984, 89.999893, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1482.890747, -2145.657226, -11.610012, 0.000037, -89.999984, 89.999893, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1482.031127, -2145.657226, -11.610012, 0.000037, -89.999984, 89.999893, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1481.040649, -2145.657226, -11.610012, 0.000037, -89.999984, 89.999893, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1480.110473, -2145.657226, -11.610012, 0.000044, -89.999984, 89.999870, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1479.119995, -2145.657226, -11.610012, 0.000044, -89.999984, 89.999870, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1478.150878, -2145.657226, -11.610012, 0.000044, -89.999984, 89.999870, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1477.160400, -2145.657226, -11.610012, 0.000044, -89.999984, 89.999870, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1476.230224, -2145.657226, -11.610012, 0.000051, -89.999984, 89.999847, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1475.239746, -2145.657226, -11.610012, 0.000051, -89.999984, 89.999847, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(1233, 1484.425292, -2145.419921, -9.639980, 89.999992, 180.000015, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 14668, "711c", "cj_white_wall2", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(19354, 1483.629150, -2144.003173, -10.804531, 0.000007, -0.000014, 179.999877, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19354, 1483.358886, -2144.003173, -10.804531, 0.000007, -0.000014, 179.999877, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1483.498413, -2145.657714, -10.420008, 89.999992, 90.000030, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19352, 1487.498535, -2145.424072, -11.379969, 0.000036, 0.000000, 89.999885, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 2819, "gb_bedroomclths01", "GB_clothesbed05", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1476.953247, -2145.389160, -11.358119, 0.000036, 0.000029, 89.999855, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1477.463623, -2145.389160, -10.848110, 89.999992, 356.601226, -86.601173, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1477.463623, -2145.389160, -9.938137, 89.999992, 356.601226, -86.601173, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19354, 1482.768676, -2144.003173, -10.804531, 0.000007, -0.000014, 179.999877, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19922, 1483.389160, -2144.063476, -8.619898, 0.000028, 179.999969, 89.999938, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1484.799438, -2144.063476, -8.619898, 0.000028, 179.999969, 89.999938, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1481.118530, -2144.904296, -8.619898, 0.000012, 179.999969, 179.999893, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1477.978637, -2144.904296, -8.619898, 0.000012, 179.999969, 179.999893, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19922, 1474.849243, -2144.904296, -8.619898, 0.000012, 179.999969, 179.999893, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19939, 1478.988281, -2145.261962, -9.429102, 0.000009, 0.000020, 89.999809, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1478.988281, -2145.261962, -11.339138, 0.000009, 0.000020, 89.999809, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1478.478759, -2145.261962, -10.829128, 89.999992, 1068.956542, -78.956550, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1478.478759, -2145.261962, -9.919094, 89.999992, 1068.956542, -78.956550, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1479.898193, -2145.261962, -11.339138, 0.000009, 0.000020, 89.999809, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1480.888183, -2145.261962, -11.339138, 0.000009, 0.000020, 89.999809, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1481.878173, -2145.261962, -11.339138, 0.000009, 0.000020, 89.999809, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1482.398925, -2145.261962, -10.829128, 89.999992, 1069.790527, -79.790550, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1482.398925, -2145.261962, -9.919094, 89.999992, 1069.790527, -79.790550, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1481.898193, -2145.261962, -9.429102, 0.000009, 0.000020, 89.999809, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1480.898193, -2145.261962, -9.429102, 0.000009, 0.000020, 89.999809, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1479.958251, -2145.261962, -9.429102, 0.000009, 0.000020, 89.999809, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(2398, 1478.874023, -2145.196533, -10.841761, -0.000019, 0.000009, 0.000007, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1482.548583, -2145.657714, -10.420008, 89.999992, 90.000030, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19354, 1478.149658, -2144.003173, -10.684412, 0.000007, -0.000014, 179.999877, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1478.278320, -2145.657714, -10.420008, 89.999992, 90.000030, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19354, 1478.389892, -2144.003173, -10.684412, 0.000007, -0.000014, 179.999877, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19354, 1482.380249, -2144.043212, -10.684412, 0.000007, -0.000014, 179.999877, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19940, 1475.767456, -2145.657714, -10.420008, 89.999992, 90.000030, -89.999961, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 15034, "genhotelsave", "ab_mottleWhite", 0x00000000);
    asdwtx = CreateDynamicObject(19354, 1475.898681, -2144.033203, -10.684412, 0.000007, -0.000014, 179.999877, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1476.443847, -2145.389160, -10.848110, 89.999992, 356.601226, -86.601165, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1476.443847, -2145.389160, -9.938137, 89.999992, 356.601226, -86.601165, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1476.953247, -2145.389160, -10.998133, 0.000036, -1.099967, 89.999855, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1476.953247, -2145.394775, -10.698206, 0.000036, -1.099967, 89.999855, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1476.953247, -2145.401367, -10.358242, 0.000036, -1.099967, 89.999855, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1476.953247, -2145.408203, -10.008326, 0.000036, -1.099967, 89.999855, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1476.953247, -2145.414550, -9.678371, 0.000036, -1.099967, 89.999855, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(957, 1477.765869, -2145.449218, -9.441370, 0.000000, 0.000014, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    asdwtx = CreateDynamicObject(957, 1476.235595, -2145.449218, -9.441370, 0.000000, 0.000014, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    asdwtx = CreateDynamicObject(957, 1476.875610, -2145.879638, -9.211147, 0.000000, 0.000014, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    asdwtx = CreateDynamicObject(957, 1480.456420, -2145.879638, -9.211147, 0.000000, 0.000014, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    asdwtx = CreateDynamicObject(957, 1484.876464, -2145.879638, -9.211147, 0.000000, 0.000014, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    asdwtx = CreateDynamicObject(957, 1487.826660, -2145.879638, -9.211147, 0.000000, 0.000014, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 18646, "matcolours", "grey-93-percent", 0x00000000);
    asdwtx = CreateDynamicObject(19939, 1476.960205, -2145.418457, -9.411829, 0.000036, -1.099967, 89.999855, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(2491, 1489.822021, -2150.828857, -11.719448, 0.000036, 0.000014, 89.999855, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(2491, 1484.591918, -2150.828857, -11.719448, 0.000036, 0.000014, 89.999855, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(2491, 1475.381591, -2150.828857, -11.719448, 0.000036, 0.000014, 89.999855, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14650, "ab_trukstpc", "mp_CJ_WOOD5", 0x00000000);
    asdwtx = CreateDynamicObject(2257, 1490.298706, -2148.509277, -10.164211, -0.000014, 0.000000, -89.999954, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    asdwtx = CreateDynamicObject(2257, 1487.458740, -2151.279296, -10.164211, 0.000000, -0.000014, 179.999908, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 15040, "cuntcuts", "GB_canvas06", 0x00000000);
    asdwtx = CreateDynamicObject(2257, 1475.398925, -2148.548583, -10.164211, 0.000014, 0.000000, 89.999954, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 18901, "matclothes", "bandanazigzag", 0x00000000);
    asdwtx = CreateDynamicObject(19325, 1483.725585, -2167.055175, -11.845789, -0.000022, 360.000000, -90.000045, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19325, 1477.085083, -2167.055175, -11.845789, -0.000022, 360.000000, -90.000045, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19325, 1490.366699, -2167.055175, -11.845789, -0.000022, 360.000000, -90.000045, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19325, 1483.725585, -2167.055175, -7.726222, -0.000037, 360.000000, -90.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14544, "ab_woozieb", "ap_screens1", 0x00000000);
    asdwtx = CreateDynamicObject(19325, 1477.085083, -2167.055175, -3.735987, -0.000029, 360.000000, -90.000022, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19325, 1490.366699, -2167.055175, -3.735987, -0.000029, 360.000000, -90.000022, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19325, 1477.106079, -2167.055175, -7.726222, -0.000037, 360.000000, -90.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14544, "ab_woozieb", "ap_screens1", 0x00000000);
    asdwtx = CreateDynamicObject(19325, 1483.727172, -2167.055175, -3.735987, -0.000029, 360.000000, -90.000022, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19325, 1490.355590, -2167.055175, -7.726222, -0.000037, 360.000000, -90.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14544, "ab_woozieb", "ap_screens1", 0x00000000);
    asdwtx = CreateDynamicObject(19325, 1495.936401, -2159.834472, -11.036280, 0.000029, 630.000000, 179.999847, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(19428, 1495.989013, -2156.602539, -12.793421, 0.000022, 0.000000, 89.999931, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19428, 1495.989013, -2159.902099, -12.793421, 0.000022, 0.000000, 89.999931, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19428, 1495.989013, -2163.052734, -12.793421, 0.000022, 0.000000, 89.999931, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19428, 1495.989013, -2161.222412, -11.742519, -0.000007, 270.000000, -89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(19428, 1495.999023, -2158.432373, -11.732508, -0.000007, 270.000000, -89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wall2", 0x00000000);
    asdwtx = CreateDynamicObject(1472, 1493.474487, -2152.200195, -11.673122, -0.000022, 0.000000, -89.999931, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14847, "mp_policesf", "mp_cop_vinyl", 0x00000000);
    asdwtx = CreateDynamicObject(1472, 1493.474487, -2153.701416, -11.673122, -0.000022, 0.000000, -89.999931, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14847, "mp_policesf", "mp_cop_vinyl", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1499.492309, -2152.685546, -11.332972, 0.000007, 810.000000, -90.000122, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14847, "mp_policesf", "mp_cop_vinyl", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1497.251342, -2154.476806, -12.983180, 0.000007, 720.000000, 89.999816, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    asdwtx = CreateDynamicObject(1785, 1495.935546, -2158.981689, -9.170557, -0.000029, 0.000037, -0.699966, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, -1, "none", "none", 0xFFA9A9A9);
    asdwtx = CreateDynamicObject(19835, 1495.819824, -2158.739501, -9.141506, 0.000037, 90.000038, 89.299888, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(19087, 1499.732299, -2152.148925, -9.754908, -11.100004, -1.900007, -137.499969, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4", 0x00000000);
    asdwtx = CreateDynamicObject(19087, 1499.917602, -2152.491699, -9.755579, 9.999987, -0.000014, 177.599685, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.828125, -2152.364990, -9.491053, 0.000018, 360.000000, 117.599891, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-16", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.528930, -2152.030029, -10.977016, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-16", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.567993, -2152.104492, -10.977016, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-16", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.606933, -2152.179199, -10.977016, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-16", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.646484, -2152.254638, -10.977016, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-16", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.685302, -2152.328857, -10.977016, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-16", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.724121, -2152.403564, -10.977016, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-16", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.763671, -2152.478759, -10.977016, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-16", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.802978, -2152.554199, -10.977016, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-16", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.840087, -2152.625000, -10.977016, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-16", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.879028, -2152.699707, -10.977016, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-16", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.855224, -2152.712402, -11.003017, 0.000018, 180.000000, 117.599891, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-2", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.818237, -2152.641357, -11.003017, 0.000018, 180.000000, 117.599891, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-2", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.778930, -2152.566650, -11.003017, 0.000018, 180.000000, 117.599891, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-2", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.740112, -2152.492187, -11.003017, 0.000018, 180.000000, 117.599891, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-2", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.700683, -2152.416748, -11.003017, 0.000018, 180.000000, 117.599891, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-2", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.661376, -2152.341552, -11.003017, 0.000018, 180.000000, 117.599891, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-2", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.622924, -2152.267822, -11.003017, 0.000018, 180.000000, 117.599891, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-2", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.584106, -2152.193603, -11.003017, 0.000018, 180.000000, 117.599891, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-2", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.544677, -2152.118408, -11.003017, 0.000018, 180.000000, 117.599891, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-2", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.505371, -2152.043701, -11.003017, 0.000018, 180.000000, 117.599891, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-2", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.704345, -2152.149169, -10.027065, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-21", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.743652, -2152.224365, -10.027065, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-21", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.782470, -2152.298583, -10.027065, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-21", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.821777, -2152.374023, -10.027065, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-21", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.861206, -2152.449218, -10.027065, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-21", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.896972, -2152.517578, -10.027065, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-21", 0x00000000);
    asdwtx = CreateDynamicObject(2237, 1499.723266, -2152.379638, -10.181362, -5.199934, -180.000000, -62.400016, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(2237, 1499.863525, -2152.306396, -10.306301, 5.199925, -0.000009, 117.599891, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(19087, 1499.805419, -2152.326660, -9.996424, -0.000009, 9.999986, -152.399826, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.852050, -2152.352539, -9.517054, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-2", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.812866, -2152.277343, -9.517054, 89.999992, 205.578872, -87.978836, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-16", 0x00000000);
    asdwtx = CreateDynamicObject(19792, 1499.788818, -2152.289794, -9.491053, 0.000018, 360.000000, 117.599891, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18787, "matramps", "cardboard4-16", 0x00000000);
    asdwtx = CreateDynamicObject(19435, 1499.949462, -2152.247558, -10.516445, 0.000007, 0.000012, 29.799995, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    asdwtx = CreateDynamicObject(19482, 1494.843750, -2164.899414, -9.592127, 0.000014, 0.000000, 89.999954, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(asdwtx, 0, "UBER", 140, "Lucida Sans Unicode", 130, 1, 0xFF000000, 0x00000000, 1);
    asdwtx = CreateDynamicObject(2161, 1491.784423, -2164.932617, -11.722927, 0.000000, -0.000029, 179.999816, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2161, 1493.114379, -2164.932617, -11.722927, 0.000000, -0.000029, 179.999816, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2161, 1494.434326, -2164.932617, -11.722927, 0.000000, -0.000029, 179.999816, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2161, 1495.754760, -2164.932617, -11.722927, 0.000000, -0.000029, 179.999816, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2161, 1497.084960, -2164.932617, -11.722927, 0.000000, -0.000029, 179.999816, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2161, 1498.415405, -2164.932617, -11.722927, 0.000000, -0.000029, 179.999816, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2161, 1499.695312, -2164.932617, -11.722927, 0.000000, -0.000029, 179.999816, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2643, 1497.877685, -2164.964111, -9.383266, 0.000036, -0.000029, 179.999633, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-93-percent", 0xFF003399);
    asdwtx = CreateDynamicObject(2643, 1497.899658, -2164.971435, -9.391262, 0.000036, -0.000029, 179.999633, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-93-percent", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(2643, 1497.857666, -2164.974365, -9.393276, 0.000036, -0.000029, 179.999633, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-93-percent", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(2643, 1497.877685, -2164.984130, -9.373255, 0.000036, -0.000029, 179.999633, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-93-percent", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19483, 1498.148193, -2164.873779, -9.313442, 0.000044, 0.000036, 89.999801, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(asdwtx, 0, "A", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    asdwtx = CreateDynamicObject(19483, 1497.657714, -2164.873779, -9.303432, 0.000044, 0.000036, 89.999801, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(asdwtx, 0, "M", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    asdwtx = CreateDynamicObject(19828, 1481.423583, -2144.730712, -6.871303, 0.000014, -0.000052, 179.999603, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1714, "cj_office", "chromepipe2_32hv", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19828, 1481.423583, -2144.850585, -6.871303, 0.000014, -0.000052, 179.999603, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1714, "cj_office", "chromepipe2_32hv", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(1581, 1481.386230, -2144.834716, -6.511500, -8.999571, 360.000030, -179.999359, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19655, "mattubes", "bluedirt1", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1481.402343, -2144.840576, -6.497280, -8.999571, -0.000027, -179.999359, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18996, "mattextures", "sampblack", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1481.372314, -2144.836181, -6.526943, -8.999571, -0.000027, -179.999359, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18996, "mattextures", "sampblack", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1481.402343, -2144.836181, -6.526943, -8.999571, -0.000027, -179.999359, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18996, "mattextures", "sampblack", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1481.372314, -2144.840576, -6.497280, -8.999571, -0.000027, -179.999359, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18996, "mattextures", "sampblack", 0x00000000);
    asdwtx = CreateDynamicObject(19896, 1481.376464, -2144.825195, -6.760951, 69.999977, 180.000091, -0.000161, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1714, "cj_office", "chromepipe2_32hv", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(18867, 1481.038574, -2144.199707, -6.818508, -0.000000, -0.000055, -162.999664, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18996, "mattextures", "sampred", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 18996, "mattextures", "sampwhite", 0x00000000);
    asdwtx = CreateDynamicObject(1581, 1481.036132, -2144.231689, -6.793972, 89.999992, 449.149719, -89.149322, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18996, "mattextures", "sampblack", 0x00000000);
    asdwtx = CreateDynamicObject(1719, 1480.656982, -2145.142333, -6.531702, 0.000014, 89.999946, 179.999603, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18996, "mattextures", "sampblack", 0xFFFFFFFF);
    SetDynamicObjectMaterial(asdwtx, 1, 18646, "matcolours", "grey-10-percent", 0x00000000);
    asdwtx = CreateDynamicObject(1719, 1480.496826, -2145.142333, -6.551723, 0.000014, 270.000000, 179.999603, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18996, "mattextures", "sampblack", 0xFFFFFFFF);
    SetDynamicObjectMaterial(asdwtx, 1, 18646, "matcolours", "grey-10-percent", 0x00000000);
    asdwtx = CreateDynamicObject(1904, 1480.576660, -2145.106201, -6.613613, 0.000052, 90.000015, 89.999809, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19655, "mattubes", "bluedirt1", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19428, 1479.972167, -2144.111816, -8.596035, -0.000000, 0.000046, -0.000182, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18996, "mattextures", "sampwhite", 0x00000000);
    asdwtx = CreateDynamicObject(19828, 1481.523681, -2144.780761, -6.911281, -0.000052, 270.000000, -89.999839, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1714, "cj_office", "chromepipe2_32hv", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19828, 1481.237304, -2144.786621, -6.911281, -0.000052, 270.000000, -89.999839, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1714, "cj_office", "chromepipe2_32hv", 0x00000000);
    asdwtx = CreateDynamicObject(19828, 1481.323486, -2144.850585, -6.871303, 0.000014, -0.000052, 179.999603, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1714, "cj_office", "chromepipe2_32hv", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19828, 1481.323486, -2144.730712, -6.871303, 0.000014, -0.000052, 179.999603, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 1714, "cj_office", "chromepipe2_32hv", 0xFFFFFFFF);
    asdwtx = CreateDynamicObject(19428, 1481.633056, -2144.511230, -6.905055, -0.000000, 90.000045, -0.000182, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18996, "mattextures", "sampwhite", 0x00000000);
    asdwtx = CreateDynamicObject(19937, 1482.910400, -2144.344726, -8.016872, -0.000000, 0.000046, -0.000182, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "ws_runwaytarmac", 0xFF707070);
    asdwtx = CreateDynamicObject(19940, 1482.345947, -2144.385253, -7.857020, -0.000000, 90.000045, -0.000182, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "ws_runwaytarmac", 0xFF707070);
    asdwtx = CreateDynamicObject(19937, 1482.910400, -2146.245117, -8.016872, -0.000000, 0.000046, -0.000182, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "ws_runwaytarmac", 0xFF707070);
    asdwtx = CreateDynamicObject(19937, 1482.500244, -2146.245117, -8.016872, -0.000000, 0.000046, -0.000182, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "ws_runwaytarmac", 0xFF707070);
    asdwtx = CreateDynamicObject(19940, 1482.506103, -2144.385253, -7.647059, -0.000000, 0.000046, -0.000182, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "ws_runwaytarmac", 0xFF707070);
    asdwtx = CreateDynamicObject(19940, 1482.506103, -2144.385253, -7.237026, -0.000000, 0.000046, -0.000182, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "ws_runwaytarmac", 0xFF707070);
    asdwtx = CreateDynamicObject(2161, 1482.757324, -2144.476074, -8.563137, -0.000046, -0.000000, -90.000038, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(asdwtx, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(2161, 1482.757324, -2143.895751, -8.563137, -0.000046, -0.000000, -90.000038, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(asdwtx, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(19935, 1482.502685, -2143.879882, -8.020290, -0.000000, 0.000046, -0.000182, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "ws_runwaytarmac", 0xFF707070);
    asdwtx = CreateDynamicObject(1827, 1482.682861, -2146.711425, -7.410610, -0.000000, 0.000046, -0.000182, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19962, "samproadsigns", "materialtext1", 0xFFFFFFFF);
    SetDynamicObjectMaterial(asdwtx, 1, 18996, "mattextures", "sampwhite", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1495.162963, -2165.016357, -13.242797, -0.000029, 360.000000, -90.000091, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    asdwtx = CreateDynamicObject(2161, 1487.519775, -2150.139160, -7.712673, -0.000029, -0.000022, -89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2161, 1487.519775, -2148.809082, -7.712673, -0.000029, -0.000022, -89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2161, 1487.519775, -2147.489257, -7.712673, -0.000029, -0.000022, -89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2161, 1487.519775, -2146.168701, -7.712673, -0.000029, -0.000022, -89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2161, 1487.519775, -2144.838378, -7.712673, -0.000029, -0.000022, -89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2161, 1487.519775, -2143.508056, -7.712673, -0.000029, -0.000022, -89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(1977, 1469.242797, -2151.970703, -11.784145, 0.000000, 0.000029, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(2490, 1469.114746, -2152.224853, -10.912380, 0.000000, 0.000029, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 2541, "cj_ss_1", "cj_juice", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(2490, 1469.114746, -2152.184814, -10.732386, 0.000000, 0.000029, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 2541, "cj_ss_1", "CJ_CEREAL", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(2490, 1469.114746, -2152.224853, -10.392359, 0.000000, 0.000029, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 2535, "cj_ss_4", "CJ_FISHY", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(2490, 1469.114746, -2152.184814, -10.212368, 0.000000, 0.000029, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 2543, "cj_ss_3", "CJ_DOOG_FOOD", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(2490, 1469.114746, -2152.224853, -9.882349, 0.000000, 0.000029, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 10310, "boigas_sfe", "vgnburger_256", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(2443, 1469.230102, -2151.926025, -11.939234, 0.000000, 0.000029, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(19797, 1469.545898, -2152.400634, -10.074184, 0.000000, 90.000030, 0.000020, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(asdwtx, 0, "g", 130, "Webdings", 40, 1, 0xFFFFFFFF, 0x00000000, 1);
    asdwtx = CreateDynamicObject(1977, 1472.003295, -2151.970703, -11.784145, 0.000000, 0.000045, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(2490, 1471.875244, -2152.224853, -10.912380, 0.000000, 0.000045, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 2541, "cj_ss_1", "cj_juice", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(2490, 1471.875244, -2152.184814, -10.732386, 0.000000, 0.000045, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 2541, "cj_ss_1", "CJ_CEREAL", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(2490, 1471.875244, -2152.224853, -10.392359, 0.000000, 0.000045, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 2535, "cj_ss_4", "CJ_FISHY", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(2490, 1471.875244, -2152.184814, -10.212368, 0.000000, 0.000045, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 2543, "cj_ss_3", "CJ_DOOG_FOOD", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(2490, 1471.875244, -2152.224853, -9.882349, 0.000000, 0.000045, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 10310, "boigas_sfe", "vgnburger_256", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(2443, 1471.990600, -2151.926025, -11.939234, 0.000000, 0.000045, 0.000000, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 2, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 19962, "samproadsigns", "materialtext1", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 5, 19962, "samproadsigns", "materialtext1", 0x00000000);
    asdwtx = CreateDynamicObject(19797, 1472.306396, -2152.400634, -10.074184, 0.000000, 90.000045, 0.000020, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(asdwtx, 0, "g", 130, "Webdings", 40, 1, 0xFFFFFFFF, 0x00000000, 1);
    asdwtx = CreateDynamicObject(2163, 1487.488769, -2144.312988, -3.347377, -0.000007, 179.999984, -89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2163, 1487.488769, -2146.093994, -3.347377, -0.000007, 179.999984, -89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2163, 1487.488769, -2147.864013, -3.347377, -0.000007, 179.999984, -89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2163, 1487.488769, -2149.633544, -3.347377, -0.000007, 179.999984, -89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2163, 1487.438720, -2150.563964, -3.357508, -0.000007, 179.999984, -89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1488.796386, -2142.945800, -5.367214, 89.999992, 360.000000, -89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(19448, 1488.796386, -2151.085937, -3.047023, 89.999992, 360.000000, -89.999984, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 14385, "trailerkb", "tr_wood1", 0x00000000);
    asdwtx = CreateDynamicObject(2290, 1475.927856, -2148.360107, -7.761439, 0.000052, 0.000007, 89.999732, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18646, "matcolours", "grey-30-percent", 0x00000000);
    asdwtx = CreateDynamicObject(2387, 1478.029541, -2147.646972, -7.911342, 0.000037, 0.000007, 89.999778, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 3, 14581, "ab_mafiasuitea", "ab_books", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 4, 4010, "lanblokb2", "bluewhitebuildwall2", 0x00000000);
    asdwtx = CreateDynamicObject(1579, 1475.920043, -2146.420898, -7.081447, 89.999992, 503.979370, -87.979492, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    asdwtx = CreateDynamicObject(1579, 1475.927368, -2148.290771, -7.081447, 89.999992, 574.879394, -87.979492, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    asdwtx = CreateDynamicObject(19893, 1477.511962, -2147.675292, -6.911342, -0.000027, -0.000027, -124.599868, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 1, 14802, "lee_bdupsflat", "Bdup_Poster", 0x00000000);
    asdwtx = CreateDynamicObject(2257, 1475.422729, -2147.276611, -5.051235, 0.000029, 0.000007, 89.999801, 444, 4, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(asdwtx, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(asdwtx, 1, 16150, "ufo_bar", "ufo_pics1", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(19445, 1465.534912, -2169.205810, -12.522060, 0.000004, 0.000014, -0.000068, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(859, 1468.171142, -2165.887939, -10.920557, 0.000006, -0.000012, 139.699966, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(804, 1467.439941, -2170.920410, -10.026821, 0.000000, 0.000020, -0.000037, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(859, 1468.427001, -2168.505859, -10.920557, -0.000017, -0.000007, -110.299995, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(804, 1467.229736, -2166.168945, -10.026821, -0.000014, 0.000004, -89.999992, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(867, 1466.419189, -2168.287109, -11.027248, 0.000000, 0.000020, -0.000037, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(859, 1467.385253, -2172.119140, -10.920557, 0.000006, -0.000012, 139.699966, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1708, 1486.265991, -2168.156005, -11.779871, -0.000014, 0.000000, -90.599906, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1708, 1486.316040, -2172.246093, -11.779871, -0.000014, 0.000000, -89.999954, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(19807, 1476.770263, -2169.101074, -10.726467, 0.000014, -0.000017, 158.099975, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(19807, 1478.955322, -2169.537841, -10.716457, 0.000006, -0.000022, -177.799819, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2855, 1479.593505, -2169.873779, -10.386623, 0.000022, 0.000007, 89.999900, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2894, 1481.439697, -2169.353271, -10.546413, 60.199985, -0.000046, -159.399826, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2855, 1486.474121, -2170.013916, -11.206995, 0.000007, -0.000007, 179.999847, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2654, 1484.706176, -2145.448974, -11.172144, 0.000014, 0.000000, 89.999954, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2390, 1478.872070, -2145.172119, -9.973232, -0.000019, 0.000009, 0.000007, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2390, 1479.642089, -2145.172119, -9.973232, -0.000019, 0.000009, 0.000007, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2390, 1480.472167, -2145.172119, -9.973232, -0.000019, 0.000009, 0.000007, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2390, 1481.271972, -2145.172119, -9.973232, -0.000019, 0.000009, 0.000007, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2390, 1481.981933, -2145.172119, -9.973232, -0.000019, 0.000009, 0.000007, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2398, 1479.644042, -2145.196533, -10.841761, -0.000019, 0.000009, 0.000007, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2398, 1480.473876, -2145.196533, -10.841761, -0.000019, 0.000009, 0.000007, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2398, 1481.253662, -2145.196533, -10.841761, -0.000019, 0.000009, 0.000007, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2398, 1482.013671, -2145.196533, -10.841761, -0.000019, 0.000009, 0.000007, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(18645, 1477.183349, -2145.489013, -11.220240, -0.000014, 0.000000, -86.199966, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(18645, 1476.714477, -2145.520263, -11.220240, -0.000014, 0.000000, -86.199966, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(18645, 1476.714477, -2145.520263, -10.849880, -0.000014, 0.000000, -86.199966, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(18645, 1477.183227, -2145.489257, -10.849880, -0.000014, 0.000000, -86.199966, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(18645, 1476.714477, -2145.520263, -10.539576, -0.000022, 0.000000, -86.199943, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(18645, 1477.183227, -2145.489257, -10.539576, -0.000022, 0.000000, -86.199943, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(18645, 1476.714477, -2145.520263, -10.209255, -0.000029, 0.000001, -86.199920, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(18645, 1477.183227, -2145.489257, -10.209255, -0.000029, 0.000001, -86.199920, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(18645, 1476.714477, -2145.520263, -9.879116, -0.000037, 0.000001, -86.199897, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(18645, 1477.183227, -2145.489257, -9.879116, -0.000037, 0.000001, -86.199897, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(18645, 1476.714477, -2145.520263, -9.549098, -0.000045, 0.000003, -86.199874, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(18645, 1477.183227, -2145.489257, -9.549098, -0.000045, 0.000003, -86.199874, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1484.781250, -2148.994628, -11.715296, 0.000000, 0.000029, 0.400000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1484.774414, -2148.024414, -11.715296, 0.000000, 0.000029, 0.400000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1485.731445, -2148.988037, -11.715296, 0.000000, 0.000037, 0.400000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1485.724609, -2148.017822, -11.715296, 0.000000, 0.000037, 0.400000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1486.681152, -2148.981445, -11.715296, 0.000000, 0.000044, 0.400000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1486.674316, -2148.011230, -11.715296, 0.000000, 0.000044, 0.400000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1487.641113, -2148.974853, -11.715296, 0.000000, 0.000051, 0.400000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1487.634277, -2148.004638, -11.715296, 0.000000, 0.000051, 0.400000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1488.591308, -2148.968261, -11.715296, 0.000000, 0.000059, 0.400000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1488.584472, -2147.998046, -11.715296, 0.000000, 0.000059, 0.400000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1476.171142, -2148.994628, -11.715296, 0.000000, 0.000037, 0.400000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1476.164306, -2148.024414, -11.715296, 0.000000, 0.000037, 0.400000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1477.121337, -2148.988037, -11.715296, 0.000000, 0.000044, 0.400000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1477.114501, -2148.017822, -11.715296, 0.000000, 0.000044, 0.400000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1478.071044, -2148.981445, -11.715296, 0.000000, 0.000050, 0.400000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1746, 1478.064208, -2148.011230, -11.715296, 0.000000, 0.000050, 0.400000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1474.209838, -2155.492919, -11.710659, 0.000000, -0.000014, 179.999908, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1473.673828, -2154.177001, -11.712856, 0.000000, 0.000014, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1476.850097, -2155.492919, -11.710659, 0.000000, -0.000022, 179.999862, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1476.314086, -2154.177001, -11.712856, 0.000000, 0.000022, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1479.220825, -2155.492919, -11.710659, 0.000000, -0.000029, 179.999816, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1478.684814, -2154.177001, -11.712856, 0.000000, 0.000029, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1481.740356, -2155.492919, -11.710659, 0.000000, -0.000037, 179.999771, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1481.204345, -2154.177001, -11.712856, 0.000000, 0.000037, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1484.049560, -2155.492919, -11.710659, 0.000000, -0.000045, 179.999725, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1483.513549, -2154.177001, -11.712856, 0.000000, 0.000045, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1486.380004, -2155.492919, -11.710659, 0.000000, -0.000052, 179.999679, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1485.843994, -2154.177001, -11.712856, 0.000000, 0.000052, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1474.209838, -2159.062500, -11.710659, 0.000000, -0.000022, 179.999862, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1473.673828, -2157.746582, -11.712856, 0.000000, 0.000022, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1476.850097, -2159.062500, -11.710659, 0.000000, -0.000029, 179.999816, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1476.314086, -2157.746582, -11.712856, 0.000000, 0.000029, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1479.220825, -2159.062500, -11.710659, 0.000000, -0.000037, 179.999771, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1478.684814, -2157.746582, -11.712856, 0.000000, 0.000037, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1481.740356, -2159.062500, -11.710659, 0.000000, -0.000045, 179.999725, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1481.204345, -2157.746582, -11.712856, 0.000000, 0.000045, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1484.049560, -2159.062500, -11.710659, 0.000000, -0.000052, 179.999679, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1483.513549, -2157.746582, -11.712856, 0.000000, 0.000052, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1486.380004, -2159.062500, -11.710659, 0.000000, -0.000059, 179.999633, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1485.843994, -2157.746582, -11.712856, 0.000000, 0.000059, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1474.209838, -2162.372558, -11.710659, 0.000000, -0.000045, 179.999725, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1473.673828, -2161.056640, -11.712856, 0.000000, 0.000045, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1476.850097, -2162.372558, -11.710659, 0.000000, -0.000052, 179.999679, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1476.314086, -2161.056640, -11.712856, 0.000000, 0.000052, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1479.220825, -2162.372558, -11.710659, 0.000000, -0.000059, 179.999633, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1478.684814, -2161.056640, -11.712856, 0.000000, 0.000059, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1481.740356, -2162.372558, -11.710659, 0.000000, -0.000068, 179.999588, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1481.204345, -2161.056640, -11.712856, 0.000000, 0.000068, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1484.049560, -2162.372558, -11.710659, 0.000000, -0.000075, 179.999542, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1483.513549, -2161.056640, -11.712856, 0.000000, 0.000075, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 1486.380004, -2162.372558, -11.710659, 0.000000, -0.000082, 179.999496, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 1485.843994, -2161.056640, -11.712856, 0.000000, 0.000082, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1492.722167, -2157.199951, -11.592983, 0.000014, 0.000000, 89.999954, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1492.722167, -2158.560791, -11.592983, 0.000014, 0.000000, 89.999954, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1492.722167, -2159.842041, -11.592983, 0.000014, 0.000000, 89.999954, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1492.722167, -2161.162109, -11.592983, 0.000014, 0.000000, 89.999954, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1492.722167, -2162.620849, -11.592983, 0.000014, 0.000000, 89.999954, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1499.112670, -2162.620849, -11.592983, -0.000014, -0.000006, -89.999961, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1499.112670, -2161.260009, -11.592983, -0.000014, -0.000006, -89.999961, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1499.112670, -2159.978759, -11.592983, -0.000014, -0.000006, -89.999961, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1499.112670, -2158.658691, -11.592983, -0.000014, -0.000006, -89.999961, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1499.112670, -2157.199951, -11.592983, -0.000014, -0.000006, -89.999961, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(19611, 1495.942260, -2158.973144, -7.455897, 0.000000, 179.999984, -179.999938, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(3111, 1499.765625, -2152.360839, -10.254665, 84.299987, 0.000095, -62.400157, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(19808, 1481.438476, -2144.220947, -6.785975, 0.000014, -0.000052, 179.999603, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1481.241210, -2142.676513, -7.731716, -0.000014, 0.000037, 0.000014, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, 1481.241210, -2146.386230, -7.741663, -0.000014, 0.000007, -179.999893, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2270, 1487.023193, -2143.837158, -5.726771, -0.000014, 0.000000, -89.999954, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2270, 1487.023193, -2150.257568, -5.726771, -0.000014, 0.000000, -89.999954, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2484, 1487.321777, -2147.023925, -5.506679, 0.000014, 0.000000, 89.999954, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(948, 1475.901855, -2149.298583, -7.791347, 0.000029, 0.000007, 89.999801, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2002, 1476.641479, -2142.727294, -7.716823, 0.000000, 0.000014, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2332, 1478.325805, -2142.734375, -7.276821, 0.000000, 0.000014, 0.000000, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(2852, 1478.068603, -2147.557128, -6.901331, 0.000037, 0.000007, 89.999778, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1667, 1477.479736, -2147.232421, -6.801356, 0.000037, 0.000007, 89.999778, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(1520, 1477.849365, -2147.114257, -6.831386, 0.000037, 0.000007, 89.999778, 444, 4, -1, 200.00, 200.00); 
    CreateDynamicObject(948, 1475.901855, -2145.367919, -7.791347, 0.000029, 0.000007, 89.999801, 444, 4, -1, 200.00, 200.00);
}