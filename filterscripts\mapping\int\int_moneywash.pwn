CreateMoneywashInt()
{
    new STREAMER_TAG_OBJECT:mnsxht;

    mnsxht = CreateDynamicObject(18981, 1477.758178, -26.387659, 7.583236, 180.000000, 90.000000, 0.000000, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    mnsxht = CreateDynamicObject(19445, 1485.297363, -38.736591, 9.823242, 0.000000, 0.000000, 90.000000, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 4586, "skyscrap3_lan2", "sl_skyscrpr05wall1", 0x00000000);
    mnsxht = CreateDynamicObject(19445, 1475.666992, -38.736591, 9.823242, 0.000000, 0.000000, 90.000000, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 4586, "skyscrap3_lan2", "sl_skyscrpr05wall1", 0x00000000);
    mnsxht = CreateDynamicObject(19445, 1485.297363, -38.696594, 8.033233, 180.000000, 0.000000, 90.000000, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 3049, "qrydrx", "ws_corr_1_red", 0x00000000);
    mnsxht = CreateDynamicObject(19445, 1475.687500, -38.696594, 8.033233, 180.000000, 0.000000, 90.000000, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 3049, "qrydrx", "ws_corr_1_red", 0x00000000);
    mnsxht = CreateDynamicObject(19445, 1470.795898, -33.856460, 9.823242, 0.000007, 0.000000, -0.000060, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 4586, "skyscrap3_lan2", "sl_skyscrpr05wall1", 0x00000000);
    mnsxht = CreateDynamicObject(19445, 1470.835937, -33.876968, 8.033233, 0.000000, 179.999984, 179.999862, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 3049, "qrydrx", "ws_corr_1_red", 0x00000000);
    mnsxht = CreateDynamicObject(19445, 1475.666992, -28.956604, 9.823242, 0.000000, 0.000000, -89.999969, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 4586, "skyscrap3_lan2", "sl_skyscrpr05wall1", 0x00000000);
    mnsxht = CreateDynamicObject(19445, 1485.297363, -28.956604, 9.823242, 0.000000, 0.000000, -89.999969, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 4586, "skyscrap3_lan2", "sl_skyscrpr05wall1", 0x00000000);
    mnsxht = CreateDynamicObject(19445, 1475.666992, -28.996601, 8.033233, 0.000007, 179.999984, 89.999969, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 3049, "qrydrx", "ws_corr_1_red", 0x00000000);
    mnsxht = CreateDynamicObject(19445, 1485.276855, -28.996601, 8.033233, 0.000007, 179.999984, 89.999969, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 3049, "qrydrx", "ws_corr_1_red", 0x00000000);
    mnsxht = CreateDynamicObject(19445, 1490.070068, -33.876968, 9.823242, 0.000007, 0.000007, 179.999832, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 4586, "skyscrap3_lan2", "sl_skyscrpr05wall1", 0x00000000);
    mnsxht = CreateDynamicObject(19445, 1490.030029, -33.856460, 8.033233, 0.000000, 179.999984, -0.000197, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 3049, "qrydrx", "ws_corr_1_red", 0x00000000);
    mnsxht = CreateDynamicObject(2633, 1473.174682, -37.398365, 7.093219, 0.000000, 0.000000, 0.000000, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 1, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    mnsxht = CreateDynamicObject(2633, 1477.404418, -37.398365, 7.093219, 0.000000, 0.000000, 0.000000, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 1, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    mnsxht = CreateDynamicObject(19353, 1480.950439, -37.783519, 8.063285, 0.000000, -72.399986, 0.000000, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    mnsxht = CreateDynamicObject(18981, 1477.758178, -26.387659, 12.033271, 180.000000, 90.000000, 0.000000, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 4550, "skyscr1_lan2", "sl_librarywall1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1475.335083, -38.480377, 9.669006, 360.000000, 90.000000, 90.000000, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1475.335083, -36.090385, 11.199005, 360.000000, 180.000000, 90.000000, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1475.335083, -26.870378, 11.199005, 360.000000, 180.000000, 90.000000, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1475.335083, -29.320362, 8.838993, 360.000000, 270.000000, 90.000000, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1475.335083, -31.480394, 11.199005, 360.000000, 180.000000, 90.000000, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1471.263549, -38.480377, 9.669006, 0.000000, 90.000000, 89.999977, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1471.263549, -36.090385, 11.199005, 0.000000, 180.000000, 89.999977, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1471.263549, -26.870378, 11.199005, 0.000000, 180.000000, 89.999977, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1471.263549, -29.320362, 8.838993, 0.000000, 270.000000, 89.999977, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1471.263549, -31.480394, 11.199005, 0.000000, 180.000000, 89.999977, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1478.795898, -38.480377, 9.669006, 0.000007, 90.000000, 89.999954, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1478.795898, -36.090385, 11.199005, 0.000007, 180.000000, 89.999954, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1478.795898, -26.870378, 11.199005, 0.000007, 180.000000, 89.999954, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1478.795898, -29.320362, 8.838993, 0.000007, 270.000000, 89.999954, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1478.795898, -31.480394, 11.199005, 0.000007, 180.000000, 89.999954, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1482.496093, -38.480377, 9.669006, 0.000014, 90.000000, 89.999931, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1482.496093, -36.090385, 11.199005, 0.000014, 180.000000, 89.999931, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1482.496093, -26.870378, 11.199005, 0.000014, 180.000000, 89.999931, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1482.496093, -29.320362, 8.838993, 0.000014, 270.000000, 89.999931, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1482.496093, -31.480394, 11.199005, 0.000014, 180.000000, 89.999931, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1486.347045, -38.480377, 9.669006, 0.000022, 90.000000, 89.999908, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1486.347045, -36.090385, 11.199005, 0.000022, 180.000000, 89.999908, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1486.347045, -26.870378, 11.199005, 0.000022, 180.000000, 89.999908, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1486.347045, -29.320362, 8.838993, 0.000022, 270.000000, 89.999908, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1486.347045, -31.480394, 11.199005, 0.000022, 180.000000, 89.999908, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1489.718261, -38.480377, 9.669006, 0.000029, 90.000000, 89.999885, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1489.718261, -36.090385, 11.199005, 0.000029, 180.000000, 89.999885, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1489.718261, -26.870378, 11.199005, 0.000029, 180.000000, 89.999885, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1489.718261, -29.320362, 8.838993, 0.000029, 270.000000, 89.999885, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2960, 1489.718261, -31.480394, 11.199005, 0.000029, 180.000000, 89.999885, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 18882, "hugebowls", "metalplate1", 0x00000000);
    mnsxht = CreateDynamicObject(2747, 1484.729858, -30.007101, 8.493232, 0.000000, 0.000007, 0.000000, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(mnsxht, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(mnsxht, 2, 18646, "matcolours", "grey-20-percent", 0x00000000);
    mnsxht = CreateDynamicObject(2747, 1486.279541, -30.007101, 8.493232, 0.000000, 0.000007, 0.000000, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(mnsxht, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(mnsxht, 2, 18646, "matcolours", "grey-20-percent", 0x00000000);
    mnsxht = CreateDynamicObject(2747, 1489.344482, -32.132011, 8.493232, 0.000007, 0.000007, 89.999946, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(mnsxht, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(mnsxht, 2, 18646, "matcolours", "grey-20-percent", 0x00000000);
    mnsxht = CreateDynamicObject(2747, 1489.344482, -30.582330, 8.493232, 0.000007, 0.000007, 89.999946, 87, 1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mnsxht, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(mnsxht, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(mnsxht, 2, 18646, "matcolours", "grey-20-percent", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(2970, 1472.679687, -38.613800, 8.719009, 0.000000, 0.000000, 90.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1627, 1473.652099, -38.741897, 11.199016, 0.000000, 0.000000, 630.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1615, 1473.053710, -38.762756, 11.189006, 0.000000, 0.000000, -90.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 1475.337524, -32.138835, 11.443244, 0.000000, 0.000000, 90.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 1475.337524, -35.988826, 11.443244, 0.000000, 0.000000, 90.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 1478.806518, -32.138835, 11.443244, 0.000014, 0.000000, 89.999954, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 1478.806518, -35.988826, 11.443244, 0.000014, 0.000000, 89.999954, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 1482.506713, -32.138835, 11.443244, 0.000022, 0.000000, 89.999931, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 1482.506713, -35.988826, 11.443244, 0.000022, 0.000000, 89.999931, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 1486.347045, -32.138835, 11.443244, 0.000029, 0.000000, 89.999908, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 1486.347045, -35.988826, 11.443244, 0.000029, 0.000000, 89.999908, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2395, 1486.713623, -29.486574, 7.603233, 0.000000, 0.000000, 0.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2395, 1483.003784, -29.486574, 7.603233, 0.000000, 0.000000, 0.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2395, 1489.883544, -29.906578, 7.603233, 0.000000, 0.000000, -90.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1428, 1487.920776, -29.973260, 8.083236, 0.000000, 0.000000, -28.099988, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2201, 1484.743896, -29.852619, 8.943241, 0.000000, 0.000000, 6.599998, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1829, 1479.600341, -29.848667, 8.563236, 0.000000, 0.000000, -11.199998, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19627, 1485.056152, -29.877531, 8.943241, 0.000000, 0.000000, -56.099998, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2953, 1485.799560, -29.862405, 8.913242, 0.000000, 0.000000, -163.800079, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19873, 1486.807861, -29.743480, 8.993236, 0.000000, 0.000000, 0.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(2196, 1488.968627, -32.423664, 8.893238, 0.000000, 0.000000, 129.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(11728, 1490.005126, -30.160575, 9.163234, 0.000000, 0.000000, -87.200012, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(16666, 1472.611572, -30.531663, 7.533264, 0.000000, 0.000000, 720.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(929, 1477.226684, -29.997282, 9.043247, 0.000000, 0.000000, 90.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(952, 1488.906494, -35.844299, 9.363246, 0.000000, 0.000000, 180.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, 1471.734863, -35.055912, 8.573238, 0.000000, 0.000000, 0.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1208, 1485.505615, -38.267097, 8.083236, 0.000000, 0.000000, 0.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(1208, 1484.735595, -38.267097, 8.083236, 0.000000, 0.000000, 0.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19898, 1474.321289, -34.404731, 8.123237, 0.000000, 0.000000, 0.000000, 87, 1, -1, 200.00, 200.00); 
    CreateDynamicObject(19898, 1478.553955, -31.544652, 8.123237, 0.000000, 0.000000, 75.199989, 87, 1, -1, 200.00, 200.00);
}