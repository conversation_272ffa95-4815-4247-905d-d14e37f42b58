RemoveSmeltingStoneBuilding(playerid)
{
    RemoveBuildingForPlayer(playerid, 3569, 2212.090, -2267.070, 14.937, 0.250);
    RemoveBuildingForPlayer(playerid, 3747, 2212.090, -2267.070, 14.937, 0.250);
    RemoveBuildingForPlayer(playerid, 3569, 2204.629, -2274.409, 14.937, 0.250);
    RemoveBuildingForPlayer(playerid, 3747, 2204.629, -2274.409, 14.937, 0.250);
    RemoveBuildingForPlayer(playerid, 3569, 2219.419, -2259.520, 14.882, 0.250);
    RemoveBuildingForPlayer(playerid, 3747, 2219.419, -2259.520, 14.882, 0.250);
    RemoveBuildingForPlayer(playerid, 3627, 2220.780, -2261.050, 15.906, 0.250);
    RemoveBuildingForPlayer(playerid, 3686, 2220.780, -2261.050, 15.906, 0.250);
    RemoveBuildingForPlayer(playerid, 3569, 2226.969, -2252.139, 14.937, 0.250);
    RemoveBuildingForPlayer(playerid, 3747, 2226.969, -2252.139, 14.937, 0.250);
    RemoveBuildingForPlayer(playerid, 3569, 2234.389, -2244.830, 14.937, 0.250);
    RemoveBuildingForPlayer(playerid, 3747, 2234.389, -2244.830, 14.937, 0.250);
    RemoveBuildingForPlayer(playerid, 3627, 2195.090, -2216.840, 15.906, 0.250);
    RemoveBuildingForPlayer(playerid, 3686, 2195.090, -2216.840, 15.906, 0.250);
    RemoveBuildingForPlayer(playerid, 5244, 2198.850, -2213.919, 14.882, 0.250);
    RemoveBuildingForPlayer(playerid, 5305, 2198.850, -2213.919, 14.882, 0.250);
    RemoveBuildingForPlayer(playerid, 3577, 2170.080, -2257.659, 16.039, 0.250);
    RemoveBuildingForPlayer(playerid, 3632, 2168.830, -2257.520, 17.250, 0.250);
    RemoveBuildingForPlayer(playerid, 3632, 2169.350, -2258.070, 17.242, 0.250);
    RemoveBuildingForPlayer(playerid, 3627, 2169.120, -2276.590, 15.906, 0.250);
    RemoveBuildingForPlayer(playerid, 3686, 2169.120, -2276.590, 15.906, 0.250);
    RemoveBuildingForPlayer(playerid, 3633, 2167.659, -2256.780, 12.750, 0.250);
    RemoveBuildingForPlayer(playerid, 3633, 2167.659, -2256.780, 13.710, 0.250);
    RemoveBuildingForPlayer(playerid, 3633, 2167.659, -2256.780, 14.671, 0.250);
    RemoveBuildingForPlayer(playerid, 3632, 2167.800, -2257.350, 16.382, 0.250);
    RemoveBuildingForPlayer(playerid, 3632, 2167.169, -2257.129, 16.406, 0.250);
    RemoveBuildingForPlayer(playerid, 5132, 2163.290, -2251.610, 14.140, 0.250);
    RemoveBuildingForPlayer(playerid, 3632, 2158.570, -2251.020, 15.812, 0.250);
    RemoveBuildingForPlayer(playerid, 3632, 2158.050, -2250.510, 15.812, 0.250);
    RemoveBuildingForPlayer(playerid, 3633, 2154.510, -2254.479, 14.210, 0.250);
    RemoveBuildingForPlayer(playerid, 3633, 2153.770, -2253.090, 14.203, 0.250);
    RemoveBuildingForPlayer(playerid, 3631, 2149.139, -2266.909, 12.875, 0.250);
    RemoveBuildingForPlayer(playerid, 3632, 2150.659, -2251.550, 12.765, 0.250);
    RemoveBuildingForPlayer(playerid, 3632, 2150.280, -2250.850, 12.765, 0.250);
    RemoveBuildingForPlayer(playerid, 3631, 2163.379, -2262.689, 16.351, 0.250);
    RemoveBuildingForPlayer(playerid, 5260, 2161.340, -2264.909, 14.015, 0.250);
    RemoveBuildingForPlayer(playerid, 3631, 2161.850, -2264.090, 16.351, 0.250);
    RemoveBuildingForPlayer(playerid, 3633, 2158.010, -2257.270, 16.218, 0.250);
    RemoveBuildingForPlayer(playerid, 5262, 2152.709, -2256.780, 15.210, 0.250);
    RemoveBuildingForPlayer(playerid, 3633, 2140.379, -2254.100, 13.929, 0.250);
    RemoveBuildingForPlayer(playerid, 3633, 2142.909, -2256.340, 13.929, 0.250);
    RemoveBuildingForPlayer(playerid, 3631, 2142.300, -2255.899, 12.875, 0.250);
    RemoveBuildingForPlayer(playerid, 3632, 2144.300, -2258.149, 13.929, 0.250);
    RemoveBuildingForPlayer(playerid, 3633, 2150.699, -2252.909, 16.234, 0.250);
    RemoveBuildingForPlayer(playerid, 3632, 2149.810, -2253.370, 16.234, 0.250);
    RemoveBuildingForPlayer(playerid, 5131, 2151.729, -2273.300, 18.656, 0.250);
    RemoveBuildingForPlayer(playerid, 5215, 2151.729, -2273.300, 18.656, 0.250);
}

CreateSmeltingStoneExt()
{
    new STREAMER_TAG_OBJECT:smltxst;
    smltxst = CreateDynamicObject(5131, 2151.754150, -2272.839843, 18.631908, 0.000000, 0.000000, -44.299972, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(smltxst, 7, 3355, "cxref_savhus", "des_brick1", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 12, 16136, "des_telescopestuff", "ws_palebrickwall1", 0x00000000);
    smltxst = CreateDynamicObject(16083, 2149.570800, -2265.710937, 8.638050, 0.000006, -0.000004, 126.499992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(smltxst, 0, 10850, "bakerybit2_sfse", "frate64_yellow", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 1, 10850, "bakerybit2_sfse", "frate64_yellow", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 2, 10850, "bakerybit2_sfse", "frate64_yellow", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 4, 914, "industrialext", "cj_yellowgenerator", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 5, 914, "industrialext", "cj_yellowgenerator", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 6, 914, "industrialext", "cj_yellowgenerator", 0x00000000);
    smltxst = CreateDynamicObject(16446, 2149.612792, -2265.658447, 17.154968, 0.000005, 0.000005, 48.200008, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(smltxst, 0, 1243, "buoy", "yellowrust_64", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 1, 10850, "bakerybit2_sfse", "frate64_yellow", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 2, 10850, "bakerybit2_sfse", "frate64_yellow", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 3, 10850, "bakerybit2_sfse", "frate64_yellow", 0x00000000);
    smltxst = CreateDynamicObject(19376, 2171.774658, -2249.641357, 14.379796, 0.000000, 0.000000, -43.899959, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(smltxst, 0, 1677, "wshxrefhse2", "yellowbeige_128", 0x00000000);
    smltxst = CreateDynamicObject(18980, 2168.272705, -2253.522460, 7.509715, 0.000000, 0.000000, -43.699974, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(smltxst, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    smltxst = CreateDynamicObject(19355, 2173.299072, -2248.133544, 15.680181, -0.000005, 0.000005, -43.999980, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(smltxst, 0, "Safety can be fun", 130, "Arial", 49, 1, 0xFFFFFFFF, 0x00000000, 1);
    smltxst = CreateDynamicObject(19355, 2173.199951, -2248.236572, 15.020184, -0.000005, 0.000005, -43.999980, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(smltxst, 0, "d", 100, "Webdings", 50, 1, 0xFF39C5F7, 0x00000000, 1);
    smltxst = CreateDynamicObject(19951, 2171.614501, -2249.927734, 12.799166, 0.000000, 0.000000, 46.099998, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(smltxst, 1, 1419, "break_fence3", "CJ_FRAME_Glass", 0x00000000);
    smltxst = CreateDynamicObject(16083, 2155.763427, -2271.390869, 14.358057, 0.000006, -0.000004, 396.500000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(smltxst, 0, 10850, "bakerybit2_sfse", "frate64_yellow", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 1, 10850, "bakerybit2_sfse", "frate64_yellow", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 2, 10850, "bakerybit2_sfse", "frate64_yellow", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 4, 914, "industrialext", "cj_yellowgenerator", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 5, 914, "industrialext", "cj_yellowgenerator", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 6, 914, "industrialext", "cj_yellowgenerator", 0x00000000);
    smltxst = CreateDynamicObject(3258, 2135.746582, -2257.037353, -17.572076, 0.000000, 0.000000, -46.299995, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(smltxst, 1, 1381, "cranes_dyn2", "ws_sheetsteel", 0x00000000);
    smltxst = CreateDynamicObject(19353, 2149.558837, -2265.707519, 12.617049, 180.000000, 90.000000, 44.699981, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(smltxst, 0, 18752, "volcano", "lavalake", 0x00000000);
    smltxst = CreateDynamicObject(16083, 2138.564941, -2254.148681, 14.408063, 0.000006, -0.000004, 396.500000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(smltxst, 0, 10850, "bakerybit2_sfse", "frate64_yellow", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 1, 10850, "bakerybit2_sfse", "frate64_yellow", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 2, 10850, "bakerybit2_sfse", "frate64_yellow", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 4, 914, "industrialext", "cj_yellowgenerator", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 5, 914, "industrialext", "cj_yellowgenerator", 0x00000000);
    SetDynamicObjectMaterial(smltxst, 6, 914, "industrialext", "cj_yellowgenerator", 0x00000000);
    smltxst = CreateDynamicObject(19355, 2178.794189, -2253.941650, 18.550201, -0.000005, 0.000005, -44.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(smltxst, 0, "Smelter", 100, "Arial", 40, 1, 0xFF000000, 0x00000000, 1);
    smltxst = CreateDynamicObject(19355, 2179.812500, -2252.854492, 19.970205, -0.000005, 0.000005, -44.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(smltxst, 0, ".", 60, "Arial", 180, 0, 0xFFFFC300, 0x00000000, 1);
    smltxst = CreateDynamicObject(19355, 2177.192626, -2255.548583, 18.670206, -0.000005, 0.000005, -44.199989, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(smltxst, 0, "F", 100, "Webdings", 70, 1, 0xFFFFC300, 0x00000000, 1);
    smltxst = CreateDynamicObject(18762, 2239.248535, -2220.124023, 14.926622, 0.000000, 0.000000, 44.700019, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(smltxst, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    smltxst = CreateDynamicObject(18766, 2226.286865, -2207.145019, 13.436884, 0.000000, 0.000000, -44.900001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(smltxst, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    smltxst = CreateDynamicObject(19355, 2226.793212, -2207.040283, 13.810183, -0.000005, 0.000005, 44.799983, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(smltxst, 0, "Smelter", 90, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    smltxst = CreateDynamicObject(19357, 2227.793212, -2208.057128, 13.325933, 0.000000, 0.000000, 45.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(smltxst, 0, 18835, "mickytextures", "whiteforletters", 0x00000000);
    smltxst = CreateDynamicObject(19357, 2225.606933, -2205.870849, 13.325933, 0.000000, 0.000000, 45.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(smltxst, 0, 18835, "mickytextures", "whiteforletters", 0x00000000);
    smltxst = CreateDynamicObject(19355, 2226.020019, -2206.272216, 14.750191, -0.000005, 0.000005, 44.799983, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(smltxst, 0, ".", 30, "Arial", 60, 1, 0xFFFFC300, 0x00000000, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(2936, 2150.510498, -2265.785888, 12.474373, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2936, 2149.174072, -2265.317382, 12.374371, 0.000000, 0.000000, -69.500007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2936, 2149.695800, -2266.712402, 12.394371, 0.000000, 0.000000, -69.500007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(905, 2150.939941, -2272.261474, 13.614406, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(905, 2147.830810, -2269.756591, 13.614406, 0.000000, 0.000000, -69.200012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1303, 2142.998291, -2267.477294, 12.424265, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(905, 2143.485107, -2266.037597, 12.514403, 0.000000, 0.000000, -69.200012, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1239, 2173.231689, -2248.296875, 15.009198, 0.000005, 0.000005, 44.400005, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3258, 2165.522949, -2262.968017, -16.882091, 0.000000, 0.000000, -46.299995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3258, 2164.540283, -2264.793701, -16.882091, 0.000000, 0.000000, -46.299995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3864, 2145.808593, -2244.962402, 13.815086, 0.000000, 0.000000, 163.300048, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3864, 2143.146484, -2253.783691, 13.815086, 0.000000, 0.000000, 104.200057, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1538, 2133.352783, -2278.569824, 19.671875, 0.000000, 0.000000, 135.199966, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3864, 2183.715820, -2280.239257, 18.695098, 0.000000, 0.000000, -81.099975, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3864, 2202.665527, -2276.288085, 18.695098, 0.000000, 0.000000, -69.499984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(905, 2161.939697, -2267.154785, 12.669239, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(905, 2163.000732, -2267.154785, 12.669239, 0.000000, 0.000000, -16.800001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(905, 2162.419921, -2267.691650, 12.828924, 21.799999, 0.000000, -16.800001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(905, 2161.072021, -2268.574707, 12.586208, -6.200018, 0.000000, 52.699989, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3864, 2219.927246, -2211.628906, 18.695098, 0.000000, 0.000000, 81.899986, 0, 0, -1, 200.00, 200.00);
}