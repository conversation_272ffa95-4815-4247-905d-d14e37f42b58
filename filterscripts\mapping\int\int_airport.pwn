CreateAirportLSInt()
{
    new STREAMER_TAG_OBJECT:arrtxt;

    //LOS SANTOS
    arrtxt = CreateDynamicObject(8661, 1756.918334, -2518.860595, 19.286909, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18764, 1754.344848, -2516.690673, 17.816917, 0.000000, 0.000044, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 4829, "airport_las", "Grass_128HV", 0x00000000);
    arrtxt = CreateDynamicObject(18766, 1756.373535, -2516.700927, 15.536044, 0.000044, 90.000000, 89.999862, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(arrtxt, 1, -1, "none", "none", 0xFF4E4945);
    arrtxt = CreateDynamicObject(3632, 1756.545410, -2519.260498, 20.056932, 0.000000, 0.000044, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    arrtxt = CreateDynamicObject(18766, 1754.032958, -2519.071777, 15.536916, 0.000000, 89.999954, 179.999725, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(arrtxt, 1, -1, "none", "none", 0xFF4E4945);
    arrtxt = CreateDynamicObject(3632, 1756.545410, -2514.199707, 20.056932, 0.000000, 0.000044, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    arrtxt = CreateDynamicObject(18766, 1754.032958, -2514.362060, 15.536916, 0.000000, 89.999954, 179.999725, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(arrtxt, 1, -1, "none", "none", 0xFF4E4945);
    arrtxt = CreateDynamicObject(18766, 1751.704223, -2516.759033, 15.536044, -0.000044, 90.000000, -89.999832, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(arrtxt, 1, -1, "none", "none", 0xFF4E4945);
    arrtxt = CreateDynamicObject(3632, 1751.532348, -2514.199462, 20.056932, 0.000000, -0.000044, 179.999618, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    arrtxt = CreateDynamicObject(3632, 1751.532348, -2519.260253, 20.056932, 0.000000, -0.000044, 179.999618, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    arrtxt = CreateDynamicObject(970, 1756.556884, -2516.801757, 20.126916, 89.999992, 180.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(970, 1754.166259, -2519.253662, 20.126916, 89.999992, 90.000045, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1751.466918, -2515.486816, 19.286911, 0.000000, 90.000015, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    arrtxt = CreateDynamicObject(970, 1753.924682, -2514.179931, 20.126916, 89.999992, 266.598205, -86.598289, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(8661, 1756.918334, -2498.872070, 19.286909, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(8661, 1756.918334, -2538.840576, 19.286909, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(971, 1772.395141, -2506.447998, 20.786895, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    SetDynamicObjectMaterial(arrtxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(971, 1772.405151, -2506.447998, 20.786895, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(arrtxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(971, 1772.395141, -2506.447998, 27.896932, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    SetDynamicObjectMaterial(arrtxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(971, 1772.405151, -2506.447998, 27.896932, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(arrtxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(971, 1772.395141, -2528.045410, 20.786895, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    SetDynamicObjectMaterial(arrtxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(971, 1772.405151, -2528.045410, 20.786895, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(arrtxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(971, 1772.395141, -2528.045410, 27.896932, 0.000045, 0.000000, 89.999862, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    SetDynamicObjectMaterial(arrtxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(971, 1772.405151, -2528.045410, 27.896932, 0.000045, 0.000000, 89.999862, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(arrtxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(975, 1772.395141, -2520.500000, 19.106908, -0.000014, 270.000000, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "a51_glass", 0xFF333333);
    SetDynamicObjectMaterial(arrtxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(975, 1772.445190, -2520.500000, 19.106908, -0.000014, 270.000000, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(975, 1772.395141, -2517.308105, 19.106908, -0.000022, 270.000000, -89.999938, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "a51_glass", 0xFF333333);
    SetDynamicObjectMaterial(arrtxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(975, 1772.445190, -2517.308105, 19.106908, -0.000022, 270.000000, -89.999938, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(975, 1772.395141, -2514.125732, 19.106908, -0.000029, 270.000000, -89.999916, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "a51_glass", 0xFF333333);
    SetDynamicObjectMaterial(arrtxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(975, 1772.445190, -2514.125732, 19.106908, -0.000029, 270.000000, -89.999916, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1772.428710, -2511.653320, 20.996902, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1772.418701, -2511.653320, 24.396894, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1772.428710, -2514.164062, 24.366893, 89.999992, 89.999992, -89.999992, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1772.428710, -2517.663330, 24.366893, 89.999992, 89.999992, -89.999992, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1772.427978, -2520.264404, 24.366893, 89.999992, 89.999992, -89.999992, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1772.428710, -2522.805175, 20.996902, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1772.428710, -2522.805175, 24.396894, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1772.428710, -2512.612792, 26.926912, 89.999992, 89.999992, -89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1772.428710, -2516.112060, 26.926912, 89.999992, 89.999992, -89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1772.427978, -2521.865234, 26.926912, 89.999992, 89.999992, -89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1772.427978, -2518.404296, 26.926912, 89.999992, 89.999992, -89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(971, 1772.395141, -2515.160156, 29.636903, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    SetDynamicObjectMaterial(arrtxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(971, 1772.405151, -2515.160156, 29.636903, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(arrtxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(971, 1772.415161, -2519.227294, 29.636903, 0.000051, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 18878, "ferriswheel", "railing3", 0xFF333333);
    SetDynamicObjectMaterial(arrtxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(971, 1772.425170, -2519.227294, 29.636903, 0.000051, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(arrtxt, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1767.773193, -2504.832763, 20.678901, 14.999999, 5.000039, -0.000009, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1765.269531, -2504.832275, 20.678903, 14.999999, -4.999958, -0.000009, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1766.520874, -2504.823486, 20.646919, 23.299989, 0.000040, -0.000015, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1744.438964, -2529.588867, 20.678901, 14.999999, 5.000021, 179.999847, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1746.942626, -2529.589355, 20.678903, 14.999999, -4.999976, 179.999847, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1745.691284, -2529.598144, 20.646919, 23.299985, 0.000024, 179.999862, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1746.942626, -2504.832763, 20.678901, 14.999999, 5.000030, -0.000007, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1744.438964, -2504.832275, 20.678903, 14.999999, -4.999968, -0.000007, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1745.690307, -2504.823486, 20.646919, 23.299991, 0.000031, -0.000012, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1765.269531, -2529.588867, 20.678901, 14.999999, 5.000013, 179.999847, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1767.773193, -2529.589355, 20.678903, 14.999999, -4.999983, 179.999847, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1766.521850, -2529.598144, 20.646919, 23.299987, 0.000014, 179.999877, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18981, 1759.912719, -2532.950927, 18.956884, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1759.912719, -2501.551513, 18.956884, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(11714, 1757.147949, -2502.031250, 20.556900, 0.000014, 0.000000, 89.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8391, "ballys01", "vgncorpdoor1_512", 0x00000000);
    arrtxt = CreateDynamicObject(11714, 1754.176635, -2502.031250, 20.556900, 0.000014, 0.000000, 89.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8391, "ballys01", "vgncorpdoor1_512", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1752.649414, -2501.541503, 20.176921, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, -1, "none", "none", 0xFF4E4945);
    arrtxt = CreateDynamicObject(19373, 1751.466918, -2517.968261, 19.285999, 0.000000, 90.000015, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    arrtxt = CreateDynamicObject(8324, 1772.738891, -2517.475097, 26.256921, -0.000007, 0.000000, -89.399986, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterialText(arrtxt, 1, "ARIVENA INTERNATIONAL AIRPORT", 120, "Calibri", 20, 1, 0xFF333333, 0x00000000, 1);
    arrtxt = CreateDynamicObject(1297, 1767.983642, -2525.215087, 31.286954, 0.000000, 171.999984, -179.999938, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(18766, 1745.960815, -2534.542968, 16.426906, 0.000004, 90.000007, 44.999988, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    SetDynamicObjectMaterial(arrtxt, 1, -1, "none", "none", 0xFF4E4945);
    arrtxt = CreateDynamicObject(18766, 1745.960815, -2534.542968, 26.426904, 0.000004, 90.000007, 44.999988, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    SetDynamicObjectMaterial(arrtxt, 1, -1, "none", "none", 0xFF4E4945);
    arrtxt = CreateDynamicObject(18981, 1756.341918, -2536.452392, 18.956884, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1746.666015, -2539.887695, 19.432029, 89.999992, 269.318817, -89.318679, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19939, 1746.421386, -2538.731689, 20.282922, 0.000033, 89.999992, 179.999588, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19940, 1746.620849, -2538.221923, 19.502918, 89.999992, 449.318786, -89.318679, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19940, 1746.621582, -2539.902343, 19.502922, 89.999992, 449.318786, -89.318679, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19937, 1746.635009, -2538.252441, 19.252660, 79.999984, 0.000306, -0.000302, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1746.635253, -2539.905273, 19.249666, 79.999984, -0.000306, -179.999526, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19477, 1746.365234, -2539.072265, 19.822931, 0.000033, 0.000000, 179.999633, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "CHECK-IN", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    arrtxt = CreateDynamicObject(19939, 1746.421386, -2539.422363, 20.282922, 0.000033, 89.999992, 179.999588, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19937, 1746.666259, -2539.257080, 19.432928, 89.999992, 269.318817, -89.318679, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19939, 1746.627075, -2539.031005, 19.666912, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(18980, 1746.929687, -2543.028564, 20.551191, 14.999944, 5.000021, -89.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1746.851806, -2541.412353, 20.534702, 14.999944, -4.999976, -89.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1747.556884, -2542.195068, 20.646919, 23.299934, 0.000024, -89.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(19937, 1746.666015, -2546.197998, 19.432029, 89.999992, 269.659454, -89.659309, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19939, 1746.421386, -2545.041992, 20.282922, 0.000033, 89.999984, 179.999542, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19940, 1746.620849, -2544.532226, 19.502918, 89.999992, 449.659423, -89.659309, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19940, 1746.621582, -2546.212646, 19.502922, 89.999992, 449.659423, -89.659309, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19937, 1746.635009, -2544.562744, 19.252660, 79.999984, 0.000349, -0.000345, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1746.635253, -2546.215576, 19.249666, 79.999984, -0.000349, -179.999450, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19477, 1746.365234, -2545.382568, 19.822931, 0.000033, -0.000006, 179.999588, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "CHECK-IN", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    arrtxt = CreateDynamicObject(19939, 1746.421386, -2545.732666, 20.282922, 0.000033, 89.999984, 179.999542, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19937, 1746.666259, -2545.567382, 19.432928, 89.999992, 269.659454, -89.659309, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19939, 1746.627075, -2545.341308, 19.666912, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(18980, 1746.929687, -2549.338867, 20.551191, 14.999935, 5.000021, -89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1746.851806, -2547.722656, 20.534702, 14.999935, -4.999976, -89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1747.556884, -2548.505371, 20.646919, 23.299924, 0.000024, -89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(8661, 1756.918334, -2558.812255, 19.286909, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1746.666015, -2552.569824, 19.432029, 89.999992, 269.659454, -89.659309, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19939, 1746.421386, -2551.413818, 20.282922, 0.000033, 89.999984, 179.999542, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19940, 1746.620849, -2550.904052, 19.502918, 89.999992, 449.659423, -89.659309, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19940, 1746.621582, -2552.584472, 19.502922, 89.999992, 449.659423, -89.659309, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19937, 1746.635009, -2550.934570, 19.252660, 79.999984, 0.000349, -0.000345, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1746.635253, -2552.587402, 19.249666, 79.999984, -0.000349, -179.999450, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19477, 1746.365234, -2551.754394, 19.822931, 0.000033, -0.000006, 179.999588, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "CHECK-IN", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    arrtxt = CreateDynamicObject(19939, 1746.421386, -2552.104492, 20.282922, 0.000033, 89.999984, 179.999542, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19937, 1746.666259, -2551.939208, 19.432928, 89.999992, 269.659454, -89.659309, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19939, 1746.627075, -2551.713134, 19.666912, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(18980, 1746.929687, -2555.710693, 20.551191, 14.999935, 5.000021, -89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1746.851806, -2554.094482, 20.534702, 14.999935, -4.999976, -89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1747.556884, -2554.877197, 20.646919, 23.299924, 0.000024, -89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(19937, 1746.666015, -2558.880126, 19.432029, 89.999992, 269.829803, -89.829635, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19939, 1746.421386, -2557.724121, 20.282922, 0.000033, 89.999977, 179.999496, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19940, 1746.620849, -2557.214355, 19.502918, 89.999992, 449.829772, -89.829635, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19940, 1746.621582, -2558.894775, 19.502922, 89.999992, 449.829772, -89.829635, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19937, 1746.635009, -2557.244873, 19.252660, 79.999984, 0.000394, -0.000388, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1746.635253, -2558.897705, 19.249666, 79.999984, -0.000394, -179.999374, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19477, 1746.365234, -2558.064697, 19.822931, 0.000033, -0.000012, 179.999542, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "CHECK-IN", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    arrtxt = CreateDynamicObject(19939, 1746.421386, -2558.414794, 20.282922, 0.000033, 89.999977, 179.999496, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19937, 1746.666259, -2558.249511, 19.432928, 89.999992, 269.829803, -89.829635, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19939, 1746.627075, -2558.023437, 19.666912, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(18980, 1736.791625, -2548.411376, 19.006887, -0.000014, 90.000000, -90.000007, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18980, 1736.791625, -2523.461669, 19.006887, -0.000014, 90.000000, -90.000007, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18980, 1736.791625, -2498.462402, 19.006887, -0.000014, 90.000000, -90.000007, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18981, 1754.341430, -2549.321777, 18.956884, 0.000000, -0.000007, 179.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1754.211303, -2549.291748, 12.206885, 0.000000, -0.000007, 179.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18980, 1754.192626, -2549.290527, 20.956918, 0.000007, 90.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF777777);
    arrtxt = CreateDynamicObject(18981, 1756.621704, -2516.481201, 28.616870, 0.000000, 90.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(2645, 1746.896362, -2532.869384, 21.216909, -0.000004, -0.000004, -135.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    SetDynamicObjectMaterial(arrtxt, 1, 2817, "gb_bedrmrugs01", "GB_livingrug03", 0x00000000);
    arrtxt = CreateDynamicObject(19787, 1745.977539, -2533.747802, 21.236919, -0.000004, -0.000004, -135.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 1, 2772, "airp_prop", "CJ_AIRP_S_2", 0x00000000);
    arrtxt = CreateDynamicObject(19787, 1744.754028, -2534.972412, 21.236919, -0.000004, -0.000004, -135.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 1, 2772, "airp_prop", "CJ_AIRP_S_1", 0x00000000);
    arrtxt = CreateDynamicObject(18981, 1756.481933, -2536.530029, 12.206885, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18980, 1756.532592, -2536.548095, 20.956918, 0.000000, 89.999992, 179.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF777777);
    arrtxt = CreateDynamicObject(18981, 1755.952026, -2560.580078, 18.956884, 0.000000, 0.000000, -89.999969, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1756.192382, -2560.502441, 12.206885, 0.000000, 0.000000, 89.999938, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18980, 1756.341918, -2560.484375, 20.956918, 0.000000, 90.000000, -0.000059, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF777777);
    arrtxt = CreateDynamicObject(2200, 1753.467773, -2537.504394, 19.296909, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(2200, 1753.467773, -2539.715087, 19.296909, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(2200, 1753.467773, -2541.926269, 19.296909, -0.000014, 0.000000, -89.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(8661, 1736.300537, -2548.654052, 12.596906, 0.000014, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(8661, 1736.300537, -2508.684570, 12.596905, 0.000014, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(2200, 1753.467773, -2553.843017, 19.296909, -0.000014, 0.000000, -89.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(2200, 1753.467773, -2556.054199, 19.296909, -0.000022, 0.000000, -89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(2200, 1753.467773, -2558.264892, 19.296909, -0.000022, 0.000000, -89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(11714, 1753.666381, -2546.988525, 20.556900, 0.000007, -0.000014, 179.999877, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "lasjmposh2", 0x00000000);
    arrtxt = CreateDynamicObject(11714, 1753.666381, -2549.968261, 20.556900, 0.000007, -0.000014, 179.999877, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "lasjmposh2", 0x00000000);
    arrtxt = CreateDynamicObject(18766, 1746.009033, -2499.942138, 16.426906, 0.000014, 89.999992, 134.999984, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    SetDynamicObjectMaterial(arrtxt, 1, -1, "none", "none", 0xFF4E4945);
    arrtxt = CreateDynamicObject(19787, 1745.213867, -2499.925292, 21.236919, -0.000014, 0.000004, -45.000003, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 1, 2772, "airp_prop", "CJ_AIRP_S_2", 0x00000000);
    arrtxt = CreateDynamicObject(19787, 1746.438476, -2501.148681, 21.236919, -0.000014, 0.000004, -45.000003, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 1, 2772, "airp_prop", "CJ_AIRP_S_1", 0x00000000);
    arrtxt = CreateDynamicObject(18766, 1746.009033, -2499.942138, 26.396919, 0.000014, 89.999992, 134.999984, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    SetDynamicObjectMaterial(arrtxt, 1, -1, "none", "none", 0xFF4E4945);
    arrtxt = CreateDynamicObject(18981, 1744.219848, -2542.682861, 6.716884, 0.000000, -0.000007, 179.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF08996E);
    arrtxt = CreateDynamicObject(19552, 1799.590454, -2506.161621, 19.246967, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19377, 1731.900634, -2565.644531, 19.186920, 0.000000, 90.000015, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1743.951171, -2573.560058, 18.956884, 0.000007, 0.000007, 0.000006, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1738.720947, -2567.618164, 18.956884, 0.000014, 0.000000, 89.999984, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1739.221191, -2561.290283, 6.756884, 0.000014, 0.000000, 89.999984, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1737.200073, -2561.290283, 6.756884, 0.000014, 0.000000, 179.999984, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1749.810180, -2549.303466, 6.716884, 0.000000, -0.000007, 269.999938, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(8661, 1756.918334, -2478.910888, 19.286909, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19377, 1742.389648, -2536.363769, 12.511907, 0.000067, 90.000015, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16639, "a51_labs", "dam_terazzo", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1749.221313, -2530.582275, 6.716884, 0.000000, -0.000007, 269.999938, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(19377, 1742.389648, -2546.865478, 12.511908, 0.000067, 90.000015, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16639, "a51_labs", "dam_terazzo", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19443, 1737.626220, -2531.841552, 14.431911, 0.000022, -0.000067, 179.999496, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(19443, 1737.626220, -2540.902587, 14.431909, 0.000022, -0.000067, 179.999496, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(19443, 1737.626220, -2533.402587, 11.571908, 0.000022, -0.000081, 179.999404, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(19443, 1737.626220, -2539.443115, 11.571908, 0.000022, -0.000081, 179.999404, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(1499, 1737.666992, -2537.853515, 12.531911, 0.000067, 0.000029, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 7088, "casinoshops1", "247sign2", 0x00000000);
    arrtxt = CreateDynamicObject(1499, 1737.647705, -2534.983154, 12.531911, -0.000067, -0.000029, -89.999916, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 7088, "casinoshops1", "247sign2", 0x00000000);
    arrtxt = CreateDynamicObject(2679, 1737.583251, -2534.176513, 12.417854, 0.000067, 360.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(2679, 1737.583251, -2538.637695, 12.667854, 0.000067, 180.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(19477, 1737.599853, -2533.577392, 14.091902, 0.000007, -0.000067, 179.999511, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    arrtxt = CreateDynamicObject(19477, 1737.599853, -2533.577392, 15.481904, 0.000007, -0.000067, 179.999511, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    arrtxt = CreateDynamicObject(2679, 1737.583251, -2534.176513, 14.817849, 0.000067, 360.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(2679, 1737.583251, -2538.637695, 15.077855, 0.000067, 180.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(19477, 1737.599853, -2536.427978, 15.691912, -0.000007, 179.999984, -0.000012, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    arrtxt = CreateDynamicObject(19477, 1737.599853, -2540.009521, 14.091902, 0.000014, -0.000067, 179.999496, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    arrtxt = CreateDynamicObject(19377, 1741.259521, -2536.344726, 16.111000, 0.000067, 90.000015, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8391, "ballys01", "ballywall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1737.626220, -2531.841552, 10.941922, 0.000022, -0.000067, 179.999496, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(19443, 1737.626220, -2540.902587, 10.951911, 0.000022, -0.000067, 179.999496, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(2679, 1737.583251, -2533.546630, 16.267852, 0.000067, 270.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(2679, 1737.583251, -2539.005615, 16.267852, 0.000067, 270.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(19377, 1742.309814, -2536.363769, 16.311916, 0.000067, 90.000015, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(19786, 1736.552368, -2536.672607, 16.551908, -0.000067, -0.000029, -89.999916, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 1, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(18981, 1749.810180, -2504.512207, 6.716882, 0.000000, -0.000007, 269.999938, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(19377, 1741.258544, -2537.544433, 16.101037, 0.000067, 90.000015, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8391, "ballys01", "ballywall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(2679, 1737.583251, -2536.827636, 15.287854, 0.000067, 450.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(2679, 1737.583251, -2536.277099, 15.287854, 0.000067, 450.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(19477, 1737.599853, -2540.009521, 15.481904, 0.000014, -0.000067, 179.999496, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    arrtxt = CreateDynamicObject(2531, 1743.035156, -2541.515380, 12.597848, -0.000045, -0.000045, -90.000076, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    arrtxt = CreateDynamicObject(2531, 1743.035156, -2539.524414, 12.597848, -0.000045, -0.000051, -90.000122, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    arrtxt = CreateDynamicObject(2679, 1737.693115, -2534.176513, 12.417854, 0.000067, 360.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(2679, 1737.693115, -2534.176513, 14.857855, 0.000067, 360.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(2717, 1737.545166, -2531.901855, 14.291913, 0.000067, 0.000014, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(2717, 1737.545166, -2540.951904, 14.291913, 0.000067, 0.000014, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(2531, 1743.035156, -2540.535400, 12.597848, -0.000045, -0.000051, -90.000122, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    arrtxt = CreateDynamicObject(2531, 1743.035156, -2537.552734, 12.597848, -0.000045, -0.000059, -90.000167, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    arrtxt = CreateDynamicObject(2679, 1737.693115, -2538.637695, 12.667854, 0.000067, 180.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(2679, 1737.693115, -2538.637695, 15.087854, 0.000067, 180.000000, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(2679, 1737.693115, -2533.546630, 16.267852, -0.000067, 270.000000, -89.999824, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(2679, 1737.693115, -2539.936767, 16.267852, -0.000067, 270.000000, -89.999824, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(2679, 1737.693115, -2535.945312, 16.067848, -0.000067, 270.000000, -89.999824, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(2679, 1737.692626, -2536.845947, 16.067848, -0.000067, 270.000000, -89.999824, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0xFFFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(1347, 1737.137451, -2534.450927, 13.072553, 0.000067, 0.000007, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 12984, "sw_block11", "shoptopb128", 0x00000000);
    arrtxt = CreateDynamicObject(1347, 1737.137451, -2538.371337, 13.072553, 0.000067, 0.000007, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 12984, "sw_block11", "shoptopb128", 0x00000000);
    arrtxt = CreateDynamicObject(2658, 1737.506591, -2534.568115, 14.392558, -0.000067, -0.000006, -89.999778, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 1733, "cj_commercial", "CJ_SPRUNK_FRONT", 0x00000000);
    arrtxt = CreateDynamicObject(2658, 1737.506591, -2538.228759, 14.392558, -0.000067, -0.000006, -89.999778, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 1733, "cj_commercial", "CJ_SPRUNK_FRONT", 0x00000000);
    arrtxt = CreateDynamicObject(19475, 1737.577636, -2539.059326, 14.152557, 0.000007, -0.000067, 179.999542, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(19475, 1737.577636, -2539.669921, 14.152557, 0.000007, -0.000067, 179.999542, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(19475, 1737.577636, -2533.088378, 14.152557, 0.000014, -0.000067, 179.999511, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(19475, 1737.577636, -2533.698974, 14.152557, 0.000014, -0.000067, 179.999511, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(2531, 1743.035156, -2542.526367, 12.597848, -0.000045, -0.000045, -90.000076, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    arrtxt = CreateDynamicObject(2531, 1743.035156, -2538.563720, 12.597848, -0.000045, -0.000059, -90.000167, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    arrtxt = CreateDynamicObject(1431, 1738.189941, -2539.103271, 12.597847, 0.000051, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2212, "burger_tray", "burgerkids", 0x00000000);
    arrtxt = CreateDynamicObject(19377, 1742.389648, -2548.892333, 10.941911, 0.000038, 360.000000, 89.999855, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF08996E);
    arrtxt = CreateDynamicObject(19377, 1742.389648, -2531.160888, 10.941911, 0.000038, 360.000000, 89.999855, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF08996E);
    arrtxt = CreateDynamicObject(2543, 1740.106689, -2537.405517, 12.597847, -0.000045, 0.000082, -89.999832, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 2, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(2543, 1740.106689, -2538.395996, 12.597847, -0.000045, 0.000082, -89.999832, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 2, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(2541, 1741.147216, -2538.396240, 12.597847, 0.000045, -0.000067, 89.999328, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2212, "burger_tray", "sprinkler", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 2212, "burger_tray", "burgerside", 0x00000000);
    arrtxt = CreateDynamicObject(2541, 1741.147216, -2537.405761, 12.597847, 0.000045, -0.000067, 89.999328, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2212, "burger_tray", "sprinkler", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 2212, "burger_tray", "burgerside", 0x00000000);
    arrtxt = CreateDynamicObject(2542, 1741.008789, -2546.109863, 12.597847, 0.000045, 0.000090, 89.999832, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(2542, 1741.008789, -2545.149414, 12.597847, 0.000045, 0.000090, 89.999832, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(2541, 1740.078613, -2545.149414, 12.597847, -0.000045, -0.000074, -90.000411, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(2541, 1740.078613, -2546.109863, 12.597847, -0.000045, -0.000074, -90.000411, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(2579, 1740.520996, -2546.742431, 13.497844, -0.000081, 0.000045, 0.000220, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 18632, "fishingrod", "handle2", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    arrtxt = CreateDynamicObject(2585, 1740.624511, -2536.772949, 13.497844, -0.000075, -0.000045, -179.999465, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 18632, "fishingrod", "handle2", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    arrtxt = CreateDynamicObject(19477, 1737.599853, -2543.139404, 14.091902, 0.000014, -0.000081, 179.999404, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    arrtxt = CreateDynamicObject(2582, 1739.843505, -2533.843750, 13.077850, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 7088, "casinoshops1", "GB_nastybar19", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 3, 12848, "cunte_town1", "sprunk_temp", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 5, 18264, "cw2_cinemablockcs_t", "GB_Last of Mullets", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 6, 15040, "cuntcuts", "piratesign01_128", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 7, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(19477, 1737.599853, -2543.139404, 15.481904, 0.000014, -0.000081, 179.999404, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    arrtxt = CreateDynamicObject(2543, 1740.106689, -2539.356933, 12.597847, -0.000045, 0.000090, -89.999832, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 2, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(2543, 1740.106689, -2540.347412, 12.597847, -0.000045, 0.000090, -89.999832, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 2, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(2541, 1741.147216, -2540.347656, 12.597847, 0.000045, -0.000075, 89.999282, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(2541, 1741.147216, -2539.357177, 12.597847, 0.000045, -0.000075, 89.999282, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(2542, 1741.008789, -2544.158447, 12.597847, 0.000045, 0.000096, 89.999832, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(2542, 1741.008789, -2543.197998, 12.597847, 0.000045, 0.000096, 89.999832, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(2541, 1740.078613, -2543.197998, 12.597847, -0.000045, -0.000081, -90.000457, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2767, "cb_details", "pattern1_cb", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 2767, "cb_details", "fillets_cb", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 3, 8618, "ceasersign", "ceaserssign01_128", 0x00000000);
    arrtxt = CreateDynamicObject(2541, 1740.078613, -2544.158447, 12.597847, -0.000045, -0.000081, -90.000457, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2767, "cb_details", "pattern1_cb", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 2767, "cb_details", "fillets_cb", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 3, 8618, "ceasersign", "ceaserssign01_128", 0x00000000);
    arrtxt = CreateDynamicObject(2582, 1738.393066, -2533.843750, 13.077850, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 7088, "casinoshops1", "GB_nastybar19", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 3, 12848, "cunte_town1", "sprunk_temp", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 5, 18264, "cw2_cinemablockcs_t", "GB_Last of Mullets", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 6, 15040, "cuntcuts", "piratesign01_128", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 7, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(2626, 1741.553833, -2533.254150, 13.007846, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(2579, 1738.410888, -2531.374023, 14.357849, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 18632, "fishingrod", "line", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 18632, "fishingrod", "handle2", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    arrtxt = CreateDynamicObject(2579, 1739.561035, -2531.374023, 14.357849, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 18632, "fishingrod", "line", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 18632, "fishingrod", "handle2", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    arrtxt = CreateDynamicObject(2578, 1740.681152, -2531.374023, 14.357849, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 18632, "fishingrod", "handle2", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    arrtxt = CreateDynamicObject(2578, 1741.801025, -2531.374023, 14.357849, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFF22B1C);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 3, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 18632, "fishingrod", "handle2", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 5, 19625, "ciggy1", "ciggy1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 6, 19897, "cigarettepacks", "cigarettepack1", 0x00000000);
    arrtxt = CreateDynamicObject(19475, 1737.577636, -2542.189208, 14.152557, 0.000007, -0.000081, 179.999450, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(19475, 1737.577636, -2542.799804, 14.152557, 0.000007, -0.000081, 179.999450, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(19475, 1737.577636, -2543.579589, 14.152557, 0.000007, -0.000090, 179.999404, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(19475, 1737.577636, -2544.190185, 14.152557, 0.000007, -0.000090, 179.999404, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(19475, 1737.577636, -2545.010986, 14.152557, 0.000007, -0.000096, 179.999359, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(19475, 1737.577636, -2545.621582, 14.152557, 0.000007, -0.000096, 179.999359, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(19477, 1737.599853, -2545.900146, 14.091902, 0.000014, -0.000090, 179.999359, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    arrtxt = CreateDynamicObject(19477, 1737.599853, -2545.900146, 15.481904, 0.000014, -0.000090, 179.999359, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    arrtxt = CreateDynamicObject(19443, 1737.596923, -2543.443115, 12.556907, 90.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(19443, 1737.596923, -2546.893798, 12.556907, 90.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(19447, 1737.570922, -2534.958496, 17.266916, 0.000000, 0.000000, 180.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(2531, 1743.035156, -2543.497070, 12.597848, -0.000052, -0.000045, -90.000053, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1737.596923, -2547.674560, 13.786911, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(19443, 1737.596923, -2550.364013, 16.346918, 89.999992, 89.999992, -89.999992, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(19377, 1741.259521, -2543.554443, 16.131002, 0.000067, 90.000015, 89.999763, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8391, "ballys01", "ballywall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(19447, 1737.570922, -2544.237548, 17.266916, 0.000000, 0.000000, 180.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFEC2C00);
    arrtxt = CreateDynamicObject(2531, 1743.035156, -2544.508056, 12.597848, -0.000052, -0.000045, -90.000053, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 4, 14582, "casmafbar", "beerfridge128", 0x00000000);
    arrtxt = CreateDynamicObject(19447, 1736.791503, -2544.017333, 17.796928, 0.000000, 0.000000, 180.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(19447, 1736.790039, -2534.927246, 17.796928, 0.000000, 0.000000, 180.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18981, 1744.331420, -2492.300781, 6.706881, 0.000000, -0.000007, 179.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(8661, 1756.918334, -2458.922119, 19.286909, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1756.341918, -2473.891601, 18.956884, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1746.666015, -2477.326904, 19.432029, 89.999992, 269.829803, -89.829635, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19939, 1746.421386, -2476.170898, 20.282922, 0.000033, 89.999977, 179.999496, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19940, 1746.620849, -2475.661132, 19.502918, 89.999992, 449.829772, -89.829635, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19940, 1746.621582, -2477.341552, 19.502922, 89.999992, 449.829772, -89.829635, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19937, 1746.635009, -2475.691650, 19.252660, 79.999984, 0.000392, -0.000387, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1746.635253, -2477.344482, 19.249666, 79.999984, -0.000392, -179.999374, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19477, 1746.365234, -2476.511474, 19.822931, 0.000033, -0.000014, 179.999542, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "CHECK-IN", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    arrtxt = CreateDynamicObject(19939, 1746.421386, -2476.861572, 20.282922, 0.000033, 89.999977, 179.999496, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19937, 1746.666259, -2476.696289, 19.432928, 89.999992, 269.829803, -89.829635, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19939, 1746.627075, -2476.470214, 19.666912, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(18980, 1746.929687, -2480.467773, 20.551191, 14.999925, 5.000020, -89.999908, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1746.851806, -2478.851562, 20.534702, 14.999925, -4.999976, -89.999908, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1747.556884, -2479.634277, 20.646919, 23.299915, 0.000024, -89.999908, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(19937, 1746.666015, -2483.637207, 19.432029, 89.999992, 269.914978, -89.914794, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19939, 1746.421386, -2482.481201, 20.282922, 0.000033, 89.999969, 179.999450, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19940, 1746.620849, -2481.971435, 19.502918, 89.999992, 449.914947, -89.914794, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19940, 1746.621582, -2483.651855, 19.502922, 89.999992, 449.914947, -89.914794, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19937, 1746.635009, -2482.001953, 19.252660, 79.999984, 0.000437, -0.000430, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1746.635253, -2483.654785, 19.249666, 79.999984, -0.000437, -179.999298, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(2667, 1744.638183, -2531.281005, 14.387848, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2212, "burger_tray", "sprunk_cb", 0x00000000);
    arrtxt = CreateDynamicObject(2667, 1745.979248, -2531.281005, 14.387848, 0.000000, 0.000052, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(19918, 1742.202148, -2533.426513, 13.517853, 0.000000, 0.000060, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2221, "donut_tray", "rustycoffeerap_rb", 0x00000000);
    arrtxt = CreateDynamicObject(19918, 1742.202148, -2533.106201, 13.517853, -0.000004, 0.000059, -5.499998, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2221, "donut_tray", "rustycoffeerap_rb", 0x00000000);
    arrtxt = CreateDynamicObject(19477, 1746.365234, -2482.821777, 19.822931, 0.000033, -0.000020, 179.999496, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "CHECK-IN", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    arrtxt = CreateDynamicObject(18066, 1743.587768, -2539.194091, 15.417853, 0.000052, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 7088, "casinoshops1", "crapdoor1_256", 0x00000000);
    arrtxt = CreateDynamicObject(19939, 1746.421386, -2483.171875, 20.282922, 0.000033, 89.999969, 179.999450, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19937, 1746.666259, -2483.006591, 19.432928, 89.999992, 269.914978, -89.914794, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(11727, 1741.538330, -2540.490966, 16.037849, 89.999992, 90.000091, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(11727, 1741.538330, -2537.340576, 16.037849, 89.999992, 90.000091, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(11727, 1741.538330, -2533.530517, 16.037849, 89.999992, 90.000076, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(11727, 1745.189453, -2540.490966, 15.847847, 89.999992, 90.000122, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(11727, 1745.189453, -2537.340576, 15.847847, 89.999992, 90.000122, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(11727, 1745.189453, -2533.530517, 15.847847, 89.999992, 90.000106, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19939, 1746.627075, -2482.780517, 19.666912, 0.000000, 0.000044, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19834, 1743.565429, -2537.083251, 15.937847, 0.000059, 0.000000, 89.999816, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(19834, 1743.565429, -2539.413818, 15.937847, 0.000059, 0.000000, 89.999816, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(19834, 1743.575439, -2540.424316, 15.937847, 0.000059, 0.000000, 89.999816, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 7088, "casinoshops1", "247sign1", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1746.929687, -2486.778076, 20.551191, 14.999917, 5.000020, -89.999885, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1746.851806, -2485.161865, 20.534702, 14.999917, -4.999976, -89.999885, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1747.556884, -2485.944580, 20.646919, 23.299905, 0.000024, -89.999885, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(19937, 1746.666015, -2490.009033, 19.432029, 89.999992, 269.914978, -89.914794, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19939, 1746.421386, -2488.853027, 20.282922, 0.000033, 89.999969, 179.999450, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19940, 1746.620849, -2488.343261, 19.502918, 89.999992, 449.914947, -89.914794, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19940, 1746.621582, -2490.023681, 19.502922, 89.999992, 449.914947, -89.914794, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19937, 1746.635009, -2488.373779, 19.252660, 79.999984, 0.000437, -0.000430, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1746.635253, -2490.026611, 19.249666, 79.999984, -0.000437, -179.999298, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19477, 1746.365234, -2489.193603, 19.822931, 0.000033, -0.000020, 179.999496, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "CHECK-IN", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    arrtxt = CreateDynamicObject(19939, 1746.421386, -2489.543701, 20.282922, 0.000033, 89.999969, 179.999450, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19937, 1746.666259, -2489.378417, 19.432928, 89.999992, 269.914978, -89.914794, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19939, 1746.627075, -2489.152343, 19.666912, 0.000000, 0.000044, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(18980, 1746.929687, -2493.149902, 20.551191, 14.999917, 5.000020, -89.999885, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1746.851806, -2491.533691, 20.534702, 14.999917, -4.999976, -89.999885, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1747.556884, -2492.316406, 20.646919, 23.299905, 0.000024, -89.999885, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8675, "wddngchpl02", "vgschapelwall02_128", 0x00000000);
    arrtxt = CreateDynamicObject(19937, 1746.666015, -2496.319335, 19.432029, 89.999992, 269.957580, -89.957374, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19939, 1746.421386, -2495.163330, 20.282922, 0.000033, 89.999961, 179.999404, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19940, 1746.620849, -2494.653564, 19.502918, 89.999992, 449.957550, -89.957374, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19940, 1746.621582, -2496.333984, 19.502922, 89.999992, 449.957550, -89.957374, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19937, 1746.635009, -2494.684082, 19.252660, 79.999984, 0.000480, -0.000475, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1746.635253, -2496.336914, 19.249666, 79.999984, -0.000480, -179.999221, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19477, 1746.365234, -2495.503906, 19.822931, 0.000033, -0.000028, 179.999450, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "CHECK-IN", 130, "Palatino Linotype", 30, 1, 0xFF333333, 0x00000000, 1);
    arrtxt = CreateDynamicObject(19939, 1746.421386, -2495.854003, 20.282922, 0.000033, 89.999961, 179.999404, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "sjmlawarwall3", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(19937, 1746.666259, -2495.688720, 19.432928, 89.999992, 269.957580, -89.957374, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19939, 1746.627075, -2495.462646, 19.666912, 0.000000, 0.000051, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF00B8D4);
    arrtxt = CreateDynamicObject(18981, 1754.341430, -2486.760986, 18.956884, 0.000000, -0.000022, 179.999862, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1754.211303, -2486.730957, 12.206885, 0.000000, -0.000022, 179.999862, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18980, 1754.192626, -2486.729736, 20.956918, 0.000022, 90.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF777777);
    arrtxt = CreateDynamicObject(18980, 1756.532592, -2473.987304, 20.956918, 0.000000, 89.999977, 179.999862, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF777777);
    arrtxt = CreateDynamicObject(18981, 1756.372436, -2498.019287, 18.956884, -0.000022, 0.000000, -89.999900, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1756.612792, -2497.941650, 12.206885, 0.000022, 0.000000, 89.999870, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18980, 1756.762329, -2497.923583, 20.956918, 0.000000, 90.000022, -0.000059, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF777777);
    arrtxt = CreateDynamicObject(2200, 1753.467773, -2474.943603, 19.296909, -0.000022, 0.000000, -89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(2200, 1753.467773, -2477.154296, 19.296909, -0.000022, 0.000000, -89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(2200, 1753.467773, -2479.365478, 19.296909, -0.000029, 0.000000, -89.999908, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(2200, 1753.467773, -2491.282226, 19.296909, -0.000029, 0.000000, -89.999908, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(2200, 1753.467773, -2493.493408, 19.296909, -0.000037, 0.000000, -89.999885, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(2200, 1753.467773, -2495.704101, 19.296909, -0.000037, 0.000000, -89.999885, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(11714, 1753.666381, -2484.427734, 20.556900, 0.000007, -0.000029, 179.999786, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "lasjmposh2", 0x00000000);
    arrtxt = CreateDynamicObject(11714, 1753.666381, -2487.407470, 20.556900, 0.000007, -0.000029, 179.999786, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "lasjmposh2", 0x00000000);
    arrtxt = CreateDynamicObject(18981, 1743.951171, -2461.899169, 18.956884, 0.000007, 0.000022, 0.000004, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1756.312622, -2473.959960, 12.206885, 0.000022, 0.000000, 89.999870, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(19377, 1732.551269, -2468.989746, 19.186920, 0.000000, 90.000015, 179.999893, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(8661, 1736.300537, -2464.970458, 12.606905, 0.000014, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6882, "vgnland", "hiwaygravel1_256", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18980, 1736.791015, -2485.411132, 19.000000, -0.000014, 90.000000, -90.000007, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18763, 1735.811767, -2560.581054, 17.012849, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18763, 1735.811767, -2560.581054, 12.102846, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18763, 1728.940917, -2560.581054, 17.012849, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18763, 1728.940917, -2560.581054, 12.102846, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18763, 1735.811767, -2474.409423, 17.012849, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18763, 1735.811767, -2474.409423, 12.102846, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18763, 1728.940917, -2474.409423, 17.012849, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18763, 1728.940917, -2474.409423, 12.102846, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18981, 1739.221191, -2467.229248, 18.956884, 0.000014, 0.000000, 89.999984, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19913, 1726.325439, -2542.578613, 15.886907, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x0FFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90BBBBBB);
    arrtxt = CreateDynamicObject(1499, 1737.646972, -2495.911865, 12.531909, 0.000096, 0.000029, 89.999671, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    arrtxt = CreateDynamicObject(1499, 1737.627685, -2493.041503, 12.531909, -0.000096, -0.000027, -89.999824, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    arrtxt = CreateDynamicObject(19477, 1737.579833, -2494.486328, 15.691910, -0.000007, 179.999984, -0.000011, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x80777777);
    arrtxt = CreateDynamicObject(1897, 1737.596191, -2494.040771, 15.181913, -89.999992, 90.001960, 90.001632, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.606201, -2494.921386, 15.181913, -89.999992, -89.998077, 90.001609, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.596191, -2494.040771, 15.371914, -89.999992, 90.001754, 90.001403, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.606201, -2494.921386, 15.371914, -89.999992, -89.998245, 90.001388, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.612548, -2492.930175, 12.581977, 0.000018, 180.197845, -179.801406, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.604736, -2492.930175, 14.771965, 0.000018, 180.197845, -179.801406, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.634521, -2496.011962, 13.521950, 0.000000, 180.197845, 0.197916, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.636962, -2496.011962, 14.131953, 0.000000, 180.197845, 0.197916, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1737.612792, -2496.747558, 14.246907, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.612792, -2492.237304, 14.246907, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.612060, -2493.176269, 16.116907, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.611083, -2495.796875, 16.116907, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(1897, 1737.615722, -2491.490722, 14.011976, 0.000019, 180.197845, 0.198364, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.621093, -2491.490722, 15.461972, 0.000019, 180.197845, 0.198364, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.571655, -2488.928955, 13.261343, 0.000004, 180.197845, -89.801406, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.571289, -2488.908935, 15.491713, -0.000037, 0.197876, -89.801506, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.610473, -2489.969238, 16.481977, 89.999992, 285.132843, -104.736526, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.605346, -2492.199218, 16.481977, 89.999992, 75.467193, -75.070930, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.643676, -2491.768798, 13.031975, -89.999992, 271.241851, 90.845504, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.625366, -2489.969238, 13.031975, -89.999992, 91.055061, 90.658668, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.571655, -2488.178466, 13.261343, -0.000000, 180.197845, -89.801383, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19325, 1737.578857, -2488.061035, 15.016908, 0.000000, 179.999984, -179.999984, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    arrtxt = CreateDynamicObject(1897, 1737.571289, -2488.158447, 15.491713, -0.000045, 0.197876, -89.801483, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.621093, -2485.538330, 14.011976, 0.000018, 180.197845, -179.801422, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.615722, -2485.538330, 15.461972, 0.000018, 180.197845, -179.801422, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.610473, -2486.708740, 16.481977, 89.999992, 277.884460, -97.488128, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.585327, -2488.938720, 16.481977, 89.999992, 532.807128, 7.589134, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.669311, -2487.787597, 13.041972, -89.999992, 272.087005, 91.690689, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.655395, -2486.708740, 13.031975, -89.999992, 91.713577, 91.317207, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1737.612060, -2487.325683, 17.406906, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.622070, -2487.281982, 12.116907, 89.999992, 90.000015, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.622070, -2490.761718, 12.116907, 89.999992, 90.000015, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.611450, -2490.794677, 17.406906, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.611450, -2494.284423, 17.406906, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.611450, -2497.763916, 17.406906, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.611450, -2501.264404, 17.406906, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.614990, -2502.903564, 17.406906, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(1897, 1737.615722, -2503.453369, 14.011976, 0.000019, 180.197845, 0.198364, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.621093, -2503.453369, 15.461972, 0.000019, 180.197845, 0.198364, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.621093, -2500.761474, 14.011976, 0.000019, 180.197845, -179.801422, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.615722, -2500.951660, 15.461972, -0.000014, 0.197882, 0.198424, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.610473, -2501.931884, 16.481977, 89.999992, 277.884460, -97.488128, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.599975, -2503.481445, 16.481977, 89.999992, 82.807128, -82.410865, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.645874, -2503.541259, 13.031975, -89.999992, 270.819122, 90.422752, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.625366, -2501.931884, 13.031975, -89.999992, 90.725738, 90.329322, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.615722, -2500.192871, 14.011976, 0.000000, 180.197845, 0.198364, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19325, 1737.578857, -2500.023681, 15.016908, 0.000000, 179.999984, -179.999938, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    arrtxt = CreateDynamicObject(1897, 1737.620727, -2500.002685, 15.461972, -0.000014, 360.197814, -179.801483, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.621093, -2497.500976, 14.011976, 0.000018, 180.197845, -179.801376, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.615722, -2497.500976, 15.461972, 0.000018, 180.197845, -179.801376, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.610473, -2498.671386, 16.481977, 89.999992, 274.156311, -93.759963, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.605346, -2500.901367, 16.481977, 89.999992, 86.585121, -86.188842, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.669311, -2499.750244, 13.041972, -89.999992, 271.241851, 90.845504, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(1897, 1737.655395, -2498.671386, 13.031975, -89.999992, 91.055061, 90.658668, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1737.612792, -2504.188964, 14.246907, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.612792, -2504.188964, 17.526901, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.612792, -2484.807617, 14.246907, 0.000000, 0.000045, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.612792, -2484.807617, 17.526901, 0.000000, 0.000045, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.622070, -2499.158447, 12.116907, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.622070, -2502.638183, 12.116907, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19377, 1742.495849, -2485.030517, 14.026923, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19377, 1742.495849, -2504.070312, 14.026923, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(18981, 1750.162109, -2492.300781, 12.106882, 0.000000, 89.999992, 179.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14865, "gf2", "mp_bobbie_wall", 0x00000000);
    arrtxt = CreateDynamicObject(19805, 1740.689697, -2485.224365, 14.906887, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    arrtxt = CreateDynamicObject(2394, 1739.364379, -2485.582031, 13.326889, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    arrtxt = CreateDynamicObject(2394, 1739.364379, -2486.252685, 13.326889, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    arrtxt = CreateDynamicObject(2394, 1741.965820, -2485.582031, 13.326889, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    arrtxt = CreateDynamicObject(2394, 1741.965820, -2486.252685, 13.326889, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    arrtxt = CreateDynamicObject(2411, 1738.738281, -2490.238281, 13.966883, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    arrtxt = CreateDynamicObject(18762, 1738.722534, -2490.267333, 11.166880, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(2411, 1738.738281, -2487.877197, 13.966883, -0.000014, 0.000000, -89.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    arrtxt = CreateDynamicObject(18762, 1738.722534, -2487.906250, 11.166880, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19805, 1740.689697, -2503.935546, 14.906887, 0.000000, 0.000022, 180.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    arrtxt = CreateDynamicObject(2394, 1739.364379, -2502.812988, 13.326889, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 2374, "cloth_track_t", "tshirterisyell", 0x00000000);
    arrtxt = CreateDynamicObject(2394, 1739.364379, -2503.483642, 13.326889, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 2374, "cloth_track_t", "trackytop1pro", 0x00000000);
    arrtxt = CreateDynamicObject(2394, 1741.965820, -2502.812988, 13.326889, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 2374, "cloth_track_t", "shirtshortblu", 0x00000000);
    arrtxt = CreateDynamicObject(2394, 1741.965820, -2503.483642, 13.326889, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 2374, "cloth_track_t", "sweatrstar", 0x00000000);
    arrtxt = CreateDynamicObject(2411, 1738.738281, -2501.038085, 13.966883, -0.000014, 0.000000, -89.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    arrtxt = CreateDynamicObject(18762, 1738.722534, -2501.067138, 11.166880, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(2411, 1738.738281, -2498.677001, 13.966883, -0.000022, 0.000000, -89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    arrtxt = CreateDynamicObject(18762, 1738.722534, -2498.706054, 11.166880, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19937, 1742.252685, -2495.393066, 12.606884, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1742.252685, -2493.524902, 12.606884, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1741.982421, -2495.453125, 12.856884, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1741.982421, -2493.532958, 12.856884, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19861, 1743.832519, -2494.597900, 16.476882, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "Z I P", 130, "Palatino Linotype", 120, 1, 0xFFFFFFFF, 0x00000000, 1);
    arrtxt = CreateDynamicObject(2400, 1743.755126, -2498.493408, 12.606884, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    arrtxt = CreateDynamicObject(2400, 1743.745117, -2498.493408, 10.946882, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(18066, 1743.770385, -2499.957763, 14.946889, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(1297, 1744.324096, -2498.189941, 16.156883, 180.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(1297, 1744.324096, -2501.431640, 16.156883, 180.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(2400, 1743.755126, -2487.621582, 12.606884, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    arrtxt = CreateDynamicObject(2400, 1743.745117, -2487.621582, 10.946882, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(18066, 1743.770385, -2489.085937, 14.946889, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(1297, 1744.324096, -2487.318115, 16.156883, 0.000000, 179.999984, -179.999984, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(1297, 1744.324096, -2490.559814, 16.156883, 0.000000, 179.999984, -179.999984, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1740.395019, -2496.505859, 17.586887, 0.000000, 90.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1740.395019, -2499.665771, 17.586887, 0.000000, 90.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1740.395019, -2493.325195, 17.586887, 0.000000, 90.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1740.395019, -2490.164794, 17.586887, 0.000000, 90.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    arrtxt = CreateDynamicObject(19860, 1743.844726, -2491.290527, 13.826884, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 12978, "ce_payspray", "sf_spray2", 0x00000000);
    arrtxt = CreateDynamicObject(2654, 1743.553222, -2497.151855, 12.776885, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    arrtxt = CreateDynamicObject(2654, 1743.553222, -2502.753417, 12.776885, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 2732, "cj_zip_sign", "CJ_ZIP_2", 0x00000000);
    arrtxt = CreateDynamicObject(19805, 1737.490966, -2494.405273, 16.766897, 0.000000, 0.000022, 270.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2875, "cj_gash", "CJ_ZIP_3", 0x00000000);
    arrtxt = CreateDynamicObject(18981, 1751.659790, -2528.473388, 6.716882, 0.000000, -0.000007, 359.999938, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(8661, 1756.299682, -2508.684570, 12.596905, 0.000014, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1726.339721, -2478.061279, 1.436879, 0.000000, -0.000007, 359.999938, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1726.339721, -2503.030517, 1.436879, 0.000000, -0.000007, 359.999938, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1726.339721, -2528.012207, 1.436879, 0.000000, 0.000000, -0.000060, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1726.339721, -2552.981445, 1.436879, 0.000000, 0.000000, -0.000060, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1726.349487, -2556.691162, 8.956882, 0.000000, 0.000000, -0.000060, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1726.349487, -2478.179443, 8.956882, 0.000000, 0.000000, -0.000060, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(18981, 1737.200073, -2472.710937, 6.756863, 0.000014, 0.000000, 179.999984, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19913, 1726.325439, -2492.586914, 15.886907, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x0FFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90BBBBBB);
    arrtxt = CreateDynamicObject(19913, 1727.296264, -2542.578613, 26.706945, 10.000043, 0.000007, 89.999893, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x0FFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90BBBBBB);
    arrtxt = CreateDynamicObject(19913, 1727.296264, -2492.586914, 26.706945, 10.000043, 0.000007, 89.999893, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x0FFFFFFF);
    SetDynamicObjectMaterial(arrtxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x90BBBBBB);
    arrtxt = CreateDynamicObject(19552, 1790.470947, -2506.161621, 29.906967, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "ws_stationfloor", 0xFF777777);
    arrtxt = CreateDynamicObject(18981, 1749.221313, -2526.872314, 6.716884, 0.000000, -0.000007, 269.999938, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18766, 1737.174072, -2528.680664, 13.556915, 180.000000, 90.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(11714, 1751.145874, -2514.690673, 13.816894, 0.000014, 0.000000, 179.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "lasjmposh2", 0x00000000);
    arrtxt = CreateDynamicObject(11714, 1751.145874, -2517.650390, 13.816894, 0.000014, 0.000000, 179.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6284, "bev_law2", "lasjmposh2", 0x00000000);
    arrtxt = CreateDynamicObject(18763, 1735.811767, -2560.581054, 22.602848, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18763, 1735.811767, -2560.581054, 27.572849, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18763, 1728.940917, -2560.581054, 27.502853, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18763, 1728.940917, -2560.581054, 22.592849, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(19373, 1735.894287, -2559.556152, 18.866916, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1735.894287, -2560.326171, 18.866916, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1735.894287, -2561.036132, 18.866916, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1735.894287, -2561.836181, 18.866916, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1728.903442, -2559.556152, 18.866916, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1728.903442, -2560.326171, 18.866916, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1728.903442, -2561.036132, 18.866916, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1728.903442, -2561.836181, 18.866916, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(18763, 1735.791748, -2474.061035, 22.602848, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18763, 1735.791748, -2474.061035, 27.572849, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18763, 1728.920898, -2474.061035, 27.502853, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18763, 1728.920898, -2474.061035, 22.592849, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(19373, 1735.874267, -2473.036132, 18.866916, 0.000014, 0.000000, 89.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1735.874267, -2473.806152, 18.866916, 0.000014, 0.000000, 89.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1735.874267, -2474.516113, 18.866916, 0.000014, 0.000000, 89.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1735.874267, -2475.316162, 18.866916, 0.000014, 0.000000, 89.999954, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1728.883422, -2473.036132, 18.866916, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1728.883422, -2473.806152, 18.866916, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1728.883422, -2474.516113, 18.866916, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1728.883422, -2475.316162, 18.866916, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(8661, 1736.300537, -2468.725585, 12.596905, 0.000014, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19373, 1728.831298, -2484.971435, 10.976907, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1725.691284, -2484.971435, 10.976907, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1739.015502, -2484.971435, 10.976907, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1735.875488, -2484.971435, 10.976907, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(8661, 1736.300537, -2570.861572, 12.606905, 0.000014, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 6882, "vgnland", "hiwaygravel1_256", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19373, 1735.810180, -2550.883544, 10.986906, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1729.079589, -2550.883544, 10.986906, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1726.039550, -2550.883544, 10.986906, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(18981, 1751.659790, -2503.493408, 6.716882, 0.000000, -0.000007, 359.999938, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16093, "a51_ext", "ws_whitewall2_top", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(2991, 1731.047607, -2539.958496, 12.224157, 89.999992, 45.098960, -135.098999, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(2991, 1732.487670, -2539.958496, 12.224157, 89.999992, 180.000000, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1731.766967, -2539.375488, 12.166906, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1731.767456, -2540.475830, 12.166906, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(2991, 1731.047607, -2532.297363, 12.224157, 89.999992, 25.596660, -115.596694, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(2991, 1732.487670, -2532.297363, 12.224157, 89.999992, 180.000015, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1731.766967, -2531.714355, 12.166906, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1731.767456, -2532.814697, 12.166906, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(2991, 1731.047607, -2518.704345, 12.224157, 89.999992, 25.596660, -115.596694, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(2991, 1732.487670, -2518.704345, 12.224157, 89.999992, 180.000015, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1731.766967, -2518.121337, 12.166906, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1731.767456, -2519.221679, 12.166906, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(2991, 1731.047607, -2511.043212, 12.224157, 89.999992, 13.407106, -103.407119, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(2991, 1732.487670, -2511.043212, 12.224157, 89.999992, 180.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1731.766967, -2510.460205, 12.166906, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1731.767456, -2511.560546, 12.166906, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(2991, 1731.047607, -2502.213134, 12.224157, 89.999992, 13.407106, -103.407119, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(2991, 1732.487670, -2502.213134, 12.224157, 89.999992, 180.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1731.766967, -2501.630126, 12.166906, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1731.767456, -2502.730468, 12.166906, 0.000000, 0.000029, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(2991, 1731.047607, -2494.552001, 12.224157, 89.999992, 6.794076, -96.794067, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(2991, 1732.487670, -2494.552001, 12.224157, 89.999992, 180.000045, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF4E4945);
    SetDynamicObjectMaterial(arrtxt, 1, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1731.766967, -2493.968994, 12.166906, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1731.767456, -2495.069335, 12.166906, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1737.008056, -2523.825439, 21.346916, -0.000007, 0.000045, 0.000022, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(19373, 1737.017822, -2523.265625, 22.816923, -0.000007, 0.000045, 0.000022, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "INFORMATION", 140, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    arrtxt = CreateDynamicObject(19373, 1737.007324, -2522.635253, 21.346916, -0.000007, 0.000045, 0.000022, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(19128, 1737.099365, -2523.217285, 20.546913, 89.999992, 185.863647, -95.863578, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2813, "gb_books01", "GB_novels07", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2813, "gb_books01", "GB_novels04", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1737.008056, -2510.434814, 21.346916, -0.000007, 0.000052, 0.000022, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(19373, 1737.017822, -2509.875000, 22.816923, -0.000007, 0.000052, 0.000022, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "INFORMATION", 140, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    arrtxt = CreateDynamicObject(8293, 1735.577270, -2569.025634, 24.466934, -0.000018, -0.000024, -142.499908, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2813, "gb_books01", "GB_novels10", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "Bow_church_grass_alt", 0x00000000);
    arrtxt = CreateDynamicObject(8293, 1735.577270, -2465.815673, 24.466934, -0.000012, -0.000018, 37.500072, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2813, "gb_books01", "GB_novels11", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 10101, "2notherbuildsfe", "Bow_church_grass_alt", 0x00000000);
    arrtxt = CreateDynamicObject(19377, 1731.810791, -2468.989746, 19.166919, 0.000000, 90.000015, 179.999893, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14803, "bdupsnew", "Bdup2_Artex", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19624, 1745.922241, -2528.499267, 19.736913, 0.000000, 0.000000, 32.599998, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 3, 3881, "apsecurity_sfxrf", "leather_seat_256", 0x00000000);
    arrtxt = CreateDynamicObject(7212, 1737.000244, -2498.656982, 20.936922, 0.000000, 0.000000, 180.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(arrtxt, 9, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    arrtxt = CreateDynamicObject(7212, 1737.040283, -2535.866699, 20.936922, 0.000000, 0.000000, 180.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x90FFFFFF);
    SetDynamicObjectMaterial(arrtxt, 9, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    arrtxt = CreateDynamicObject(640, 1743.041870, -2563.580810, 19.866912, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 674, "gta_deserttrees", "sm_josh_leaf", 0x00000000);
    arrtxt = CreateDynamicObject(640, 1727.241943, -2564.510742, 19.866912, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 674, "gta_deserttrees", "sm_josh_leaf", 0x00000000);
    arrtxt = CreateDynamicObject(640, 1743.041870, -2471.082763, 19.866912, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 674, "gta_deserttrees", "sm_josh_leaf", 0x00000000);
    arrtxt = CreateDynamicObject(640, 1727.281005, -2470.393066, 19.866912, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 674, "gta_deserttrees", "sm_josh_leaf", 0x00000000);
    arrtxt = CreateDynamicObject(18981, 1752.781738, -2516.481201, 28.576868, 0.000000, 90.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18765, 1754.886596, -2516.939208, 30.436908, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    arrtxt = CreateDynamicObject(19861, 1751.108886, -2516.324218, 17.206909, 0.000000, 0.000000, 270.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "ARRIVALS GATE", 130, "Arial", 65, 1, 0xFFFFFFFF, 0x00000000, 1);
    arrtxt = CreateDynamicObject(19443, 1751.201904, -2514.947753, 16.066909, 90.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(19443, 1751.201049, -2517.388183, 16.066909, 90.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(2256, 1751.129150, -2507.145996, 13.956903, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "ventb128", 0x00000000);
    arrtxt = CreateDynamicObject(2256, 1751.129150, -2511.915771, 13.956903, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "ventb128", 0x00000000);
    arrtxt = CreateDynamicObject(2256, 1751.129150, -2520.284179, 13.956903, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "ventb128", 0x00000000);
    arrtxt = CreateDynamicObject(2256, 1751.129150, -2525.053955, 13.956903, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "ventb128", 0x00000000);
    arrtxt = CreateDynamicObject(19786, 1751.212524, -2524.407470, 15.936911, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 1, 2772, "airp_prop", "CJ_AIRP_S_2", 0x00000000);
    arrtxt = CreateDynamicObject(19786, 1751.212524, -2510.716308, 15.936911, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 1, 2772, "airp_prop", "CJ_AIRP_S_2", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1750.859985, -2515.597412, 18.756929, 90.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(19377, 1755.880859, -2513.126953, 13.206905, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(19377, 1755.880859, -2519.207275, 13.206905, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(2256, 1751.139160, -2514.138183, 17.526908, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    arrtxt = CreateDynamicObject(2256, 1751.139160, -2515.979003, 17.526908, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    arrtxt = CreateDynamicObject(2256, 1751.139160, -2517.820800, 17.526908, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    arrtxt = CreateDynamicObject(2256, 1751.149169, -2518.211181, 17.526908, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    arrtxt = CreateDynamicObject(19786, 1751.212524, -2507.495605, 15.936911, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 1, 2772, "airp_prop", "CJ_AIRP_S_2", 0x00000000);
    arrtxt = CreateDynamicObject(19786, 1751.212524, -2521.316894, 15.936911, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 1, 2772, "airp_prop", "CJ_AIRP_S_2", 0x00000000);
    arrtxt = CreateDynamicObject(19447, 1736.946655, -2516.094726, 17.676904, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(19940, 1737.021118, -2518.646728, 16.996910, -0.000014, -89.999977, -0.000075, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19861, 1737.078857, -2514.944335, 18.796915, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "International Airport", 130, "Segoe UI", 55, 1, 0xFFBBBBBB, 0x00000000, 1);
    arrtxt = CreateDynamicObject(19861, 1737.078857, -2514.944335, 18.126909, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "ARIVENA", 130, "Segoe UI", 125, 1, 0xFFBBBBBB, 0x00000000, 1);
    arrtxt = CreateDynamicObject(19373, 1737.007324, -2509.244628, 21.346916, -0.000007, 0.000052, 0.000022, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(19128, 1737.099365, -2509.826660, 20.546913, 89.999992, 182.939514, -92.939422, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2813, "gb_books01", "GB_novels04", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2813, "gb_books01", "GB_novels04", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1737.612060, -2487.325683, 18.396905, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.612060, -2490.816162, 18.396905, 89.999992, 90.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.612060, -2494.315917, 18.396905, 89.999992, 90.000045, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.612060, -2497.806396, 18.396905, 89.999992, 90.000045, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.612060, -2501.315429, 18.396905, 89.999992, 90.000061, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(19443, 1737.612060, -2503.254394, 18.396905, 89.999992, 90.000061, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFF636F87);
    arrtxt = CreateDynamicObject(18763, 1745.092041, -2494.648681, 17.816879, 0.000000, 90.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(19861, 1743.832519, -2494.597900, 15.956882, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "CLOTHING STORE", 130, "Palatino Linotype", 30, 1, 0xFFFFFFFF, 0x00000000, 1);
    arrtxt = CreateDynamicObject(19552, 1665.100585, -2515.351074, 12.506969, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 9514, "711_sfw", "ws_carpark2", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1762.588867, -2503.062744, 20.686906, -0.000022, 0.000022, -89.999900, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(19373, 1763.148681, -2503.072509, 22.156913, -0.000022, 0.000022, -89.999900, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "INFORMATION", 140, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    arrtxt = CreateDynamicObject(19373, 1762.898437, -2530.875976, 20.686906, -0.000022, 0.000022, 90.000038, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(19443, 1758.599609, -2501.541503, 20.176921, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, -1, "none", "none", 0xFF4E4945);
    arrtxt = CreateDynamicObject(19443, 1756.799560, -2501.541503, 21.846927, 0.000000, 90.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, -1, "none", "none", 0xFF4E4945);
    arrtxt = CreateDynamicObject(19443, 1754.469848, -2501.541503, 21.846927, 0.000000, 90.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, -1, "none", "none", 0xFF4E4945);
    arrtxt = CreateDynamicObject(2658, 1751.780273, -2502.112060, 21.066907, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 3967, "cj_airprt", "cj_juank_1", 0x00000000);
    arrtxt = CreateDynamicObject(2658, 1759.420410, -2502.112060, 21.066907, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 3967, "cj_airprt", "cj_juank_1", 0x00000000);
    arrtxt = CreateDynamicObject(1444, 1751.336059, -2503.011718, 20.076917, 0.000000, 0.000000, 29.699996, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 3967, "cj_airprt", "cj_juank_1", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1763.778930, -2503.062011, 20.686906, -0.000022, 0.000022, -89.999900, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(19128, 1763.197143, -2503.154052, 19.886903, 90.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2813, "gb_books01", "GB_novels05", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2813, "gb_books01", "GB_novels05", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1762.338623, -2530.866210, 22.156913, -0.000022, 0.000022, 90.000038, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "INFORMATION", 140, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    arrtxt = CreateDynamicObject(19373, 1761.708374, -2530.876708, 20.686906, -0.000022, 0.000022, 90.000038, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(19128, 1762.290161, -2530.784667, 19.886903, 89.999992, 179.971725, 720.028198, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2813, "gb_books01", "GB_novels04", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2813, "gb_books01", "GB_novels04", 0x00000000);
    arrtxt = CreateDynamicObject(18980, 1759.911132, -2501.625732, 23.716917, 0.000000, 90.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(18980, 1759.911132, -2532.927734, 23.716917, 0.000000, 90.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0xFF706353);
    arrtxt = CreateDynamicObject(19377, 1752.260620, -2502.005371, 29.456901, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    arrtxt = CreateDynamicObject(19377, 1761.900756, -2502.005371, 29.456901, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    arrtxt = CreateDynamicObject(19377, 1768.050903, -2502.004882, 29.456901, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    arrtxt = CreateDynamicObject(19377, 1752.260620, -2532.524414, 29.456901, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    arrtxt = CreateDynamicObject(19377, 1761.900756, -2532.524414, 29.456901, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    arrtxt = CreateDynamicObject(19377, 1768.050903, -2532.523925, 29.456901, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 14706, "labig2int2", "HS3_wall2", 0xFF706353);
    arrtxt = CreateDynamicObject(11714, 1753.217651, -2532.474853, 20.556900, 0.000006, 0.000000, -89.999992, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8391, "ballys01", "vgncorpdoor1_512", 0x00000000);
    arrtxt = CreateDynamicObject(11714, 1756.188964, -2532.474853, 20.556900, 0.000006, 0.000000, -89.999992, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 8391, "ballys01", "vgncorpdoor1_512", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1757.716186, -2532.964599, 20.176921, 0.000000, 0.000000, 179.999847, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, -1, "none", "none", 0xFF4E4945);
    arrtxt = CreateDynamicObject(19443, 1751.765991, -2532.964599, 20.176921, 0.000000, 0.000000, 179.999847, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, -1, "none", "none", 0xFF4E4945);
    arrtxt = CreateDynamicObject(19443, 1753.566040, -2532.964599, 21.846927, 0.000000, 90.000000, 179.999847, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, -1, "none", "none", 0xFF4E4945);
    arrtxt = CreateDynamicObject(19443, 1755.895751, -2532.964599, 21.846927, 0.000000, 90.000000, 179.999847, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, -1, "none", "none", 0xFF4E4945);
    arrtxt = CreateDynamicObject(2658, 1758.585327, -2532.394042, 21.066907, 0.000000, -0.000007, 179.999847, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 3967, "cj_airprt", "cj_juank_1", 0x00000000);
    arrtxt = CreateDynamicObject(2658, 1750.945190, -2532.394042, 21.066907, 0.000000, -0.000007, 179.999847, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 3967, "cj_airprt", "cj_juank_1", 0x00000000);
    arrtxt = CreateDynamicObject(1444, 1750.390869, -2531.337402, 20.076917, 0.000000, 0.000000, -14.300003, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 3967, "cj_airprt", "cj_juank_1", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1727.126831, -2506.042724, 14.026893, -0.000007, 0.000052, 0.000022, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(19373, 1727.136596, -2505.482910, 15.496900, -0.000007, 0.000052, 0.000022, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "INFORMATION", 140, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    arrtxt = CreateDynamicObject(19373, 1727.126098, -2504.852539, 14.026893, -0.000007, 0.000052, 0.000022, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(19128, 1727.218139, -2505.434570, 13.226890, 89.999992, 182.939514, -92.939422, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2813, "gb_books01", "GB_novels05", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2813, "gb_books01", "GB_novels04", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1727.126831, -2526.354248, 14.026893, -0.000007, 0.000060, 0.000022, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(19373, 1727.136596, -2525.794433, 15.496900, -0.000007, 0.000060, 0.000022, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "INFORMATION", 140, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    arrtxt = CreateDynamicObject(19373, 1727.126098, -2525.164062, 14.026893, -0.000007, 0.000060, 0.000022, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0xFF555555);
    arrtxt = CreateDynamicObject(19128, 1727.218139, -2525.746093, 13.226890, 89.999992, 181.470764, -91.470657, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2813, "gb_books01", "GB_novels07", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 2813, "gb_books01", "GB_novels04", 0x00000000);
    arrtxt = CreateDynamicObject(19940, 1737.021118, -2518.646728, 17.446905, -0.000014, -89.999977, -0.000075, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 18646, "matcolours", "red-4", 0xFFFF0000);
    arrtxt = CreateDynamicObject(19940, 1772.274658, -2517.085937, 27.276922, -0.000014, -89.999977, 179.999816, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    arrtxt = CreateDynamicObject(19940, 1772.274658, -2517.085937, 27.726917, -0.000014, -89.999977, 179.999816, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 18646, "matcolours", "red-4", 0xFFFF0000);
    arrtxt = CreateDynamicObject(1297, 1767.983642, -2510.284179, 31.286954, 0.000000, 171.999984, -179.999893, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(1297, 1743.882446, -2525.215087, 31.286954, 0.000000, 171.999984, -179.999938, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(1297, 1743.882446, -2510.284179, 31.286954, 0.000000, 171.999984, -179.999893, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(1297, 1755.763183, -2525.215087, 31.286954, 0.000000, 171.999984, -179.999893, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(1297, 1755.763183, -2510.284179, 31.286954, 0.000000, 171.999984, -179.999847, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    arrtxt = CreateDynamicObject(1431, 1738.189941, -2541.283447, 12.597847, 0.000051, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2212, "burger_tray", "burgerkids", 0x00000000);
    arrtxt = CreateDynamicObject(1431, 1738.149780, -2547.253906, 12.597847, 0.000051, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2212, "burger_tray", "burgerkids", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1747.966674, -2515.486816, 19.286911, 0.000000, 90.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1747.966674, -2517.968261, 19.285999, 0.000000, 90.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    arrtxt = CreateDynamicObject(19936, 1746.298461, -2514.171142, 19.286911, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19936, 1746.298461, -2519.514892, 19.286911, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(2163, 1746.289184, -2515.106689, 19.366912, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(2163, 1746.289184, -2517.767822, 19.366912, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1751.077026, -2515.486816, 20.876466, 0.000000, 180.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 4835, "airoads_las", "aarprt8LAS", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1751.076049, -2517.968261, 20.876466, 0.000000, 180.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 4835, "airoads_las", "aarprt8LAS", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1748.766235, -2515.486816, 22.535987, 0.000000, 270.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 4835, "airoads_las", "aarprt8LAS", 0x00000000);
    arrtxt = CreateDynamicObject(19373, 1748.766235, -2517.968261, 22.536964, 0.000000, 270.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 4835, "airoads_las", "aarprt8LAS", 0x00000000);
    arrtxt = CreateDynamicObject(19937, 1751.180541, -2514.847167, 22.090589, 0.000000, -45.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1751.180541, -2516.746582, 22.090589, 0.000000, -45.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1751.180541, -2518.606445, 22.090589, 0.000000, -44.999992, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19373, 1751.277221, -2515.486816, 21.056470, 0.000000, 180.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19373, 1751.276245, -2517.968261, 21.056470, 0.000000, 180.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19373, 1749.607055, -2515.486816, 22.865993, 0.000000, 270.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19373, 1749.607055, -2517.968261, 22.866970, 0.000000, 270.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1751.180541, -2518.625732, 22.720586, 0.000000, -89.999992, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1751.180541, -2514.825195, 22.720586, 0.000000, -89.999992, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1746.066284, -2518.092041, 19.311935, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1746.066284, -2516.191406, 19.311935, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19937, 1746.066040, -2515.600830, 19.311935, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(2163, 1745.858764, -2518.578613, 19.366912, -0.000007, 0.000000, 89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(2163, 1745.858764, -2515.917480, 19.366912, -0.000007, 0.000000, 89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(2387, 1748.374633, -2515.195312, 19.366903, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 10101, "2notherbuildsfe", "ferry_build14", 0xFF333333);
    arrtxt = CreateDynamicObject(19939, 1748.696655, -2515.099365, 20.196903, 0.000000, 90.000030, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19939, 1748.401977, -2515.077880, 20.196903, 0.000000, 89.999961, 179.999664, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(2387, 1748.374633, -2518.207275, 19.366903, 0.000000, 0.000022, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 3, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 10101, "2notherbuildsfe", "ferry_build14", 0xFF333333);
    arrtxt = CreateDynamicObject(19939, 1748.696655, -2518.111328, 20.196903, 0.000000, 90.000038, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(19939, 1748.401977, -2518.089843, 20.196903, 0.000000, 89.999954, 179.999618, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(2375, 1751.076538, -2514.773193, 19.501909, -0.000007, -0.000007, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 3, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 16640, "a51", "ws_stationfloor", 0xFF555555);
    SetDynamicObjectMaterial(arrtxt, 5, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(2375, 1751.076538, -2516.372802, 19.501909, -0.000007, -0.000007, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 3, 2423, "cj_ff_counters", "CJ_Laminate1", 0x00000000);
    SetDynamicObjectMaterial(arrtxt, 4, 16640, "a51", "ws_stationfloor", 0xFF555555);
    SetDynamicObjectMaterial(arrtxt, 5, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    arrtxt = CreateDynamicObject(2585, 1750.740356, -2515.287353, 20.931909, -0.000007, -0.000007, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 4, 18031, "cj_exp", "CJ_DS_WINDOW", 0x00000000);
    arrtxt = CreateDynamicObject(2585, 1750.740356, -2516.647460, 20.931909, -0.000007, -0.000007, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 4, 2028, "cj_games", "CJ_CONSOLETOP", 0x00000000);
    arrtxt = CreateDynamicObject(2585, 1750.740356, -2518.247802, 20.931909, -0.000007, -0.000007, -89.999961, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 4, 2655, "cj_banner", "CJ_SUBURBAN_1", 0x00000000);
    arrtxt = CreateDynamicObject(19443, 1747.838134, -2514.018554, 20.796918, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10973, "mall_sfse", "ws_grilleshade", 0xFFBBBBBB);
    arrtxt = CreateDynamicObject(19443, 1747.838134, -2519.350097, 20.796918, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 10973, "mall_sfse", "ws_grilleshade", 0x00000000);
    arrtxt = CreateDynamicObject(2599, 1746.068725, -2520.712158, 19.716911, 0.000000, 0.000000, -45.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "Electronic Store", 140, "Segoe Script", 90, 1, 0xFFFFFFFF, 0xFF555555, 1);
    arrtxt = CreateDynamicObject(2599, 1746.104125, -2520.676757, 19.926916, 0.000000, 0.000000, -45.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterialText(arrtxt, 0, "~", 140, "Webdings", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    arrtxt = CreateDynamicObject(2599, 1746.068603, -2513.086669, 19.716911, -0.000009, 0.000000, -134.999984, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(arrtxt, 0, "Electronic Store", 140, "Segoe Script", 90, 1, 0xFFFFFFFF, 0xFF555555, 1);
    arrtxt = CreateDynamicObject(2599, 1746.104003, -2513.121826, 19.926916, -0.000009, 0.000000, -134.999984, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterialText(arrtxt, 0, "~", 140, "Webdings", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    arrtxt = CreateDynamicObject(18766, 1749.044433, -2509.454101, 12.770131, 90.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    arrtxt = CreateDynamicObject(18766, 1749.044433, -2522.583740, 12.740133, 90.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(arrtxt, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(2936, 1755.069824, -2515.831787, 20.406906, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2936, 1754.659667, -2515.561523, 20.516908, 0.000035, 0.000012, 69.999984, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3439, 1753.965454, -2516.931396, 20.526920, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1892, 1768.363159, -2514.307617, 19.286909, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1892, 1768.363159, -2517.808593, 19.286909, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1892, 1768.363159, -2521.099853, 19.286909, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3439, 1771.145751, -2511.821777, 20.526920, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3439, 1771.145751, -2522.642578, 20.526920, 0.000000, 0.000037, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1757.517089, -2516.786132, 19.701902, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1754.041992, -2513.220947, 19.701902, 0.000007, -0.000014, 179.999862, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1754.041992, -2520.171142, 19.701902, -0.000007, 0.000014, -0.000006, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1744.433227, -2540.174316, 19.786911, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1744.433227, -2538.002197, 19.786911, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1744.433227, -2546.484619, 19.786911, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1744.433227, -2544.312500, 19.786911, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1744.433227, -2552.856445, 19.786911, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1744.433227, -2550.684326, 19.786911, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1744.433227, -2559.166748, 19.786911, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1744.433227, -2556.994628, 19.786911, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3586, 1732.411132, -2555.335205, 15.846920, 0.000000, 0.000014, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.646240, -2534.041992, 14.471908, 0.000014, -0.000067, 179.999511, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.646240, -2532.781494, 14.511911, -0.000012, 0.000067, 0.000012, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2533.091796, 15.391917, 89.999992, 89.787414, -89.787284, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2533.091796, 15.011908, 89.999992, 89.787414, -89.787284, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2533.091796, 13.271899, 89.999992, 89.787414, -89.787284, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.646240, -2539.983398, 14.501912, 0.000022, -0.000067, 179.999496, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.646240, -2538.722900, 14.431914, -0.000020, 0.000067, 0.000037, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2539.033203, 15.391917, 89.999992, 89.787429, -89.787284, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2539.033203, 15.011908, 89.999992, 89.787429, -89.787284, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2539.033203, 13.261902, 89.999992, 89.787429, -89.787284, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2535.982421, 15.181913, -89.999992, 90.107124, 90.106933, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.626220, -2536.863037, 15.181913, -89.999992, -89.894157, 90.105659, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2535.982421, 15.371916, -89.999992, 90.092643, 90.092430, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.626220, -2536.863037, 15.371916, -89.999992, -89.908271, 90.091499, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.632568, -2534.871826, 12.581979, 0.000020, 180.197845, -179.801681, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.624755, -2534.871826, 14.771966, 0.000020, 180.197845, -179.801681, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.634521, -2537.953613, 13.521952, 0.000000, 180.197845, 0.197918, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.636962, -2537.953613, 14.131953, 0.000000, 180.197845, 0.197918, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1737.082763, -2540.643310, 12.957556, -0.000067, -0.000006, -89.999778, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 1737.082763, -2532.241455, 12.957556, -0.000067, -0.000006, -89.999778, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1987, 1740.939941, -2548.325439, 12.507843, 0.000000, -0.000059, 179.999633, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.646240, -2543.113281, 14.501912, 0.000022, -0.000081, 179.999404, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1994, 1740.121337, -2533.188964, 12.597846, 0.000007, -0.000059, 179.999588, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1993, 1739.151123, -2533.188964, 12.597846, 0.000007, -0.000059, 179.999588, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1995, 1738.160644, -2533.188964, 12.597846, 0.000007, -0.000059, 179.999588, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1885, 1738.048339, -2534.439453, 12.597848, 0.000052, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.646240, -2541.852783, 14.431914, -0.000020, 0.000081, 0.000037, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2542.163085, 15.391917, 89.999992, 89.946968, -89.946792, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1987, 1741.820556, -2548.325439, 12.507843, 0.000000, -0.000068, 179.999588, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1987, 1742.690917, -2548.325439, 12.507843, 0.000000, -0.000068, 179.999588, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2542.163085, 15.011908, 89.999992, 89.946968, -89.946792, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2542.163085, 13.261902, 89.999992, 89.946968, -89.946792, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.646240, -2544.503662, 14.501912, 0.000022, -0.000090, 179.999359, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2543.553466, 15.391917, 89.999992, 89.973571, -89.973373, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2543.553466, 15.011908, 89.999992, 89.973571, -89.973373, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2543.553466, 13.261902, 89.999992, 89.973571, -89.973373, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.646240, -2545.935058, 14.501912, 0.000022, -0.000096, 179.999313, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2544.984863, 15.391917, 89.999992, 89.986885, -89.986663, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2544.984863, 15.011908, 89.999992, 89.986885, -89.986663, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1897, 1737.616210, -2544.984863, 13.261902, 89.999992, 89.986885, -89.986663, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1744.433227, -2477.613525, 19.786911, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1744.433227, -2475.441406, 19.786911, 0.000037, 0.000000, 89.999885, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2362, 1741.925292, -2533.128173, 13.547850, 0.000000, 0.000060, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1744.433227, -2483.923828, 19.786911, 0.000044, 0.000000, 89.999862, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1744.433227, -2481.751708, 19.786911, 0.000044, 0.000000, 89.999862, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1744.433227, -2490.295654, 19.786911, 0.000044, 0.000000, 89.999862, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1744.433227, -2488.123535, 19.786911, 0.000044, 0.000000, 89.999862, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1744.433227, -2496.605957, 19.786911, 0.000051, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1744.433227, -2494.433837, 19.786911, 0.000050, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3586, 1732.381103, -2479.299072, 15.846920, 0.000000, 0.000012, 179.999893, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2371, 1740.047119, -2485.945068, 12.606884, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2371, 1742.648559, -2485.945068, 12.606884, 0.000007, 0.000000, 89.999977, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2371, 1740.047119, -2503.176025, 12.606884, 0.000022, 0.000000, 89.999931, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2371, 1742.648559, -2503.176025, 12.606884, 0.000029, 0.000000, 89.999908, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1743.737792, -2500.382324, 13.836879, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1743.737792, -2499.642333, 13.836879, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1743.737792, -2501.123291, 13.836879, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1743.737792, -2498.852539, 13.836879, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1743.737792, -2489.510498, 13.836879, -0.000014, 0.000000, -89.999954, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1743.737792, -2488.770507, 13.836879, -0.000014, 0.000000, -89.999954, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1743.737792, -2490.251464, 13.836879, -0.000014, 0.000000, -89.999954, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2374, 1743.737792, -2487.980712, 13.836879, -0.000014, 0.000000, -89.999954, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2072, 1740.769042, -2495.103027, 17.356901, 0.000000, 90.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2072, 1740.769042, -2491.133056, 17.356901, 0.000000, 90.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2072, 1740.769042, -2498.903808, 17.356901, 0.000000, 90.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2412, 1738.394165, -2496.333007, 12.606884, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2412, 1738.394165, -2491.971435, 12.606884, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3969, 1749.286132, -2504.217041, 13.566905, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1572, 1740.618530, -2524.821289, 13.186902, 0.000000, 0.000000, -102.699996, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1572, 1742.567382, -2520.718750, 13.186902, 0.000000, 0.000000, -102.699996, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1572, 1738.828613, -2506.559326, 13.186902, 0.000000, 0.000000, 6.699996, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2372, 1738.105468, -2507.084716, 12.596904, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2372, 1740.005859, -2507.084716, 12.596904, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1572, 1738.981689, -2507.860107, 13.186902, 0.000000, 0.000000, 30.299995, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19787, 1741.512817, -2505.139160, 14.806911, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19787, 1739.272338, -2505.139160, 14.806911, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19787, 1739.272338, -2526.333251, 14.806911, 0.000000, 0.000000, 179.999893, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19787, 1741.512817, -2526.333251, 14.806911, 0.000000, 0.000000, 179.999893, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(822, 1729.166137, -2478.250000, 12.596907, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(822, 1735.136474, -2478.250000, 12.596907, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(905, 1728.627197, -2483.204589, 12.876907, 0.000000, 0.000000, 110.500000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(905, 1729.433959, -2482.479248, 12.627618, -159.900024, 0.000000, 96.300010, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(822, 1735.397949, -2557.435302, 12.596907, 0.000000, 0.000007, 179.999893, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(822, 1729.427612, -2557.435302, 12.596907, 0.000000, 0.000007, 179.999893, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(905, 1735.936889, -2552.480712, 12.876907, 0.000007, -0.000001, -69.500038, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(905, 1735.130126, -2553.206054, 12.627618, -20.099971, -179.999984, 96.299942, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1572, 1727.811401, -2526.888916, 13.186902, 0.000000, 0.000000, 77.300003, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1572, 1728.020019, -2547.451904, 13.186902, 0.000000, 0.000000, 101.399986, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2790, 1726.939453, -2547.808349, 16.336910, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2790, 1726.939453, -2487.498291, 16.336910, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2790, 1726.939453, -2553.948730, 16.336910, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2790, 1726.939453, -2481.457519, 16.336910, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1572, 1727.811401, -2508.179931, 13.186902, 0.000007, 0.000000, 77.299980, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19624, 1745.391479, -2528.838378, 19.736913, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2790, 1743.446533, -2471.041015, 22.416908, 0.000000, 0.000000, 270.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2790, 1743.446533, -2563.682617, 22.416908, 0.000000, 0.000000, 270.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1444, 1738.555419, -2525.025146, 13.396906, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1749.033325, -2513.353515, 13.116905, 0.000050, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1749.033325, -2519.193847, 13.116905, 0.000050, 0.000000, 89.999839, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1746.223388, -2513.353515, 13.116905, 0.000059, 0.000000, 89.999816, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2773, 1746.223388, -2519.193847, 13.116905, 0.000059, 0.000000, 89.999816, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2790, 1747.001098, -2505.031494, 16.416912, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2790, 1747.001098, -2526.363769, 16.416912, 0.000000, 0.000000, 180.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8038, 1686.557739, -2453.091552, 32.424674, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3663, 1715.989135, -2494.688232, 14.474685, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3663, 1715.989135, -2535.949462, 14.474685, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3663, 1715.989135, -2569.790039, 14.474685, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8038, 1687.887573, -2581.911621, 32.424674, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3666, 1674.386596, -2492.091308, 12.934687, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3666, 1674.386596, -2510.551513, 12.934687, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3666, 1674.386596, -2527.553710, 12.934687, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3666, 1674.386596, -2546.013916, 12.934687, 0.000000, 0.000007, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1572, 1760.254882, -2503.381591, 19.836900, 0.000017, 0.000027, -12.700035, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1572, 1769.288330, -2505.417480, 19.836898, 0.000017, 0.000027, -61.200031, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1572, 1768.204956, -2507.457275, 19.836898, 0.000017, 0.000027, -88.200027, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1572, 1768.834228, -2527.487548, 19.836889, 0.000017, 0.000027, -40.100032, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19786, 1749.804931, -2502.040771, 21.526941, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19786, 1749.040283, -2532.465332, 21.526941, 0.000000, -0.000007, 179.999847, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1987, 1740.090209, -2548.325439, 12.507843, 0.000000, -0.000059, 179.999633, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1987, 1739.229980, -2548.325439, 12.507843, 0.000000, -0.000059, 179.999633, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19860, 1743.714721, -2534.471191, 13.826884, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1748.702026, -2514.688720, 20.426900, 89.999992, 180.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1748.702026, -2515.138671, 20.426900, 89.999992, 180.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1748.702026, -2515.488525, 20.426900, 89.999992, 180.000030, -89.999961, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1748.396606, -2515.488525, 20.426900, 89.999992, 356.598236, -86.598289, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1748.396606, -2515.038574, 20.426900, 89.999992, 356.598236, -86.598289, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1748.396606, -2514.688720, 20.426900, 89.999992, 356.598236, -86.598289, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1748.702026, -2517.700683, 20.426900, 89.999992, 180.000045, -89.999961, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1748.702026, -2518.150634, 20.426900, 89.999992, 180.000045, -89.999961, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1748.702026, -2518.500488, 20.426900, 89.999992, 180.000045, -89.999961, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1748.396606, -2518.500488, 20.426900, 89.999992, 358.297607, -88.297630, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1748.396606, -2518.050537, 20.426900, 89.999992, 358.297607, -88.297630, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18874, 1748.396606, -2517.700683, 20.426900, 89.999992, 358.297607, -88.297630, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19787, 1747.054931, -2515.784912, 22.186916, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19787, 1747.054931, -2517.815185, 22.186916, -0.000007, 0.000000, -89.999977, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1514, 1746.160766, -2516.794189, 20.512851, 0.000000, 0.000000, -90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 1744.303222, -2521.751464, 13.435035, 0.000000, 0.000000, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, 1747.703125, -2507.060546, 13.435035, 0.000000, 0.000000, 83.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19624, 1746.836547, -2512.066894, 13.363054, 90.000000, 4.299999, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19624, 1750.320068, -2525.184570, 13.353054, 90.000000, 4.299999, 0.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19559, 1747.844726, -2520.183837, 13.239685, 270.000000, 0.000000, -82.100006, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(4890, 1814.321777, -2519.905517, 18.624485, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(4890, 1814.321777, -2571.420166, 18.624485, 0.000000, 0.000000, 360.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(4890, 1814.321777, -2425.025390, 18.534488, 0.000000, 0.000000, 720.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3996, 1786.661987, -2554.935302, 19.328252, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3996, 1786.661987, -2451.755371, 19.338253, 0.000000, 0.000000, 90.000000, 60, 0, -1, 200.00, 200.00);
}