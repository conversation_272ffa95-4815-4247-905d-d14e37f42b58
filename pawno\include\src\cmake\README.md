Name                                             | Description
:------------------------------------------------|:--------------------------------------------------------
[AddSAMPPlugin](AddSAMPPlugin.cmake)             | Provides `add_samp_plugin()` function
[AddSAMPPluginTest](AddSAMPPluginTest.cmake)     | Provides `add_samp_plugin_test()` function
[AddSAMPPluginTestPR](AddSAMPPluginTestPR.cmake) | Same as above but uses [plugin-runner][plugin-runner] instead of [samp-server-cli][samp-server-cli]
[AMXConfig](AMXConfig.cmake)                     | Defines platform-specific macros expected by AMX headers
[FindPawnCC](FindPawnCC.cmake)                   | Finds Pawn compiler executable
[FindPluginRunner](FindPluginRunner.cmake)       | Finds [plugin-runner][plugin-runner] executable
[FindSAMPSDK](FindSAMPSDK.cmake)                 | Finds SA-MP plugin SDK
[FindSAMPGDK](FindSAMPGDK.cmake)                 | Finds [GDK][sampgdk] library and headers
[FindSAMPServer](FindSAMPServer.cmake)           | Finds SA-MP server executable
[FindSAMPServerCLI](FindSAMPServerCLI.cmake)     | Finds [samp-server-cli][samp-server-cli]

[sampgdk]: https://github.com/Zeex/sampgdk
[samp-server-cli]: https://github.com/Zeex/samp-server-cli
[plugin-runner]: https://github.com/Zeex/plugin-runner
