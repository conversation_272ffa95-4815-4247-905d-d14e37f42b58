#include <YSI_Coding\y_hooks>

new Float: <PERSON><PERSON>Plant[][6] =
{
    {2053.115966, -1246.523681, 22.632564, 0.000000, 0.000000, 90.000000},
    {2048.965820, -1246.523681, 22.462568, 0.000000, 0.000000, 90.000000},
    {2044.896118, -1246.501586, 22.481046, 5.699998, 5.500000, 135.200027},
    {2040.145263, -1247.007202, 22.462568, 0.000000, 0.000000, 58.200012},
    {2043.570190, -1241.482666, 22.082569, 0.000000, 0.000000, 58.200012},
    {2047.480712, -1241.741333, 22.302570, 0.000000, 0.000000, 108.900009},
    {2052.003417, -1240.193359, 22.472570, 0.000000, 0.000000, 1.300014},
    {2051.904296, -1235.894165, 22.422571, 0.000000, 0.000000, 30.400016},
    {2044.498291, -1236.865600, 21.992567, 0.000000, 0.000000, 30.400016},
    {2039.799438, -1240.714111, 21.822565, 0.000000, 0.000000, 69.300018},
    {2036.413696, -1245.632324, 22.312459, 0.000000, 3.599998, 50.100025},
    {2031.619262, -1245.866699, 22.312459, 0.000000, 3.599998, 92.800041},
    {2034.785034, -1241.202148, 21.768531, 0.000000, 3.599998, 83.300033},
    {2038.699829, -1235.219238, 21.706623, 0.000000, 3.599998, 108.600036},
    {2032.832153, -1236.545410, 21.417766, 0.000000, -0.200000, 25.000047},
    {2029.179809, -1241.635864, 21.858707, 0.000000, -0.200000, 25.000047},
    {2027.454833, -1237.563232, 21.414905, 0.000000, -0.200000, 119.600051},
    {2031.957763, -1232.022583, 21.254909, 0.000000, -0.200000, 169.900054},
    {2038.532104, -1230.308837, 21.633356, 0.000000, -0.200000, -155.199951},
    {2044.009399, -1232.646118, 22.109422, 0.000000, -0.200000, -155.199951},
    {2050.389892, -1230.566284, 22.478349, 2.599999, -0.200000, -46.499950},
    {2045.276489, -1226.938720, 21.937526, 2.599999, -0.200000, 39.700042},
    {2042.606689, -1223.721801, 21.797021, 2.599999, -0.200000, 39.700042},
    {2048.009521, -1218.586669, 22.192970, 2.599999, -0.200000, 64.500045},
    {2051.077148, -1222.654418, 22.540077, -0.599997, -0.200000, 64.500045},
    {2053.524902, -1226.998413, 22.637380, -0.599997, -0.200000, 91.100028},
    {2034.748413, -1226.795532, 21.549194, 2.599999, -0.200000, 3.800040},
    {2040.843627, -1220.253173, 21.794197, 2.599999, -0.200000, 39.500038},
    {2037.763916, -1224.801513, 21.631265, 2.599999, -0.200000, -51.499961},
    {2046.513549, -1214.334594, 22.129770, 2.599999, -0.200000, 56.300037},
    {2044.155151, -1219.441162, 21.924688, 2.599999, -0.200000, 127.200012},
    {2054.279296, -1216.818359, 22.696243, -3.399998, -0.200000, 90.100036},
    {2051.144531, -1216.823852, 22.510023, -3.399998, -0.200000, 90.100036},
    {2054.279296, -1211.448486, 22.576240, -3.399991, -0.200000, 90.100013},
    {2051.144531, -1211.453979, 22.390020, -3.399991, -0.200000, 90.100013},
    {2054.279296, -1205.567749, 22.576240, -3.399983, -0.200000, 90.099990},
    {2051.144531, -1205.573242, 22.390020, -3.399983, -0.200000, 90.099990},
    {2054.279296, -1198.958007, 22.576240, -3.399976, -0.200000, 90.099967},
    {2051.144531, -1198.963500, 22.390020, -3.399976, -0.200000, 90.099967},
    {2054.279296, -1193.017822, 22.576240, -3.399967, -0.200000, 90.099945},
    {2051.144531, -1193.023315, 22.390020, -3.399967, -0.200000, 90.099945},
    {2054.279296, -1186.207397, 22.656242, -3.399967, -0.200000, 90.099945},
    {2051.144531, -1186.212890, 22.470022, -3.399967, -0.200000, 90.099945},
    {2054.279296, -1180.267211, 22.656242, -3.399960, -0.200000, 90.099922},
    {2051.144531, -1180.272705, 22.470022, -3.399960, -0.200000, 90.099922},
    {2054.279296, -1173.678100, 22.536239, -3.399960, -0.200000, 90.099922},
    {2051.144531, -1173.683593, 22.350019, -3.399960, -0.200000, 90.099922},
    {2054.279296, -1167.737915, 22.536239, -3.399951, -0.200000, 90.099899},
    {2051.144531, -1167.743408, 22.350019, -3.399951, -0.200000, 90.099899},
    {2047.524169, -1176.225830, 22.162862, -2.699959, -0.200000, 126.799926},
    {2043.240966, -1171.614501, 21.862276, -1.299960, -0.200000, 143.599929},
    {2047.363525, -1169.695434, 22.126258, -2.699959, 1.199998, 143.599929},
    {2038.518066, -1168.127563, 21.662817, -1.299960, 2.200000, 143.599929},
    {2040.361694, -1166.099365, 21.673282, -1.299960, 2.200000, 161.499938},
    {2044.608154, -1167.528564, 22.005441, -1.299960, 2.200000, 161.499938},
    {2047.300170, -1164.780151, 22.068490, -1.299960, 2.200000, -178.500076},
    {2052.856689, -1163.011840, 22.440652, -1.299960, 2.200000, -178.500076},
    {2054.968750, -1157.881469, 22.654722, -1.299960, 2.200000, -88.500076},
    {2054.802734, -1151.587890, 22.626392, -1.299960, 2.200000, -88.500076},
    {2051.262939, -1151.679443, 22.656755, -1.299960, 2.200000, -59.500087},
    {2046.695922, -1148.901367, 22.803501, 6.600039, -0.199999, 30.499912},
    {2052.601806, -1148.426391, 22.777189, 6.600039, -0.199999, 7.399910},
    {2041.357421, -1151.993530, 22.414560, 6.600039, -0.199999, 30.499912},
    {2040.524047, -1148.245727, 22.964841, 6.600039, -0.199999, 11.299915},
    {2035.571044, -1150.042846, 22.761075, 0.000038, -6.300000, 99.099929},
    {2035.511108, -1157.046630, 21.840414, -3.399962, -3.899999, 99.099929},
    {2035.478759, -1163.693725, 21.519006, -3.399962, -3.899999, 99.099929},
    {2031.349609, -1157.701782, 21.420246, -5.599962, -3.899999, 99.099929},
    {2030.788696, -1162.498291, 21.180719, 1.700037, 0.199999, 170.199920},
    {2024.549804, -1159.146484, 20.830629, -1.199962, 4.099999, 170.199920},
    {2044.294067, -1193.037597, 22.533996, -3.399967, -0.200000, 90.099945},
    {2040.185546, -1193.043945, 22.049493, -3.399967, -0.200000, 90.099945},
    {2040.173828, -1187.114379, 22.070154, -3.399967, -0.200000, 104.799942},
    {2043.987670, -1186.109985, 22.474704, -3.399967, -0.200000, 120.099945},
    {2040.246459, -1180.770507, 22.183574, -3.399967, 3.199998, 137.199966},
    {2035.518920, -1176.351684, 21.672439, -3.399967, 3.199998, 137.199966},
    {2032.616455, -1179.500000, 21.238016, -3.399967, 3.199998, 90.499954},
    {2036.895629, -1182.430419, 21.807392, -3.399967, 3.199998, 90.499954},
    {2033.519897, -1186.952636, 21.395004, -0.199967, 6.299999, 160.499923},
    {2027.885375, -1184.905273, 20.563596, -3.399967, 6.299999, 160.499923},
    {2028.710693, -1180.062377, 20.935464, 4.200033, 2.799998, 250.499923},
    {2030.879638, -1173.851806, 21.326250, 4.200033, 2.799998, 250.499923},
    {2025.793579, -1189.177246, 20.307273, 4.200033, 2.799998, -91.800094},
    {2030.046997, -1190.822143, 20.830213, 4.200033, 2.799998, -91.800094},
    {2034.057373, -1192.184814, 21.227752, 4.200033, 1.799999, -91.800094},
    {2037.417846, -1192.296386, 21.594831, 4.200033, 2.799998, -91.800094},
    {2043.403198, -1200.305297, 22.380205, -3.399967, -0.200000, 79.199951},
    {2037.994506, -1198.403442, 21.764038, -1.699968, 1.399999, 169.700012},
    {2031.757568, -1197.246337, 20.969194, -0.599968, 1.799999, 169.700012},
    {2025.266479, -1195.043090, 20.286119, -0.599968, 5.799999, 169.700012},
    {2037.240112, -1202.571044, 21.508335, 2.800032, 4.299999, 169.700012},
    {2041.413452, -1207.406372, 22.108953, 2.800032, 4.299999, -106.600006},
    {2037.898559, -1212.549804, 21.763282, 5.000031, 3.000000, -116.500000},
    {2036.037963, -1206.473999, 21.342914, 1.900032, -5.400000, -11.700003},
    {2030.119262, -1201.731323, 20.687377, 1.900032, -5.400000, -11.700003},
    {2023.834228, -1199.648437, 19.994617, 1.900032, -5.400000, -64.999984},
    {2029.096557, -1206.555297, 20.782188, -1.399967, -5.400000, -11.700003},
    {2033.722167, -1211.067260, 21.113557, -7.699966, -0.900000, 55.100002},
    {2033.403564, -1216.549560, 21.485994, -5.499968, -0.900000, 37.700008},
    {2027.409301, -1220.208496, 21.140935, -5.499968, -0.900000, 25.000007},
    {2020.988647, -1222.302368, 20.642576, -5.499968, -0.900000, 25.000007},
    {2020.331054, -1218.467407, 20.357719, -3.599968, -2.500000, -6.599992},
    {2019.875366, -1201.515136, 19.590221, 1.200034, -5.999999, -27.499988},
    {2020.253051, -1195.139038, 19.607170, 2.200034, -0.799999, -98.999992},
    {2021.245849, -1188.730834, 19.945535, 7.100034, -0.799999, -98.999992},
    {2021.967285, -1184.133178, 20.010135, 5.200037, 4.699999, -155.099975},
    {2017.426391, -1188.160034, 19.337615, 1.700036, 4.699999, -101.999954},
    {2016.830200, -1194.131469, 19.167964, 7.000032, -1.900000, -89.299964}
};

new MowingVeh[3],
    MowingCountingGrass[MAX_PLAYERS],
    MowingTimerSecond[MAX_PLAYERS],
    STREAMER_TAG_OBJECT:MowingObject[MAX_PLAYERS][sizeof(MowingPlant)],
    STREAMER_TAG_AREA:MowingArea[MAX_PLAYERS][sizeof(MowingPlant)];

static counts[MAX_PLAYERS];
hook OnGameModeInit()
{
    CreateDynamicObject(970, 2112.819335, -1174.593505, 23.813419, 0.000022, -2.299999, 88.599952, 0, 0, -1, 50.00, 50.00); 
    CreateDynamicObject(970, 2112.726806, -1178.759277, 23.676898, 0.000022, -1.499999, 88.599952, 0, 0, -1, 50.00, 50.00); 
    CreateDynamicObject(970, 2112.623779, -1182.906494, 23.552169, -0.003099, -2.099904, 88.799865, 0, 0, -1, 50.00, 50.00); 
    CreateDynamicObject(970, 2112.536132, -1187.072021, 23.392086, -0.003099, -2.299905, 88.799865, 0, 0, -1, 50.00, 50.00); 
    CreateDynamicObject(970, 2113.949707, -1174.593505, 23.813419, 0.000038, -2.299999, 88.599906, 0, 0, -1, 50.00, 50.00); 
    CreateDynamicObject(970, 2113.857177, -1178.759277, 23.676898, 0.000038, -1.499999, 88.599906, 0, 0, -1, 50.00, 50.00); 
    CreateDynamicObject(970, 2113.754150, -1182.906494, 23.552169, -0.003083, -2.099904, 88.799819, 0, 0, -1, 50.00, 50.00); 
    CreateDynamicObject(970, 2113.666503, -1187.072021, 23.392086, -0.003083, -2.299904, 88.799819, 0, 0, -1, 50.00, 50.00); 
    CreateDynamicObject(19353, 2112.741699, -1172.488281, 21.629180, 1.199999, 0.000000, 90.000000, 0, 0, -1, 50.00, 50.00); 

    CreateDynamicPickup(1239, 23, 2123.0942,-1169.3950,24.2708, 0, 0, -1, 30.00, -1, 0);
    CreateDynamic3DTextLabel("[ Mowing Sidejob Point ]", 0xFF99A4FF, 2123.0942,-1169.3950,24.2708+0.5, 10.00, INVALID_PLAYER_ID, INVALID_VEHICLE_ID, 1, 0, 0, -1, 10.00, -1, 0);
    
    MowingVeh[0] = AddStaticVehicleEx(572,2122.3044,-1174.8407,23.8310,90.1317,126,126,60000,false);
	MowingVeh[1] = AddStaticVehicleEx(572,2122.3340,-1177.8486,23.7337,91.8570,126,126,60000,false);
	MowingVeh[2] = AddStaticVehicleEx(572,2122.5283,-1181.9128,23.7190,92.8568,126,126,60000,false);

    static string[144];
    for(new x; x < sizeof(MowingVeh);x++)
    {
        format(string, sizeof(string), "{E75480}MOW-%d", x+1);
        SetVehicleNumberPlate(MowingVeh[x], string);
        SetTimerEx("RespawnPV", 1000, false, "d", MowingVeh[x]);
        SetVehicleSpeedCap(MowingVeh[x], 30.0);
    }
    return 1;
}

IsAMowingSidejobVeh(carid)
{
	for(new v; v < sizeof(MowingVeh); v++) 
	{
	    if(carid == MowingVeh[v]) return 1;
	}
	return 0;
}

StartMowingSidejob(playerid)
{
    for(new x; x < sizeof(MowingPlant); x++)
    {
        if(DestroyDynamicObject(MowingObject[playerid][x]))
            MowingObject[playerid][x] = STREAMER_TAG_OBJECT: INVALID_STREAMER_ID;

        if(DestroyDynamicArea(MowingArea[playerid][x]))
            MowingArea[playerid][x] = STREAMER_TAG_AREA: INVALID_STREAMER_ID;

        MowingObject[playerid][x] = CreateDynamicObject(14400, MowingPlant[x][0], MowingPlant[x][1], MowingPlant[x][2], MowingPlant[x][3], MowingPlant[x][4], MowingPlant[x][5], 0, 0, playerid, 200.00, 200.00, -1);
        MowingArea[playerid][x] = CreateDynamicCircle(MowingPlant[x][0], MowingPlant[x][1], 3.5, 0, 0, playerid);
    }
    MowingTimerSecond[playerid] = 80;
    AccountData[playerid][pSideJob] = SIDEJOB_MOWING;
    ShowTDN(playerid, NOTIFICATION_WARNING, "Segera potong rumput di taman untuk mendapatkan uang.");
    return 1;
}

StopMowingSidejob(playerid)
{
    for(new x; x < sizeof(MowingPlant); x++)
    {
        if(DestroyDynamicObject(MowingObject[playerid][x]))
            MowingObject[playerid][x] = STREAMER_TAG_OBJECT: INVALID_STREAMER_ID;

        if(DestroyDynamicArea(MowingArea[playerid][x]))
            MowingArea[playerid][x] = STREAMER_TAG_AREA: INVALID_STREAMER_ID;
    }
    AccountData[playerid][pSideJob] = SIDEJOB_NONE;
    PlayerPlaySound(playerid, 5201, 0.0, 0.0, 0.0);

    static Float:pricing, string[144];
    pricing = MowingCountingGrass[playerid] * 2;
    GivePlayerMoneyEx(playerid, floatround(pricing));
    format(string, sizeof(string), "[Sidejob] "WHITE"The mowing task is complete, and you have earned a payment of "SEAGREEN"$%s.", FormatMoney(floatround(pricing)));
    SendClientMessage(playerid, X11_CORAL, string);
    MowingCountingGrass[playerid] = 0;
    AccountData[playerid][pMowingSidejobDelay] = 1800;
    RemovePlayerFromVehicle(playerid);
    SetTimerEx("RespawnPV", 1500, false, "d", SavingVehID[playerid]);

    PlayerPlaySound(playerid, 183, 0.0, 0.0, 0.0);
    GameTextForPlayer(playerid, sprintf("mission passed!~n~~w~$%s",FormatMoney(floatround(pricing))), 8900, 0);
    SetTimerEx("StopMissPassed", 8000, false, "i", playerid);
    return 1;
}

CancelMowingSideJob(playerid)
{
    for(new x; x < sizeof(MowingPlant); x++)
    {
        if(DestroyDynamicObject(MowingObject[playerid][x]))
            MowingObject[playerid][x] = STREAMER_TAG_OBJECT: INVALID_STREAMER_ID;

        if(DestroyDynamicArea(MowingArea[playerid][x]))
            MowingArea[playerid][x] = STREAMER_TAG_AREA: INVALID_STREAMER_ID;
    }
    AccountData[playerid][pSideJob] = SIDEJOB_NONE;
    PlayerPlaySound(playerid, 5201, 0.0, 0.0, 0.0);
    MowingCountingGrass[playerid] = 0;
    MowingTimerSecond[playerid] = 0;
    AccountData[playerid][pMowingSidejobDelay] = 1800;
    RemovePlayerFromVehicle(playerid);
    SetTimerEx("RespawnPV", 1500, false, "d", SavingVehID[playerid]);
    return 1;
}

ptask CheckingMowingGrass[1000](playerid)
{
    if(IsPlayerConnected(playerid) && AccountData[playerid][pSideJob] == SIDEJOB_MOWING)
    {
        for(new x; x < sizeof(MowingPlant); x++)
        {
            if(IsPlayerInRangeOfPoint(playerid, 3.5, MowingPlant[x][0], MowingPlant[x][1], MowingPlant[x][2]) && IsValidDynamicArea(MowingArea[playerid][x]))
            {
                counts[playerid]++;
                if(DestroyDynamicObject(MowingObject[playerid][x]))
                    MowingObject[playerid][x] = STREAMER_TAG_OBJECT: INVALID_STREAMER_ID;

                if(DestroyDynamicArea(MowingArea[playerid][x]))
                    MowingArea[playerid][x] = STREAMER_TAG_AREA: INVALID_STREAMER_ID;
                
                MowingCountingGrass[playerid] += (random(5) * counts[playerid]);
                ShowPlayerFooter(playerid, sprintf("~y~%d ~w~Rumput", MowingCountingGrass[playerid]), 1300);
            }
            else
            {
                counts[playerid] = 0;
            }
        }
    }
    return 1;
}

ptask UpdatingMowingTime[1000](playerid) 
{
    if(IsPlayerConnected(playerid) && AccountData[playerid][pSideJob] == SIDEJOB_MOWING)
    {
        if(MowingTimerSecond[playerid] > 0)
        {
            MowingTimerSecond[playerid]--;
            GameTextForPlayer(playerid, sprintf("%d", MowingTimerSecond[playerid]), 1000, 4);
        }
        else
        {
            MowingTimerSecond[playerid] = 0;
            StopMowingSidejob(playerid);
        }

        if(IsAMowingSidejobVeh(SavingVehID[playerid]))
        {
            static Float:vhealth;
            GetVehicleHealth(SavingVehID[playerid], vhealth);

            if(vhealth < 350.0)
            {
                ShowTDN(playerid, NOTIFICATION_ERROR, "Anda gagal menyelesaikan tugas mowing karena kendaraan rusak.");
                CancelMowingSideJob(playerid);
            }
        }
    }
    return 1;
}