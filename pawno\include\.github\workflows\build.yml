
name: Build

on:
  push:
    branches: [ master ]
  pull_request:
    branches: [ master ]

env:
  RELEASE_BIN: samp_bcrypt
  RELEASE_ADDS: README.md LICENSE

jobs:
  build:
    name: Build release

    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        build: [linux, windows]
        include:
          - build: linux
            os: ubuntu-latest
            rust: stable-i686
          - build: windows
            os: windows-latest
            rust: stable-i686

    steps:
    - uses: actions/checkout@v1

    - name: Install gcc-multilib
      run: |
        sudo apt update
        sudo apt install gcc-multilib -y
      if: matrix.os == 'ubuntu-latest'

    - name: Install Rust (rustup)
      run: rustup update ${{ matrix.rust }} --no-self-update && rustup default ${{ matrix.rust }}
      shell: bash

    - name: Build
      run: cargo build --verbose --release

    - name: Create artifact directory
      run: mkdir artifacts

    - name: Create archive for Linux
      run: 7z a -ttar -so -an ./target/release/lib${{ env.RELEASE_BIN }}.so ${{ env.RELEASE_ADDS }} | 7z a -si ./artifacts/${{ env.RELEASE_BIN }}-linux-x86.tar.gz
      if: matrix.os == 'ubuntu-latest'

    - name: Create archive for Windows
      run: 7z a -tzip ./artifacts/${{ env.RELEASE_BIN }}-windows-x86.zip ./target/release/${{ env.RELEASE_BIN }}.dll ${{ env.RELEASE_ADDS }}
      if: matrix.os == 'windows-latest'

    - uses: actions/upload-artifact@v1
      name: Upload archive
      with:
        name: ${{ runner.os }}
        path: artifacts/
        
        
