RemoveWashStoneBuilding(playerid)
{
    RemoveBuildingForPlayer(playerid, 3168, -27.695, 1362.550, 8.085, 0.250);
    RemoveBuildingForPlayer(playerid, 3343, -27.695, 1362.550, 8.085, 0.250);
    RemoveBuildingForPlayer(playerid, 3172, -18.703, 1387.609, 8.085, 0.250);
    RemoveBuildingForPlayer(playerid, 3345, -18.703, 1387.609, 8.085, 0.250);
    RemoveBuildingForPlayer(playerid, 3175, -1.250, 1392.900, 7.929, 0.250);
    RemoveBuildingForPlayer(playerid, 3347, -1.250, 1392.900, 7.929, 0.250);
    RemoveBuildingForPlayer(playerid, 3172, 6.492, 1379.390, 8.085, 0.250);
    RemoveBuildingForPlayer(playerid, 3345, 6.492, 1379.390, 8.085, 0.250);
    RemoveBuildingForPlayer(playerid, 3168, 6.445, 1344.060, 8.085, 0.250);
    RemoveBuildingForPlayer(playerid, 3343, 6.445, 1344.060, 8.085, 0.250);
    RemoveBuildingForPlayer(playerid, 3172, 23.125, 1342.989, 8.085, 0.250);
    RemoveBuildingForPlayer(playerid, 3345, 23.125, 1342.989, 8.085, 0.250);
    RemoveBuildingForPlayer(playerid, 3175, 27.265, 1360.160, 7.929, 0.250);
    RemoveBuildingForPlayer(playerid, 3347, 27.265, 1360.160, 7.929, 0.250);
    RemoveBuildingForPlayer(playerid, 1697, 37.992, 1358.880, 9.765, 0.250);
    RemoveBuildingForPlayer(playerid, 1697, 37.726, 1370.150, 9.765, 0.250);
    RemoveBuildingForPlayer(playerid, 1697, 37.179, 1380.979, 9.765, 0.250);
    RemoveBuildingForPlayer(playerid, 3175, -22.078, 1346.459, 7.929, 0.250);
    RemoveBuildingForPlayer(playerid, 3347, -22.078, 1346.459, 7.929, 0.250);
}

CreateWashStoneExt()
{
    CreateDynamicObject(8210, 7.655217, 1400.091308, 9.437227, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(987, -31.950077, 1348.382812, 8.550035, 0.000000, 0.000000, -90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3623, 1.027981, 1377.908935, 12.561871, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18245, 4.426001, 1385.921508, 13.771845, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3623, -13.062032, 1377.898925, 12.571871, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3623, -0.062033, 1342.548583, 12.471872, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(987, -31.950077, 1336.385742, 8.660040, 0.000000, 0.000000, -90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8210, -4.424790, 1324.792602, 9.437227, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(987, -31.950077, 1388.382812, 8.060038, 0.000000, 0.000000, -90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(987, -31.950077, 1354.382812, 8.130039, 0.000000, 0.000000, -90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8210, -3.894786, 1400.102172, 9.437227, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(987, -31.950077, 1400.385742, 8.160038, 0.000000, 0.000000, -90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8210, 34.965206, 1372.521606, 9.427227, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3214, 29.386344, 1349.055908, 16.771875, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16071, 29.418006, 1363.299438, 11.471886, 0.000000, 0.000000, 300.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16076, 21.374845, 1387.375732, 8.271851, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8210, 7.475202, 1324.792602, 9.437227, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8210, 34.975204, 1352.292480, 9.437227, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16304, 5.136405, 1366.994384, 6.771874, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16304, -0.263590, 1352.197998, 6.671875, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16304, -22.189062, 1337.741577, 10.771877, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(987, -31.950077, 1379.382812, 8.060037, 0.000000, 0.000000, -90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16304, 5.136405, 1354.797363, 7.371874, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16304, -3.663589, 1351.398193, 6.371875, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16304, -7.963590, 1352.197998, 6.671875, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16304, -11.163600, 1351.698120, 6.471876, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16304, -0.263590, 1368.197753, 6.471876, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16304, -3.663589, 1368.297729, 6.371875, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16304, -7.963590, 1368.297729, 6.471876, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16304, -11.163600, 1367.797851, 6.471876, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11280, -42.091239, 1356.218017, 9.496397, 2.000000, 0.000000, 106.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11280, -48.591140, 1363.716430, 9.496397, -1.000000, 0.000000, 261.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19975, -52.234767, 1348.041381, 9.721052, 0.000000, 0.000000, -39.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19975, -54.346069, 1369.559692, 9.184807, 0.000000, 0.000000, 235.000000, 0, 0, -1, 200.00, 200.00); 
}