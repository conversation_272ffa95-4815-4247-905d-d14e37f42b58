new Text:NewspaperTD[20];

CreateNewspaperTD()
{
    NewspaperTD[0] = TextDrawCreate(316.000000, 154.000000, "_");
	TextDrawFont(NewspaperTD[0], 1);
	TextDrawLetterSize(NewspaperTD[0], 0.600000, 20.800003);
	TextDrawTextSize(NewspaperTD[0], 298.500000, 275.000000);
	TextDrawSetOutline(NewspaperTD[0], 1);
	TextDrawSetShadow(NewspaperTD[0], 0);
	TextDrawAlignment(NewspaperTD[0], 2);
	TextDrawColor(NewspaperTD[0], -1);
	TextDrawBackgroundColor(NewspaperTD[0], 255);
	TextDrawBoxColor(NewspaperTD[0], -1094795521);
	TextDrawUseBox(NewspaperTD[0], 1);
	TextDrawSetProportional(NewspaperTD[0], 1);
	TextDrawSetSelectable(NewspaperTD[0], 0);

	NewspaperTD[1] = TextDrawCreate(316.000000, 157.000000, "Arivena Times");
	TextDrawFont(NewspaperTD[1], 0);
	TextDrawLetterSize(NewspaperTD[1], 0.595833, 2.599998);
	TextDrawTextSize(NewspaperTD[1], 400.000000, 127.000000);
	TextDrawSetOutline(NewspaperTD[1], 0);
	TextDrawSetShadow(NewspaperTD[1], 0);
	TextDrawAlignment(NewspaperTD[1], 2);
	TextDrawColor(NewspaperTD[1], 255);
	TextDrawBackgroundColor(NewspaperTD[1], 255);
	TextDrawBoxColor(NewspaperTD[1], 50);
	TextDrawUseBox(NewspaperTD[1], 0);
	TextDrawSetProportional(NewspaperTD[1], 1);
	TextDrawSetSelectable(NewspaperTD[1], 0);

	NewspaperTD[2] = TextDrawCreate(316.000000, 192.000000, "_");
	TextDrawFont(NewspaperTD[2], 1);
	TextDrawLetterSize(NewspaperTD[2], 0.600000, -0.199997);
	TextDrawTextSize(NewspaperTD[2], 298.500000, 275.000000);
	TextDrawSetOutline(NewspaperTD[2], 1);
	TextDrawSetShadow(NewspaperTD[2], 0);
	TextDrawAlignment(NewspaperTD[2], 2);
	TextDrawColor(NewspaperTD[2], 255);
	TextDrawBackgroundColor(NewspaperTD[2], 255);
	TextDrawBoxColor(NewspaperTD[2], 255);
	TextDrawUseBox(NewspaperTD[2], 1);
	TextDrawSetProportional(NewspaperTD[2], 1);
	TextDrawSetSelectable(NewspaperTD[2], 0);

	NewspaperTD[3] = TextDrawCreate(316.000000, 204.000000, "_");
	TextDrawFont(NewspaperTD[3], 1);
	TextDrawLetterSize(NewspaperTD[3], 0.600000, -0.199997);
	TextDrawTextSize(NewspaperTD[3], 298.500000, 275.000000);
	TextDrawSetOutline(NewspaperTD[3], 1);
	TextDrawSetShadow(NewspaperTD[3], 0);
	TextDrawAlignment(NewspaperTD[3], 2);
	TextDrawColor(NewspaperTD[3], 255);
	TextDrawBackgroundColor(NewspaperTD[3], 255);
	TextDrawBoxColor(NewspaperTD[3], 255);
	TextDrawUseBox(NewspaperTD[3], 1);
	TextDrawSetProportional(NewspaperTD[3], 1);
	TextDrawSetSelectable(NewspaperTD[3], 0);

	NewspaperTD[4] = TextDrawCreate(316.000000, 191.000000, "Kota Arivena");
	TextDrawFont(NewspaperTD[4], 1);
	TextDrawLetterSize(NewspaperTD[4], 0.162500, 1.150000);
	TextDrawTextSize(NewspaperTD[4], 400.000000, 127.000000);
	TextDrawSetOutline(NewspaperTD[4], 0);
	TextDrawSetShadow(NewspaperTD[4], 0);
	TextDrawAlignment(NewspaperTD[4], 2);
	TextDrawColor(NewspaperTD[4], 255);
	TextDrawBackgroundColor(NewspaperTD[4], 255);
	TextDrawBoxColor(NewspaperTD[4], 50);
	TextDrawUseBox(NewspaperTD[4], 0);
	TextDrawSetProportional(NewspaperTD[4], 1);
	TextDrawSetSelectable(NewspaperTD[4], 0);

	NewspaperTD[5] = TextDrawCreate(211.000000, 161.000000, "_");
	TextDrawFont(NewspaperTD[5], 1);
	TextDrawLetterSize(NewspaperTD[5], 0.600000, -0.199997);
	TextDrawTextSize(NewspaperTD[5], 298.500000, 40.000000);
	TextDrawSetOutline(NewspaperTD[5], 1);
	TextDrawSetShadow(NewspaperTD[5], 0);
	TextDrawAlignment(NewspaperTD[5], 2);
	TextDrawColor(NewspaperTD[5], 255);
	TextDrawBackgroundColor(NewspaperTD[5], 255);
	TextDrawBoxColor(NewspaperTD[5], 255);
	TextDrawUseBox(NewspaperTD[5], 1);
	TextDrawSetProportional(NewspaperTD[5], 1);
	TextDrawSetSelectable(NewspaperTD[5], 0);

	NewspaperTD[6] = TextDrawCreate(211.000000, 184.000000, "_");
	TextDrawFont(NewspaperTD[6], 1);
	TextDrawLetterSize(NewspaperTD[6], 0.600000, -0.199997);
	TextDrawTextSize(NewspaperTD[6], 298.500000, 40.000000);
	TextDrawSetOutline(NewspaperTD[6], 1);
	TextDrawSetShadow(NewspaperTD[6], 0);
	TextDrawAlignment(NewspaperTD[6], 2);
	TextDrawColor(NewspaperTD[6], 255);
	TextDrawBackgroundColor(NewspaperTD[6], 255);
	TextDrawBoxColor(NewspaperTD[6], 255);
	TextDrawUseBox(NewspaperTD[6], 1);
	TextDrawSetProportional(NewspaperTD[6], 1);
	TextDrawSetSelectable(NewspaperTD[6], 0);

	NewspaperTD[7] = TextDrawCreate(211.000000, 161.000000, "'All the News That's Fit to Print'");
	TextDrawFont(NewspaperTD[7], 1);
	TextDrawLetterSize(NewspaperTD[7], 0.120833, 1.150000);
	TextDrawTextSize(NewspaperTD[7], 400.000000, 39.500000);
	TextDrawSetOutline(NewspaperTD[7], 0);
	TextDrawSetShadow(NewspaperTD[7], 0);
	TextDrawAlignment(NewspaperTD[7], 2);
	TextDrawColor(NewspaperTD[7], 255);
	TextDrawBackgroundColor(NewspaperTD[7], 255);
	TextDrawBoxColor(NewspaperTD[7], 50);
	TextDrawUseBox(NewspaperTD[7], 0);
	TextDrawSetProportional(NewspaperTD[7], 1);
	TextDrawSetSelectable(NewspaperTD[7], 0);

	NewspaperTD[8] = TextDrawCreate(190.500000, 162.000000, "_");
	TextDrawFont(NewspaperTD[8], 1);
	TextDrawLetterSize(NewspaperTD[8], 0.595833, 2.150003);
	TextDrawTextSize(NewspaperTD[8], 298.000000, -1.500000);
	TextDrawSetOutline(NewspaperTD[8], 1);
	TextDrawSetShadow(NewspaperTD[8], 0);
	TextDrawAlignment(NewspaperTD[8], 2);
	TextDrawColor(NewspaperTD[8], 255);
	TextDrawBackgroundColor(NewspaperTD[8], 255);
	TextDrawBoxColor(NewspaperTD[8], 255);
	TextDrawUseBox(NewspaperTD[8], 1);
	TextDrawSetProportional(NewspaperTD[8], 1);
	TextDrawSetSelectable(NewspaperTD[8], 0);

	NewspaperTD[9] = TextDrawCreate(231.899993, 162.000000, "_");
	TextDrawFont(NewspaperTD[9], 1);
	TextDrawLetterSize(NewspaperTD[9], 0.595833, 2.150003);
	TextDrawTextSize(NewspaperTD[9], 298.000000, -1.500000);
	TextDrawSetOutline(NewspaperTD[9], 1);
	TextDrawSetShadow(NewspaperTD[9], 0);
	TextDrawAlignment(NewspaperTD[9], 2);
	TextDrawColor(NewspaperTD[9], 255);
	TextDrawBackgroundColor(NewspaperTD[9], 255);
	TextDrawBoxColor(NewspaperTD[9], 255);
	TextDrawUseBox(NewspaperTD[9], 1);
	TextDrawSetProportional(NewspaperTD[9], 1);
	TextDrawSetSelectable(NewspaperTD[9], 0);

	NewspaperTD[10] = TextDrawCreate(422.000000, 158.000000, "LATE CITY EDITION");
	TextDrawFont(NewspaperTD[10], 1);
	TextDrawLetterSize(NewspaperTD[10], 0.162500, 0.900000);
	TextDrawTextSize(NewspaperTD[10], 400.000000, 127.000000);
	TextDrawSetOutline(NewspaperTD[10], 0);
	TextDrawSetShadow(NewspaperTD[10], 0);
	TextDrawAlignment(NewspaperTD[10], 2);
	TextDrawColor(NewspaperTD[10], 255);
	TextDrawBackgroundColor(NewspaperTD[10], 255);
	TextDrawBoxColor(NewspaperTD[10], 50);
	TextDrawUseBox(NewspaperTD[10], 0);
	TextDrawSetProportional(NewspaperTD[10], 1);
	TextDrawSetSelectable(NewspaperTD[10], 0);

	NewspaperTD[11] = TextDrawCreate(316.000000, 208.000000, "Perubahan Siklus Perekonomian Kota Arivena Belakangan ini");
	TextDrawFont(NewspaperTD[11], 1);
	TextDrawLetterSize(NewspaperTD[11], 0.241666, 2.199999);
	TextDrawTextSize(NewspaperTD[11], 400.000000, 242.000000);
	TextDrawSetOutline(NewspaperTD[11], 0);
	TextDrawSetShadow(NewspaperTD[11], 0);
	TextDrawAlignment(NewspaperTD[11], 2);
	TextDrawColor(NewspaperTD[11], 255);
	TextDrawBackgroundColor(NewspaperTD[11], 255);
	TextDrawBoxColor(NewspaperTD[11], 50);
	TextDrawUseBox(NewspaperTD[11], 0);
	TextDrawSetProportional(NewspaperTD[11], 1);
	TextDrawSetSelectable(NewspaperTD[11], 0);

	NewspaperTD[12] = TextDrawCreate(185.000000, 240.000000, "Pemerintah mengungumkan perubahan ekonomi yang terjadi dalam beberapa waktu terakhir ini, dimulai dari harga sebagai berikut;");
	TextDrawFont(NewspaperTD[12], 1);
	TextDrawLetterSize(NewspaperTD[12], 0.162000, 1.149999);
	TextDrawTextSize(NewspaperTD[12], 237.500000, 127.000000);
	TextDrawSetOutline(NewspaperTD[12], 0);
	TextDrawSetShadow(NewspaperTD[12], 0);
	TextDrawAlignment(NewspaperTD[12], 1);
	TextDrawColor(NewspaperTD[12], 255);
	TextDrawBackgroundColor(NewspaperTD[12], 255);
	TextDrawBoxColor(NewspaperTD[12], 50);
	TextDrawUseBox(NewspaperTD[12], 0);
	TextDrawSetProportional(NewspaperTD[12], 1);
	TextDrawSetSelectable(NewspaperTD[12], 0);

	NewspaperTD[13] = TextDrawCreate(248.000000, 239.000000, "Ikan, dahulu $30.00 sekarang menjadi $10.00, sedangkan Ayam Kemas dahulu $50.00 sekarang $15.00");
	TextDrawFont(NewspaperTD[13], 1);
	TextDrawLetterSize(NewspaperTD[13], 0.145, 1.050);
	TextDrawTextSize(NewspaperTD[13], 345.000000, 127.000000);
	TextDrawSetOutline(NewspaperTD[13], 0);
	TextDrawSetShadow(NewspaperTD[13], 0);
	TextDrawAlignment(NewspaperTD[13], 1);
	TextDrawColor(NewspaperTD[13], 255);
	TextDrawBackgroundColor(NewspaperTD[13], 255);
	TextDrawBoxColor(NewspaperTD[13], 50);
	TextDrawUseBox(NewspaperTD[13], 0);
	TextDrawSetProportional(NewspaperTD[13], 1);
	TextDrawSetSelectable(NewspaperTD[13], 0);

	NewspaperTD[14] = TextDrawCreate(445.000000, 333.000000, "ld_beat:cross");
	TextDrawFont(NewspaperTD[14], 4);
	TextDrawLetterSize(NewspaperTD[14], 0.600000, 2.000000);
	TextDrawTextSize(NewspaperTD[14], 17.000000, 17.000000);
	TextDrawSetOutline(NewspaperTD[14], 1);
	TextDrawSetShadow(NewspaperTD[14], 0);
	TextDrawAlignment(NewspaperTD[14], 1);
	TextDrawColor(NewspaperTD[14], -16776961);
	TextDrawBackgroundColor(NewspaperTD[14], 255);
	TextDrawBoxColor(NewspaperTD[14], 50);
	TextDrawUseBox(NewspaperTD[14], 1);
	TextDrawSetProportional(NewspaperTD[14], 1);
	TextDrawSetSelectable(NewspaperTD[14], 1);

	NewspaperTD[15] = TextDrawCreate(242.500000, 236.000000, "_");
	TextDrawFont(NewspaperTD[15], 1);
	TextDrawLetterSize(NewspaperTD[15], 0.595833, 10.000000);
	TextDrawTextSize(NewspaperTD[15], 298.000000, -1.899999);
	TextDrawSetOutline(NewspaperTD[15], 1);
	TextDrawSetShadow(NewspaperTD[15], 0);
	TextDrawAlignment(NewspaperTD[15], 2);
	TextDrawColor(NewspaperTD[15], 255);
	TextDrawBackgroundColor(NewspaperTD[15], 255);
	TextDrawBoxColor(NewspaperTD[15], 255);
	TextDrawUseBox(NewspaperTD[15], 1);
	TextDrawSetProportional(NewspaperTD[15], 1);
	TextDrawSetSelectable(NewspaperTD[15], 0);

	NewspaperTD[16] = TextDrawCreate(346.500000, 236.000000, "_");
	TextDrawFont(NewspaperTD[16], 1);
	TextDrawLetterSize(NewspaperTD[16], 0.595833, 10.000000);
	TextDrawTextSize(NewspaperTD[16], 298.000000, -1.899999);
	TextDrawSetOutline(NewspaperTD[16], 1);
	TextDrawSetShadow(NewspaperTD[16], 0);
	TextDrawAlignment(NewspaperTD[16], 2);
	TextDrawColor(NewspaperTD[16], 255);
	TextDrawBackgroundColor(NewspaperTD[16], 255);
	TextDrawBoxColor(NewspaperTD[16], 255);
	TextDrawUseBox(NewspaperTD[16], 1);
	TextDrawSetProportional(NewspaperTD[16], 1);
	TextDrawSetSelectable(NewspaperTD[16], 0);

	NewspaperTD[17] = TextDrawCreate(248.000000, 273.000000, "Harga Board dahulu $15.00 sekarang menjadi $45.00 dan Clothes dahulu $75.00 sekarang $25.00 serta Minyak dari $45.00 menjadi $65.00");
	TextDrawFont(NewspaperTD[17], 1);
	TextDrawLetterSize(NewspaperTD[17], 0.145, 1.050);
	TextDrawTextSize(NewspaperTD[17], 345.000000, 127.000000);
	TextDrawSetOutline(NewspaperTD[17], 0);
	TextDrawSetShadow(NewspaperTD[17], 0);
	TextDrawAlignment(NewspaperTD[17], 1);
	TextDrawColor(NewspaperTD[17], 255);
	TextDrawBackgroundColor(NewspaperTD[17], 255);
	TextDrawBoxColor(NewspaperTD[17], 50);
	TextDrawUseBox(NewspaperTD[17], 0);
	TextDrawSetProportional(NewspaperTD[17], 1);
	TextDrawSetSelectable(NewspaperTD[17], 0);

	NewspaperTD[18] = TextDrawCreate(352.000000, 239.000000, "Silver Block dari $45.00 jadi $25.00 untuk Gold Ingot dari $25.00 menjadi $65.00 serta Gem Alloy dari $25.00 sekarang $66.00");
	TextDrawFont(NewspaperTD[18], 1);
	TextDrawLetterSize(NewspaperTD[18], 0.145, 1.050);
	TextDrawTextSize(NewspaperTD[18], 451.000000, 126.500000);
	TextDrawSetOutline(NewspaperTD[18], 0);
	TextDrawSetShadow(NewspaperTD[18], 0);
	TextDrawAlignment(NewspaperTD[18], 1);
	TextDrawColor(NewspaperTD[18], 255);
	TextDrawBackgroundColor(NewspaperTD[18], 255);
	TextDrawBoxColor(NewspaperTD[18], 50);
	TextDrawUseBox(NewspaperTD[18], 0);
	TextDrawSetProportional(NewspaperTD[18], 1);
	TextDrawSetSelectable(NewspaperTD[18], 0);

	NewspaperTD[19] = TextDrawCreate(352.000000, 281.000000, "Sedangkan untuk barang hasil tambang Platinum Elixir dari $75.00 sekarang $85.00");
	TextDrawFont(NewspaperTD[19], 1);
	TextDrawLetterSize(NewspaperTD[19], 0.145, 1.050);
	TextDrawTextSize(NewspaperTD[19], 451.000000, 126.500000);
	TextDrawSetOutline(NewspaperTD[19], 0);
	TextDrawSetShadow(NewspaperTD[19], 0);
	TextDrawAlignment(NewspaperTD[19], 1);
	TextDrawColor(NewspaperTD[19], 255);
	TextDrawBackgroundColor(NewspaperTD[19], 255);
	TextDrawBoxColor(NewspaperTD[19], 50);
	TextDrawUseBox(NewspaperTD[19], 0);
	TextDrawSetProportional(NewspaperTD[19], 1);
	TextDrawSetSelectable(NewspaperTD[19], 0);
}

ShowKoranTD(playerid)
{
    static string[512];
    format(string, sizeof(string), "Harga Ayam Kemas dahulu $%s sekarang $%s dan Fermented Milk dari $%s menjadi $%s", FormatMoney(OldChickenSalary), FormatMoney(ChickenSalary), FormatMoney(OldMilkSalary), FormatMoney(MilkSalary));
    TextDrawSetString(NewspaperTD[13], string);

    format(string, sizeof(string), "Harga Board dahulu $%s sekarang menjadi $%s dan Clothes dahulu $%s sekarang $%s serta Minyak dari $%s menjadi $%s", FormatMoney(OldBoardSalary), FormatMoney(BoardSalary), FormatMoney(OldClothesSalary), FormatMoney(ClothesSalary), FormatMoney(OldOilSalary), FormatMoney(OilSalary));
    TextDrawSetString(NewspaperTD[17], string);

    format(string, sizeof(string), "Silver Block dari $%s jadi $%s untuk Gold Ingot dari $%s menjadi $%s serta Gem Alloy dari $%s sekarang $%s", FormatMoney(OldSilverSalary), FormatMoney(SilverSalary), FormatMoney(OldGoldSalary), FormatMoney(GoldSalary), FormatMoney(OldGemSalary), FormatMoney(GemSalary));
    TextDrawSetString(NewspaperTD[18], string);

    format(string, sizeof(string), "Sedangkan untuk barang hasil tambang Platinum Elixir dari $%s sekarang $%s dan Milk Cream dari $%s menjadi $%s", FormatMoney(OldElixirSalary), FormatMoney(ElixirSalary), FormatMoney(OldMilkCreamSalary), FormatMoney(MilkCreamSalary));
    TextDrawSetString(NewspaperTD[19], string);

    for(new x; x < 20; x++)
    {
        TextDrawShowForPlayer(playerid, NewspaperTD[x]);
    }
}

HideKoranTD(playerid)
{
    for(new x; x < 20; x++)
    {
        TextDrawHideForPlayer(playerid, NewspaperTD[x]);
    }
}