CreatePomBensinExt()
{
	//Idlewood Pom
	/*
    CreateDynamicObject(970, 1940.86621, -1779.19507, 12.91650,   0.00000, 0.00000, 90.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, 1940.86621, -1775.03516, 12.91650,   0.00000, 0.00000, 90.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, 1940.86621, -1770.87512, 12.91650,   0.00000, 0.00000, 90.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, 1940.86621, -1766.71509, 12.91650,   0.00000, 0.00000, 90.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, 1942.44617, -1766.71509, 12.91650,   0.00000, 0.00000, 90.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, 1942.44617, -1770.87512, 12.91650,   0.00000, 0.00000, 90.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, 1942.44617, -1775.03516, 12.91650,   0.00000, 0.00000, 90.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, 1942.44617, -1779.19507, 12.91650,   0.00000, 0.00000, 90.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, 1941.64709, -1781.13574, 13.15740,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, 1941.64709, -1776.41565, 13.15740,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, 1941.64709, -1769.33557, 13.15740,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, 1941.64709, -1764.73560, 13.15740,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);
	*/
	
	//Flint Range Pom
	CreateDynamicObject(970, -91.49886, -1162.46313, 1.63630,   0.00000, 0.00000, 67.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, -92.78320, -1161.85803, 1.63630,   0.00000, 0.00000, 67.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, -86.14960, -1164.96851, 1.63630,   0.00000, 0.00000, 67.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, -84.86960, -1165.58850, 1.63630,   0.00000, 0.00000, 67.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, -89.44960, -1176.32849, 1.63630,   0.00000, 0.00000, 67.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, -90.76960, -1175.88855, 1.63630,   0.00000, 0.00000, 67.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, -96.20960, -1173.46851, 1.63630,   0.00000, 0.00000, 67.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, -97.52960, -1173.02844, 1.63630,   0.00000, 0.00000, 67.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, -96.06990, -1171.30798, 2.00810,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, -97.62050, -1175.04016, 2.00810,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, -90.81930, -1177.79785, 1.72810,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, -89.26190, -1174.05945, 1.72810,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, -86.32880, -1167.18030, 1.94810,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, -84.66540, -1163.27429, 1.94810,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, -91.42371, -1160.38647, 1.96650,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, -93.10860, -1164.36157, 1.96650,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);

	
	//Dilimore Pom
	CreateDynamicObject(970, 654.71649, -559.80042, 15.87980,   0.00000, 0.00000, 90.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, 656.51648, -559.80042, 15.87980,   0.00000, 0.00000, 90.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, 655.63251, -557.79340, 16.04080,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, 656.51819, -570.21399, 15.85990,   0.00000, 0.00000, 90.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(970, 654.73822, -570.21399, 15.85990,   0.00000, 0.00000, 90.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, 655.59442, -572.25928, 16.04030,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);
	
	//Vinewood Pom
	CreateDynamicObject(1686, 1006.02747, -936.66614, 41.32340,   0.00000, 0.00000, -83.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1686, 1008.71442, -936.24432, 41.32340,   0.00000, 0.00000, -83.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1686, 1001.73126, -937.26318, 41.32340,   0.00000, 0.00000, -83.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1686, 998.82202, -937.69202, 41.32340,   0.00000, 0.00000, -83.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(984, 1003.91779, -937.62097, 41.60440,   0.00000, 0.00000, -82.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(984, 1003.93445, -936.01080, 41.60440,   0.00000, 0.00000, -82.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, 1011.91199, -935.69238, 41.84460,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, 995.76361, -938.00739, 41.84460,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);

	//Montgomery Pom
	CreateDynamicObject(983, 1379.72791, 460.06619, 19.34420,   0.00000, 0.00000, 66.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(983, 1383.53882, 458.37027, 19.34420,   0.00000, 0.00000, 66.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(983, 1384.02710, 459.42404, 19.34420,   0.00000, 0.00000, 66.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(983, 1380.22229, 461.13635, 19.34420,   0.00000, 0.00000, 66.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, 1376.89697, 462.00653, 19.56420,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);
	CreateDynamicObject(1215, 1386.82275, 457.47653, 19.56420,   0.00000, 0.00000, 0.00000, 0, 0, -1, 200.00, 200.00, -1);

	//my pombensin
	CreateDynamicObject(1676, 73.848045, 1219.194213, 19.635879, 0.000007, 0.000001, 74.400016, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(1676, 68.001571, 1220.826538, 19.635879, 0.000007, 0.000001, 74.400016, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(970, 74.359916, 1218.068359, 18.650304, 0.000000, 0.000000, 74.200050, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(970, 72.666458, 1218.547485, 18.650304, 0.000000, 0.000000, 74.900039, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(970, 68.543876, 1219.659790, 18.650304, 0.000000, 0.000000, 74.900039, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(970, 66.786788, 1220.133300, 18.650304, 0.000000, 0.000000, 74.900039, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(19121, 68.325569, 1222.180541, 18.677885, 0.000000, 0.000000, -17.599988, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(19121, 74.211647, 1220.627197, 18.677885, 0.000000, 0.000000, -17.599988, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(1244, -737.080200, 2744.822998, 46.964035, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(970, -737.205017, 2744.162353, 46.734889, 0.000000, 0.000000, -1.200000, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(970, -737.179321, 2745.412841, 46.734889, 0.000000, 0.000000, -1.200000, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(19121, -739.120117, 2744.785400, 46.757610, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(19121, -735.230163, 2744.785400, 46.757610, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(1244, -2026.582275, 156.730911, 28.789745, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(970, -2025.914672, 156.743423, 28.567230, 0.000000, 0.000000, 89.700004, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(970, -2027.224609, 156.750320, 28.567230, 0.000000, 0.000000, 89.700004, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(19121, -2026.592407, 160.233993, 28.609462, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
	CreateDynamicObject(19121, -2026.592407, 153.284072, 28.609462, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
}