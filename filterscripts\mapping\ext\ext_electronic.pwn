CreateElectroExt()
{
    new STREAMER_TAG_OBJECT:electroxt;
    electroxt = CreateDynamicObject(19866, 442.860565, -1806.220458, 5.347147, -89.999992, 353.246124, 83.246025, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(19866, 442.860565, -1809.360717, 5.347147, -89.999992, 353.246124, 83.246025, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(19866, 442.860565, -1806.809814, 7.927145, -0.000014, 450.000000, -179.999801, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(19866, 442.860565, -1804.409790, 5.347147, -89.999992, 353.246124, 83.246025, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(19866, 442.860565, -1811.240966, 5.347147, -89.999992, 353.246124, 83.246025, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(19866, 442.860565, -1808.843139, 7.927145, -0.000014, 450.000000, -179.999801, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(19866, 442.860565, -1806.809814, 5.037133, -0.000014, 450.000000, -179.999801, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(19866, 442.860565, -1808.843139, 5.037133, -0.000014, 450.000000, -179.999801, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(975, 442.313446, -1811.018676, 3.517139, -0.000022, 630.000000, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 19325, "lsmall_shops", "lsmall_window01", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 4, 19480, "signsurf", "sign", 0x00000000);
    electroxt = CreateDynamicObject(975, 442.313446, -1804.709228, 3.517139, -0.000022, 630.000000, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 19325, "lsmall_shops", "lsmall_window01", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 4, 19480, "signsurf", "sign", 0x00000000);
    electroxt = CreateDynamicObject(18762, 442.480804, -1803.809448, 5.687147, -0.000022, 0.000014, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    electroxt = CreateDynamicObject(18762, 442.480804, -1811.819946, 5.687147, -0.000022, 0.000014, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    electroxt = CreateDynamicObject(18762, 445.460784, -1799.957641, 8.507139, 0.000014, 90.000030, -0.000014, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    electroxt = CreateDynamicObject(18880, 442.391326, -1812.428955, 8.027147, 0.000022, 179.999984, 90.000022, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(18880, 442.391326, -1803.208862, 8.027147, 0.000022, 179.999984, 90.000022, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(975, 442.358367, -1801.574462, 3.587140, -0.000022, 270.000000, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 3629, "arprtxxref_las", "grn_window2_16", 0x90FFFFFF);
    SetDynamicObjectMaterial(electroxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 4, 19480, "signsurf", "sign", 0x00000000);
    electroxt = CreateDynamicObject(975, 442.358367, -1814.224853, 3.587140, -0.000022, 270.000000, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 3629, "arprtxxref_las", "grn_window2_16", 0x90FFFFFF);
    SetDynamicObjectMaterial(electroxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 4, 19480, "signsurf", "sign", 0x00000000);
    electroxt = CreateDynamicObject(975, 444.028656, -1799.904052, 3.587140, 0.000029, 270.000000, -0.000060, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 3629, "arprtxxref_las", "grn_window2_16", 0x90FFFFFF);
    SetDynamicObjectMaterial(electroxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 4, 19480, "signsurf", "sign", 0x00000000);
    electroxt = CreateDynamicObject(975, 444.028656, -1815.734985, 3.587140, 0.000029, 270.000000, -0.000060, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 3629, "arprtxxref_las", "grn_window2_16", 0x90FFFFFF);
    SetDynamicObjectMaterial(electroxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 4, 19480, "signsurf", "sign", 0x00000000);
    electroxt = CreateDynamicObject(975, 442.358367, -1801.574462, 0.577130, -0.000022, 270.000000, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 3629, "arprtxxref_las", "grn_window2_16", 0x90FFFFFF);
    SetDynamicObjectMaterial(electroxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 4, 19480, "signsurf", "sign", 0x00000000);
    electroxt = CreateDynamicObject(975, 442.358367, -1814.224853, 0.577130, -0.000022, 270.000000, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 3629, "arprtxxref_las", "grn_window2_16", 0x90FFFFFF);
    SetDynamicObjectMaterial(electroxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 4, 19480, "signsurf", "sign", 0x00000000);
    electroxt = CreateDynamicObject(975, 444.028656, -1799.904052, 0.577130, 0.000045, 270.000000, -0.000105, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 3629, "arprtxxref_las", "grn_window2_16", 0x90FFFFFF);
    SetDynamicObjectMaterial(electroxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 4, 19480, "signsurf", "sign", 0x00000000);
    electroxt = CreateDynamicObject(975, 444.028656, -1815.734985, 0.577130, 0.000045, 270.000000, -0.000105, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 3629, "arprtxxref_las", "grn_window2_16", 0x90FFFFFF);
    SetDynamicObjectMaterial(electroxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 4, 19480, "signsurf", "sign", 0x00000000);
    electroxt = CreateDynamicObject(975, 442.358367, -1801.574462, 2.967130, -0.000022, 270.000000, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 19480, "signsurf", "sign", 0x90FFFFFF);
    SetDynamicObjectMaterial(electroxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 4, 19480, "signsurf", "sign", 0x00000000);
    electroxt = CreateDynamicObject(975, 442.358367, -1814.224853, 2.967130, -0.000022, 270.000000, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 19480, "signsurf", "sign", 0x90FFFFFF);
    SetDynamicObjectMaterial(electroxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 4, 19480, "signsurf", "sign", 0x00000000);
    electroxt = CreateDynamicObject(975, 444.028656, -1799.904052, 2.967130, 0.000045, 270.000000, -0.000105, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 19480, "signsurf", "sign", 0x90FFFFFF);
    SetDynamicObjectMaterial(electroxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 4, 19480, "signsurf", "sign", 0x00000000);
    electroxt = CreateDynamicObject(975, 444.028656, -1815.734985, 2.967130, 0.000045, 270.000000, -0.000105, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 19480, "signsurf", "sign", 0x90FFFFFF);
    SetDynamicObjectMaterial(electroxt, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 4, 19480, "signsurf", "sign", 0x00000000);
    electroxt = CreateDynamicObject(18762, 442.480804, -1801.958984, 8.507139, -0.000022, 90.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    electroxt = CreateDynamicObject(18762, 442.480804, -1813.668457, 8.507139, -0.000022, 90.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    electroxt = CreateDynamicObject(18762, 445.470794, -1815.668457, 8.507139, 0.000014, 90.000030, -0.000014, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    electroxt = CreateDynamicObject(18762, 442.480804, -1806.958984, 8.507139, -0.000022, 90.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    electroxt = CreateDynamicObject(18762, 442.480804, -1811.958984, 8.507139, -0.000022, 90.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    electroxt = CreateDynamicObject(19445, 450.342376, -1799.890747, 6.277155, -0.000022, 0.000014, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 9514, "711_sfw", "shingles2", 0x00000000);
    electroxt = CreateDynamicObject(19445, 450.342376, -1815.730957, 6.277155, -0.000022, 0.000014, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 9514, "711_sfw", "shingles2", 0x00000000);
    electroxt = CreateDynamicObject(18762, 450.470794, -1815.668457, 8.507139, 0.000014, 90.000030, -0.000014, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    electroxt = CreateDynamicObject(18762, 452.831024, -1815.668457, 8.507139, 0.000014, 90.000030, -0.000014, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    electroxt = CreateDynamicObject(19445, 455.222503, -1811.000488, 6.277155, 0.000014, 0.000022, -0.000014, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 9514, "711_sfw", "shingles2", 0x00000000);
    electroxt = CreateDynamicObject(19445, 455.223480, -1804.619384, 6.278157, 0.000014, 0.000022, -0.000014, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 9514, "711_sfw", "shingles2", 0x00000000);
    electroxt = CreateDynamicObject(18762, 455.191864, -1813.668945, 8.507139, 0.000022, 89.999992, 89.999870, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    electroxt = CreateDynamicObject(18762, 455.191864, -1808.688842, 8.507139, 0.000022, 89.999992, 89.999870, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    electroxt = CreateDynamicObject(18762, 455.191864, -1803.758911, 8.507139, 0.000022, 89.999992, 89.999870, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    electroxt = CreateDynamicObject(18762, 455.191864, -1802.298706, 8.507139, 0.000022, 89.999992, 89.999870, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    electroxt = CreateDynamicObject(18762, 450.460784, -1799.957641, 8.507139, 0.000014, 90.000030, -0.000014, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    electroxt = CreateDynamicObject(18762, 453.190887, -1799.957641, 8.507139, 0.000014, 90.000030, -0.000014, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18646, "matcolours", "grey-80-percent", 0x00000000);
    electroxt = CreateDynamicObject(19445, 442.434173, -1810.991210, 10.698923, 0.000014, 0.000022, -0.000014, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18237, "cuntwbtzzcs_t", "des_dinerwall", 0x00000000);
    electroxt = CreateDynamicObject(19445, 442.436126, -1804.720336, 10.698923, 0.000014, 0.000022, -0.000014, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18237, "cuntwbtzzcs_t", "des_dinerwall", 0x00000000);
    electroxt = CreateDynamicObject(19445, 447.177093, -1799.989746, 10.698923, 0.000022, -0.000014, 89.999870, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18237, "cuntwbtzzcs_t", "des_dinerwall", 0x00000000);
    electroxt = CreateDynamicObject(19445, 450.447479, -1799.991699, 10.698923, 0.000022, -0.000014, 89.999870, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18237, "cuntwbtzzcs_t", "des_dinerwall", 0x00000000);
    electroxt = CreateDynamicObject(19445, 455.345794, -1810.991210, 10.698923, 0.000022, 0.000022, -0.000037, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18237, "cuntwbtzzcs_t", "des_dinerwall", 0x00000000);
    electroxt = CreateDynamicObject(19445, 455.347747, -1804.720336, 10.698923, 0.000022, 0.000022, -0.000037, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18237, "cuntwbtzzcs_t", "des_dinerwall", 0x00000000);
    electroxt = CreateDynamicObject(19445, 447.177093, -1815.719604, 10.698923, 0.000022, -0.000022, 89.999824, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18237, "cuntwbtzzcs_t", "des_dinerwall", 0x00000000);
    electroxt = CreateDynamicObject(19445, 450.447479, -1815.721557, 10.698923, 0.000022, -0.000022, 89.999824, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18237, "cuntwbtzzcs_t", "des_dinerwall", 0x00000000);
    electroxt = CreateDynamicObject(18762, 445.460784, -1799.957641, 12.937157, 0.000022, 90.000030, -0.000037, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(18762, 442.480804, -1801.958984, 12.937157, -0.000022, 90.000030, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(18762, 442.480804, -1813.668457, 12.937157, -0.000022, 90.000030, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(18762, 445.470794, -1815.668457, 12.937157, 0.000022, 90.000030, -0.000037, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(18762, 442.480804, -1806.958984, 12.937157, -0.000022, 90.000030, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(18762, 442.480804, -1811.958984, 12.937157, -0.000022, 90.000030, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(18762, 450.470794, -1815.668457, 12.937157, 0.000022, 90.000030, -0.000037, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(18762, 452.831024, -1815.668457, 12.937157, 0.000022, 90.000030, -0.000037, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(18762, 455.191864, -1813.668945, 12.937157, 0.000022, 89.999984, 89.999824, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(18762, 455.191864, -1808.688842, 12.937157, 0.000022, 89.999984, 89.999824, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(18762, 455.191864, -1803.758911, 12.937157, 0.000022, 89.999984, 89.999824, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(18762, 455.191864, -1802.298706, 12.937157, 0.000022, 89.999984, 89.999824, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(18762, 450.460784, -1799.957641, 12.937157, 0.000022, 90.000030, -0.000037, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(18762, 453.190887, -1799.957641, 12.937157, 0.000022, 90.000030, -0.000037, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(14394, 441.010955, -1807.782470, 4.257140, 0.000022, 0.000022, -0.000037, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(19426, 441.703704, -1803.808227, 3.527139, -0.000022, 0.000014, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(19426, 441.703704, -1811.818237, 3.527139, -0.000022, 0.000014, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(19445, 444.128387, -1810.885864, 4.837141, 0.000007, 90.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 15055, "svlamid", "AH_flroortile3", 0x00000000);
    electroxt = CreateDynamicObject(19445, 444.129364, -1804.716064, 4.836140, 0.000007, 90.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 15055, "svlamid", "AH_flroortile3", 0x00000000);
    electroxt = CreateDynamicObject(14394, 446.951263, -1811.653686, 4.067140, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    electroxt = CreateDynamicObject(14394, 446.951263, -1803.833496, 4.068140, 0.000007, -0.000022, 179.999771, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    electroxt = CreateDynamicObject(19445, 448.198822, -1804.716064, 4.516137, 0.000007, 90.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 15055, "svlamid", "AH_flroortile3", 0x00000000);
    electroxt = CreateDynamicObject(19445, 448.198822, -1810.856567, 4.515135, 0.000007, 90.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 15055, "svlamid", "AH_flroortile3", 0x00000000);
    electroxt = CreateDynamicObject(19445, 451.698822, -1804.716064, 4.516137, 0.000007, 90.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 15055, "svlamid", "AH_flroortile3", 0x00000000);
    electroxt = CreateDynamicObject(19445, 451.698822, -1810.856567, 4.515135, 0.000007, 90.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 15055, "svlamid", "AH_flroortile3", 0x00000000);
    electroxt = CreateDynamicObject(19445, 453.349090, -1804.716064, 4.515135, 0.000014, 90.000022, -0.000014, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 15055, "svlamid", "AH_flroortile3", 0x00000000);
    electroxt = CreateDynamicObject(19445, 453.349090, -1810.856567, 4.514135, 0.000014, 90.000022, -0.000014, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 15055, "svlamid", "AH_flroortile3", 0x00000000);
    electroxt = CreateDynamicObject(19353, 442.388275, -1813.483520, 3.207135, 0.000007, 0.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 9514, "711_sfw", "shingles2", 0x00000000);
    electroxt = CreateDynamicObject(19353, 442.389251, -1814.193603, 3.207135, 0.000007, 0.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 9514, "711_sfw", "shingles2", 0x00000000);
    electroxt = CreateDynamicObject(19353, 443.910858, -1815.730957, 3.207153, -0.000022, 0.000014, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 9514, "711_sfw", "shingles2", 0x00000000);
    electroxt = CreateDynamicObject(19353, 442.389251, -1801.534423, 3.207135, 0.000007, 0.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 9514, "711_sfw", "shingles2", 0x00000000);
    electroxt = CreateDynamicObject(19353, 442.390228, -1801.424316, 3.207135, 0.000007, 0.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 9514, "711_sfw", "shingles2", 0x00000000);
    electroxt = CreateDynamicObject(19353, 443.912078, -1799.890747, 3.217159, -0.000022, 0.000014, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 9514, "711_sfw", "shingles2", 0x00000000);
    electroxt = CreateDynamicObject(19445, 450.343353, -1815.719970, 6.277155, -0.000022, 0.000014, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 3922, "bistro", "Marble2", 0x00000000);
    electroxt = CreateDynamicObject(19445, 450.343353, -1799.901733, 6.277155, -0.000022, 0.000014, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 3922, "bistro", "Marble2", 0x00000000);
    electroxt = CreateDynamicObject(19445, 455.212493, -1810.990478, 6.277155, 0.000014, 0.000022, -0.000014, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 3922, "bistro", "Marble2", 0x00000000);
    electroxt = CreateDynamicObject(19445, 455.213470, -1804.629394, 6.278157, 0.000014, 0.000022, -0.000014, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 3922, "bistro", "Marble2", 0x00000000);
    electroxt = CreateDynamicObject(19445, 452.969696, -1804.716064, 8.905142, 0.000029, 90.000022, -0.000060, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    electroxt = CreateDynamicObject(19445, 452.968719, -1810.856567, 8.904144, 0.000029, 90.000022, -0.000060, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    electroxt = CreateDynamicObject(19445, 444.658050, -1804.716064, 8.905142, 0.000029, 90.000022, -0.000060, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    electroxt = CreateDynamicObject(19445, 444.657073, -1810.856567, 8.904144, 0.000029, 90.000022, -0.000060, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    electroxt = CreateDynamicObject(19353, 449.478973, -1814.067260, 8.904144, 0.000029, 90.000022, -0.000060, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    electroxt = CreateDynamicObject(19353, 446.847991, -1814.067260, 8.905142, 0.000029, 90.000022, -0.000060, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    electroxt = CreateDynamicObject(19353, 449.478973, -1801.524780, 8.905142, 0.000037, 90.000022, -0.000082, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    electroxt = CreateDynamicObject(19353, 446.847991, -1801.524780, 8.906145, 0.000037, 90.000022, -0.000082, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 3621, "dockcargo1_las", "dt_ceiling1", 0x00000000);
    electroxt = CreateDynamicObject(19866, 448.848480, -1812.100097, 9.072079, -0.000022, 90.000007, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    electroxt = CreateDynamicObject(19866, 448.848480, -1802.719360, 9.072079, -0.000022, 90.000007, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    electroxt = CreateDynamicObject(19866, 450.888275, -1805.949462, 9.072079, 0.000007, 90.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    electroxt = CreateDynamicObject(19866, 450.888275, -1809.870117, 9.072079, 0.000007, 90.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    electroxt = CreateDynamicObject(19866, 445.967987, -1805.949462, 9.072079, 0.000014, 90.000022, -0.000014, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    electroxt = CreateDynamicObject(19866, 445.967987, -1809.870117, 9.072079, 0.000014, 90.000022, -0.000014, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    electroxt = CreateDynamicObject(19445, 449.248870, -1810.856567, 9.264143, 0.000029, 90.000022, -0.000060, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(19445, 449.248870, -1808.166259, 9.264143, 0.000029, 90.000022, -0.000060, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(19445, 448.198944, -1808.166259, 9.264143, 0.000029, 90.000022, -0.000060, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(19325, 450.524261, -1815.628784, 5.961083, 0.000022, -0.000014, 89.999870, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(19353, 450.498748, -1815.545288, 6.002070, -0.000022, 0.000014, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    electroxt = CreateDynamicObject(19482, 450.490936, -1815.437988, 6.601076, 0.000022, -0.000029, 89.999778, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterialText(electroxt, 0, "sBox", 130, "Arial", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    electroxt = CreateDynamicObject(2662, 450.223846, -1815.432983, 6.101064, -0.000029, -0.000022, -179.999740, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterialText(electroxt, 0, "w     e     e     t", 130, "Arial", 90, 1, 0xFFFFFFFF, 0x00000000, 1);
    electroxt = CreateDynamicObject(19893, 451.740081, -1813.536987, 4.801076, -0.000007, -0.000022, -179.999816, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 1, 15042, "svsfsm", "GB_rapposter01", 0x00000000);
    electroxt = CreateDynamicObject(19893, 452.690643, -1813.536987, 4.801076, -0.000007, -0.000022, -179.999816, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 1, 15042, "svsfsm", "GB_rapposter01", 0x00000000);
    electroxt = CreateDynamicObject(2435, 450.938079, -1813.583740, 4.601074, -0.000014, -0.000022, -179.999786, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(2435, 450.018035, -1813.583740, 4.601074, -0.000014, -0.000022, -179.999786, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(2435, 449.097991, -1813.583740, 4.601074, -0.000014, -0.000022, -179.999786, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(2435, 448.177947, -1813.583740, 4.601074, -0.000014, -0.000022, -179.999786, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(1716, 449.651702, -1813.751708, 5.606022, 0.000014, 0.000029, -90.000091, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 5150, "wiresetc_las2", "ganggraf01_LA_m", 0xFFFFFFFF);
    SetDynamicObjectMaterial(electroxt, 1, -1, "none", "none", 0xFF303030);
    electroxt = CreateDynamicObject(2266, 449.336761, -1813.986694, 5.716125, 0.000050, -0.000014, -0.000433, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    electroxt = CreateDynamicObject(2266, 449.356781, -1812.985839, 5.716125, -0.000050, 0.000014, -179.999664, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    electroxt = CreateDynamicObject(19808, 449.213958, -1813.776977, 5.676041, 0.000029, 0.000000, -0.000266, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, -1, "none", "none", 0xFF404040);
    electroxt = CreateDynamicObject(19874, 449.691131, -1813.748291, 5.656024, 0.000000, -0.000029, 90.000129, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 18064, "ab_sfammuunits", "gun_blackbox", 0xFFFFFFFF);
    electroxt = CreateDynamicObject(2413, 452.535980, -1813.677734, 4.581076, -0.000007, -0.000022, -179.999816, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(19894, 452.220184, -1813.536987, 4.801076, -0.000007, -0.000022, -179.999816, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 1, 15042, "svsfsm", "GB_rapposter01", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1811.138549, 6.431085, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1811.138549, 5.131074, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1809.808593, 6.431085, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1809.808593, 5.131074, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1808.478637, 6.431085, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1808.478637, 5.131074, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1807.148681, 6.431085, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1807.148681, 5.131074, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1805.818725, 6.431085, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1805.818725, 5.131074, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1804.488769, 6.431085, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1804.488769, 5.131074, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1803.158813, 6.431085, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1803.158813, 5.131074, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1801.828857, 6.431085, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1801.828857, 5.131074, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1800.498901, 6.431085, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2161, 455.085662, -1800.498901, 5.131074, -0.000022, 0.000022, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 1229, "signs", "phonesign_128", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(18762, 454.622039, -1802.518066, 4.652073, -0.000022, 90.000007, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    electroxt = CreateDynamicObject(18762, 454.622039, -1807.498413, 4.652073, -0.000022, 90.000007, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    electroxt = CreateDynamicObject(18762, 454.623016, -1809.489257, 4.653075, -0.000022, 90.000007, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    electroxt = CreateDynamicObject(11710, 449.085296, -1800.193237, 5.479514, -29.399995, 90.000022, 0.000019, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(2592, 450.526214, -1800.230834, 5.512070, 0.000007, 0.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    electroxt = CreateDynamicObject(11710, 451.995819, -1800.193237, 5.479514, -29.399995, 90.000022, 0.000019, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(11710, 450.505340, -1800.193237, 5.479514, -29.399995, 90.000022, 0.000019, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(11710, 449.805145, -1800.193237, 5.479514, -29.399995, 90.000022, 0.000019, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(11710, 451.265106, -1800.193237, 5.479514, -29.399995, 90.000022, 0.000019, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(2117, 450.082977, -1803.859741, 4.602074, 0.000007, 0.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    electroxt = CreateDynamicObject(2117, 450.082977, -1810.270385, 4.602074, 0.000007, 0.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    electroxt = CreateDynamicObject(2117, 450.082977, -1807.019775, 4.602074, 0.000007, 0.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    electroxt = CreateDynamicObject(2631, 450.620452, -1803.704223, 4.562073, 0.000007, 0.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14569, "traidman", "darkgrey_carpet_256", 0x00000000);
    electroxt = CreateDynamicObject(2631, 450.620452, -1807.014526, 4.562073, 0.000007, 0.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14569, "traidman", "darkgrey_carpet_256", 0x00000000);
    electroxt = CreateDynamicObject(2631, 450.620452, -1810.294799, 4.562073, 0.000007, 0.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14569, "traidman", "darkgrey_carpet_256", 0x00000000);
    electroxt = CreateDynamicObject(2387, 444.211761, -1801.465942, 4.880836, -0.000022, 0.000007, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 3, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 4, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(2387, 444.211761, -1813.866943, 4.880836, -0.000022, 0.000007, -89.999900, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 3, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 4, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(19893, 444.776336, -1801.212402, 5.862075, 0.000022, -0.000014, 89.999870, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(19893, 444.776336, -1802.013061, 5.862075, 0.000022, -0.000014, 89.999870, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(19893, 444.776336, -1801.612670, 5.862075, 0.000022, -0.000014, 89.999870, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(19893, 443.855438, -1802.013183, 5.862075, -0.000022, 0.000000, -89.999961, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(19893, 443.855438, -1801.212524, 5.862075, -0.000022, 0.000000, -89.999961, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(19893, 443.855438, -1801.612915, 5.862075, -0.000022, 0.000000, -89.999961, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(18867, 444.597015, -1802.036621, 5.989297, 70.599990, -0.000022, 89.999938, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 3, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(18867, 444.597015, -1801.636596, 5.989297, 70.599990, -0.000022, 89.999938, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 3, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(18867, 444.597015, -1801.226440, 5.989297, 70.599990, -0.000022, 89.999938, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 3, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(18867, 444.041595, -1801.186523, 5.989297, 70.599952, 0.000044, -89.999961, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 3, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(18867, 444.041595, -1801.586547, 5.989297, 70.599952, 0.000044, -89.999961, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 3, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(18867, 444.041595, -1801.996704, 5.989297, 70.599952, 0.000044, -89.999961, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 3, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(19893, 444.776336, -1813.603027, 5.862075, 0.000022, -0.000022, 89.999824, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(19893, 444.776336, -1814.403686, 5.862075, 0.000022, -0.000022, 89.999824, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(19893, 444.776336, -1814.003295, 5.862075, 0.000022, -0.000022, 89.999824, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(19893, 443.855438, -1814.403808, 5.862075, -0.000022, 0.000007, -89.999961, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(19893, 443.855438, -1813.603149, 5.862075, -0.000022, 0.000007, -89.999961, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(19893, 443.855438, -1814.003540, 5.862075, -0.000022, 0.000007, -89.999961, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    electroxt = CreateDynamicObject(18867, 444.597015, -1814.427246, 5.989297, 70.599983, -0.000044, 89.999923, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 3, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(18867, 444.597015, -1814.027221, 5.989297, 70.599983, -0.000044, 89.999923, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 3, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(18867, 444.597015, -1813.617065, 5.989297, 70.599983, -0.000044, 89.999923, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 3, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(18867, 444.041595, -1813.577148, 5.989297, 70.599952, 0.000067, -89.999984, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 3, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(18867, 444.041595, -1813.977172, 5.989297, 70.599952, 0.000067, -89.999984, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 3, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(18867, 444.041595, -1814.387329, 5.989297, 70.599952, 0.000067, -89.999984, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    SetDynamicObjectMaterial(electroxt, 3, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    electroxt = CreateDynamicObject(2631, 444.040374, -1807.834960, 4.902075, 0.000022, -0.000007, 89.999916, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 14712, "lahss2bint2", "WH_carpet2", 0x00000000);
    electroxt = CreateDynamicObject(19426, 442.306732, -1803.109008, 10.957138, 89.999992, 103.368453, -103.368484, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    electroxt = CreateDynamicObject(19482, 442.190032, -1803.131103, 11.071083, -0.000045, -0.000051, 179.999740, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterialText(electroxt, 0, "sBox", 130, "Arial", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    electroxt = CreateDynamicObject(2662, 442.185028, -1803.398193, 10.571070, -0.000051, 0.000045, -89.999870, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterialText(electroxt, 0, "w     e     e     t", 130, "Arial", 90, 1, 0xFFFFFFFF, 0x00000000, 1);
    electroxt = CreateDynamicObject(19426, 442.356781, -1802.778686, 10.697132, 89.999992, 103.368453, -103.368484, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(19426, 442.356781, -1803.469116, 10.697132, 89.999992, 103.368453, -103.368484, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(19426, 442.356781, -1802.778686, 11.287144, 89.999992, 103.368537, -103.368431, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    electroxt = CreateDynamicObject(19426, 442.356781, -1803.469116, 11.287144, 89.999992, 103.368537, -103.368431, 0, 0, -1, 200.0, 200.0); 
    SetDynamicObjectMaterial(electroxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(1514, 448.351531, -1813.612060, 5.891077, -0.000007, -0.000022, -179.999816, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(1806, 449.176727, -1814.927734, 4.601074, 0.000007, 0.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(18869, 451.627655, -1813.582763, 5.141076, -0.000007, -0.000022, -179.999816, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(18869, 452.698089, -1813.582763, 5.141076, -0.000007, -0.000022, -179.999816, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(18867, 452.388275, -1813.582763, 5.141076, -0.000007, -0.000022, -179.999816, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(18871, 452.048187, -1813.582763, 5.141076, -0.000007, -0.000022, -179.999816, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(18869, 451.627655, -1813.582763, 5.451079, -0.000014, -0.000022, -179.999786, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(18869, 452.698089, -1813.582763, 5.451079, -0.000014, -0.000022, -179.999786, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(18867, 452.388275, -1813.582763, 5.451079, -0.000014, -0.000022, -179.999786, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(18871, 452.048187, -1813.582763, 5.451079, -0.000014, -0.000022, -179.999786, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(18868, 449.063079, -1800.202026, 5.559589, 59.399978, 0.000044, -0.000030, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(18865, 449.782806, -1800.202026, 5.559589, 59.399978, 0.000044, -0.000030, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(18869, 450.492645, -1800.202026, 5.559589, 59.399978, 0.000044, -0.000030, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(18866, 451.242889, -1800.202026, 5.559589, 59.399978, 0.000044, -0.000030, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(18868, 451.973236, -1800.202026, 5.559589, 59.399978, 0.000044, -0.000030, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(19893, 449.916961, -1803.842529, 5.412071, 0.000007, 0.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(19893, 451.277313, -1803.842529, 5.412071, 0.000007, 0.000022, 0.000007, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(19893, 450.606658, -1803.842529, 5.412071, -0.000007, -0.000022, -179.999816, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(19893, 451.277313, -1807.022949, 5.412071, 0.000007, -0.000022, 179.999816, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(19893, 449.916961, -1807.022949, 5.412071, 0.000007, -0.000022, 179.999816, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(19893, 450.606658, -1807.022949, 5.412071, 0.000000, 0.000022, 0.000029, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(19893, 449.916961, -1810.263305, 5.412071, 0.000014, 0.000022, -0.000014, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(19893, 451.277313, -1810.263305, 5.412071, 0.000014, 0.000022, -0.000014, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(19893, 450.606658, -1810.263305, 5.412071, -0.000014, -0.000022, -179.999786, 0, 0, -1, 200.0, 200.0); 
    CreateDynamicObject(671, 438.294372, -1816.958374, 4.546875, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.0, 200.0);
}