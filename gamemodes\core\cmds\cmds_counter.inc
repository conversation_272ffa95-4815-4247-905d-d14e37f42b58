YCMD:tr(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Cheat Slap!");
    KickEx(playerid);
    return 1;
}

YCMD:menu(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Cleo Menu!");
    KickEx(playerid);
    return 1;
}

YCMD:invisible(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Invisible!");
    KickEx(playerid);
    return 1;
}

YCMD:invis(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Invisible!");
    KickEx(playerid);
    return 1;
    
}

YCMD:marker(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Marker Teleport!");
    KickEx(playerid);
    return 1;
    
}

YCMD:rem(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion REM.cs!");
    KickEx(playerid);
    return 1;
}

YCMD:fcrash(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Crasher.luac!");
    KickEx(playerid);
    return 1;
}

YCMD:fspawn(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion FSpawn.cs!");
    KickEx(playerid);
    return 1;
}

YCMD:be(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion FSpawn.cs!");
    return 1;
}

YCMD:xray(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Wallhack!");
    KickEx(playerid);
    return 1;
}
YCMD:kill(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Kill.cs!");
    KickEx(playerid);
    return 1;
}
YCMD:gunspawn(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Gun Spawn!");
    SetWeapons(playerid);
    KickEx(playerid);
    return 1;
}
YCMD:whack(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Gun Spawn!");
    SetWeapons(playerid);
    KickEx(playerid);
    return 1;
}
YCMD:dgun(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Gun Spawn!");
    SetWeapons(playerid);
    KickEx(playerid);
    return 1;
}
YCMD:pgun(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Gun Spawn!");
    SetWeapons(playerid);
    KickEx(playerid);
    return 1;
}
YCMD:xgun(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Gun Spawn!");
    SetWeapons(playerid);
    KickEx(playerid);
    return 1;
}
YCMD:skema(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Skema!");
    KickEx(playerid);
    return 1;
}
YCMD:sekema(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Skema!");
    KickEx(playerid);
    return 1;
}
YCMD:cboom(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion CBoom!");
    KickEx(playerid);
    return 1;
}
YCMD:fdeath(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Fake Death/Fake Kill!");
    KickEx(playerid);
    return 1;
}
YCMD:fkill(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Fake Death/Fake Kill!");
    KickEx(playerid);
    return 1;
}
YCMD:intoarce(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Cheat Troll!");
    KickEx(playerid);
    return 1;
}
YCMD:co(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Cheat Troll!");
    KickEx(playerid);
    return 1;
}
YCMD:zboara(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Cheat Troll!");
    KickEx(playerid);
    return 1;
}
YCMD:bubule(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Cheat Troll!");
    KickEx(playerid);
    return 1;
}
YCMD:reconnect(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Fake Client!");
    KickEx(playerid);
    return 1;
}

YCMD:recon(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Fake Client!");
    KickEx(playerid);
    return 1;
}

YCMD:name(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Fake Client!");
    KickEx(playerid);
    return 1;
}

YCMD:connect(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Fake Client!");
    KickEx(playerid);
    return 1;
}

YCMD:disconnect(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Fake Client!");
    KickEx(playerid);
    return 1;
}

YCMD:massban(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Massive Ban!");
    KickEx(playerid);
    return 1;
}

YCMD:massiveban(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Massive Ban!");
    KickEx(playerid);
    return 1;
}

YCMD:maprecord(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Map Stealer!");
    KickEx(playerid);
    return 1;
}

YCMD:mapinfo(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Map Stealer!");
    KickEx(playerid);
    return 1;
}

YCMD:mapsave(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Map Stealer!");
    KickEx(playerid);
    return 1;
}

YCMD:mapstealer(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Map Stealer!");
    KickEx(playerid);
    return 1;
}

YCMD:mapsteal(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on suspicion Map Stealer!");
    KickEx(playerid);
    return 1;
}

YCMD:rco(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on RCO!");
    SetWeapons(playerid);
    KickEx(playerid);
    return 1;
}

YCMD:rcignite(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on RC Ignite!");
    SetWeapons(playerid);
    KickEx(playerid);
    return 1;
}

YCMD:rchell(playerid, params[], help)
{
    SendClientMessage(playerid, Y_RED, "[AntiCheat] {DBD7D2}You were kicked from the server on RC Hell!");
    SetWeapons(playerid);
    KickEx(playerid);
    return 1;
}

YCMD:startrecording(playerid, params[], help)
{
    SendStaffMessage(Y_RED, "[AntiCheat] "YELLOW"%s(%i) {DBD7D2}ditendang dari server karena diduga map stealer.", GetName(playerid), playerid);
    Kick(playerid);
    return 1;
}

YCMD:stoprecording(playerid, params[], help) = startrecording;
YCMD:savemodels(playerid, params[], help) = startrecording;
YCMD:saveremovebuildings(playerid, params[], help) = startrecording;