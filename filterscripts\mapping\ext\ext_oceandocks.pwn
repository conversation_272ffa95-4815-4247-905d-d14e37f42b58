/*v2
RemoveOceanDocksBuilding(playerid)
{
    RemoveBuildingForPlayer(playerid, 3578, 2546.050, -2396.590, 13.171, 0.250);
}

CreateOceanDocksExt()
{
    static dockxts;
    dockxts = CreateDynamicObject(19377, 2815.984619, -2436.873535, 11.745882, 0.000000, 99.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 14651, "ab_trukstpd", "Bow_bar_flooring", 0x00000000);
    dockxts = CreateDynamicObject(19377, 2805.659667, -2436.873535, 12.569174, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 14651, "ab_trukstpd", "Bow_bar_flooring", 0x00000000);
    dockxts = CreateDynamicObject(16088, 2719.436523, -2405.291748, 12.500892, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(16088, 2719.436523, -2503.861328, 12.500892, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(18981, 2789.567138, -2430.511474, 4.675115, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(18981, 2789.567138, -2443.451171, 4.675115, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(18766, 2782.076660, -2434.417724, 16.666706, 90.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 3979, "civic01_lan", "airportwall_256128", 0x00000000);
    dockxts = CreateDynamicObject(18766, 2782.076660, -2440.038574, 16.666706, 90.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 3979, "civic01_lan", "airportwall_256128", 0x00000000);
    dockxts = CreateDynamicObject(18766, 2793.967041, -2434.417724, 16.666706, 89.999992, 270.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 3979, "civic01_lan", "airportwall_256128", 0x00000000);
    dockxts = CreateDynamicObject(18766, 2793.967041, -2440.038574, 16.666706, 89.999992, 270.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 3979, "civic01_lan", "airportwall_256128", 0x00000000);
    dockxts = CreateDynamicObject(18762, 2777.565917, -2433.455810, 16.658439, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(18762, 2777.565917, -2440.736083, 16.658439, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(18762, 2786.756591, -2433.455810, 16.658439, 0.000029, 90.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(18762, 2786.756591, -2440.736083, 16.658439, 0.000037, 90.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(18762, 2789.286865, -2433.455810, 16.658439, 0.000037, 90.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(18762, 2789.286865, -2440.736083, 16.658439, 0.000045, 90.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(18762, 2798.626220, -2433.455810, 16.658439, 0.000045, 90.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(18762, 2798.626220, -2440.736083, 16.658439, 0.000052, 90.000000, 89.999839, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(18980, 2787.184326, -2437.134277, 16.624427, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(18981, 2786.677978, -2435.312011, 12.165117, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 15034, "genhotelsave", "walp57S", 0xFF4E4945);
    dockxts = CreateDynamicObject(18766, 2775.144042, -2437.194335, 12.137166, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 3979, "civic01_lan", "airportwall_256128", 0x00000000);
    dockxts = CreateDynamicObject(19377, 2795.539794, -2436.873535, 12.569174, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 14651, "ab_trukstpd", "Bow_bar_flooring", 0x00000000);
    dockxts = CreateDynamicObject(948, 2779.593261, -2437.265625, 12.659989, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 1, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    SetDynamicObjectMaterial(dockxts, 3, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    dockxts = CreateDynamicObject(948, 2787.013427, -2437.265625, 12.659989, 0.000014, 0.000007, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 1, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    SetDynamicObjectMaterial(dockxts, 3, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    dockxts = CreateDynamicObject(948, 2788.873535, -2437.265625, 12.659989, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 1, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    SetDynamicObjectMaterial(dockxts, 3, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    dockxts = CreateDynamicObject(948, 2796.293701, -2437.265625, 12.659989, 0.000022, 0.000007, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 1, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    SetDynamicObjectMaterial(dockxts, 3, 19787, "samplcdtvs1", "samplcdtv1screen", 0x00000000);
    dockxts = CreateDynamicObject(18762, 2799.176025, -2437.115966, 13.805113, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(18766, 2776.693603, -2443.784667, 12.137166, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 3979, "civic01_lan", "airportwall_256128", 0x00000000);
    dockxts = CreateDynamicObject(18766, 2776.693603, -2429.845214, 12.137166, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 3979, "civic01_lan", "airportwall_256128", 0x00000000);
    dockxts = CreateDynamicObject(8674, 2788.558837, -2441.892822, 16.295118, 90.000000, 90.000007, 179.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x90555555);
    dockxts = CreateDynamicObject(8674, 2788.558837, -2431.562988, 16.295118, 90.000000, 90.000007, 179.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x90555555);
    dockxts = CreateDynamicObject(19913, 2811.007812, -2407.101318, 8.325110, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(19913, 2811.007812, -2466.631103, 8.325110, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(19373, 2774.725830, -2437.278076, 13.318437, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(dockxts, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    dockxts = CreateDynamicObject(19373, 2774.665771, -2437.278076, 13.198439, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(dockxts, 0, "PELABUHAN BELAWAN", 140, "Arial", 40, 1, 0xFF555555, 0x00000000, 1);
    dockxts = CreateDynamicObject(19373, 2774.705810, -2437.278076, 14.138438, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(dockxts, 0, "J", 140, "Webdings", 199, 1, 0xFF555555, 0x00000000, 1);
    dockxts = CreateDynamicObject(3352, 2718.700683, -2402.655761, 19.935110, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(dockxts, 0, "PELABUHAN", 130, "Arial", 90, 1, 0xFF333333, 0x00000000, 1);
    dockxts = CreateDynamicObject(3352, 2718.700683, -2408.885742, 19.935110, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(dockxts, 0, "BELAWAN", 130, "Arial", 90, 1, 0xFF333333, 0x00000000, 1);
    dockxts = CreateDynamicObject(3352, 2720.251464, -2506.144042, 19.935110, 0.000000, 0.000007, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(dockxts, 0, "SELAMAT", 130, "Arial", 90, 1, 0xFF333333, 0x00000000, 1);
    dockxts = CreateDynamicObject(3352, 2720.251464, -2501.353271, 19.935110, 0.000000, 0.000007, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(dockxts, 0, "JALAN", 130, "Arial", 90, 1, 0xFF333333, 0x00000000, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(3578, 2542.275878, -2392.347656, 13.311180, 0.000000, 0.000000, 45.300033, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9245, 2787.028808, -2338.133789, 24.352813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(5837, 2713.802978, -2397.989990, 14.092815, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3474, 2768.239990, -2354.648437, 19.460208, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3474, 2744.590332, -2441.038574, 19.330217, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1380, 2794.478515, -2393.086669, 36.400947, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8886, 2779.987060, -2471.797607, 15.905907, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8886, 2792.946533, -2477.547607, 15.905907, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8884, 2727.989746, -2493.078857, 16.103179, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3630, 2740.608886, -2473.784912, 14.103183, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3630, 2740.608886, -2476.884277, 14.103183, 180.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3630, 2740.608886, -2476.884277, 17.123188, 270.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8335, 2744.724853, -2391.939453, 16.542806, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1378, 2801.056152, -2538.492919, 36.564620, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8886, 2689.186767, -2389.129150, 16.027486, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1238, 2718.088867, -2401.669677, 12.830934, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1238, 2717.098876, -2401.299316, 12.830934, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3066, 2703.712646, -2416.360107, 13.680937, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3066, 2727.642822, -2416.360107, 13.680937, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3066, 2766.442382, -2388.520507, 13.680937, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(5837, 2713.164062, -2496.559570, 14.172809, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2784.966308, -2436.868896, 13.170119, 0.000007, -0.000014, 179.999862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2781.576660, -2436.868896, 13.170119, 0.000007, -0.000014, 179.999862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2781.576660, -2437.668701, 13.170119, -0.000007, 0.000014, -0.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2784.966796, -2437.668701, 13.170119, -0.000007, 0.000014, -0.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2794.246582, -2436.868896, 13.170119, 0.000007, -0.000022, 179.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2790.856933, -2436.868896, 13.170119, 0.000007, -0.000022, 179.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2790.856933, -2437.668701, 13.170119, -0.000007, 0.000022, -0.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2794.247070, -2437.668701, 13.170119, -0.000007, 0.000022, -0.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3660, 2788.073730, -2431.156005, 14.195116, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3660, 2788.073730, -2442.574951, 14.195116, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 2819.074707, -2441.692138, 11.889656, 0.000000, 9.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 2814.955322, -2441.692138, 12.541986, 0.000000, 9.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 2819.074707, -2432.092041, 11.889656, 0.000000, 9.000006, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 2814.955322, -2432.092041, 12.541986, 0.000000, 9.000006, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 2813.078857, -2441.692138, 12.839212, 0.000000, 9.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 2813.058837, -2432.092041, 12.842337, 0.000000, 9.000006, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18257, 2821.994140, -2449.732421, 11.089873, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
}
*/

/*v3
RemoveOceanDocksBuilding(playerid)
{
    RemoveBuildingForPlayer(playerid, 3624, 2788.159, -2455.879, 16.726, 0.250);
    RemoveBuildingForPlayer(playerid, 3710, 2788.159, -2455.879, 16.726, 0.250);
    RemoveBuildingForPlayer(playerid, 3624, 2788.159, -2417.790, 16.726, 0.250);
    RemoveBuildingForPlayer(playerid, 3710, 2788.159, -2417.790, 16.726, 0.250);
    RemoveBuildingForPlayer(playerid, 3624, 2788.159, -2493.979, 16.726, 0.250);
    RemoveBuildingForPlayer(playerid, 3710, 2788.159, -2493.979, 16.726, 0.250);
    RemoveBuildingForPlayer(playerid, 1278, 2773.340, -2443.169, 26.703, 0.250);
    RemoveBuildingForPlayer(playerid, 1278, 2773.340, -2479.969, 26.703, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2783.780, -2410.209, 14.671, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2797.520, -2410.080, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2791.949, -2410.209, 14.656, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2783.780, -2425.350, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2791.949, -2425.350, 14.671, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2783.780, -2448.479, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2783.780, -2463.820, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2791.949, -2463.820, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2791.949, -2448.479, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2797.520, -2448.340, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2783.780, -2486.959, 14.656, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2791.949, -2486.959, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 4525, -1810.699, 391.945, 18.015, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2783.780, -2501.840, 14.695, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2791.949, -2501.840, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2797.520, -2486.830, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 3626, 2746.409, -2453.479, 14.078, 0.250);
    RemoveBuildingForPlayer(playerid, 3770, 2746.409, -2453.479, 14.078, 0.250);
    RemoveBuildingForPlayer(playerid, 3577, 2744.570, -2436.189, 13.343, 0.250);
    RemoveBuildingForPlayer(playerid, 3577, 2744.570, -2427.320, 13.351, 0.250);
    RemoveBuildingForPlayer(playerid, 3578, 2747.010, -2480.239, 13.171, 0.250);
    RemoveBuildingForPlayer(playerid, 3707, 2716.229, -2452.590, 20.203, 0.250);
    RemoveBuildingForPlayer(playerid, 3708, 2716.229, -2452.590, 20.203, 0.250);
}

CreateOceanDocksExt()
{
    ocdxetx = CreateDynamicObject(3707, 2716.229980, -2452.590087, 20.203100, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 1975, "texttest", "kb_blue", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 5, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 6, 1975, "texttest", "kb_blue", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 7, 1975, "texttest", "kb_blue", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 8, 4833, "airprtrunway_las", "policeha02black_128", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 9, 10765, "airportgnd_sfse", "black64", 0x00000000);
    ocdxetx = CreateDynamicObject(1508, 2729.005615, -2449.818115, 18.203662, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 4877, "downtown1_las", "hotdoor01_law", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2733.421142, -2462.621337, 27.424308, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 4714, "lanlacma_tr_lan2", "sl_galleryrail1", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2733.421142, -2442.612548, 27.424308, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 4714, "lanlacma_tr_lan2", "sl_galleryrail1", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2733.421142, -2452.621337, 27.424308, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 4714, "lanlacma_tr_lan2", "sl_galleryrail1", 0x00000000);
    ocdxetx = CreateDynamicObject(19482, 2733.932373, -2456.258544, 28.518735, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "WELC", 130, "Arial", 199, 1, 0xFFFF3300, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2733.932373, -2451.259033, 28.518735, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "OME", 130, "Arial", 199, 1, 0xFFFF3300, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2733.932373, -2447.409667, 28.518735, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "TO", 130, "Arial", 199, 1, 0xFFFF3300, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2733.932373, -2459.100830, 26.588727, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "PORT", 130, "Arial", 199, 1, 0xFF0066FF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2733.932373, -2454.710205, 26.588727, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "OF", 130, "Arial", 199, 1, 0xFF0066FF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2733.932373, -2450.460693, 26.588727, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "BELA", 130, "Arial", 199, 1, 0xFF0066FF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2733.932373, -2445.741699, 26.588727, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "WAN", 130, "Arial", 199, 1, 0xFF0066FF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2733.962646, -2463.009521, 27.568737, 0.000000, 0.000051, 359.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "m", 130, "Wingdings", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2734.002685, -2442.269287, 27.968740, 0.000000, 0.000051, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "o", 130, "Webdings", 199, 1, 0xFF000000, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2733.982421, -2462.991210, 27.968740, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "o", 130, "Webdings", 199, 1, 0xFF000000, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2733.982666, -2442.269287, 27.568737, 0.000000, 0.000051, 179.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "m", 130, "Wingdings", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(18981, 2794.562255, -2416.894775, 12.190819, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2794.562255, -2441.895751, 12.190819, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2794.562255, -2466.884277, 12.190819, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2794.562255, -2491.833984, 12.190819, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2783.461181, -2416.894775, 12.190819, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2783.461181, -2441.895751, 12.190819, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2783.461181, -2466.884277, 12.190819, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2783.461181, -2491.833984, 12.190819, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2775.953857, -2403.894042, 12.246739, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2785.954345, -2403.894042, 12.246739, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2795.953369, -2403.894042, 12.246739, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2802.063720, -2403.894042, 12.246739, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2775.953857, -2504.832031, 12.246739, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2785.954345, -2504.832031, 12.246739, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2795.953369, -2504.832031, 12.246739, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2802.063720, -2504.832031, 12.246739, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2807.563720, -2500.330322, 12.246739, 0.000014, 0.000014, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2807.563720, -2490.329833, 12.246739, 0.000014, 0.000014, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2807.563720, -2480.330810, 12.246739, 0.000014, 0.000014, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2807.563720, -2474.220458, 12.246739, 0.000014, 0.000014, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2807.563720, -2434.506347, 12.246739, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2807.563720, -2424.505859, 12.246739, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2807.563720, -2414.506835, 12.246739, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2807.563720, -2408.396484, 12.246739, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2807.563720, -2464.219238, 12.246739, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2807.563720, -2454.218750, 12.246739, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2807.563720, -2444.219726, 12.246739, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2807.563720, -2438.109375, 12.246739, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2771.453857, -2409.388427, 12.246747, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2771.453857, -2499.339355, 12.246768, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2771.453857, -2474.339355, 12.246768, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2771.453857, -2434.818603, 12.246764, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2774.842041, -2442.719970, 12.246747, 0.000035, 0.000009, 134.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2774.842285, -2466.432373, 12.246768, 0.000035, 0.000019, 44.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2778.232910, -2458.252929, 12.246768, 0.000037, 0.000014, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2778.232910, -2450.903320, 12.246768, 0.000037, 0.000014, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2774.842041, -2442.719970, 14.296730, 0.000040, 0.000003, 134.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2774.842285, -2466.432373, 14.296753, 0.000040, 0.000025, 44.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2778.232910, -2458.252929, 14.296753, 0.000045, 0.000014, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2778.232910, -2450.903320, 14.296753, 0.000045, 0.000014, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2778.233642, -2444.569335, 19.064285, -0.000014, 0.000000, -89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 4714, "lanlacma_tr_lan2", "sl_galleryrail1", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2778.233642, -2464.578125, 19.064285, -0.000014, 0.000000, -89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 4714, "lanlacma_tr_lan2", "sl_galleryrail1", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2778.233642, -2454.569335, 19.064285, -0.000014, 0.000000, -89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 4714, "lanlacma_tr_lan2", "sl_galleryrail1", 0x00000000);
    ocdxetx = CreateDynamicObject(19482, 2777.722412, -2450.932128, 20.158712, 0.000000, -0.000007, 179.999710, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "WELC", 130, "Arial", 199, 1, 0xFFFF3300, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2777.722412, -2455.931640, 20.158712, 0.000000, -0.000007, 179.999710, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "OME", 130, "Arial", 199, 1, 0xFFFF3300, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2777.722412, -2459.781005, 20.158712, 0.000000, -0.000007, 179.999710, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "TO", 130, "Arial", 199, 1, 0xFFFF3300, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2777.722412, -2448.089843, 18.228704, 0.000000, 0.000007, 179.999710, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "PORT", 130, "Arial", 199, 1, 0xFF0066FF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2777.722412, -2452.480468, 18.228704, 0.000000, 0.000007, 179.999710, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "OF", 130, "Arial", 199, 1, 0xFF0066FF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2777.722412, -2456.729980, 18.228704, 0.000000, 0.000007, 179.999710, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "BELA", 130, "Arial", 199, 1, 0xFF0066FF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2777.722412, -2461.448974, 18.228704, 0.000000, 0.000007, 179.999710, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "WAN", 130, "Arial", 199, 1, 0xFF0066FF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2777.692138, -2444.181152, 19.208713, 0.000000, 0.000029, 179.999649, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "m", 130, "Wingdings", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2777.652099, -2464.921386, 19.608715, 0.000000, 0.000075, -0.000121, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "o", 130, "Webdings", 199, 1, 0xFF000000, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2777.672363, -2444.199462, 19.608715, 0.000000, 0.000014, 179.999710, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "o", 130, "Webdings", 199, 1, 0xFF000000, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2777.672119, -2464.921386, 19.208713, 0.000000, 0.000075, -0.000121, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "m", 130, "Wingdings", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(18766, 2778.232910, -2464.572021, 14.296753, 0.000045, 0.000014, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2778.232910, -2444.580078, 14.296751, 0.000045, 0.000014, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2771.453857, -2474.339355, 14.296791, 0.000037, 0.000014, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2771.453857, -2434.818603, 14.296785, 0.000037, 0.000014, 89.999855, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19482, 2778.743164, -2458.170410, 20.158712, 0.000000, 0.000007, -0.000304, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "WELC", 130, "Arial", 199, 1, 0xFFFF3300, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2778.743164, -2453.170898, 20.158712, 0.000000, 0.000007, -0.000304, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "OME", 130, "Arial", 199, 1, 0xFFFF3300, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2778.743164, -2449.321533, 20.158712, 0.000000, 0.000007, -0.000304, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "TO", 130, "Arial", 199, 1, 0xFFFF3300, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2778.743164, -2461.012695, 18.228704, 0.000000, 0.000022, -0.000304, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "PORT", 130, "Arial", 199, 1, 0xFF0066FF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2778.743164, -2456.622070, 18.228704, 0.000000, 0.000022, -0.000304, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "OF", 130, "Arial", 199, 1, 0xFF0066FF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2778.743164, -2452.372558, 18.228704, 0.000000, 0.000022, -0.000304, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "BELA", 130, "Arial", 199, 1, 0xFF0066FF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2778.743164, -2447.653564, 18.228704, 0.000000, 0.000022, -0.000304, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "WAN", 130, "Arial", 199, 1, 0xFF0066FF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2778.773437, -2464.921386, 19.208713, 0.000000, 0.000045, -0.000365, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "m", 130, "Wingdings", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2778.813476, -2444.181152, 19.608715, 0.000000, 0.000059, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "o", 130, "Webdings", 199, 1, 0xFF000000, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2778.793212, -2464.903076, 19.608715, 0.000000, 0.000029, -0.000304, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "o", 130, "Webdings", 199, 1, 0xFF000000, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19482, 2778.793457, -2444.181152, 19.208713, 0.000000, 0.000059, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "m", 130, "Wingdings", 199, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19454, 2777.042480, -2409.213134, 10.943115, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2777.032470, -2499.514892, 10.943115, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2782.042480, -2409.213134, 10.943115, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2782.032470, -2499.514892, 10.943115, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2787.042480, -2409.213134, 10.943115, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2787.032470, -2499.514892, 10.943115, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2792.042480, -2409.213134, 10.943115, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2792.032470, -2499.514892, 10.943115, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2797.042480, -2409.213134, 10.943115, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2797.032470, -2499.514892, 10.943115, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.042480, -2409.213134, 10.943115, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.032470, -2499.514892, 10.943115, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.032470, -2484.505126, 10.943115, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.032470, -2479.505126, 10.943115, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.032470, -2474.505126, 10.943115, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.032470, -2469.505126, 10.943115, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.032470, -2464.505126, 10.943115, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.032470, -2459.505126, 10.943115, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.032470, -2454.505126, 10.943115, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.032470, -2449.505126, 10.943115, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.032470, -2444.505126, 10.943115, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.032470, -2439.505126, 10.943115, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.032470, -2434.505126, 10.943115, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.032470, -2429.505126, 10.943115, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.032470, -2424.505126, 10.943115, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2775.953857, -2398.894042, 10.246739, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2785.954345, -2398.894042, 10.246739, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2795.953369, -2398.894042, 10.246739, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2802.063720, -2398.894042, 10.246739, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2775.953857, -2509.832031, 10.246739, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2785.954345, -2509.832031, 10.246739, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2795.953369, -2509.832031, 10.246739, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2802.063720, -2509.832031, 10.246739, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2771.453857, -2504.331054, 10.246768, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2807.563720, -2505.332031, 10.246749, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2771.453857, -2404.388183, 10.246747, 0.000029, 0.000014, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2807.563720, -2403.394775, 10.246739, 0.000022, 0.000014, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2802.073974, -2401.884521, 12.246739, 90.000022, 0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "desgreengrass", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2792.073730, -2401.884521, 12.246739, 90.000022, 0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "desgreengrass", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2782.072509, -2401.884521, 12.246739, 90.000022, 0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "desgreengrass", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2776.959716, -2401.884521, 12.256738, 90.000022, 0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "desgreengrass", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2802.073974, -2506.833007, 12.246739, 89.999992, -161.565032, -18.435070, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "desgreengrass", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2792.073730, -2506.833007, 12.246739, 89.999992, -161.565032, -18.435070, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "desgreengrass", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2782.072509, -2506.833007, 12.246739, 89.999992, -161.565032, -18.435070, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "desgreengrass", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2776.959716, -2506.833007, 12.256738, 89.999992, -161.565032, -18.435070, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "desgreengrass", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2738.650878, -2441.895751, 12.190819, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2738.650878, -2466.884277, 12.190819, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2738.650878, -2474.742919, 12.190819, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2738.650878, -2430.394042, 12.190819, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18646, "matcolours", "grey-90-percent", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2735.187744, -2487.221923, 13.236424, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2739.349121, -2487.221923, 13.236424, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2743.520751, -2487.221923, 13.236424, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2747.682128, -2487.221923, 13.236424, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2735.187744, -2417.918945, 13.236424, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2739.349121, -2417.918945, 13.236424, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2743.520751, -2417.918945, 13.236424, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2747.682128, -2417.918945, 13.236424, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2732.930908, -2465.482666, 17.122276, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2732.930908, -2461.312500, 17.122276, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2732.930908, -2457.142822, 17.122276, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2732.930908, -2452.972900, 17.122276, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2732.930908, -2439.672851, 17.122276, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2732.930908, -2443.843505, 17.122276, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2732.930908, -2445.932373, 17.122276, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2738.243652, -2465.073486, 12.231893, 89.999992, 90.000022, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3980, "cityhall_lan", "man_cellarfloor128", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2738.243652, -2460.074462, 12.231893, 89.999992, 90.000022, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3980, "cityhall_lan", "man_cellarfloor128", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2738.243652, -2455.085205, 12.231893, 89.999992, 90.000022, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3980, "cityhall_lan", "man_cellarfloor128", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2735.187744, -2474.144042, 13.236424, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2739.349121, -2474.144042, 13.236424, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2743.520751, -2474.144042, 13.236424, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2747.682128, -2474.144042, 13.236424, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2735.187744, -2431.012451, 13.236424, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2739.349121, -2431.012451, 13.236424, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2743.520751, -2431.012451, 13.236424, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(970, 2747.682128, -2431.012451, 13.236424, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18878, "ferriswheel", "railing3", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 3440, "airportpillar", "metalic_64", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2738.243652, -2468.073486, 15.211834, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2738.243652, -2437.083984, 15.211834, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2738.243652, -2468.073486, 20.201885, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2738.243652, -2437.083984, 20.201885, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2738.243652, -2465.073486, 22.191904, 89.999992, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2738.243652, -2460.074462, 22.191904, 89.999992, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2738.243652, -2455.085205, 22.191904, 89.999992, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2738.243652, -2450.084716, 22.191904, 89.999992, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2738.243652, -2445.085205, 22.191904, 89.999992, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2738.243652, -2440.085449, 22.191904, 89.999992, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2728.251708, -2468.073486, 15.211834, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2728.251708, -2437.083984, 15.211834, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2728.251708, -2468.073486, 20.201885, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2728.251708, -2437.083984, 20.201885, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2738.243652, -2450.084716, 12.231893, 89.999992, 90.000022, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3980, "cityhall_lan", "man_cellarfloor128", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2738.243652, -2445.085205, 12.231893, 89.999992, 90.000022, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3980, "cityhall_lan", "man_cellarfloor128", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2738.243652, -2440.085449, 12.231893, 89.999992, 90.000022, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3980, "cityhall_lan", "man_cellarfloor128", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2728.250732, -2465.073486, 12.231893, 89.999992, 90.000038, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3980, "cityhall_lan", "man_cellarfloor128", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2728.250732, -2460.074462, 12.231893, 89.999992, 90.000038, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3980, "cityhall_lan", "man_cellarfloor128", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2728.250732, -2455.085205, 12.231893, 89.999992, 90.000038, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3980, "cityhall_lan", "man_cellarfloor128", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2728.250732, -2450.084716, 12.231893, 89.999992, 90.000038, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3980, "cityhall_lan", "man_cellarfloor128", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2728.250732, -2445.085205, 12.231893, 89.999992, 90.000038, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3980, "cityhall_lan", "man_cellarfloor128", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2728.250732, -2440.085449, 12.231893, 89.999992, 90.000038, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3980, "cityhall_lan", "man_cellarfloor128", 0x00000000);
    ocdxetx = CreateDynamicObject(949, 2738.229492, -2437.968017, 13.368571, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 1, 5461, "glenpark6d_lae", "GB_truckdepot20", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 2, 11413, "fosterflowers", "starflower3", 0x00000000);
    ocdxetx = CreateDynamicObject(949, 2738.229492, -2467.208251, 13.368571, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 1, 5461, "glenpark6d_lae", "GB_truckdepot20", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 2, 11413, "fosterflowers", "starflower3", 0x00000000);
    ocdxetx = CreateDynamicObject(949, 2743.293212, -2436.968017, 13.338569, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 1, 5461, "glenpark6d_lae", "GB_truckdepot20", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 2, 11413, "fosterflowers", "starflower3", 0x00000000);
    ocdxetx = CreateDynamicObject(949, 2743.293212, -2468.208251, 13.338569, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 1, 5461, "glenpark6d_lae", "GB_truckdepot20", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 2, 11413, "fosterflowers", "starflower3", 0x00000000);
    ocdxetx = CreateDynamicObject(949, 2733.436523, -2453.015380, 13.368571, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 1, 5461, "glenpark6d_lae", "GB_truckdepot20", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 2, 11413, "fosterflowers", "starflower3", 0x00000000);
    ocdxetx = CreateDynamicObject(949, 2733.436523, -2448.761230, 13.368571, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 1, 5461, "glenpark6d_lae", "GB_truckdepot20", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 2, 11413, "fosterflowers", "starflower3", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(617, 2774.082763, -2402.112548, 12.527254, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(617, 2784.082763, -2402.112548, 12.527254, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(617, 2794.082763, -2402.112548, 12.527254, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(617, 2804.082763, -2402.112548, 12.527254, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(617, 2774.082763, -2506.987060, 12.527254, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(617, 2784.082763, -2506.987060, 12.527254, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(617, 2794.082763, -2506.987060, 12.527254, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(617, 2804.082763, -2506.987060, 12.527254, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2770.579101, -2432.481201, 13.316834, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2770.579101, -2437.850585, 13.316834, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2770.579101, -2476.631347, 13.316834, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2770.579101, -2471.261474, 13.316834, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2770.579101, -2411.689697, 13.316834, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2770.579101, -2406.318359, 13.316834, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2770.579101, -2496.998535, 13.316834, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 2770.579101, -2502.367675, 13.316834, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2750.215820, -2478.967041, 13.394023, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2750.215820, -2426.251464, 13.394023, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2750.215820, -2452.467041, 13.394023, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(628, 2729.806152, -2446.055908, 18.522808, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(628, 2729.806152, -2453.546142, 18.522808, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(628, 2729.806152, -2453.546142, 18.522808, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3574, 2740.320068, -2424.551025, 15.134509, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2973, 2773.114501, -2405.690429, 12.512331, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2973, 2773.114501, -2408.249755, 12.512331, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2973, 2773.114501, -2413.359619, 12.512331, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2973, 2773.114501, -2410.799560, 12.512331, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2973, 2773.114501, -2408.249755, 14.942326, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2973, 2773.114501, -2410.799560, 14.942326, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2772.565673, -2503.556640, 12.665596, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2772.565673, -2502.005859, 12.665596, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2772.565673, -2500.456298, 12.665596, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2772.565673, -2498.915771, 12.665596, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2772.565673, -2497.366455, 12.665596, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2772.565673, -2495.816162, 12.665596, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2772.565673, -2494.265625, 12.665596, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2772.565673, -2500.456298, 14.255603, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2772.565673, -2498.915771, 14.255603, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2772.565673, -2497.366455, 14.255603, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2806.448242, -2414.479492, 12.665596, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2806.448242, -2412.928710, 12.665596, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2806.448242, -2411.379150, 12.665596, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2806.448242, -2409.838623, 12.665596, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2806.448242, -2408.289306, 12.665596, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2806.448242, -2406.739013, 12.665596, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2806.448242, -2405.188476, 12.665596, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2806.448242, -2411.379150, 14.255603, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2806.448242, -2409.838623, 14.255603, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2975, 2806.448242, -2408.289306, 14.255603, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2973, 2805.891845, -2495.393798, 12.512331, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2973, 2805.891845, -2497.953125, 12.512331, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2973, 2805.891845, -2503.062988, 12.512331, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2973, 2805.891845, -2500.502929, 12.512331, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2973, 2805.891845, -2497.953125, 14.942326, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2973, 2805.891845, -2500.502929, 14.942326, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3474, 2775.698974, -2422.095458, 18.991481, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3474, 2775.979248, -2486.836914, 18.991481, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8886, 2802.558105, -2419.878662, 16.102407, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8886, 2802.297851, -2489.148681, 16.102407, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2780.955810, -2464.414062, 13.394023, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2780.955810, -2444.784912, 13.394023, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2780.955810, -2454.605468, 13.394023, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2775.536132, -2449.884521, 13.394023, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3569, 2780.795898, -2471.290039, 15.069473, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3569, 2780.795898, -2437.978515, 15.069473, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3569, 2780.795898, -2433.978515, 15.069473, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3569, 2780.795898, -2475.290039, 15.069473, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2775.536132, -2459.295410, 13.394023, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2750.215820, -2436.251464, 13.394023, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2750.215820, -2468.967041, 13.394023, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2741.168457, -2467.174804, 13.192035, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2735.348632, -2467.174804, 13.192035, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2735.348632, -2438.005371, 13.192035, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2741.160644, -2438.005371, 13.192035, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 2733.540771, -2456.486572, 13.192035, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1892, 2750.442871, -2463.084716, 12.698301, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1892, 2750.442871, -2459.364746, 12.698301, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1892, 2750.442871, -2446.563720, 12.698301, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1892, 2750.442871, -2443.153320, 12.698301, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19364, 2778.258056, -2447.083984, 19.235927, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19364, 2778.258056, -2450.284423, 19.235927, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19364, 2778.258056, -2453.484130, 19.235927, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19364, 2778.258056, -2456.675292, 19.235927, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19364, 2778.258056, -2459.875732, 19.235927, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19364, 2778.268066, -2462.054687, 19.235927, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00);
}
*/

RemoveOceanDocksBuilding(playerid)
{
    RemoveBuildingForPlayer(playerid, 3707, 2716.229, -2452.590, 20.203, 0.250);
    RemoveBuildingForPlayer(playerid, 3708, 2716.229, -2452.590, 20.203, 0.250);
    RemoveBuildingForPlayer(playerid, 3689, 2685.379, -2366.050, 19.953, 0.250);
    RemoveBuildingForPlayer(playerid, 3690, 2685.379, -2366.050, 19.953, 0.250);
    RemoveBuildingForPlayer(playerid, 1226, 2637.169, -2385.870, 16.414, 0.250);
    RemoveBuildingForPlayer(playerid, 1226, 2692.679, -2387.479, 16.414, 0.250);
    RemoveBuildingForPlayer(playerid, 3753, 2615.110, -2464.620, 3.039, 0.250);
    RemoveBuildingForPlayer(playerid, 3758, 2615.110, -2464.620, 3.039, 0.250);
    RemoveBuildingForPlayer(playerid, 3574, 2789.209, -2377.629, 15.218, 0.250);
    RemoveBuildingForPlayer(playerid, 3744, 2789.209, -2377.629, 15.218, 0.250);
    RemoveBuildingForPlayer(playerid, 3574, 2771.070, -2372.449, 15.218, 0.250);
    RemoveBuildingForPlayer(playerid, 3744, 2771.070, -2372.449, 15.218, 0.250);
    RemoveBuildingForPlayer(playerid, 620, 2208.320, -1864.949, 10.804, 0.250);
    RemoveBuildingForPlayer(playerid, 3574, 2774.800, -2386.850, 15.218, 0.250);
    RemoveBuildingForPlayer(playerid, 3744, 2774.800, -2386.850, 15.218, 0.250);
    RemoveBuildingForPlayer(playerid, 3626, 2795.830, -2394.239, 14.171, 0.250);
    RemoveBuildingForPlayer(playerid, 3770, 2795.830, -2394.239, 14.171, 0.250);
    RemoveBuildingForPlayer(playerid, 3578, 2571.159, -2421.129, 13.171, 0.250);
    RemoveBuildingForPlayer(playerid, 3578, 2546.050, -2396.590, 13.171, 0.250);
    RemoveBuildingForPlayer(playerid, 3623, 2618.860, -2429.300, 17.070, 0.250);
    RemoveBuildingForPlayer(playerid, 3709, 2618.860, -2429.300, 17.070, 0.250);
    RemoveBuildingForPlayer(playerid, 3623, 2639.550, -2429.300, 17.070, 0.250);
    RemoveBuildingForPlayer(playerid, 3709, 2639.550, -2429.300, 17.070, 0.250);
    RemoveBuildingForPlayer(playerid, 3623, 2660.479, -2429.300, 17.070, 0.250);
    RemoveBuildingForPlayer(playerid, 3709, 2660.479, -2429.300, 17.070, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2783.780, -2425.350, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2791.949, -2425.350, 14.671, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2797.520, -2410.080, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2791.949, -2410.209, 14.656, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2783.780, -2410.209, 14.671, 0.250);
    RemoveBuildingForPlayer(playerid, 3624, 2788.159, -2417.790, 16.726, 0.250);
    RemoveBuildingForPlayer(playerid, 3710, 2788.159, -2417.790, 16.726, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2783.780, -2448.479, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2791.949, -2448.479, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2797.520, -2448.340, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2791.949, -2463.820, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 3761, 2783.780, -2463.820, 14.632, 0.250);
    RemoveBuildingForPlayer(playerid, 3624, 2788.159, -2455.879, 16.726, 0.250);
    RemoveBuildingForPlayer(playerid, 3710, 2788.159, -2455.879, 16.726, 0.250);
    RemoveBuildingForPlayer(playerid, 1278, 2773.340, -2443.169, 26.703, 0.250);
    RemoveBuildingForPlayer(playerid, 1635, 2704.370, -2487.870, 20.562, 0.250);
    RemoveBuildingForPlayer(playerid, 3622, 2503.540, -2366.510, 16.046, 0.250);
    RemoveBuildingForPlayer(playerid, 3687, 2503.540, -2366.510, 16.046, 0.250);
    RemoveBuildingForPlayer(playerid, 1635, 2512.010, -2375.090, 16.742, 0.250);
    RemoveBuildingForPlayer(playerid, 3626, 2746.409, -2453.479, 14.078, 0.250);
    RemoveBuildingForPlayer(playerid, 3770, 2746.409, -2453.479, 14.078, 0.250);
    RemoveBuildingForPlayer(playerid, 3620, 2814.270, -2521.489, 25.515, 0.250);
    RemoveBuildingForPlayer(playerid, 3746, 2814.270, -2521.489, 25.515, 0.250);
    RemoveBuildingForPlayer(playerid, 3620, 2814.270, -2356.570, 25.515, 0.250);
    RemoveBuildingForPlayer(playerid, 3746, 2814.270, -2356.570, 25.515, 0.250);
    RemoveBuildingForPlayer(playerid, 3577, 2744.570, -2436.189, 13.343, 0.250);
    RemoveBuildingForPlayer(playerid, 3577, 2744.570, -2427.320, 13.351, 0.250);
    RemoveBuildingForPlayer(playerid, 1345, 339.718, 52.984, 3.265, 0.250);
    RemoveBuildingForPlayer(playerid, 3574, 2551.530, -2472.699, 15.218, 0.250);
    RemoveBuildingForPlayer(playerid, 3744, 2551.530, -2472.699, 15.218, 0.250);
    RemoveBuildingForPlayer(playerid, 3620, 2568.449, -2483.389, 25.515, 0.250);
    RemoveBuildingForPlayer(playerid, 3746, 2568.449, -2483.389, 25.515, 0.250);
    RemoveBuildingForPlayer(playerid, 3574, 2774.800, -2534.949, 15.218, 0.250);
    RemoveBuildingForPlayer(playerid, 3744, 2774.800, -2534.949, 15.218, 0.250);
    RemoveBuildingForPlayer(playerid, 3574, 2771.070, -2520.550, 15.218, 0.250);
    RemoveBuildingForPlayer(playerid, 3744, 2771.070, -2520.550, 15.218, 0.250);
    RemoveBuildingForPlayer(playerid, 1226, 2675.570, -2466.850, 16.414, 0.250);
}

CreateOceanDocksExt()
{
    new STREAMER_TAG_OBJECT: ocdxetx;
    ocdxetx = CreateDynamicObject(19377, 2624.094726, -2358.020507, 7.452033, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2629.243164, -2358.020507, 7.452033, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2623.853759, -2350.375244, 12.176714, 0.000000, 90.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2648.802246, -2350.375244, 12.176715, 0.000000, 90.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2673.723144, -2350.375244, 12.176715, 0.000000, 90.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2698.671630, -2350.375244, 12.196716, 0.000000, 90.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2638.860839, -2358.020507, 7.452033, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2648.488525, -2358.020507, 7.452033, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2658.106933, -2358.020507, 7.452033, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2667.735839, -2358.020507, 7.452033, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2623.853759, -2375.379394, 12.176715, 0.000000, 90.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2648.802246, -2375.379394, 12.176715, 0.000000, 90.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2673.723144, -2375.379394, 12.176715, 0.000000, 90.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2698.671630, -2375.379394, 12.186716, 0.000000, 90.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2677.363769, -2358.020507, 7.452033, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2686.983154, -2358.020507, 7.452033, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2628.065185, -2371.421386, 7.452033, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2629.243164, -2371.421386, 7.452033, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2638.860839, -2371.421386, 7.452033, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2648.488525, -2371.421386, 7.452033, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2658.106933, -2371.421386, 7.452033, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2667.735839, -2371.421386, 7.452033, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2677.363769, -2371.421386, 7.452033, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2686.983154, -2371.421386, 7.452033, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2623.888427, -2364.750244, 12.613038, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2629.251708, -2364.750244, 12.612036, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2638.881103, -2364.750244, 12.612036, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2648.517822, -2364.750244, 12.612036, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2658.147949, -2364.750244, 12.612036, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2667.777343, -2364.750244, 12.612036, 0.000014, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2677.426025, -2364.750244, 12.612036, 0.000022, 90.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2687.056152, -2364.750244, 12.612036, 0.000022, 90.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2696.685546, -2364.750244, 12.612036, 0.000022, 90.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2625.680175, -2377.237548, 7.452044, -0.000022, 0.000000, 0.000068, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2625.680175, -2381.857910, 7.452044, -0.000022, 0.000000, 0.000068, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2687.014648, -2386.766357, 7.452033, -0.000037, 0.000000, -89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2677.383789, -2386.766357, 7.452033, -0.000037, 0.000000, -89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2667.733398, -2386.766357, 7.452033, -0.000045, 0.000000, -89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2617.155517, -2368.825927, 12.602037, 0.000018, 90.000015, 314.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2610.350097, -2375.629882, 12.602037, 0.000018, 90.000015, 134.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2605.428222, -2380.547851, 12.602037, 0.000018, 90.000015, 134.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2617.186523, -2377.520996, 7.452033, -0.000001, 0.000011, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2610.388916, -2384.317626, 7.452033, -0.000001, 0.000011, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2614.461425, -2362.856201, 7.452033, 0.000009, -0.000011, 135.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2686.984619, -2357.179443, 7.452033, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2607.651123, -2369.665527, 7.452033, 0.000009, -0.000011, 135.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2601.444091, -2375.873535, 7.462034, 0.000009, -0.000011, 135.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2617.151367, -2350.375244, 12.166711, 0.000000, 90.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    ocdxetx = CreateDynamicObject(18981, 2609.871337, -2375.379394, 12.146725, 0.000000, 90.000045, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2619.363281, -2366.618164, 12.609036, 0.000018, 90.000015, 134.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2619.917236, -2374.790283, 7.452033, -0.000001, 0.000011, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2615.919677, -2361.397949, 7.452033, 0.000009, -0.000011, 135.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2677.353759, -2357.179443, 7.452033, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2667.703369, -2357.179443, 7.452033, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2658.072509, -2357.179443, 7.452033, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2648.433593, -2357.179443, 7.452033, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2638.802734, -2357.179443, 7.452033, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2633.711669, -2357.179443, 7.452033, -0.000022, 0.000000, -89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2624.080810, -2357.179443, 7.452033, -0.000022, 0.000000, -89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2619.349853, -2352.290283, 7.452033, -0.000022, 0.000000, 0.000068, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2619.349853, -2343.129150, 7.452031, -0.000022, 0.000000, 0.000068, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2686.984619, -2338.228027, 7.452033, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2677.353759, -2338.228027, 7.452033, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2667.703369, -2338.228027, 7.452033, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2658.072509, -2338.228027, 7.452033, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2648.433593, -2338.228027, 7.452033, -0.000022, 0.000000, -89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2638.802734, -2338.228027, 7.452033, -0.000022, 0.000000, -89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2633.711669, -2338.228027, 7.452033, -0.000029, 0.000000, -89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2624.080810, -2338.228027, 7.452033, -0.000029, 0.000000, -89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2687.014648, -2372.335449, 7.452033, -0.000029, 0.000000, -89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2677.383789, -2372.335449, 7.452033, -0.000029, 0.000000, -89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2667.733398, -2372.335449, 7.452033, -0.000037, 0.000000, -89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2658.102539, -2372.335449, 7.452033, -0.000037, 0.000000, -89.999885, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2648.463623, -2372.335449, 7.452033, -0.000045, 0.000000, -89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2638.832763, -2372.335449, 7.452033, -0.000045, 0.000000, -89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2633.741699, -2372.335449, 7.452033, -0.000052, 0.000000, -89.999839, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2630.410156, -2372.335449, 7.452029, -0.000052, 0.000000, -89.999839, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2658.102539, -2386.766357, 7.452033, -0.000045, 0.000000, -89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2648.463623, -2386.766357, 7.452033, -0.000052, 0.000000, -89.999839, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2638.832763, -2386.766357, 7.452033, -0.000052, 0.000000, -89.999839, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2633.741699, -2386.766357, 7.452033, -0.000060, 0.000000, -89.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2630.410156, -2386.766357, 7.452029, -0.000060, 0.000000, -89.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2691.740966, -2377.237548, 7.452044, -0.000022, 0.000007, 0.000068, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2691.740966, -2381.857910, 7.452044, -0.000022, 0.000007, 0.000068, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2691.708007, -2352.290283, 7.452033, -0.000022, 0.000007, 0.000068, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2691.708007, -2343.129150, 7.452031, -0.000022, 0.000007, 0.000068, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2696.632324, -2371.421386, 7.452033, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2704.351562, -2371.421386, 7.452033, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2709.080810, -2366.520996, 7.452033, 0.000007, 0.000000, 179.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2696.612304, -2358.020507, 7.452033, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2704.351562, -2358.020507, 7.452033, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(19377, 2709.080810, -2362.919921, 7.452033, 0.000007, 0.000000, 179.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    ocdxetx = CreateDynamicObject(3474, 2593.437011, -2422.625976, 19.388738, 0.000000, 0.000000, 225.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 7, 19278, "skydiveplatforms", "hazardtile19-2", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 9, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 11, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(2934, 2591.469482, -2419.625488, 19.790620, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19089, 2591.915283, -2418.394287, 26.580625, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    ocdxetx = CreateDynamicObject(19089, 2590.267822, -2420.041748, 26.580625, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    ocdxetx = CreateDynamicObject(19089, 2590.112060, -2420.197509, 26.580625, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    ocdxetx = CreateDynamicObject(19089, 2592.057128, -2418.252441, 26.580625, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    ocdxetx = CreateDynamicObject(8651, 2825.227539, -2394.140625, 9.856811, -9.000000, 90.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(8651, 2825.227539, -2392.149658, 9.856811, -9.000000, 90.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(8651, 2825.227539, -2390.157714, 9.856811, -9.000000, 90.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(8651, 2825.227539, -2388.166015, 9.856811, -9.000000, 90.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(8651, 2825.227539, -2386.166015, 9.856811, -9.000000, 90.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(8651, 2825.227539, -2384.175781, 9.856811, -9.000000, 90.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(8651, 2825.542968, -2382.475097, 9.796753, 9.000000, 90.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(3458, 2760.165771, -2377.492187, 11.157977, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 2, 4595, "crparkgm_lan2", "sl_dtcparklines1", 0x00000000);
    ocdxetx = CreateDynamicObject(8651, 2825.227539, -2443.723632, 9.856810, -9.000005, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(8651, 2825.227539, -2441.732666, 9.856810, -9.000005, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(8651, 2825.227539, -2439.740722, 9.856810, -9.000005, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(8651, 2825.227539, -2437.749023, 9.856810, -9.000005, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(8651, 2825.227539, -2435.749023, 9.856810, -9.000005, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(8651, 2825.227539, -2433.758789, 9.856810, -9.000005, 90.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(8651, 2825.542968, -2432.058105, 9.796752, 9.000005, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2813.406005, -2381.620361, 12.185854, 9.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2818.343017, -2381.620361, 11.403685, 9.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2813.406005, -2394.941162, 12.185854, 9.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2818.343017, -2394.941162, 11.403685, 9.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2813.406005, -2431.229980, 12.185853, 9.000005, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2818.343017, -2431.229980, 11.403684, 9.000005, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2813.406005, -2444.550781, 12.185853, 9.000020, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2818.343017, -2444.550781, 11.403684, 9.000020, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    ocdxetx = CreateDynamicObject(3626, 2785.360107, -2375.269775, 14.171895, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19538, 2672.582275, -2361.531738, 12.697976, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(19545, 2626.541748, -2365.832031, 12.686724, 0.000000, 0.000000, 135.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(19545, 2613.410400, -2357.779785, 12.686724, 0.000000, 0.000000, 135.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(19545, 2611.919921, -2361.559570, 12.686724, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(3578, 2590.755615, -2429.181640, 13.171895, 0.000000, 0.000000, -45.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2573.345214, -2421.947753, 13.171895, 0.000000, 0.000000, -135.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2582.687500, -2421.113525, 13.171895, 0.000000, 0.000000, -45.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19481, 2788.425048, -2472.718994, 12.702806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(3578, 2591.441406, -2411.892333, 13.171895, 0.000000, 0.000000, -45.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2599.559814, -2420.010742, 13.171895, 0.000000, 0.000000, -45.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2582.793212, -2403.244140, 13.171895, 0.000000, 0.000000, -45.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2587.029296, -2399.008056, 13.171895, 0.000000, 0.000000, -45.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2595.656494, -2407.621093, 13.171895, 0.000000, 0.000000, -45.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2603.634277, -2415.570556, 13.171895, 0.000000, 0.000000, -45.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2563.077392, -2411.679931, 13.171895, 0.000000, 0.000000, -135.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2552.265869, -2400.868408, 13.171895, 0.000000, 0.000000, -135.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19538, 2775.730712, -2392.843505, 12.685003, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19595, "lsappartments1", "carpet4-256x256", 0x00000000);
    ocdxetx = CreateDynamicObject(19538, 2775.730712, -2499.614990, 12.685003, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19595, "lsappartments1", "carpet4-256x256", 0x00000000);
    ocdxetx = CreateDynamicObject(19481, 2788.425048, -2394.412841, 12.692806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2788.425048, -2378.531738, 12.692806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2788.425048, -2391.882324, 12.692806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(7191, 2806.665527, -2353.352050, 14.252569, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(7191, 2784.266357, -2330.891113, 14.252569, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(7191, 2739.344970, -2330.891113, 14.252569, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19481, 2766.454589, -2386.110839, 12.702806, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "__", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(9131, 2806.685546, -2380.229003, 15.138737, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19545, 2727.632080, -2361.614990, 12.672812, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19595, "lsappartments1", "carpet4-256x256", 0x00000000);
    ocdxetx = CreateDynamicObject(19477, 1341.798461, 1574.664062, 10.870310, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "Apakah\nBetul", 130, "Arial", 45, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19545, 2712.652832, -2361.614990, 12.672812, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19595, "lsappartments1", "carpet4-256x256", 0x00000000);
    ocdxetx = CreateDynamicObject(19545, 2697.663818, -2361.614990, 12.672812, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19595, "lsappartments1", "carpet4-256x256", 0x00000000);
    ocdxetx = CreateDynamicObject(19545, 2682.674072, -2361.614990, 12.672812, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19595, "lsappartments1", "carpet4-256x256", 0x00000000);
    ocdxetx = CreateDynamicObject(19545, 2554.243652, -2376.052734, 12.678754, 0.000000, 0.000000, 225.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14876, "gf4", "mp_tank_floor", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19545, 2543.658203, -2386.651855, 12.678754, 0.000000, 0.000000, 225.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14876, "gf4", "mp_tank_floor", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19545, 2533.065917, -2397.272460, 12.678754, 0.000000, 0.000000, 225.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14876, "gf4", "mp_tank_floor", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19545, 2598.431396, -2420.240478, 12.678753, 0.000000, 0.000000, 225.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14876, "gf4", "mp_tank_floor", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19545, 2587.832275, -2430.825927, 12.678754, 0.000000, 0.000000, 225.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14876, "gf4", "mp_tank_floor", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19545, 2560.034179, -2408.389160, 12.668754, 0.000000, 0.000000, 225.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14876, "gf4", "mp_tank_floor", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19545, 2634.853271, -2451.549560, 12.668754, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14876, "gf4", "mp_tank_floor", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19545, 2634.853271, -2436.570556, 12.668754, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14876, "gf4", "mp_tank_floor", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19545, 2634.853271, -2422.890869, 12.658755, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 14876, "gf4", "mp_tank_floor", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(9131, 2806.685546, -2380.229003, 12.868740, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19538, 2751.218994, -2499.621582, 12.695002, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19595, "lsappartments1", "carpet4-256x256", 0x00000000);
    ocdxetx = CreateDynamicObject(19538, 2751.218994, -2392.843505, 12.675003, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19595, "lsappartments1", "carpet4-256x256", 0x00000000);
    ocdxetx = CreateDynamicObject(19481, 2788.425048, -2428.084472, 12.692806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2788.425048, -2441.335205, 12.702806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2788.425048, -2399.624267, 12.702806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2788.425048, -2405.144775, 12.702806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2788.425048, -2410.224853, 12.702806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2788.425048, -2415.745605, 12.702806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2788.425048, -2425.587646, 12.702806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2788.425048, -2420.706298, 12.702806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2788.425048, -2377.700927, 12.692806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2788.425048, -2442.201171, 12.702806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(10831, 2649.253417, -2437.977539, 17.564996, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19481, 2803.935791, -2457.348876, 12.712805, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2803.935791, -2420.018554, 12.712805, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2803.935791, -2399.327880, 12.712805, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2766.454589, -2392.201416, 12.702806, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "__", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2766.454589, -2434.001953, 12.702806, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "__", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2766.454589, -2439.972900, 12.722805, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "__", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2761.085449, -2456.061767, 12.702806, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "____", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2783.065185, -2442.201171, 12.702806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2783.054931, -2472.718994, 12.702806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2761.085449, -2465.022705, 12.702806, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "____", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2738.654541, -2398.662841, 12.692806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2738.654541, -2406.083496, 12.692806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2738.654541, -2497.043457, 12.712805, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2738.654541, -2504.734619, 12.712805, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2754.375732, -2481.683593, 12.712805, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2754.375732, -2427.682617, 12.712805, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2757.963378, -2377.721191, 12.692806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2725.483154, -2384.020019, 12.702806, 0.000000, 90.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "__", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2738.654541, -2391.722167, 12.692806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2754.374755, -2399.222167, 12.702806, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "_", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2754.374755, -2397.432128, 12.692806, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "_", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2761.085449, -2477.502685, 12.702806, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "____", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2769.488037, -2491.793212, 12.702806, 0.000000, 90.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "__", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2769.488037, -2502.413330, 12.702806, 0.000000, 90.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "__", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2761.085449, -2502.222167, 12.702806, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "_", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2761.085449, -2505.243164, 12.712805, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "_", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(17072, 2807.666503, -2542.933349, 17.264993, 0.000000, 0.000000, 267.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 2, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(3578, 2612.543457, -2421.499511, 13.171895, 0.000000, 0.000000, 157.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3073, 2560.055175, -2456.099365, 14.298764, 0.000000, 0.000000, 310.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 18018, "genintintbarb", "Gen_Gantry_Rust", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 12992, "ce_oldbridge", "Gen_Rusty_Poll", 0x00000000);
    ocdxetx = CreateDynamicObject(2932, 2579.631347, -2422.698730, 14.112575, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(2934, 2573.960449, -2425.542480, 14.122575, 0.000000, 0.000000, 135.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(2935, 2580.926025, -2436.947509, 14.062575, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(2934, 2576.542724, -2428.124755, 14.122575, 0.000000, 0.000000, 135.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(2932, 2585.316650, -2428.384033, 14.112575, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(2934, 2573.841308, -2428.350585, 17.012580, 0.000000, 0.000000, 225.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(2932, 2576.710937, -2425.619140, 17.022577, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(2935, 2578.344970, -2439.528564, 14.062575, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(2935, 2575.643554, -2442.229980, 14.062575, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(10252, 2693.740478, -2451.532958, 14.285869, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(10252, 2695.100830, -2457.842773, 14.285869, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2541.602050, -2390.204589, 13.171895, 0.000000, 0.000000, -135.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2533.746337, -2382.348876, 13.171895, 0.000000, 0.000000, -135.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2765.570312, -2451.056396, 13.411464, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19481, 2788.425048, -2434.779296, 12.702806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(9131, 2720.245849, -2380.229003, 15.138736, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(9131, 2720.245849, -2380.229003, 12.878733, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(9131, 2730.556396, -2380.229003, 15.138736, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(9131, 2730.556396, -2380.229003, 12.868739, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(9131, 2739.476318, -2380.229003, 15.138736, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(9131, 2739.476318, -2380.229003, 12.878738, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(9131, 2720.245849, -2383.149902, 15.138736, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(9131, 2720.245849, -2383.149902, 12.878739, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19461, 2590.352539, -2406.679199, 12.903143, 0.000009, 90.000015, 44.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16640, "a51", "sl_metalwalk", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2597.162353, -2413.489013, 12.903145, 0.000009, 90.000015, 44.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16640, "a51", "sl_metalwalk", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2603.943603, -2420.270263, 12.481842, 5.000010, 90.000015, 44.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16640, "a51", "sl_metalwalk", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2602.557861, -2421.472900, 10.987551, 5.000010, 180.000015, 44.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17036, "cuntwbt", "bluemetal05", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2605.167480, -2418.863281, 10.987551, 5.000010, 180.000015, 44.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17036, "cuntwbt", "bluemetal05", 0x00000000);
    ocdxetx = CreateDynamicObject(2789, 2725.289794, -2380.360351, 14.455002, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 15041, "bigsfsave", "ah_greencarp", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(9131, 2720.245849, -2352.448242, 15.138735, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(9131, 2720.245849, -2352.448730, 12.888737, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(9131, 2734.205566, -2352.538085, 15.138735, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(9131, 2734.205566, -2352.529052, 12.868738, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(9131, 2734.205566, -2330.959228, 15.138736, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(9131, 2734.205566, -2330.959228, 12.878735, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(1685, 2712.298095, -2448.795410, 13.352414, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(969, 2720.615478, -2401.562744, 12.825704, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 3, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(969, 2720.615478, -2508.010742, 12.825704, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 3, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(3578, 2505.305419, -2380.285400, 13.171895, 0.000000, 0.000000, -45.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2520.020751, -2395.000732, 13.171895, 0.000000, 0.000000, -45.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2494.351074, -2369.331054, 13.171895, 0.000000, 0.000000, -45.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2486.056884, -2361.036376, 13.171895, 0.000000, 0.000000, -45.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19461, 2595.867675, -2414.783691, 11.553142, 0.000009, 180.000015, 44.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17036, "cuntwbt", "bluemetal05", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2598.477783, -2412.173583, 11.553142, 0.000009, 180.000015, 44.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17036, "cuntwbt", "bluemetal05", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2591.666748, -2405.362548, 11.553142, 0.000009, 180.000015, 44.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17036, "cuntwbt", "bluemetal05", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2589.058349, -2407.974365, 11.553142, 0.000009, 180.000015, 44.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17036, "cuntwbt", "bluemetal05", 0x00000000);
    ocdxetx = CreateDynamicObject(19817, 2658.838867, -2445.918945, 10.838748, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2583.591796, -2399.890136, 12.479891, 5.000010, 90.000015, 224.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16640, "a51", "sl_metalwalk", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2582.389648, -2401.305175, 10.997305, 5.000010, 180.000015, 224.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17036, "cuntwbt", "bluemetal05", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2584.992675, -2398.702148, 10.997305, 5.000010, 180.000015, 224.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17036, "cuntwbt", "bluemetal05", 0x00000000);
    ocdxetx = CreateDynamicObject(3578, 2532.083007, -2353.507812, 13.171895, 0.000000, 0.000000, 44.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2541.678466, -2352.426269, 13.171895, 0.000000, 0.000000, 134.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(3578, 2521.611572, -2363.979248, 13.171895, 0.000000, 0.000000, 44.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 12992, "ce_oldbridge", "BLOCK2_high", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19817, 2651.108154, -2445.918945, 10.838748, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(19817, 2643.327636, -2445.918945, 10.838748, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2669, "cj_chris", "cj_metalplate2", 0x00000000);
    ocdxetx = CreateDynamicObject(19089, 2591.073730, -2421.159179, 26.580625, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    ocdxetx = CreateDynamicObject(19089, 2591.215332, -2421.017578, 26.580625, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    ocdxetx = CreateDynamicObject(19089, 2592.884765, -2419.348144, 26.580625, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    ocdxetx = CreateDynamicObject(19089, 2593.026367, -2419.206542, 26.580625, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    ocdxetx = CreateDynamicObject(2932, 2579.587402, -2438.311523, 16.952577, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(2932, 2577.013671, -2440.885253, 16.952577, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFF009900);
    SetDynamicObjectMaterial(ocdxetx, 1, -1, "none", "none", 0xFF009933);
    ocdxetx = CreateDynamicObject(2934, 2593.429687, -2457.397949, 14.122574, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(2934, 2601.409912, -2457.397949, 14.122574, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19481, 2788.425048, -2384.882324, 12.692806, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "___________________", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19866, 2786.854248, -2540.398437, 12.425004, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2567, "ab", "ab_metaledge", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2786.854248, -2529.588134, 12.425004, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2567, "ab", "ab_metaledge", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2786.854248, -2545.387695, 12.425004, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2567, "ab", "ab_metaledge", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2786.864257, -2549.528320, 12.425004, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2567, "ab", "ab_metaledge", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2787.716308, -2551.734375, 11.604998, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2797.346923, -2551.804443, 14.455000, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2796.315917, -2551.794433, 14.445000, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2787.716308, -2551.734375, 15.104999, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2806.926513, -2549.528320, 12.425004, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2567, "ab", "ab_metaledge", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2806.926513, -2544.547607, 12.425004, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2567, "ab", "ab_metaledge", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2806.926513, -2529.547363, 12.425004, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2567, "ab", "ab_metaledge", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2806.926513, -2534.548095, 12.425004, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2567, "ab", "ab_metaledge", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2806.926513, -2539.549560, 12.425004, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2567, "ab", "ab_metaledge", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2787.846435, -2527.283691, 11.604998, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2787.846435, -2527.283691, 15.104997, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2796.467529, -2527.283691, 15.124999, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2797.498046, -2527.273681, 15.134998, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2797.498046, -2527.283691, 11.634996, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2796.467529, -2527.293701, 11.635004, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2806.098876, -2527.283691, 15.105003, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2792.078857, -2547.199462, 20.542581, 0.000000, -56.199996, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2791.655273, -2547.199462, 20.826284, 0.000000, -56.199996, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2788.429931, -2547.199462, 20.556623, 0.000000, -56.199996, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2788.828369, -2547.199462, 20.823640, 0.000000, -56.199996, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2798.750244, -2547.199462, 20.542581, 0.000000, -56.199989, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2798.326660, -2547.199462, 20.826284, 0.000000, -56.199989, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2795.101318, -2547.199462, 20.556623, 0.000000, -56.200004, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2795.499755, -2547.199462, 20.823640, 0.000000, -56.200004, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2805.421875, -2547.199462, 20.542581, 0.000000, -56.199981, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2804.998291, -2547.199462, 20.826284, 0.000000, -56.199981, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2801.772949, -2547.199462, 20.556623, 0.000000, -56.200012, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.171386, -2547.199462, 20.823640, 0.000000, -56.200012, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2792.078857, -2537.568359, 20.542581, 0.000000, -56.199989, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2791.655273, -2537.568359, 20.826284, 0.000000, -56.199989, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2788.429931, -2537.568359, 20.556623, 0.000000, -56.200004, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2788.828369, -2537.568359, 20.823640, 0.000000, -56.200004, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2798.750244, -2537.568359, 20.542581, 0.000000, -56.199981, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2798.326660, -2537.568359, 20.826284, 0.000000, -56.199981, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2795.101318, -2537.568359, 20.556623, 0.000000, -56.200012, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2795.499755, -2537.568359, 20.823640, 0.000000, -56.200012, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2805.421875, -2537.568359, 20.542581, 0.000000, -56.199974, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2804.998291, -2537.568359, 20.826284, 0.000000, -56.199974, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2801.772949, -2537.568359, 20.556623, 0.000000, -56.200019, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.171386, -2537.568359, 20.823640, 0.000000, -56.200019, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2792.078857, -2531.877929, 20.542581, 0.000000, -56.199981, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2791.655273, -2531.877929, 20.826284, 0.000000, -56.199981, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2788.429931, -2531.877929, 20.556623, 0.000000, -56.200012, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2788.833984, -2531.877929, 20.815330, 0.000000, -56.200012, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2798.750244, -2531.877929, 20.542581, 0.000000, -56.199974, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2798.326660, -2531.877929, 20.826284, 0.000000, -56.199974, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2795.101318, -2531.877929, 20.556623, 0.000000, -56.200019, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2795.499755, -2531.877929, 20.823640, 0.000000, -56.200019, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2805.421875, -2531.877929, 20.542581, 0.000000, -56.199966, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2804.998291, -2531.877929, 20.826284, 0.000000, -56.199966, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2801.772949, -2531.877929, 20.556623, 0.000000, -56.200027, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19454, 2802.171386, -2531.877929, 20.823640, 0.000000, -56.200027, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3629, "arprtxxref_las", "corrRoof_64HV", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2806.810546, -2532.165039, 17.825008, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2806.810546, -2541.785644, 17.825008, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2805.947509, -2551.734375, 11.604998, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2805.947509, -2551.734375, 15.094996, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2806.800537, -2546.995361, 17.825008, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2801.930908, -2551.735595, 17.935010, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2791.749511, -2551.735595, 17.935010, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2806.800537, -2546.995361, 14.325008, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2806.800537, -2537.365478, 14.325008, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2806.810546, -2532.175537, 14.325008, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2796.966552, -2551.724365, 17.955005, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2787.031005, -2546.995361, 17.825008, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2787.031005, -2537.364746, 17.825008, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2787.010986, -2532.134521, 17.825008, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2791.953369, -2529.542236, 12.435006, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot08", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2801.953857, -2529.542236, 12.435006, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot08", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2801.953857, -2534.533203, 12.435006, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot08", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2791.953369, -2540.492675, 12.435006, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot08", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2801.953857, -2539.524169, 12.435006, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot08", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2801.953857, -2544.513183, 12.435006, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot08", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2801.953857, -2549.323242, 12.435006, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot08", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2791.953369, -2545.492919, 12.435006, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot08", 0x00000000);
    ocdxetx = CreateDynamicObject(18766, 2791.953369, -2549.323974, 12.435006, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot08", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2787.031005, -2546.995361, 14.335008, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2787.000976, -2542.704589, 14.335008, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2787.000976, -2530.315185, 14.335008, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2787.010986, -2529.044921, 11.265007, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2804.998779, -2527.283691, 17.675003, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2801.509033, -2527.283691, 17.675003, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2798.018066, -2527.283691, 17.675003, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2794.518554, -2527.283691, 17.675003, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2791.029052, -2527.283691, 17.675003, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2788.827880, -2527.273681, 17.665002, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(18980, 2793.612792, -2539.532714, 19.174997, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16640, "a51", "bonyrd_skin2", 0x00000000);
    ocdxetx = CreateDynamicObject(18980, 2800.043945, -2539.532714, 19.174997, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16640, "a51", "bonyrd_skin2", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2804.998779, -2527.273681, 18.755006, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2801.508544, -2527.273681, 18.755006, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2798.007812, -2527.273681, 18.755006, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2794.517578, -2527.273681, 18.755006, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2791.016845, -2527.273681, 18.755006, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19441, 2788.846435, -2527.263671, 18.755006, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17072, "truckedepotlawn", "GB_truckdepot03", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2792.421386, -2527.314697, 19.007873, 34.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2790.572753, -2527.314697, 20.254863, 34.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2789.764404, -2527.314697, 20.217231, 34.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2788.380126, -2527.314697, 19.283384, 34.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19908, 2791.062011, -2527.240234, 14.475006, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7985, "vgsswarehse02c", "GB_truckdepot12", 0x00000000);
    ocdxetx = CreateDynamicObject(19908, 2793.232177, -2527.270263, 14.475006, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7985, "vgsswarehse02c", "GB_truckdepot12", 0x00000000);
    ocdxetx = CreateDynamicObject(19908, 2799.931884, -2527.240234, 14.475006, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7985, "vgsswarehse02c", "GB_truckdepot12", 0x00000000);
    ocdxetx = CreateDynamicObject(19908, 2803.102783, -2527.260253, 14.475006, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7985, "vgsswarehse02c", "GB_truckdepot12", 0x00000000);
    ocdxetx = CreateDynamicObject(19908, 2803.102783, -2551.781250, 14.475006, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7985, "vgsswarehse02c", "GB_truckdepot12", 0x00000000);
    ocdxetx = CreateDynamicObject(19908, 2799.042968, -2551.811279, 14.475006, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7985, "vgsswarehse02c", "GB_truckdepot12", 0x00000000);
    ocdxetx = CreateDynamicObject(19908, 2794.523193, -2551.811279, 14.475006, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7985, "vgsswarehse02c", "GB_truckdepot12", 0x00000000);
    ocdxetx = CreateDynamicObject(19908, 2791.102783, -2551.781250, 14.475006, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 7985, "vgsswarehse02c", "GB_truckdepot12", 0x00000000);
    ocdxetx = CreateDynamicObject(2774, 2733.218994, -2447.622070, 12.695001, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 1, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 2, 3080, "adjumpx", "rustyboltpanel", 0x00000000);
    ocdxetx = CreateDynamicObject(2774, 2733.218994, -2461.633056, 12.695001, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3080, "adjumpx", "rustyboltpanel", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 10765, "airportgnd_sfse", "ws_oldpainted2rusty", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 2, 3080, "adjumpx", "rustyboltpanel", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2733.343750, -2458.398681, 24.334999, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3080, "adjumpx", "rustyboltpanel", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2733.343750, -2453.418945, 24.334999, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3080, "adjumpx", "rustyboltpanel", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2733.343750, -2449.128662, 24.334999, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 3080, "adjumpx", "rustyboltpanel", 0x00000000);
    ocdxetx = CreateDynamicObject(19461, 2733.338134, -2454.771484, 22.344997, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 5397, "barrio1_lae", "rufwaldock1", 0x00000000);
    ocdxetx = CreateDynamicObject(19479, 2733.465576, -2454.809082, 22.924991, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "OCEAN", 130, "Georgia", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19479, 2733.455566, -2454.809082, 21.824991, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "DOCKS", 130, "Georgia", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19479, 2733.435546, -2454.759033, 22.894990, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "OCEAN", 130, "Georgia", 50, 1, 0xFF252525, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19479, 2733.445556, -2454.759033, 21.784990, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "DOCKS", 130, "Georgia", 50, 1, 0xFF252525, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19866, 2820.630126, -2433.605712, 10.955782, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19278, "skydiveplatforms", "hazardtile19-2", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2820.630126, -2442.175537, 10.955782, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19278, "skydiveplatforms", "hazardtile19-2", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2820.630126, -2437.865234, 10.945782, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19278, "skydiveplatforms", "hazardtile19-2", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2810.219482, -2437.865234, 12.545783, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19278, "skydiveplatforms", "hazardtile19-2", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2810.219482, -2433.573486, 12.555784, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19278, "skydiveplatforms", "hazardtile19-2", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2810.219482, -2442.254394, 12.555784, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19278, "skydiveplatforms", "hazardtile19-2", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2799.130126, -2527.314697, 19.007873, 34.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2797.281494, -2527.314697, 20.254863, 34.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2796.473144, -2527.314697, 20.217231, 33.999988, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2795.088867, -2527.314697, 19.283384, 33.999988, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2805.840332, -2527.314697, 19.007873, 34.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2803.991699, -2527.314697, 20.254863, 34.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2803.183349, -2527.314697, 20.217231, 33.999977, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2801.799072, -2527.314697, 19.283384, 33.999977, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2792.421386, -2551.675048, 19.007873, 34.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2790.572753, -2551.675048, 20.254863, 34.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2789.764404, -2551.675048, 20.217231, 33.999988, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2788.380126, -2551.675048, 19.283384, 33.999988, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2799.130126, -2551.675048, 19.007873, 34.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2797.281494, -2551.675048, 20.254863, 34.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2796.473144, -2551.675048, 20.217231, 33.999977, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2795.088867, -2551.675048, 19.283384, 33.999977, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2805.840332, -2551.675048, 19.007873, 34.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2803.991699, -2551.675048, 20.254863, 34.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2803.183349, -2551.675048, 20.217231, 33.999969, 0.000000, -89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19466, 2801.799072, -2551.675048, 19.283384, 33.999969, 0.000000, -89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2796.836914, -2535.537597, 12.845005, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19278, "skydiveplatforms", "hazardtile19-2", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2796.836914, -2534.536865, 12.845005, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19278, "skydiveplatforms", "hazardtile19-2", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2789.595703, -2531.876220, 12.994998, 0.000014, 180.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16640, "a51", "concreteyellow256 copy", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19866, 2794.345947, -2531.876220, 12.994998, 0.000014, 180.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16640, "a51", "concreteyellow256 copy", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19866, 2789.595703, -2538.185302, 12.994998, 0.000022, 180.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16640, "a51", "concreteyellow256 copy", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19866, 2794.345947, -2538.185302, 12.994998, 0.000022, 180.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16640, "a51", "concreteyellow256 copy", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(18980, 2806.255126, -2539.532714, 19.174997, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16640, "a51", "bonyrd_skin2", 0x00000000);
    ocdxetx = CreateDynamicObject(18980, 2787.554443, -2539.532714, 19.174997, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 16640, "a51", "bonyrd_skin2", 0x00000000);
    ocdxetx = CreateDynamicObject(2271, 2784.775878, -2371.472167, 14.285003, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 1, 5731, "melrose15_lawn", "yoyojos_law", 0x00000000);
    ocdxetx = CreateDynamicObject(2710, 2784.070556, -2371.234863, 13.946027, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 4, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(2222, 2784.220703, -2371.292480, 13.926030, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 1, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 2, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 3, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 4, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 5, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 6, 19297, "matlights", "invisible", 0x00000000);
    SetDynamicObjectMaterial(ocdxetx, 8, 2702, "pick_up", "cj_fambly", 0x00000000);
    ocdxetx = CreateDynamicObject(11391, 2640.778076, -2425.045898, 13.945001, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    SetDynamicObjectMaterial(ocdxetx, 1, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(18762, 2664.069091, -2433.722167, 13.468751, 90.000000, -7.599997, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(18762, 2664.423583, -2437.910156, 12.668753, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(18762, 2664.423583, -2440.349853, 12.668753, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(18762, 2664.423583, -2439.089599, 12.668753, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(18762, 2663.663574, -2440.350341, 12.668753, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(18762, 2663.893798, -2437.959960, 12.668753, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(18762, 2664.504638, -2437.260742, 11.458672, -22.600002, 0.000000, 18.800001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(18762, 2663.682128, -2433.670898, 13.468751, 90.000000, -0.799997, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(18762, 2647.689697, -2424.622802, 12.708756, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2652.452636, -2427.589355, 13.608754, 90.000000, 44.800006, 44.799995, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2653.382080, -2429.186279, 13.608754, 90.000000, 44.800006, 134.799987, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(18762, 2646.048828, -2424.622802, 12.708756, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(18762, 2643.718261, -2424.422607, 11.078755, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(18762, 2642.477539, -2424.422607, 11.078755, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(18762, 2638.366210, -2424.632812, 12.718757, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(18762, 2636.706298, -2424.632812, 12.718757, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(18762, 2640.135742, -2424.878417, 11.108759, 0.000000, 0.000000, -15.699998, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(18762, 2639.210449, -2425.207031, 11.108759, 0.000000, 0.000000, -52.700000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2656.780273, -2426.684082, 12.848752, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2658.002929, -2426.746093, 12.848752, 0.000000, 0.000000, 44.799995, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2658.220214, -2425.530761, 12.848752, 0.000000, 0.000000, 44.799995, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2656.919433, -2425.513427, 12.848752, 0.000000, 0.000000, 44.799995, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2655.220214, -2426.271728, 13.608754, 0.000000, 0.000000, 44.799995, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2651.551513, -2429.173828, 13.608754, 90.000000, 44.800006, 134.799987, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2651.552001, -2426.224365, 13.608754, 90.000000, 45.400005, 134.799987, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2653.341796, -2426.217529, 13.608754, 90.000000, 45.400005, 134.799987, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2657.438232, -2429.205566, 12.428749, 0.000000, 0.000000, 13.999997, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2650.352294, -2426.417724, 12.428749, 0.000000, 0.000000, 13.999997, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2649.172607, -2425.774414, 12.058748, 0.000000, 0.000000, 13.999997, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2659.912841, -2429.231689, 13.608754, 90.000000, 44.800006, 134.799987, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2659.934082, -2426.111083, 13.608754, 90.000000, 44.800006, 134.799987, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2660.752929, -2427.647949, 13.608754, 90.000000, 44.800006, 44.799995, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2661.763183, -2429.245361, 13.608754, 90.000000, 44.800006, 134.799987, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2661.784667, -2426.124755, 13.608754, 90.000000, 44.800006, 134.799987, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2659.940185, -2431.223144, 13.162944, 66.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2661.750244, -2431.223144, 13.162944, 66.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2661.750244, -2432.044189, 12.796878, 66.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2659.919677, -2432.044189, 12.796878, 66.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(9131, 2655.829833, -2441.465087, 12.618748, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19297, "matlights", "invisible", 0x00000000);
    ocdxetx = CreateDynamicObject(12930, 2751.500244, -2497.742675, 13.514995, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(12930, 2736.619628, -2492.182373, 13.514995, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(12930, 2745.410400, -2515.362792, 13.514995, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19481, 2761.085449, -2510.671875, 12.702806, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "_", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19481, 2754.005126, -2510.671875, 12.702806, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "_", 130, "Arial", 150, 0, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(12955, 2734.194580, -2425.232177, 15.225001, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(12955, 2720.052246, -2447.075195, 15.225001, 0.000000, 0.000000, 367.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(1499, 2722.612548, -2363.024414, 12.697976, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 1, 13363, "cephotoblockcs_t", "alleydoor3", 0x00000000);
    ocdxetx = CreateDynamicObject(19627, 2641.258056, -2446.681884, 13.553035, 0.000000, 0.000000, 98.799987, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 17003, "countrys", "rustc256128", 0x00000000);
    ocdxetx = CreateDynamicObject(16378, 2727.578125, -2358.693115, 16.936172, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(1721, 2720.139404, -2357.080810, 12.711874, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, -1, "none", "none", 0xFFFFFFFF);
    ocdxetx = CreateDynamicObject(19866, 2723.235595, -2253.795410, 15.172909, -30.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19092, "pompoms", "wood-64x64", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2723.235595, -2249.465087, 12.672909, -30.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19092, "pompoms", "wood-64x64", 0x00000000);
    ocdxetx = CreateDynamicObject(19479, 2590.370605, -2420.753906, 19.988765, 0.000000, 0.000000, 225.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "ANTIMITATION", 130, "Georgia", 30, 1, 0xFF00FFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19479, 2591.954345, -2422.323486, 18.768758, 0.000000, 0.000000, 225.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "San Andreas", 130, "Palatino Linotype", 20, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19479, 2592.599365, -2418.525146, 19.988765, 0.000000, 0.000000, 405.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "ANTIMITATION", 130, "Georgia", 30, 1, 0xFF00FFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19479, 2590.979492, -2416.919433, 18.768758, 0.000000, 0.000000, 405.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(ocdxetx, 0, "San Andreas", 130, "Palatino Linotype", 20, 1, 0xFFFFFFFF, 0x00000000, 1);
    ocdxetx = CreateDynamicObject(19866, 2810.219482, -2392.633544, 12.555784, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19278, "skydiveplatforms", "hazardtile19-2", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2810.219482, -2383.993408, 12.555784, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19278, "skydiveplatforms", "hazardtile19-2", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2810.209472, -2388.184814, 12.555784, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19278, "skydiveplatforms", "hazardtile19-2", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2820.660156, -2383.993896, 10.945781, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19278, "skydiveplatforms", "hazardtile19-2", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2820.660156, -2392.565429, 10.945781, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19278, "skydiveplatforms", "hazardtile19-2", 0x00000000);
    ocdxetx = CreateDynamicObject(19866, 2820.660156, -2388.544677, 10.945781, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 19278, "skydiveplatforms", "hazardtile19-2", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2766.407470, -2412.413574, 12.509793, 89.999992, 295.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2766.703857, -2412.441162, 12.509793, 89.999992, 245.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2766.578857, -2412.361816, 12.509793, 89.999992, 370.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2766.061035, -2412.448730, 12.509793, 89.999992, 90.799995, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2765.935791, -2412.770507, 12.509793, 89.999992, 180.799987, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2765.815185, -2412.611328, 12.509793, 89.999992, 214.099853, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2765.761962, -2412.273437, 12.509793, 89.999992, 150.999893, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2765.302490, -2412.475585, 12.509793, 89.999992, 90.499900, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2764.938476, -2412.480712, 12.509793, 89.999992, 120.499900, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2764.712158, -2412.495605, 12.509793, 89.999992, 70.199913, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2764.393798, -2412.465820, 12.509793, 89.999992, 90.499916, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2764.105957, -2412.768310, 12.509793, 89.999992, 180.499908, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2764.112792, -2412.458251, 12.509793, 89.999992, 180.499908, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2764.110107, -2412.158203, 12.509793, 89.999992, 180.499908, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2763.513671, -2412.473388, 12.509793, 89.999992, 90.499916, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2763.329345, -2412.493652, 12.509793, 89.999992, 122.099922, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2763.112304, -2412.535644, 12.509793, 89.999992, 82.499916, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2762.486816, -2412.413574, 12.509793, 89.999992, 269.471221, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2762.783203, -2412.441162, 12.509793, 89.999992, 219.471221, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2762.658203, -2412.361816, 12.509793, 89.999992, 344.471221, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2680.680419, -2473.804199, 12.346721, 89.499145, -64.999763, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2680.384033, -2473.776611, 12.344135, 89.499145, -114.999771, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2680.509033, -2473.855957, 12.345225, 89.499145, 10.000237, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2681.026855, -2473.769042, 12.349744, 89.499145, -269.199737, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2681.152099, -2473.447265, 12.350837, 89.499145, -179.199752, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2681.272705, -2473.606445, 12.351889, 89.499145, -145.899902, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2681.325927, -2473.944335, 12.352355, 89.499145, -208.999847, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2681.785400, -2473.742187, 12.356365, 89.499145, -269.499847, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2682.149414, -2473.737060, 12.359540, 89.499145, -239.499847, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2682.375732, -2473.722167, 12.361515, 89.499145, -289.799835, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2682.694091, -2473.751953, 12.364294, 89.499145, -269.499847, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2682.981933, -2473.449462, 12.366805, 89.499145, -179.499832, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2682.975097, -2473.759521, 12.366745, 89.499145, -179.499832, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2682.977783, -2474.059570, 12.366769, 89.499145, -179.499832, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2683.573974, -2473.744384, 12.371972, 89.499145, -269.499847, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2683.758544, -2473.724121, 12.373583, 89.499145, -237.899826, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2683.975585, -2473.682128, 12.375476, 89.499145, -277.499847, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2684.601074, -2473.804199, 12.380935, 89.499145, -64.999763, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2684.304687, -2473.776611, 12.378348, 89.499145, -114.999771, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    ocdxetx = CreateDynamicObject(1881, 2684.429687, -2473.855957, 12.379439, 89.499145, 10.000237, 89.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(ocdxetx, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(2932, 2686.912353, -2355.239990, 14.147610, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2686.912353, -2352.100585, 14.147610, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2678.664062, -2352.102294, 14.103649, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2686.905761, -2352.102294, 17.003658, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2686.927978, -2355.232421, 17.041276, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2686.927978, -2352.102294, 19.901275, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2686.912353, -2352.100585, 22.787603, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2678.664062, -2352.102294, 17.003652, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2678.687744, -2355.232421, 14.121254, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2678.679443, -2348.951416, 14.147610, 0.000007, -0.000007, -90.000038, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2686.927734, -2348.949707, 14.103649, 0.000014, -0.000007, -90.000053, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2678.686035, -2348.949707, 17.003658, 0.000007, -0.000007, -90.000038, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2678.663818, -2348.949707, 19.901275, 0.000007, -0.000007, -90.000038, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2678.649414, -2352.068847, 19.867584, 0.000007, -0.000007, -90.000038, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2686.927734, -2348.949707, 17.003652, 0.000014, -0.000007, -90.000053, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2678.690673, -2355.239990, 17.007612, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2678.687744, -2343.278808, 14.121254, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2678.690673, -2343.286376, 17.007612, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2678.679443, -2340.150390, 14.147610, 0.000000, -0.000007, -90.000015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2678.686035, -2340.148681, 17.003658, 0.000000, -0.000007, -90.000015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2687.002929, -2340.156250, 14.121254, 0.000007, -0.000007, -90.000038, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2687.000000, -2340.148681, 17.007612, 0.000007, -0.000007, -90.000038, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2687.011230, -2343.284667, 14.147610, 0.000000, 0.000000, 89.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2687.004638, -2343.286376, 17.003658, 0.000000, 0.000000, 89.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2670.392089, -2355.239990, 14.147610, 0.000007, 0.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2670.392089, -2352.100585, 14.147610, 0.000014, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2670.385498, -2352.102294, 17.003658, 0.000014, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2670.407714, -2355.232421, 17.041276, 0.000007, 0.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2670.407714, -2352.102294, 19.901275, 0.000014, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2670.407470, -2348.949707, 14.103649, 0.000007, -0.000007, -90.000106, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2670.407470, -2348.949707, 17.003652, 0.000007, -0.000007, -90.000106, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2670.482666, -2340.156250, 14.121254, 0.000000, -0.000007, -90.000091, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2670.479736, -2340.148681, 17.007612, 0.000000, -0.000007, -90.000091, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2670.490966, -2343.284667, 14.147610, 0.000007, 0.000000, 89.999824, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2670.484375, -2343.286376, 17.003658, 0.000007, 0.000000, 89.999824, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2662.059814, -2355.239990, 14.147610, 0.000014, 0.000000, 89.999877, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2662.059814, -2352.100585, 14.147610, 0.000022, 0.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2662.053222, -2352.102294, 17.003658, 0.000022, 0.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2662.075439, -2355.232421, 17.041276, 0.000014, 0.000000, 89.999877, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2653.895996, -2343.278808, 14.121254, -0.000007, 0.000000, 89.999801, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2662.075195, -2348.949707, 14.103649, 0.000000, -0.000007, -90.000083, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2662.075195, -2348.949707, 17.003652, 0.000000, -0.000007, -90.000083, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2662.155761, -2343.278808, 14.121254, -0.000014, 0.000000, 89.999824, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2662.158691, -2343.286376, 17.007612, -0.000014, 0.000000, 89.999824, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2662.147460, -2340.150390, 14.147610, 0.000022, -0.000007, -90.000305, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2662.154052, -2340.148681, 17.003658, 0.000022, -0.000007, -90.000305, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2653.898925, -2343.286376, 17.007612, -0.000007, 0.000000, 89.999801, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2653.887695, -2340.150390, 14.147610, 0.000014, -0.000007, -90.000282, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2653.894287, -2340.148681, 17.003658, 0.000014, -0.000007, -90.000282, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2653.895996, -2340.377441, 19.891265, -0.000007, 0.000000, 449.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2653.876464, -2346.388183, 14.103649, 0.000000, -0.000007, -90.000083, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2653.926025, -2346.357666, 16.991239, -0.000007, 0.000000, 449.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2653.858154, -2355.252197, 14.121254, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2653.861083, -2355.259765, 17.007612, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2653.849853, -2352.123779, 14.147610, -0.000007, -0.000007, -89.999992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2653.856445, -2352.122070, 17.003658, -0.000007, -0.000007, -89.999992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2670.475341, -2340.158935, 19.893646, 0.000000, -0.000007, -90.000015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2686.969970, -2343.249755, 19.887596, 0.000007, -0.000007, -90.000038, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3474, 2630.961669, -2348.179443, 19.519493, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3474, 2658.671386, -2379.879638, 19.519493, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2683.943115, -2381.621093, 14.121254, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2683.946044, -2381.628662, 17.007612, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2683.934814, -2378.492675, 14.147610, -0.000014, -0.000007, -89.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2683.941406, -2378.490966, 17.003658, -0.000014, -0.000007, -89.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2675.646972, -2378.498535, 14.121254, 0.000022, -0.000014, -90.000129, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2675.644042, -2378.490966, 17.007612, 0.000022, -0.000014, -90.000129, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2675.655273, -2381.626953, 14.147610, -0.000014, 0.000007, 89.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2675.648681, -2381.628662, 17.003658, -0.000014, 0.000007, 89.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2667.236083, -2378.498535, 14.121254, 0.000014, -0.000014, -90.000106, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2667.233154, -2378.490966, 17.007612, 0.000014, -0.000014, -90.000106, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2667.244384, -2381.626953, 14.147610, -0.000007, 0.000007, 89.999946, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2667.237792, -2381.628662, 17.003658, -0.000007, 0.000007, 89.999946, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2659.132080, -2381.621093, 14.121254, 0.000007, -0.000007, 89.999855, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2659.135009, -2381.628662, 17.007612, 0.000007, -0.000007, 89.999855, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2659.123779, -2378.492675, 14.147610, 0.000000, 0.000000, -90.000083, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2659.130371, -2378.490966, 17.003658, 0.000000, 0.000000, -90.000083, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2653.856445, -2352.122070, 19.883653, -0.000007, -0.000007, -89.999992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2650.911132, -2378.498535, 17.017549, -0.000014, -180.000015, 90.000015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2650.914062, -2378.490966, 14.131319, -0.000014, -180.000015, 90.000015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2650.902832, -2381.626953, 16.991182, 0.000014, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2650.909423, -2381.628662, 14.135225, 0.000014, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2642.753906, -2381.621093, 17.017549, 0.000000, -180.000015, -90.000045, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2642.750976, -2381.628662, 14.131319, 0.000000, -180.000015, -90.000045, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2642.762207, -2378.492675, 16.991182, 0.000000, 179.999984, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2642.755615, -2378.490966, 14.135225, 0.000000, 179.999984, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2631.424560, -2379.735595, 14.121254, 0.000000, -0.000007, 179.999710, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2631.432128, -2379.732666, 17.007612, 0.000000, -0.000007, 179.999710, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2628.296142, -2379.743896, 14.147610, 0.000007, 0.000000, -0.000288, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2628.294433, -2379.737304, 17.003658, 0.000007, 0.000000, -0.000288, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2628.523193, -2379.735595, 19.891265, 0.000000, -0.000007, 179.999725, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2634.533935, -2379.755126, 14.103649, -0.000007, 0.000000, -0.000091, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2934, 2634.503417, -2379.705566, 16.991239, 0.000000, -0.000007, 179.999725, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2700.332763, -2372.844482, 13.411466, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2700.332763, -2356.633544, 13.411466, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2708.356201, -2364.773437, 13.411466, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2935, 2683.941406, -2381.602539, 19.903654, -0.000014, -0.000007, -89.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2932, 2642.750976, -2378.607910, 19.891323, 0.000000, -180.000015, -90.000045, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2689.377685, -2394.100585, 13.176614, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2710.847167, -2394.100585, 13.176614, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3753, 2615.110107, -2464.620117, 3.039060, 0.000000, 0.000000, 179.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1386, 2592.443603, -2418.744628, 27.948759, 0.000000, 0.000000, 135.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3574, 2789.209960, -2361.708740, 15.218794, 0.000000, 0.000000, 179.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3574, 2771.070068, -2357.389648, 15.218793, 0.000000, 0.000000, 179.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(620, 2208.320068, -1864.949951, 10.804697, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9586, 2679.513183, -2273.468505, 16.529375, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9590, 2688.133056, -2273.468505, 8.201227, 0.000006, 0.000044, -0.000009, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9588, 2687.433105, -2273.478515, 7.068415, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9587, 2690.503173, -2273.478515, 23.076276, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9585, 2681.802978, -2273.468505, 6.482477, 0.000006, 0.000044, -0.000009, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9821, 2617.282958, -2271.178466, 24.232477, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9584, 2606.562988, -2273.468505, 25.677776, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9698, 2618.052978, -2274.648437, 28.560575, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9820, 2617.013183, -2273.328613, 32.544975, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9583, 2616.513183, -2259.348632, 30.794977, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9822, 2620.703125, -2267.668457, 32.388778, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9819, 2621.193115, -2267.298339, 32.623077, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9818, 2621.373046, -2273.458496, 33.349678, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9761, 2680.253173, -2273.468505, 26.560575, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2661.211669, -2415.471191, 14.367554, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2666.012207, -2420.201416, 14.367554, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2651.571044, -2415.471191, 14.367554, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2641.950195, -2415.471191, 14.367554, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2632.310058, -2415.471191, 14.367554, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2622.680175, -2415.471191, 14.367554, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1278, 2766.870117, -2447.809570, 26.703100, 0.000000, 0.000000, -89.999992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2800.440673, -2396.274414, 13.411464, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2788.760498, -2396.274414, 13.411465, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2776.219726, -2396.274414, 13.411464, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2801.805908, -2380.225097, 14.418814, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(12861, 2602.031005, -2364.396728, 12.727974, 0.000000, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(12861, 2507.460693, -2370.462158, 12.697976, 0.000000, 0.000000, 315.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(7186, 2752.237060, -2341.513427, 18.523546, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3574, 2797.309082, -2373.871337, 15.218792, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2792.176513, -2380.225097, 14.418814, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2782.556152, -2380.225097, 14.418813, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2772.925292, -2380.225097, 14.418814, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2763.295166, -2380.225097, 14.418814, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2753.665527, -2380.225097, 14.418814, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2744.055908, -2380.225097, 14.418814, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2800.290527, -2430.014648, 13.411464, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2776.401611, -2430.014648, 13.411464, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2788.521484, -2430.014648, 13.411465, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1237, 2806.246093, -2420.129638, 12.685003, 0.000029, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1237, 2806.246093, -2403.961425, 12.685003, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(12861, 2771.770751, -2469.245605, 12.695001, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2720.104736, -2377.312744, 10.408803, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2720.094726, -2378.243164, 10.408803, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2720.094726, -2354.113037, 11.098805, 90.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2725.264648, -2352.473144, 14.418813, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2729.314697, -2352.472656, 14.418813, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2734.164550, -2347.563232, 14.418812, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2734.164550, -2339.563720, 14.418812, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2734.144531, -2335.753173, 14.418814, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2725.394287, -2380.225097, 14.418813, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3458, 2759.585205, -2378.172119, 15.587983, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8546, 2713.865478, -2431.619628, 15.919137, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2617.949707, -2420.210937, 14.367554, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(8546, 2713.865478, -2477.751220, 15.919137, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3626, 2749.769531, -2397.376464, 14.078100, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2622.680664, -2450.340576, 14.367554, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2617.949707, -2445.603027, 14.367554, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2631.074218, -2453.495849, 14.367553, 0.000000, 0.000000, 409.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19385, 2636.207031, -2456.630859, 14.362812, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2641.961425, -2456.621337, 14.367554, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2651.581054, -2456.621337, 14.367554, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2661.200683, -2456.621337, 14.367554, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2666.011230, -2451.811035, 14.367554, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2666.011230, -2442.199707, 14.367554, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2666.011230, -2429.759033, 14.367554, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19385, 2666.009033, -2436.050048, 14.362813, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 2719.762207, -2407.857177, 12.460936, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 2719.904296, -2405.566406, 12.573410, -18.100000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 2719.891845, -2402.936523, 12.579810, -18.100000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 2719.891845, -2407.857177, 12.579810, -18.100000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 2719.772216, -2404.796875, 12.460936, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 2719.762207, -2402.936523, 12.460936, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3036, 2694.386718, -2450.294433, 14.374053, 0.000000, 0.000000, 290.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3036, 2694.439208, -2454.250488, 14.374053, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3620, 2814.270019, -2471.109863, 25.515600, 0.000000, 0.000000, 89.999992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3620, 2802.500000, -2374.920898, 25.515600, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(12861, 2799.741699, -2453.314697, 12.705001, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2801.161376, -2446.445556, 13.411464, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2773.160644, -2446.445556, 13.411464, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2786.901855, -2446.445556, 13.411464, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 2719.891845, -2501.459960, 12.579810, -18.100000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 2719.891845, -2506.550048, 12.579810, -18.100000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 2719.895019, -2504.259765, 12.570303, -18.100000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19461, 2590.352539, -2406.679199, 12.743144, 0.000009, 90.000015, 44.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1237, 2806.246093, -2399.110107, 12.685003, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1237, 2806.246093, -2401.080810, 12.685003, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1237, 2806.246093, -2406.931884, 12.685003, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1237, 2806.246093, -2409.820068, 12.685003, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1237, 2806.246093, -2411.790771, 12.685003, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1237, 2806.246093, -2414.850341, 12.685003, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1237, 2806.246093, -2416.821044, 12.685003, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1237, 2806.246093, -2422.720947, 12.685003, 0.000029, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1237, 2806.246093, -2425.179931, 12.685003, 0.000037, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1237, 2806.246093, -2427.150634, 12.685003, 0.000037, 0.000000, 89.999885, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19461, 2597.162353, -2413.489013, 12.743144, 0.000009, 90.000015, 44.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2749.930175, -2400.644531, 13.411463, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2756.470214, -2416.885498, 13.411463, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2756.470214, -2440.906005, 13.411462, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2756.470214, -2492.275878, 13.411463, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2756.470214, -2468.674560, 13.411462, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2749.560302, -2498.826660, 13.411463, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2734.730712, -2498.826660, 13.411463, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2749.930175, -2410.575195, 13.411463, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2726.179931, -2410.575195, 13.411462, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1358, 2735.789306, -2333.697998, 13.875002, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1365, 2747.989990, -2332.567871, 13.784996, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2738.009521, -2400.644531, 13.411461, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3626, 2730.530029, -2397.376464, 14.078100, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1344, 2706.578369, -2448.419677, 13.263025, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1219, 2709.296875, -2448.620361, 13.870775, 65.499984, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1343, 2786.969726, -2370.321044, 13.465003, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3077, 2783.164550, -2377.428710, 12.635001, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1217, 2786.054199, -2370.444580, 13.061434, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1217, 2785.003417, -2370.444580, 13.061434, 0.000000, 0.000000, 92.099990, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1362, 2792.746826, -2370.639404, 13.265004, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19447, 2806.655517, -2375.124755, 14.418813, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, 2753.901123, -2413.559326, 13.343794, 0.000000, 0.000000, -89.999992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, 2753.919677, -2418.450439, 13.351594, 0.000000, 0.000000, 0.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 2515.084228, -2389.869628, 12.608754, 0.000004, -0.000004, 135.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 2514.906250, -2390.047607, 12.533216, -26.399993, -0.000006, 135.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 2512.772216, -2387.578369, 12.608754, 0.000009, -0.000009, 135.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 2512.594238, -2387.756347, 12.533216, -26.399988, -0.000012, 135.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 2510.461914, -2385.268310, 12.608754, 0.000015, -0.000015, 135.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 2510.283935, -2385.446289, 12.533216, -26.399982, -0.000018, 135.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2726.058593, -2400.644531, 13.411462, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2756.470214, -2480.625732, 13.411461, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2756.470214, -2428.865966, 13.411462, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19598, 2721.701171, -2374.287597, 15.124982, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19599, 2721.665039, -2374.273437, 15.164999, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19385, 2720.111083, -2381.885009, 14.317979, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2161, 2717.889892, -2360.272460, 16.133750, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2161, 2717.889892, -2358.141845, 16.133750, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 2717.585449, -2363.496093, 16.133750, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 2718.566162, -2368.637695, 16.133750, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 2717.585449, -2369.665527, 16.133750, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 2718.566162, -2374.848632, 16.133750, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3578, 2737.889160, -2410.575195, 13.411462, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1345, 339.718994, 52.984401, 3.265630, 0.000000, 0.000000, -148.329284, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3574, 2551.530029, -2488.181396, 15.218794, 0.000000, 0.000000, 179.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3620, 2570.280273, -2462.629638, 25.515600, 0.000000, 0.000000, 89.999992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3574, 2769.637939, -2552.972167, 15.218794, 0.000000, 0.000000, 90.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3574, 2784.280517, -2512.519287, 15.218794, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19441, 2806.098876, -2527.283691, 11.605003, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3761, 2805.942871, -2548.312011, 14.905001, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3761, 2805.942871, -2530.591552, 14.905001, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3761, 2805.942871, -2539.382080, 14.905001, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3761, 2796.862548, -2548.573242, 14.905000, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3761, 2787.912597, -2548.312988, 14.905001, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3761, 2787.922607, -2541.602783, 14.905001, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(12861, 2611.846435, -2336.298828, 12.697976, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19087, 2733.327148, -2458.078369, 24.815015, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19087, 2733.327148, -2458.818847, 24.815015, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19087, 2733.327148, -2450.898925, 24.815015, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19087, 2733.327148, -2451.639160, 24.815015, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 2787.125976, -2379.317871, 12.956026, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11730, 2786.457275, -2379.290283, 12.956026, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11730, 2785.806884, -2379.290283, 12.956026, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 2785.135742, -2379.317871, 12.956026, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11730, 2784.456054, -2379.290283, 12.956026, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2008, 2784.353515, -2374.552734, 12.885003, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1811, 2787.004638, -2371.461425, 13.505004, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1811, 2786.154052, -2371.461425, 13.505004, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2163, 2784.361572, -2371.030517, 12.925004, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1806, 2784.813476, -2375.645507, 12.935004, 0.000000, 0.000000, -21.900001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11746, 2784.345947, -2371.269042, 13.866024, 90.000000, -43.199993, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19873, 2785.495849, -2371.176757, 13.905002, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19873, 2785.395751, -2371.086669, 13.905002, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19873, 2785.445800, -2371.126708, 14.025001, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 2694.394287, -2401.053466, 16.402811, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 2714.904296, -2409.644042, 16.402811, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2770.393066, -2396.190673, 18.715005, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2782.513671, -2396.190673, 18.725006, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2794.644042, -2396.190673, 18.675001, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2806.355224, -2405.399658, 18.664995, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2806.245117, -2396.278320, 18.664995, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2782.513671, -2429.951416, 18.725006, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2770.493164, -2429.951416, 18.725006, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2806.155517, -2429.951416, 18.725006, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2794.395507, -2429.951416, 18.725006, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2806.355224, -2413.320800, 18.664995, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2806.355224, -2421.431640, 18.664995, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2756.162597, -2398.201660, 18.725006, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2721.832031, -2398.201660, 18.725006, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2756.162597, -2410.352050, 18.725006, 0.000000, 0.000000, 220.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2756.572753, -2434.892822, 18.725006, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2756.572753, -2474.717041, 18.725006, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1290, 2756.332519, -2499.087890, 18.725006, 0.000000, 0.000000, 140.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19627, 2645.526367, -2450.556396, 13.363038, 0.000000, 0.000000, 112.199966, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14686, 2645.291992, -2450.457519, 13.088752, 0.000000, 0.000000, -105.800003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14686, 2656.617431, -2450.539306, 13.088752, 0.000000, 0.000000, -65.699981, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19900, 2656.953125, -2446.697753, 12.668753, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 2662.426269, -2448.155273, 12.668753, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 2647.476806, -2451.386230, 13.718748, 180.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19900, 2641.373046, -2446.697753, 12.668753, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19921, 2649.164062, -2450.345458, 12.758760, 0.000000, 0.000000, -153.299957, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19921, 2657.013916, -2450.461425, 12.758760, 0.000000, 0.000000, -93.699928, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(939, 2634.097167, -2449.188720, 15.068757, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1236, 2632.034179, -2416.318603, 13.288754, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1327, 2634.370117, -2415.805175, 13.488754, 17.600000, 16.400003, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2121, 2636.586425, -2450.572998, 13.108754, 0.000000, 0.000000, 142.800003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2121, 2635.966308, -2449.498779, 13.108754, 0.000000, 0.000000, 98.100013, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2121, 2635.912109, -2448.294189, 13.125307, -21.900011, 0.000000, 98.100013, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1735, 2640.204833, -2450.273925, 12.668753, 0.000000, 0.000000, -140.499954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1715, 2639.686035, -2447.225097, 12.638753, 0.000000, 0.000000, -34.300003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(12927, 2670.068359, -2476.013671, 13.358754, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1711, 2638.221923, -2451.281250, 12.368748, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(12927, 2670.498779, -2430.673828, 13.378753, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1219, 2743.112548, -2512.650634, 12.935003, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1219, 2740.431640, -2512.650634, 12.935003, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1299, 2742.670166, -2513.147705, 13.615006, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2912, 2734.249023, -2436.490966, 12.686244, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2912, 2734.249023, -2435.721191, 12.686244, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2912, 2734.249023, -2436.181640, 13.396246, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3013, 2648.176757, -2451.550292, 13.827162, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3013, 2647.726318, -2451.550292, 13.827162, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19621, 2645.269287, -2450.439941, 13.446077, 0.000000, 0.000000, -147.899963, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19627, 2641.542968, -2446.865234, 13.553035, 0.000000, 0.000000, 38.199974, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19816, 2645.098388, -2450.396484, 12.946063, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19816, 2645.258544, -2450.506591, 12.946063, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19921, 2641.432373, -2445.783691, 12.768756, 0.000000, 0.000000, -153.299957, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18635, 2641.506103, -2446.502441, 13.526082, 90.000000, -101.300018, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1716, 2647.272705, -2450.121337, 12.668752, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, 2641.280517, -2446.862304, 13.554539, 99.000000, -109.300003, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(920, 2662.390380, -2450.613281, 12.988758, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(943, 2653.052734, -2452.807617, 13.358750, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(929, 2645.325683, -2452.714599, 13.568756, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(926, 2650.307128, -2452.614990, 12.928754, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(928, 2635.809814, -2451.560791, 12.948759, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1421, 2648.840087, -2452.724853, 13.428753, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2008, 2718.396728, -2356.591552, 12.641874, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2199, 2729.914062, -2363.018066, 16.126171, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1806, 2717.294921, -2357.275390, 12.711874, 0.000000, 0.000000, -120.500007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2162, 2717.991210, -2360.813720, 12.711874, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 2720.728027, -2363.545654, 11.491871, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, 2720.527832, -2374.906250, 12.711874, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19815, 2720.701904, -2363.075439, 14.701873, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19900, 2719.418701, -2369.269775, 12.711874, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14693, 2720.540771, -2369.443359, 13.121872, 0.000000, 0.000000, 99.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, 2723.934814, -2369.334228, 12.711874, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19872, 2720.302978, -2366.556884, 10.817970, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19872, 2720.302978, -2372.048095, 10.817970, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2161, 2716.200195, -2357.271240, 12.711874, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2199, 2729.914062, -2366.068847, 16.126171, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2199, 2729.914062, -2369.089599, 16.126171, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2199, 2729.914062, -2372.180419, 16.126171, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1219, 2672.686279, -2468.496093, 12.737250, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1219, 2670.315429, -2468.496093, 12.737250, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1219, 2670.315429, -2464.805419, 12.737250, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1219, 2672.676269, -2464.805419, 12.737250, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1219, 2667.654785, -2442.684814, 12.737250, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1219, 2670.025878, -2442.684814, 12.737250, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1219, 2669.956542, -2438.815185, 12.737250, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1219, 2667.576171, -2438.815185, 12.737250, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19898, 2643.091308, -2449.569580, 12.688755, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19898, 2650.842285, -2449.569580, 12.688755, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19898, 2659.083496, -2446.918945, 12.688755, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19898, 2655.973388, -2441.337646, 12.688755, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19898, 2652.452880, -2427.256103, 12.688755, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19898, 2661.002929, -2428.756591, 12.688755, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19898, 2661.002929, -2426.656005, 12.688755, 0.000000, 0.000000, 860.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19898, 2640.396240, -2425.109130, 12.688755, 0.000000, 0.000000, 63.700000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1362, 2665.449951, -2415.974609, 13.258757, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1647, 2665.517333, -2418.968017, 12.658755, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1463, 2663.185302, -2416.329101, 12.978760, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1463, 2661.024902, -2416.459228, 12.978760, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 2619.441650, -2441.210693, 16.528755, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 2619.174560, -2416.664306, 16.518753, 0.000000, 0.000000, 130.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 2619.441650, -2424.720947, 16.518753, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 2634.071777, -2424.971191, 16.518753, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 2630.691162, -2416.911376, 16.518753, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 2641.571533, -2416.911376, 16.518753, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 2658.311035, -2416.911376, 16.518753, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 2650.180908, -2416.911376, 16.518753, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 2674.621582, -2415.470947, 16.518753, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 2674.621582, -2436.110351, 16.518753, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 2674.721679, -2449.889648, 16.518753, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 2674.619873, -2471.200683, 16.534103, 0.000000, 0.000000, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 2674.679931, -2482.241210, 16.534103, 0.000000, 0.000000, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, 2653.530029, -2450.821289, 12.668753, 0.000000, 0.000000, 107.599967, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1448, 2637.981933, -2449.007568, 12.738754, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1448, 2637.981933, -2449.007568, 12.878754, 0.000000, 0.000000, 24.400001, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1448, 2637.981933, -2449.007568, 13.018754, 0.000000, 0.000000, -8.399994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19812, 2639.316894, -2451.013183, 12.918752, 0.000000, 0.000000, -166.899963, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1544, 2638.241210, -2448.413574, 12.938756, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1486, 2637.594970, -2448.502441, 13.248748, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1544, 2639.271728, -2451.074951, 13.398758, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1544, 2639.481933, -2450.974853, 13.398758, 0.000000, 0.000000, -171.900024, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19897, 2639.104248, -2450.965087, 13.428755, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2108, 2637.961914, -2449.014892, 11.318752, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00);
}