CreateLSPDInt()
{
    new STREAMER_TAG_OBJECT:pdcast;

    //academy - shooting range
    pdcast = CreateDynamicObject(19377, 1004.785827, 1671.993652, -17.566709, 0.000000, 90.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0x00000000);
    pdcast = CreateDynamicObject(19377, 1004.785644, 1662.362060, -17.566709, 0.000000, 90.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0x00000000);
    pdcast = CreateDynamicObject(19377, 994.287109, 1662.360717, -13.898799, 0.000000, 90.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    pdcast = CreateDynamicObject(19458, 1005.189880, 1676.687622, -15.736599, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 12850, "cunte_block1", "ws_redbrickold", 0x00000000);
    pdcast = CreateDynamicObject(19458, 1009.958801, 1671.782958, -15.736599, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 12850, "cunte_block1", "ws_redbrickold", 0x00000000);
    pdcast = CreateDynamicObject(19458, 1002.808776, 1671.803710, -15.736599, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 12850, "cunte_block1", "ws_redbrickold", 0x00000000);
    pdcast = CreateDynamicObject(19458, 1001.850402, 1670.004272, -15.736599, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 12850, "cunte_block1", "ws_redbrickold", 0x00000000);
    pdcast = CreateDynamicObject(2309, 1006.236022, 1673.334106, -17.479900, 0.000000, 0.000000, 97.114112, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    pdcast = CreateDynamicObject(2047, 1002.925659, 1673.260253, -14.935600, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 13131, "cunte_blockammo", "sw_locals", 0x00000000);
    pdcast = CreateDynamicObject(19388, 1008.262451, 1670.005126, -15.736599, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 12850, "cunte_block1", "ws_redbrickold", 0x00000000);
    pdcast = CreateDynamicObject(19458, 1009.957641, 1662.150512, -15.736599, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 12850, "cunte_block1", "ws_redbrickold", 0x00000000);
    pdcast = CreateDynamicObject(19366, 1007.046142, 1668.479248, -15.736599, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 12850, "cunte_block1", "ws_redbrickold", 0x00000000);
    pdcast = CreateDynamicObject(19458, 1002.314819, 1666.960815, -15.736599, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 12850, "cunte_block1", "ws_redbrickold", 0x00000000);
    pdcast = CreateDynamicObject(19458, 1009.527160, 1664.276733, -15.736599, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 12850, "cunte_block1", "ws_redbrickold", 0x00000000);
    pdcast = CreateDynamicObject(19458, 1004.792053, 1659.545898, -15.736599, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 12850, "cunte_block1", "ws_redbrickold", 0x00000000);
    pdcast = CreateDynamicObject(19458, 992.689025, 1666.964355, -15.736599, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 12850, "cunte_block1", "ws_redbrickold", 0x00000000);
    pdcast = CreateDynamicObject(19458, 999.940124, 1658.090332, -15.736599, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 12850, "cunte_block1", "ws_redbrickold", 0x00000000);
    pdcast = CreateDynamicObject(19458, 990.315307, 1658.089111, -15.736599, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 12850, "cunte_block1", "ws_redbrickold", 0x00000000);
    pdcast = CreateDynamicObject(19458, 989.084777, 1662.205810, -15.736599, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 10864, "bridgeland_sfse", "ws_altz_wall1", 0x00000000);
    pdcast = CreateDynamicObject(18980, 1001.364318, 1655.086669, -17.079999, 0.000000, 90.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0x00000000);
    pdcast = CreateDynamicObject(19403, 1000.945800, 1659.909423, -15.410160, 90.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 14584, "ab_abbatoir01", "ab_concFloor", 0x00000000);
    pdcast = CreateDynamicObject(19403, 1000.944702, 1662.530761, -15.410160, 90.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 14584, "ab_abbatoir01", "ab_concFloor", 0x00000000);
    pdcast = CreateDynamicObject(19403, 1000.946411, 1665.149902, -15.410160, 90.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 14584, "ab_abbatoir01", "ab_concFloor", 0x00000000);
    pdcast = CreateDynamicObject(2050, 989.462646, 1662.386352, -15.610480, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 18064, "ab_sfammuunits", "gun_targetc", 0x00000000);
    pdcast = CreateDynamicObject(19430, 1001.361022, 1661.074096, -17.692100, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0x00000000);
    pdcast = CreateDynamicObject(19430, 1001.361022, 1663.672485, -17.692129, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0x00000000);
    pdcast = CreateDynamicObject(19430, 1001.354309, 1658.193603, -17.692100, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0x00000000);
    pdcast = CreateDynamicObject(19430, 1001.357116, 1666.873657, -17.692100, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0x00000000);
    pdcast = CreateDynamicObject(1516, 1003.531738, 1667.289550, -17.178449, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    pdcast = CreateDynamicObject(2115, 1003.072082, 1666.327148, -17.652799, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    pdcast = CreateDynamicObject(19377, 994.287109, 1662.360717, -17.566709, 0.000000, 90.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 10101, "2notherbuildsfe", "Bow_Abpave_Gen", 0x00000000);
    pdcast = CreateDynamicObject(19377, 1004.785583, 1662.362060, -13.898799, 0.000000, 90.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    pdcast = CreateDynamicObject(19377, 1004.785827, 1671.993652, -13.898799, 0.000000, 90.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 16640, "a51", "scratchedmetal", 0x00000000);
    pdcast = CreateDynamicObject(19366, 1011.133178, 1673.266235, -15.842659, 0.000000, 90.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    pdcast = CreateDynamicObject(19366, 1010.952026, 1673.277709, -16.548679, 0.000000, 90.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 2755, "ab_dojowall", "mp_apt1_roomfloor", 0x00000000);
    pdcast = CreateDynamicObject(1893, 1005.891540, 1675.282104, -13.688119, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 12850, "cunte_block1", "ws_redbrickold", 0x00000000);
    pdcast = CreateDynamicObject(2051, 992.867553, 1659.933227, -15.634320, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(pdcast, 0, 18064, "ab_sfammuunits", "gun_targetc", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(1566, 1007.009033, 1676.591552, -16.070899, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2206, 1004.769775, 1674.074340, -17.480699, 0.000000, 0.000000, 270.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(1714, 1003.200317, 1673.120605, -17.479700, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2161, 1002.888610, 1675.727050, -17.480400, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2161, 1002.889343, 1674.399291, -17.480400, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2164, 1002.891174, 1671.011230, -17.480899, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2059, 1004.664733, 1672.386718, -16.530099, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2058, 1004.732910, 1673.974609, -16.526599, -2.000000, 0.000000, 343.747222, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(1583, 1005.539001, 1676.630493, -17.523370, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(3014, 1003.886657, 1675.239624, -17.481189, 0.000000, 0.000000, 6.776889, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(1584, 1005.534362, 1670.019165, -17.499490, 0.000000, 0.000000, 180.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(11729, 1004.074768, 1676.340576, -17.480739, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(1502, 1007.476989, 1669.990478, -17.490499, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2010, 1009.403320, 1676.161010, -17.481180, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(3025, 990.974304, 1665.455566, -13.179499, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2050, 989.462646, 1662.386352, -16.919170, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(3025, 989.397399, 1662.499633, -13.179499, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2051, 990.966613, 1665.343750, -16.966899, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2049, 990.992492, 1665.377929, -15.677000, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(349, 1001.954162, 1658.505004, -17.176500, 5.000000, 280.000000, 270.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(349, 1001.962829, 1658.722412, -17.176500, 5.000000, 280.000000, 270.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2358, 1002.669494, 1658.516235, -17.379699, 0.000000, 0.000000, 2.824320, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2359, 1003.570068, 1658.559692, -17.282730, 0.000000, 0.000000, 16.252649, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(19995, 1001.354309, 1661.352294, -16.568000, 0.000000, 90.000000, 180.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(19995, 1001.309936, 1661.491210, -16.568000, 0.000000, 90.000000, 180.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(19995, 1003.246887, 1665.953369, -16.842300, 0.000000, 90.000000, 180.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2037, 1001.168518, 1660.645385, -16.509300, 0.000000, 0.000000, 58.920200, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2037, 1001.103881, 1660.433715, -16.509300, 0.000000, 0.000000, 89.191192, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2037, 1001.628051, 1658.485351, -16.509300, 0.000000, 0.000000, 58.920200, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2690, 1004.010131, 1666.731689, -15.030949, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(347, 1009.817382, 1674.798583, -16.464000, 90.000000, 0.000000, -160.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(346, 1009.414001, 1674.076416, -16.464000, 90.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(19995, 1001.286621, 1663.011230, -16.568000, 0.000000, 90.000000, 180.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(19995, 1001.285644, 1662.871337, -16.568000, 0.000000, 90.000000, 180.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(353, 1001.557373, 1664.187255, -16.525600, 276.000000, 90.000000, 180.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(353, 1001.598205, 1665.862060, -16.523599, 276.000000, 90.000000, 250.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(351, 1003.202270, 1666.798828, -16.591400, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(347, 1001.657531, 1662.066894, -16.588300, 90.000000, 0.000000, -160.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(347, 1001.676635, 1661.766723, -16.588300, 90.000000, 0.000000, -160.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(346, 1001.508605, 1662.988891, -16.590599, 90.000000, 0.000000, 160.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(19995, 1001.306274, 1661.611328, -16.568000, 0.000000, 90.000000, 180.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(19995, 1003.239990, 1666.073242, -16.842300, 0.000000, 90.000000, 180.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(19995, 1003.233032, 1666.193359, -16.842300, 0.000000, 90.000000, 180.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2359, 1003.193481, 1666.363403, -17.282730, 0.000000, 0.000000, 16.252649, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2690, 1005.774963, 1676.467651, -15.030949, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(356, 1009.708374, 1674.785522, -15.513699, -4.000000, 270.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(356, 1009.684814, 1674.566650, -15.513699, -4.000000, 270.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(356, 1009.683349, 1674.365478, -15.513699, -4.000000, 270.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(349, 1009.674133, 1674.116455, -15.461700, -7.000000, 290.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(349, 1009.674743, 1673.936279, -15.461700, -7.000000, 290.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(349, 1009.675354, 1673.776123, -15.461700, -7.000000, 290.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(349, 1009.675903, 1673.595947, -15.461700, -7.000000, 290.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(349, 1009.666748, 1673.395751, -15.461700, -7.000000, 290.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(356, 1009.683532, 1673.161376, -15.513699, -4.000000, 270.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(357, 1009.663757, 1672.922485, -15.535699, -2.000000, 285.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(357, 1009.666687, 1672.683105, -15.535699, -2.000000, 285.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(357, 1009.662414, 1672.482910, -15.535699, -2.000000, 285.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(357, 1009.658508, 1672.302856, -15.535699, -2.000000, 285.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(358, 1009.710510, 1672.040405, -15.511699, -7.000000, 280.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(358, 1009.721557, 1671.719604, -15.511699, -7.000000, 280.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(347, 1004.028015, 1666.274047, -16.852300, 90.000000, 0.000000, -160.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(346, 1003.429565, 1666.307495, -16.852300, 90.000000, 0.000000, 160.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(346, 1003.485656, 1666.523803, -16.852300, 90.000000, 0.000000, 160.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(347, 1003.969909, 1665.989379, -16.852300, 90.000000, 0.000000, -159.940002, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(347, 1009.851501, 1674.520141, -16.464000, 90.000000, 0.000000, -160.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(347, 1009.884826, 1674.241821, -16.464000, 90.000000, 0.000000, -160.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(346, 1009.426513, 1673.855590, -16.464000, 90.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(352, 1009.641174, 1673.375244, -16.460100, 90.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(352, 1009.540283, 1673.028686, -16.460100, 90.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(352, 1009.677795, 1672.715332, -16.460100, 90.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(372, 1009.415405, 1672.475463, -16.460100, 90.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(372, 1009.421142, 1671.951904, -16.460100, 90.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2010, 1009.458862, 1664.809326, -17.478759, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(1893, 1005.844177, 1671.841674, -13.688119, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(1893, 1009.767395, 1672.764892, -13.688099, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(1893, 1007.861206, 1666.226684, -13.688119, 0.000000, 0.000000, 0.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(1893, 1002.346008, 1659.175537, -13.788100, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(1893, 1002.330383, 1664.508911, -13.788100, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(1893, 1002.428405, 1661.812866, -13.788100, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2050, 992.898864, 1659.949462, -17.091409, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(3025, 992.837646, 1660.034545, -13.179499, 0.000000, 0.000000, 90.000000, 71, 2, -1, 200.00, 200.00);
    CreateDynamicObject(2047, 1005.499877, 1670.108032, -14.845599, 0.000000, 0.000000, -179.499862, 71, 2, -1, 200.00, 200.00);
}