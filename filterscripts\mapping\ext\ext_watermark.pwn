CreateWatermarkExt()
{
    new STREAMER_TAG_OBJECT:wtrjxadwt;
    wtrjxadwt = CreateDynamicObject(1881, 1180.930786, -1416.070922, 14.150689, -0.000000, 205.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1181.227172, -1416.070922, 14.178395, -0.000000, 155.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1181.102172, -1416.070922, 14.099018, -0.000000, 280.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1180.584228, -1416.070922, 14.185762, -0.000000, 0.800000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1180.458984, -1416.070922, 14.507555, -0.000000, 90.800003, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1180.338378, -1416.070922, 14.348525, -0.000000, 124.099861, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1180.285278, -1416.070922, 14.010565, -0.000000, 60.999908, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1179.825805, -1416.070922, 14.212592, -0.000000, 0.499905, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1179.461669, -1416.070922, 14.217818, -0.000000, 30.499910, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1179.235473, -1416.070922, 14.232677, -0.000000, -19.800077, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1178.916870, -1416.070922, 14.202938, -0.000000, 0.499924, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1178.629150, -1416.070922, 14.505448, -0.000000, 90.499923, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1178.635986, -1416.070922, 14.195379, -0.000000, 90.499923, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1178.633178, -1416.070922, 13.895394, -0.000000, 90.499923, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1178.036987, -1416.070922, 14.210618, -0.000000, 0.499924, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1177.852539, -1416.070922, 14.230891, -0.000000, 32.099929, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1177.635375, -1416.070922, 14.272876, -0.000000, -7.500072, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1177.010009, -1416.070922, 14.150689, -0.000000, 205.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1177.306396, -1416.070922, 14.178395, -0.000000, 155.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1177.181396, -1416.070922, 14.099018, -0.000000, 280.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1379.816894, 13.823159, -0.000007, 205.000000, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1380.113281, 13.850866, -0.000007, 155.000000, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1379.988281, 13.771488, -0.000007, 280.000000, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1379.470336, 13.858233, -0.000007, 0.799992, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1379.345092, 14.180024, -0.000007, 90.799995, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1379.224487, 14.020994, -0.000007, 124.099853, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1379.171386, 13.683035, -0.000007, 60.999900, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1378.711914, 13.885061, -0.000007, 0.499897, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1378.347778, 13.890288, -0.000007, 30.499902, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1378.121582, 13.905147, -0.000007, -19.800085, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1377.802978, 13.875407, -0.000007, 0.499915, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1377.515258, 14.177918, -0.000007, 90.499916, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1377.522094, 13.867850, -0.000007, 90.499916, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1377.519287, 13.567865, -0.000007, 90.499916, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1376.923095, 13.883087, -0.000007, 0.499915, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1376.738647, 13.903361, -0.000007, 32.099922, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1376.521484, 13.945345, -0.000007, -7.500080, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1375.896118, 13.823159, -0.000007, 205.000000, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1376.192504, 13.850866, -0.000007, 155.000000, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1185.664184, -1376.067504, 13.771488, -0.000007, 280.000000, -90.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1259.769897, 15.374898, 0.000007, 205.000000, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1259.473510, 15.402606, 0.000007, 155.000000, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1259.598510, 15.323227, 0.000007, 280.000000, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1260.116455, 15.409973, 0.000007, 0.800000, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1260.241699, 15.731763, 0.000007, 90.800003, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1260.362304, 15.572733, 0.000007, 124.099861, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1260.415405, 15.234775, 0.000007, 60.999908, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1260.874877, 15.436800, 0.000007, 0.499904, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1261.239013, 15.442028, 0.000007, 30.499910, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1261.465209, 15.456887, 0.000007, -19.800077, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1261.783813, 15.427146, 0.000007, 0.499923, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1262.071533, 15.729658, 0.000007, 90.499923, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1262.064697, 15.419589, 0.000007, 90.499923, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1262.067504, 15.119605, 0.000007, 90.499923, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1262.663696, 15.434826, 0.000007, 0.499923, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1262.848144, 15.455101, 0.000007, 32.099929, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1263.065307, 15.497085, 0.000007, -7.500072, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1263.690673, 15.374898, 0.000007, 205.000000, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1263.394287, 15.402606, 0.000007, 155.000000, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1283.151123, -1263.519287, 15.323227, 0.000007, 280.000000, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1318.869018, -1437.902221, 17.264408, 0.000007, 205.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1318.572631, -1437.902221, 17.292114, 0.000007, 155.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1318.697631, -1437.902221, 17.212736, 0.000007, 280.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1319.215576, -1437.902221, 17.299480, 0.000007, 0.800000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1319.340820, -1437.902221, 17.621273, 0.000007, 90.800003, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1319.461425, -1437.902221, 17.462242, 0.000007, 124.099861, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1319.514526, -1437.902221, 17.124284, 0.000007, 60.999908, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1319.973999, -1437.902221, 17.326309, 0.000007, 0.499904, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1320.338134, -1437.902221, 17.331535, 0.000007, 30.499910, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1320.564331, -1437.902221, 17.346395, 0.000007, -19.800077, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1320.882934, -1437.902221, 17.316656, 0.000007, 0.499923, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1321.170654, -1437.902221, 17.619165, 0.000007, 90.499923, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1321.163818, -1437.902221, 17.309097, 0.000007, 90.499923, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1321.166625, -1437.902221, 17.009113, 0.000007, 90.499923, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1321.762817, -1437.902221, 17.324335, 0.000007, 0.499923, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1321.947265, -1437.902221, 17.344608, 0.000007, 32.099929, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1322.164428, -1437.902221, 17.386594, 0.000007, -7.500072, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1322.789794, -1437.902221, 17.264408, 0.000007, 205.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1322.493408, -1437.902221, 17.292114, 0.000007, 155.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1322.618408, -1437.902221, 17.212736, 0.000007, 280.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1379.058349, -1612.736694, 15.304440, 0.000010, 205.000000, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1378.768920, -1612.672973, 15.332147, 0.000010, 155.000000, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1378.890991, -1612.699951, 15.252769, 0.000010, 280.000000, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1379.396850, -1612.811035, 15.339514, 0.000010, 0.799985, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1379.519165, -1612.838012, 15.661305, 0.000010, 90.799987, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1379.637084, -1612.863891, 15.502275, 0.000010, 124.099845, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1379.688964, -1612.875366, 15.164317, 0.000010, 60.999893, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1380.137451, -1612.973999, 15.366342, 0.000010, 0.499889, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1380.493164, -1613.052124, 15.371569, 0.000010, 30.499895, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1380.714111, -1613.100830, 15.386428, 0.000010, -19.800092, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1381.025390, -1613.169189, 15.356688, 0.000010, 0.499908, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1381.306396, -1613.230957, 15.659199, 0.000010, 90.499908, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1381.299682, -1613.229492, 15.349131, 0.000010, 90.499908, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1381.302368, -1613.230102, 15.049146, 0.000010, 90.499908, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1381.884643, -1613.358154, 15.364368, 0.000010, 0.499908, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1382.064697, -1613.397705, 15.384642, 0.000010, 32.099914, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1382.276977, -1613.444335, 15.426627, 0.000010, -7.500087, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1382.887695, -1613.578735, 15.304440, 0.000010, 205.000000, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1382.598266, -1613.515136, 15.332147, 0.000010, 155.000000, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1382.720336, -1613.541870, 15.252769, 0.000010, 280.000000, 167.599761, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1372.953369, -1767.111083, 14.925393, 0.000007, 205.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1372.656982, -1767.111083, 14.953100, 0.000007, 155.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1372.781982, -1767.111083, 14.873722, 0.000007, 280.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1373.299926, -1767.111083, 14.960467, 0.000007, 0.800000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1373.425170, -1767.111083, 15.282258, 0.000007, 90.800003, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1373.545776, -1767.111083, 15.123228, 0.000007, 124.099861, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1373.598876, -1767.111083, 14.785269, 0.000007, 60.999908, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1374.058349, -1767.111083, 14.987295, 0.000007, 0.499904, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1374.422485, -1767.111083, 14.992522, 0.000007, 30.499910, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1374.648681, -1767.111083, 15.007381, 0.000007, -19.800077, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1374.967285, -1767.111083, 14.977641, 0.000007, 0.499923, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1375.255004, -1767.111083, 15.280152, 0.000007, 90.499923, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1375.248168, -1767.111083, 14.970084, 0.000007, 90.499923, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1375.250976, -1767.111083, 14.670099, 0.000007, 90.499923, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1375.847167, -1767.111083, 14.985321, 0.000007, 0.499923, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1376.031616, -1767.111083, 15.005595, 0.000007, 32.099929, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1376.248779, -1767.111083, 15.047579, 0.000007, -7.500072, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1376.874145, -1767.111083, 14.925393, 0.000007, 205.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1376.577758, -1767.111083, 14.953100, 0.000007, 155.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1376.702758, -1767.111083, 14.873722, 0.000007, 280.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1501.694335, -1743.175048, 12.382761, 89.999992, -96.138107, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1501.990722, -1743.202758, 12.382761, 89.999992, -146.138107, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1501.865722, -1743.123413, 12.382761, 89.999992, -21.138099, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1501.347778, -1743.210083, 12.382761, 89.999992, -300.338134, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1501.222534, -1743.531860, 12.382761, 89.999992, -210.338104, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1501.101928, -1743.372924, 12.382761, 89.999992, -177.038269, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1501.048828, -1743.034912, 12.382761, 89.999992, -240.138229, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1500.589355, -1743.236938, 12.382761, 89.999992, -300.638244, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1500.225219, -1743.242187, 12.382761, 89.999992, -270.638244, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1499.999023, -1743.257080, 12.382761, 89.999992, -320.938232, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1499.680419, -1743.227294, 12.382761, 89.999992, -300.638183, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1499.392700, -1743.529785, 12.382761, 89.999992, -210.638183, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1499.399536, -1743.219726, 12.382761, 89.999992, -210.638183, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1499.396728, -1742.919799, 12.382761, 89.999992, -210.638183, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1498.800537, -1743.234985, 12.382761, 89.999992, -300.638183, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1498.616088, -1743.255249, 12.382761, 89.999992, -269.038208, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1498.398925, -1743.297241, 12.382761, 89.999992, -308.638183, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1497.773559, -1743.175048, 12.382761, 89.999992, -96.138107, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1498.069946, -1743.202758, 12.382761, 89.999992, -146.138107, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1497.944946, -1743.123413, 12.382761, 89.999992, -21.138099, -58.861881, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.560302, -1661.868896, 12.371026, 89.999992, 309.036224, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.588012, -1661.572509, 12.371026, 89.999992, 259.036224, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.508666, -1661.697509, 12.371026, 89.999992, 384.036254, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.595336, -1662.215576, 12.371026, 89.999992, 104.836250, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.917114, -1662.340820, 12.371026, 89.999992, 194.836242, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.758178, -1662.461425, 12.371026, 89.999992, 228.136077, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.420166, -1662.514404, 12.371026, 89.999992, 165.036132, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.622192, -1662.973876, 12.371026, 89.999992, 104.536140, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.627441, -1663.338134, 12.371026, 89.999992, 134.536148, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.642333, -1663.564208, 12.371026, 89.999992, 84.236152, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.612548, -1663.882812, 12.371026, 89.999992, 104.536140, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.915039, -1664.170654, 12.371026, 89.999992, 194.536163, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.604980, -1664.163818, 12.371026, 89.999992, 194.536163, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.305053, -1664.166503, 12.371026, 89.999992, 194.536163, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.620239, -1664.762695, 12.371026, 89.999992, 104.536140, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.640502, -1664.947265, 12.371026, 89.999992, 136.136184, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.682495, -1665.164306, 12.371026, 89.999992, 96.536140, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.560302, -1665.789794, 12.371026, 89.999992, 309.036224, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.588012, -1665.493408, 12.371026, 89.999992, 259.036224, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1443.508666, -1665.618408, 12.371026, 89.999992, 384.036254, -14.036275, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1483.350097, -1607.933349, 12.873044, 89.999992, 629.471191, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1483.646484, -1607.961059, 12.873044, 89.999992, 579.471130, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1483.521484, -1607.881713, 12.873044, 89.999992, 704.471191, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1483.003417, -1607.968383, 12.873044, 89.999992, 425.271148, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1482.878173, -1608.290161, 12.873044, 89.999992, 515.271179, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1482.757568, -1608.131225, 12.873044, 89.999992, 548.570983, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1482.704589, -1607.793212, 12.873044, 89.999992, 485.471038, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1482.245117, -1607.995239, 12.873044, 89.999992, 424.971038, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1481.880859, -1608.000488, 12.873044, 89.999992, 454.971099, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1481.654785, -1608.015380, 12.873044, 89.999992, 404.671081, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1481.336181, -1607.985595, 12.873044, 89.999992, 424.971099, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1481.048339, -1608.288085, 12.873044, 89.999992, 514.971069, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1481.055175, -1607.978027, 12.873044, 89.999992, 514.971069, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1481.052490, -1607.678100, 12.873044, 89.999992, 514.971069, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1480.456298, -1607.993286, 12.873044, 89.999992, 424.971099, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1480.271728, -1608.013549, 12.873044, 89.999992, 456.571075, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1480.054687, -1608.055541, 12.873044, 89.999992, 416.971099, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1479.429199, -1607.933349, 12.873044, 89.999992, 629.471191, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1479.725585, -1607.961059, 12.873044, 89.999992, 579.471130, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1479.600585, -1607.881713, 12.873044, 89.999992, 704.471191, -64.471199, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1673.672241, -1581.888916, 16.492706, 0.000007, 205.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1673.375854, -1581.888916, 16.520414, 0.000007, 155.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1673.500854, -1581.888916, 16.441036, 0.000007, 280.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1674.018798, -1581.888916, 16.527782, 0.000007, 0.800000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1674.144042, -1581.888916, 16.849571, 0.000007, 90.800003, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1674.264648, -1581.888916, 16.690542, 0.000007, 124.099861, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1674.317749, -1581.888916, 16.352584, 0.000007, 60.999908, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1674.777221, -1581.888916, 16.554609, 0.000007, 0.499904, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1675.141357, -1581.888916, 16.559837, 0.000007, 30.499910, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1675.367553, -1581.888916, 16.574695, 0.000007, -19.800077, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1675.686157, -1581.888916, 16.544956, 0.000007, 0.499923, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1675.973876, -1581.888916, 16.847465, 0.000007, 90.499923, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1675.967041, -1581.888916, 16.537399, 0.000007, 90.499923, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1675.969848, -1581.888916, 16.237413, 0.000007, 90.499923, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1676.566040, -1581.888916, 16.552635, 0.000007, 0.499923, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1676.750488, -1581.888916, 16.572910, 0.000007, 32.099929, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1676.967651, -1581.888916, 16.614894, 0.000007, -7.500072, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1677.593017, -1581.888916, 16.492706, 0.000007, 205.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1677.296630, -1581.888916, 16.520414, 0.000007, 155.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1677.421630, -1581.888916, 16.441036, 0.000007, 280.000000, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1398.820678, 29.484882, 0.000015, 205.000000, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1398.524291, 29.512588, 0.000015, 155.000000, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1398.649291, 29.433210, 0.000015, 280.000000, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1399.167114, 29.519954, 0.000015, 0.800000, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1399.292358, 29.841747, 0.000015, 90.800003, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1399.412963, 29.682716, 0.000015, 124.099861, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1399.466186, 29.344758, 0.000015, 60.999908, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1399.925659, 29.546783, 0.000015, 0.499904, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1400.289672, 29.552009, 0.000015, 30.499910, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1400.515991, 29.566869, 0.000015, -19.800077, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1400.834350, 29.537130, 0.000015, 0.499923, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1401.122192, 29.839639, 0.000015, 90.499923, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1401.115356, 29.529571, 0.000015, 90.499923, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1401.118041, 29.229587, 0.000015, 90.499923, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1401.714477, 29.544809, 0.000015, 0.499923, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1401.898803, 29.565082, 0.000015, 32.099929, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1402.115844, 29.607069, 0.000015, -7.500072, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1402.741333, 29.484882, 0.000015, 205.000000, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1402.444946, 29.512588, 0.000015, 155.000000, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2352.514892, -1402.569946, 29.433210, 0.000015, 280.000000, 89.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2360.141113, -1275.665771, 24.282196, -0.000000, 205.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2360.437500, -1275.665771, 24.309902, -0.000000, 155.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2360.312500, -1275.665771, 24.230524, -0.000000, 280.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2359.794677, -1275.665771, 24.317268, -0.000000, 0.800000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2359.669433, -1275.665771, 24.639060, -0.000000, 90.800003, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2359.548828, -1275.665771, 24.480030, -0.000000, 124.099861, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2359.495605, -1275.665771, 24.142072, -0.000000, 60.999908, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2359.036132, -1275.665771, 24.344097, -0.000000, 0.499904, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2358.672119, -1275.665771, 24.349323, -0.000000, 30.499910, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2358.445800, -1275.665771, 24.364183, -0.000000, -19.800077, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2358.127441, -1275.665771, 24.334444, -0.000000, 0.499923, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2357.839599, -1275.665771, 24.636953, -0.000000, 90.499923, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2357.846435, -1275.665771, 24.326885, -0.000000, 90.499923, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2357.843750, -1275.665771, 24.026901, -0.000000, 90.499923, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2357.247558, -1275.665771, 24.342123, -0.000000, 0.499923, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2357.062988, -1275.665771, 24.362396, -0.000000, 32.099929, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2356.845947, -1275.665771, 24.404382, -0.000000, -7.500072, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2356.220458, -1275.665771, 24.282196, 0.000000, 205.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2356.516845, -1275.665771, 24.309902, 0.000000, 155.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2356.391845, -1275.665771, 24.230524, 0.000000, 280.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2289.110107, -1385.248046, 22.935876, 89.999992, -83.434936, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2289.082519, -1385.544433, 22.935876, 89.999992, -133.434936, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2289.161865, -1385.419433, 22.935876, 89.999992, -8.434944, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2289.074951, -1384.901855, 22.935876, 89.999992, -287.634948, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2288.753173, -1384.776367, 22.935876, 89.999992, -197.634948, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2288.912353, -1384.655761, 22.935876, 89.999992, -164.335083, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2289.250244, -1384.602539, 22.935876, 89.999992, -227.435028, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2289.048095, -1384.143066, 22.935876, 89.999992, -287.935058, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2289.042968, -1383.779296, 22.935876, 89.999992, -257.935028, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2289.028076, -1383.552734, 22.935876, 89.999992, -308.235015, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2289.057861, -1383.234375, 22.935876, 89.999992, -287.935028, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2288.755371, -1382.946777, 22.935876, 89.999992, -197.935028, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2289.065429, -1382.953613, 22.935876, 89.999992, -197.935028, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2289.365478, -1382.950683, 22.935876, 89.999992, -197.935028, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2289.050292, -1382.354492, 22.935876, 89.999992, -287.935028, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2289.030029, -1382.169921, 22.935876, 89.999992, -256.335021, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2288.988037, -1381.953125, 22.935876, 89.999992, -295.935028, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2289.110107, -1381.327636, 22.935876, 89.999992, -83.434936, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2289.082519, -1381.624023, 22.935876, 89.999992, -133.434936, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 2289.161865, -1381.499023, 22.935876, 89.999992, -8.434944, -161.565078, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1896.299560, -1235.117675, 14.151319, 87.941192, -24.839691, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1896.032348, -1234.986938, 14.142539, 87.941192, -74.839698, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1896.121093, -1235.105224, 14.147808, 87.941192, 50.160312, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1896.635864, -1235.208129, 14.160025, 87.941192, -229.039688, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1896.867309, -1234.951538, 14.156002, 87.941192, -139.039688, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1896.923461, -1235.142700, 14.163001, 87.941192, -105.739845, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1896.853515, -1235.477416, 14.172287, 87.941192, -168.839782, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1897.354492, -1235.451538, 14.180226, 87.941192, -229.339797, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1897.696777, -1235.575317, 14.190099, 87.941192, -199.339782, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1897.913452, -1235.641601, 14.195964, 87.941192, -249.639785, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1898.200683, -1235.782348, 14.205404, 87.941192, -229.339797, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1898.577026, -1235.601318, 14.206292, 87.941192, -139.339767, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1898.460571, -1235.888793, 14.213288, 87.941192, -139.339767, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1898.356933, -1236.170166, 14.220319, 87.941192, -139.339767, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1899.026000, -1236.086669, 14.229385, 87.941192, -229.339797, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1899.205566, -1236.133056, 14.233979, 87.941192, -197.739746, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1899.423339, -1236.170654, 14.238966, 87.941192, -237.339797, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1899.964721, -1236.506469, 14.258967, 87.941192, -24.839691, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1899.697509, -1236.375732, 14.250188, 87.941192, -74.839698, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1899.786132, -1236.494018, 14.255459, 87.941192, 50.160312, 29.072185, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1231.726318, -879.164672, 44.753627, 0.000030, 205.000000, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1231.687744, -878.870788, 44.781333, 0.000030, 155.000000, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1231.704101, -878.994750, 44.701957, 0.000030, 280.000000, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1231.771606, -879.508300, 44.788700, 0.000030, 0.799999, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1231.787963, -879.632446, 45.110492, 0.000030, 90.800003, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1231.803710, -879.752014, 44.951461, 0.000030, 124.099861, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1231.810668, -879.804626, 44.613502, 0.000030, 60.999908, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1231.870605, -880.260253, 44.815528, 0.000030, 0.499903, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1231.918090, -880.621215, 44.820755, 0.000030, 30.499908, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1231.947631, -880.845520, 44.835613, 0.000030, -19.800079, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1231.989135, -881.161376, 44.805873, 0.000030, 0.499922, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1232.026733, -881.446594, 45.108386, 0.000030, 90.499923, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1232.025878, -881.439880, 44.798316, 0.000030, 90.499923, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1232.026245, -881.442626, 44.498332, 0.000030, 90.499923, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1232.104125, -882.033691, 44.813556, 0.000030, 0.499922, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1232.128173, -882.216613, 44.833827, 0.000030, 32.099929, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1232.156494, -882.431884, 44.875812, 0.000030, -7.500073, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1232.238037, -883.051940, 44.753627, 0.000030, 205.000000, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1232.199462, -882.758056, 44.781333, 0.000030, 155.000000, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1232.215698, -882.882019, 44.701957, 0.000030, 280.000000, 97.499877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.336425, -907.419006, 41.652160, 89.999992, -19.999980, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.364135, -907.122619, 41.652160, 89.999992, -69.999984, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.284667, -907.247619, 41.652160, 89.999992, 55.000019, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.371582, -907.765563, 41.652160, 89.999992, -224.199951, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.693359, -907.890808, 41.652160, 89.999992, -134.199951, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.534179, -908.011413, 41.652160, 89.999992, -100.900115, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.196289, -908.064514, 41.652160, 89.999992, -164.000061, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.398315, -908.523986, 41.652160, 89.999992, -224.500061, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.403564, -908.888122, 41.652160, 89.999992, -194.500061, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.418457, -909.114318, 41.652160, 89.999992, -244.800048, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.388671, -909.432922, 41.652160, 89.999992, -224.500061, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.691162, -909.720642, 41.652160, 89.999992, -134.500030, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.381103, -909.713806, 41.652160, 89.999992, -134.500030, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.081054, -909.716613, 41.652160, 89.999992, -134.500030, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.396362, -910.312805, 41.652160, 89.999992, -224.500061, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.416625, -910.497253, 41.652160, 89.999992, -192.900024, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.458618, -910.714416, 41.652160, 89.999992, -232.500061, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.336425, -911.339782, 41.652160, 89.999992, -19.999980, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.364135, -911.043395, 41.652160, 89.999992, -69.999984, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, 1164.284667, -911.168395, 41.652160, 89.999992, 55.000019, -45.000011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -723.316284, 945.348388, 10.950842, 89.999992, 86.926528, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -723.612670, 945.376098, 10.950842, 89.999992, 36.926525, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -723.487670, 945.296691, 10.950842, 89.999992, 161.926528, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -722.969726, 945.383483, 10.950842, 89.999992, -117.273468, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -722.844482, 945.705261, 10.950842, 89.999992, -27.273468, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -722.723876, 945.546203, 10.950842, 89.999992, 6.026393, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -722.670776, 945.208251, 10.950842, 89.999992, -57.073562, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -722.211303, 945.410278, 10.950842, 89.999992, -117.573562, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -721.847167, 945.415527, 10.950842, 89.999992, -87.573562, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -721.620971, 945.430358, 10.950842, 89.999992, -137.873550, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -721.302368, 945.400634, 10.950842, 89.999992, -117.573547, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -721.014648, 945.703125, 10.950842, 89.999992, -27.573547, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -721.021484, 945.393066, 10.950842, 89.999992, -27.573547, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -721.018676, 945.093078, 10.950842, 89.999992, -27.573547, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -720.422485, 945.408325, 10.950842, 89.999992, -117.573547, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -720.238037, 945.428588, 10.950842, 89.999992, -85.973541, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -720.020874, 945.470581, 10.950842, 89.999992, -125.573532, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -719.395507, 945.348388, 10.950842, 89.999992, 86.926528, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -719.691894, 945.376098, 10.950842, 89.999992, 36.926525, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -719.566894, 945.296691, 10.950842, 89.999992, 161.926528, -61.926448, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -648.699707, 921.834716, 9.968856, 56.859508, 204.925338, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -648.890197, 921.606628, 9.984214, 56.859508, 154.925338, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -648.763671, 921.669921, 9.940713, 56.859508, 279.925354, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -648.522949, 922.134399, 9.987792, 56.859508, 0.725342, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -648.669982, 922.392578, 10.163619, 56.859508, 90.725341, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -648.491638, 922.413696, 10.076578, 56.859508, 124.025207, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -648.230224, 922.293090, 9.891782, 56.859508, 60.925254, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -648.102111, 922.765686, 10.001901, 56.859508, 0.425246, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -647.894775, 923.065063, 10.004511, 56.859508, 30.425252, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -647.773864, 923.256652, 10.012458, 56.859508, -19.874734, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -647.569091, 923.501953, 9.995981, 56.859508, 0.425265, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -647.608947, 923.883361, 10.161143, 56.859508, 90.425262, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -647.401123, 923.727355, 9.991644, 56.859508, 90.425262, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -647.194946, 923.584106, 9.827636, 56.859508, 90.425262, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -647.064697, 924.223083, 9.999559, 56.859508, 0.425265, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -646.971801, 924.383239, 10.010503, 56.859508, 32.025268, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -646.874633, 924.580749, 10.033305, 56.859508, -7.574732, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -646.429077, 925.031188, 9.966059, 56.859508, 204.925338, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -646.619628, 924.803100, 9.981418, 56.859508, 154.925338, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
    wtrjxadwt = CreateDynamicObject(1881, -646.493103, 924.866394, 9.937917, 56.859508, 279.925354, -125.324821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wtrjxadwt, 0, 2976, "gloopx", "alien_glass", 0x00000000);
}