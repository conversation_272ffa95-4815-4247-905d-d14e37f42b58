new STREAMER_TAG_OBJECT:konserxxt;

CreateKonserExt()
{
    konserxxt = CreateDynamicObject(18981, 961.412475, -1910.602172, 4.743645, 0.000002, 90.000007, 20.799999, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 17547, "eastbeach4a_lae2", "bluestucco1", 0x00000000);
    konserxxt = CreateDynamicObject(18981, 952.538391, -1887.240600, 4.743645, 0.000002, 90.000007, 20.799999, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 17547, "eastbeach4a_lae2", "bluestucco1", 0x00000000);
    konserxxt = CreateDynamicObject(18981, 938.122741, -1919.444335, 4.743645, 0.000005, 90.000015, 20.799999, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 17547, "eastbeach4a_lae2", "bluestucco1", 0x00000000);
    konserxxt = CreateDynamicObject(18981, 929.248657, -1896.082763, 4.743645, 0.000005, 90.000015, 20.799999, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 17547, "eastbeach4a_lae2", "bluestucco1", 0x00000000);
    konserxxt = CreateDynamicObject(18766, 933.945983, -1928.177368, 12.038524, 90.000000, 152.499755, 0.000000, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 18064, "ab_sfammuunits", "gun_blackbox", 0x00000000);
    konserxxt = CreateDynamicObject(18766, 933.945983, -1928.177368, 7.408526, 90.000000, 152.499755, 0.000000, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 18064, "ab_sfammuunits", "gun_blackbox", 0x00000000);
    konserxxt = CreateDynamicObject(18766, 936.244934, -1923.760986, 7.408526, 90.000000, 152.499755, 0.000000, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 18064, "ab_sfammuunits", "gun_blackbox", 0x00000000);
    konserxxt = CreateDynamicObject(18766, 932.996215, -1929.900634, 10.004439, 540.000000, -179.800216, 152.599975, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 14805, "bdupsfurn", "blacksofa01", 0x00000000);
    konserxxt = CreateDynamicObject(19128, 935.019348, -1930.413696, 9.576549, 90.000000, 90.000007, 422.600006, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 6354, "sunset03_law2", "billLA01", 0x00000000);
    konserxxt = CreateDynamicObject(19128, 931.486450, -1928.581665, 9.576549, 90.000000, 90.000007, 422.600006, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 6354, "sunset03_law2", "billLA01", 0x00000000);
    konserxxt = CreateDynamicObject(18763, 942.508361, -1925.801879, 7.733409, 0.000000, 0.000000, 62.500000, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 1809, "cj_hi_fi", "cj_speaker_s", 0x00000000);
    konserxxt = CreateDynamicObject(18763, 931.012207, -1919.817871, 7.733409, 0.000000, 0.000000, 62.500000, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 1809, "cj_hi_fi", "cj_speaker_s", 0x00000000);
    konserxxt = CreateDynamicObject(18766, 926.613647, -1926.020996, 7.408526, 90.000000, 152.499755, 90.000000, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 18064, "ab_sfammuunits", "gun_blackbox", 0x00000000);
    konserxxt = CreateDynamicObject(18981, 935.310424, -1926.322998, 4.713644, 0.000005, 90.000015, 20.799999, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 17547, "eastbeach4a_lae2", "bluestucco1", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 945.634765, -1919.618408, 4.823130, 0.000000, 0.000000, 64.900009, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 936.941406, -1915.546264, 4.823130, 0.000000, 0.000000, 64.900009, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 928.228515, -1911.468872, 4.823130, 0.000000, 0.000000, 64.900009, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 921.134033, -1905.497558, 4.823130, 0.000000, 0.000000, 34.499996, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 916.604919, -1897.070800, 4.823130, 0.000000, 0.000000, 21.699989, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 922.263244, -1885.696777, 4.823130, 0.000000, 0.000000, 110.800010, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 931.256530, -1882.280517, 4.823130, 0.000000, 0.000000, 110.800010, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 936.902404, -1880.188720, 4.803129, 0.000000, 0.000000, 110.800010, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 945.816040, -1880.378540, 4.803129, 0.000000, 0.000000, 66.900062, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 954.729431, -1883.986694, 4.803129, 0.000000, 0.000000, 69.200027, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 961.841125, -1886.666503, 4.833130, 0.000000, 0.000000, 69.200027, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 966.399414, -1893.085815, 4.833130, 0.000000, 0.000000, -178.699691, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 966.535888, -1897.782592, 4.843130, 0.000000, 0.000000, -178.699691, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 968.307922, -1907.072631, 4.843130, 0.000000, 0.000000, -159.899597, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 957.438476, -1921.398071, 4.843130, 0.000000, 0.000000, -69.899597, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 966.471740, -1918.090576, 4.843130, 0.000000, 0.000000, -69.899597, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 948.668395, -1921.086914, 4.813129, 0.000000, 0.000000, 64.900009, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 915.201660, -1893.517333, 4.773128, 0.000000, 0.000000, 21.699989, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 969.711914, -1920.602416, 4.843130, 0.000000, 0.000000, -69.899597, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 960.725036, -1923.892333, 4.843130, 0.000000, 0.000000, -69.899597, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 953.498962, -1926.580810, 4.823130, 0.000000, 0.000000, -69.899597, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 949.364562, -1929.254516, 4.823130, 0.000000, 0.000000, 20.100402, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 946.405273, -1935.312133, 4.823130, 0.000000, 0.000000, 110.100402, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 937.399475, -1938.691284, 4.823130, 0.000000, 0.000000, 111.100387, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 932.613830, -1940.483764, 4.763128, 0.000000, 0.000000, 471.100402, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 926.468261, -1937.614013, 4.763128, 0.000000, 0.000000, 561.100402, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 923.004760, -1928.638549, 4.763128, 0.000000, 0.000000, 561.100402, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 921.154541, -1923.787963, 4.733128, 0.000000, 0.000000, 561.100402, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(19445, 923.970520, -1917.642944, 4.733128, 0.000000, 0.000000, 651.100402, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3306, "cunte_house1", "pinkfence_law", 0x00000000);
    konserxxt = CreateDynamicObject(2662, 921.985595, -1906.478515, 5.862516, 0.000000, 0.000000, 124.599937, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 13598, "destructo", "sunshinebillboard", 0x00000000);
    konserxxt = CreateDynamicObject(2662, 919.686523, -1903.144775, 5.862516, 0.000000, 0.000000, 124.599937, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 10429, "hashblock1_sfs", "ws_streak_billbd", 0x00000000);
    konserxxt = CreateDynamicObject(2662, 917.938110, -1900.067993, 5.862516, 0.000000, 0.000000, 111.699920, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 17535, "lae2billboards", "billbd1_LAe", 0x00000000);
    konserxxt = CreateDynamicObject(2662, 915.997375, -1895.189331, 5.862516, 0.000000, 0.000000, 111.699920, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 10969, "scum_sfse", "ws_jaunk_billbd", 0x00000000);
    konserxxt = CreateDynamicObject(2662, 928.608825, -1911.504150, 5.862516, 0.000000, 0.000000, 155.300216, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 10969, "scum_sfse", "ws_jaunk_billbd", 0x00000000);
    konserxxt = CreateDynamicObject(2662, 932.666503, -1913.403320, 5.862516, 0.000000, 0.000000, 155.300216, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 13598, "destructo", "sunshinebillboard", 0x00000000);
    konserxxt = CreateDynamicObject(2662, 936.683044, -1915.271972, 5.862516, 0.000000, 0.000000, 155.300216, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 10429, "hashblock1_sfs", "ws_streak_billbd", 0x00000000);
    konserxxt = CreateDynamicObject(2662, 941.178283, -1917.392456, 5.862516, 0.000000, 0.000000, 155.300216, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 6349, "sunbill_law2", "SunBillB02", 0x00000000);
    konserxxt = CreateDynamicObject(2662, 948.317016, -1920.730224, 5.862516, 0.000000, 0.000000, 155.300216, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 6336, "sunset02_law2", "SunBillB08", 0x00000000);
    konserxxt = CreateDynamicObject(2662, 955.518798, -1921.947387, 5.862516, 0.000000, 0.000000, -159.899841, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 7416, "vgnbball", "banditsign_256", 0x00000000);
    konserxxt = CreateDynamicObject(2662, 961.867126, -1919.623901, 5.862516, 0.000000, 0.000000, -159.899841, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3083, "billbox", "Sprunk_postersign1", 0x00000000);
    konserxxt = CreateDynamicObject(18766, 925.254150, -1913.823852, 13.283226, 0.000000, 0.000000, -24.900007, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3967, "cj_airprt", "CJ_BLACK_RUB2", 0x00000000);
    konserxxt = CreateDynamicObject(19128, 923.571166, -1912.532958, 13.629707, 89.999992, 248.434906, -93.434951, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 6354, "sunset03_law2", "billLA01", 0x00000000);
    konserxxt = CreateDynamicObject(19128, 927.178161, -1914.213867, 13.629707, 89.999992, 248.434906, -93.434951, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 13598, "destructo", "sunshinebillboard", 0x00000000);
    konserxxt = CreateDynamicObject(7313, 925.406066, -1913.319213, 10.727231, 0.000000, 0.000000, 155.200088, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(konserxxt, 0, "DEWA 19", 70, "Arial", 35, 1, 0xFFFF0000, 0xFF000000, 1);
    konserxxt = CreateDynamicObject(18766, 949.464111, -1921.894287, 13.283226, -0.000003, 0.000006, -24.900001, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 3967, "cj_airprt", "CJ_BLACK_RUB2", 0x00000000);
    konserxxt = CreateDynamicObject(19128, 947.781127, -1920.603393, 13.629707, 89.999992, 246.718978, -91.718994, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 6354, "sunset03_law2", "billLA01", 0x00000000);
    konserxxt = CreateDynamicObject(19128, 951.388122, -1922.284301, 13.629707, 89.999992, 246.718978, -91.718994, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 13598, "destructo", "sunshinebillboard", 0x00000000);
    konserxxt = CreateDynamicObject(7313, 949.616027, -1921.389648, 10.727231, 0.000003, -0.000006, 155.200088, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(konserxxt, 0, "DEWA 19", 70, "Arial", 35, 1, 0xFFFF0000, 0xFF000000, 1);
    konserxxt = CreateDynamicObject(19611, 937.942932, -1922.150268, 7.908256, 0.000000, 0.000007, 0.000000, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    konserxxt = CreateDynamicObject(19611, 936.212768, -1921.229858, 7.908256, 0.000000, 0.000015, 0.000000, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    konserxxt = CreateDynamicObject(18763, 934.534790, -1921.648437, 5.493409, 0.000000, 270.000000, 152.500000, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 1809, "cj_hi_fi", "cj_speaker_s", 0x00000000);
    konserxxt = CreateDynamicObject(18763, 938.943420, -1923.943481, 5.493409, 0.000000, 270.000000, 152.500000, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(konserxxt, 0, 1809, "cj_hi_fi", "cj_speaker_s", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(3447, 930.729980, -1925.554443, 0.201157, 0.199999, 180.000000, 63.199966, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(3447, 938.102539, -1929.278442, 0.172324, 0.199999, 180.000000, 63.199966, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19128, 937.813659, -1924.713745, 7.886537, -0.000007, 0.000013, -27.399991, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19128, 934.288635, -1922.887573, 7.886537, -0.000007, 0.000013, -27.399991, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19157, 936.473632, -1928.892700, 8.947589, 0.000000, 0.000000, -27.600002, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19157, 932.414184, -1926.771118, 8.947589, 0.000000, 0.000000, -27.600002, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19608, 936.495178, -1923.329223, 4.634154, 0.000000, 0.000000, 152.500381, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19143, 938.068298, -1927.236450, 11.476937, 0.000000, 0.000000, -25.400009, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19144, 936.540344, -1926.444580, 11.476937, 0.000000, 0.000000, -26.900014, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19145, 934.511901, -1925.382202, 11.476937, 0.000000, 0.000000, -26.900014, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19146, 932.580932, -1924.437377, 11.476937, 0.000000, 0.000000, -26.900014, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(14407, 929.303161, -1930.792846, 4.670960, 0.000000, 0.000000, -117.999969, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 968.764709, -1914.600341, 5.774766, 0.000000, 0.000000, 20.699991, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 968.348022, -1913.498657, 5.774766, 0.000000, 0.000000, 20.699991, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 967.941711, -1912.423217, 5.774766, 0.000000, 0.000000, 20.699991, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 969.203125, -1915.761108, 5.774766, 0.000000, 0.000000, 20.699991, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(3361, 913.248291, -1885.800903, 3.155820, 0.000000, 0.000000, 111.199989, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(3361, 915.588684, -1884.894042, 3.155820, 0.000000, 0.000000, 111.199989, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(1306, 925.422790, -1913.993774, 4.973877, 0.000000, 180.000000, 154.999984, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19129, 947.450988, -1900.553222, 5.201814, 0.000000, 0.000000, -25.100008, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 917.894592, -1887.237182, 4.277057, 0.000000, 0.000000, -158.099990, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 913.366882, -1889.058227, 4.277057, 0.000000, 0.000000, -158.099990, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19966, 969.814575, -1911.246459, 4.574128, 0.000000, 0.000000, 110.599906, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 973.116149, -1919.375610, 4.582918, 0.000000, 0.000000, 109.099899, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(1306, 949.632751, -1922.064208, 4.973877, 0.000003, 180.000000, 154.999938, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19317, 938.491699, -1924.922851, 8.694446, -15.099996, 0.000000, 180.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19616, 938.422912, -1925.258300, 7.928130, 0.000000, 0.000000, 180.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19609, 935.597290, -1924.495361, 7.945555, 0.000000, 0.000000, 154.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19610, 937.958679, -1922.135009, 9.545513, 24.900005, -0.000007, 154.399963, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19610, 936.228515, -1921.214599, 9.545513, 24.900007, -0.000015, 154.399948, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19318, 933.135131, -1922.629272, 8.617209, -15.099995, -0.000007, 128.899978, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19616, 932.814697, -1922.799560, 7.928130, 0.000000, -0.000007, 128.899978, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(18981, 960.545837, -1923.680419, -7.907325, 0.000000, 0.000000, 110.600006, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(18981, 946.502624, -1922.016723, -7.907325, 0.000000, 0.000000, 200.600006, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(18981, 939.632812, -1937.619995, -8.147328, 0.000000, 0.000000, -69.199989, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(18981, 924.123352, -1930.557495, -8.147328, 0.000000, 0.000000, 20.800010, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(18981, 931.840026, -1914.799804, -8.147328, 0.000000, 0.000000, 110.800010, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(18981, 919.956970, -1905.655151, -8.147328, 0.000000, 0.000000, 200.800018, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(18981, 917.901672, -1900.326660, -8.157328, 0.000000, 0.000000, 200.800018, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(18981, 925.705810, -1884.515014, -8.157328, 0.000000, 0.000000, 290.800018, -1, -1, -1, 200.00, 200.00);
}