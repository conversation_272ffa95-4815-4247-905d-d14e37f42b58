CreateHospitalInt()
{
    new STREAMER_TAG_OBJECT:lsgmcxas;

    //pillbox interior
    lsgmcxas = CreateDynamicObject(18981, -2244.122558, 529.382385, -8.082324, 0.000000, 90.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2250.566162, 518.570922, -8.070322, 89.999992, 450.000000, -89.999992, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2260.554443, 518.570922, -8.070322, 89.999992, 450.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2250.566162, 523.570251, -8.070322, 89.999992, 450.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2240.587646, 518.570922, -8.070322, 89.999992, 450.000000, -89.999992, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2260.554443, 523.570251, -8.070322, 89.999992, 450.000000, -89.999969, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2240.587646, 523.570251, -8.070322, 89.999992, 450.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2240.587646, 521.660217, -8.060322, 89.999992, 450.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2250.586181, 521.660217, -8.060323, 89.999992, 450.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2260.584960, 521.660217, -8.060323, 89.999992, 450.000000, -89.999969, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2237.181640, 516.998107, -5.833051, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1523, -2238.759521, 517.020202, -7.592586, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(1523, -2241.800048, 516.960144, -7.592586, 0.000000, 0.000000, 360.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2246.605712, 517.003234, -5.847567, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2255.674804, 517.003234, -5.847567, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2255.000488, 517.528381, -5.833051, 0.000000, -0.000014, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2257, -2244.745117, 517.151062, -6.008354, 0.000000, -0.000007, 179.999954, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 10023, "bigwhitesfe", "zombotech1", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2237.191650, 517.008117, -5.623047, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2246.603027, 517.023254, -5.617562, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2255.682128, 517.023254, -5.617562, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1776, -2253.777832, 517.500610, -6.481233, 0.000000, -0.000022, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 5443, "glenpark1_lae", "chocolate1", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 2221, "donut_tray", "rustycoffeerap_rb", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 5443, "glenpark1_lae", "chocolate1", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2255.003417, 527.688171, -5.833051, 0.000000, -0.000029, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2255.003417, 529.328002, -5.833051, 0.000000, -0.000029, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2254.998291, 532.485839, -5.847638, 0.000000, 0.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2255.003417, 535.647521, -5.833051, 0.000000, -0.000029, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2253.334716, 535.038024, -5.833051, -0.000029, -0.000007, -89.999969, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2250.128417, 535.036804, -5.847638, 0.000029, 0.000000, 89.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2251.415527, 534.460144, -7.592323, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    lsgmcxas = CreateDynamicObject(2132, -2253.846435, 534.435363, -7.080564, 0.000000, 270.000000, -0.000029, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    lsgmcxas = CreateDynamicObject(19926, -2253.961425, 528.053344, -3.851999, -0.000060, 180.000000, -89.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19926, -2252.532714, 528.053344, -3.851999, -0.000060, 180.000000, -89.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19926, -2250.825683, 528.367553, -3.851999, -0.000035, 180.000000, -69.199943, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19926, -2248.680908, 533.982482, -3.851999, 0.000000, 180.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19926, -2248.680908, 532.072753, -3.851999, 0.000000, 180.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19926, -2249.091308, 530.464111, -3.851999, -0.000018, 180.000000, -28.499977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19926, -2249.681152, 529.374267, -3.841999, -0.000018, 180.000000, -28.499977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19926, -2253.961181, 528.053283, -6.671759, -0.000045, 899.999877, -89.999839, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19926, -2252.532470, 528.063293, -6.681758, -0.000045, 899.999877, -89.999839, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19926, -2250.825439, 528.367492, -6.671759, -0.000029, 179.999969, -69.199790, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19926, -2248.680908, 533.982482, -6.671759, -0.000001, -180.000015, 0.000007, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19926, -2248.680908, 532.072814, -6.671759, -0.000001, -180.000015, 0.000007, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19926, -2249.091064, 530.464172, -6.681758, -0.000024, -179.999984, -28.499959, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19926, -2249.680908, 529.374328, -6.671992, -0.000024, -179.999984, -28.499959, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2235.614746, 521.903625, -5.847567, 0.000000, -0.000007, 179.999954, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2235.614746, 530.293334, -5.847567, 0.000000, -0.000014, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2237.726806, 542.973449, -5.847567, 0.000000, -0.000029, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2235.624755, 521.903625, -5.627564, 0.000000, -0.000007, 179.999954, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2235.624755, 530.283264, -5.627564, 0.000000, -0.000014, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2237.736816, 542.954467, -5.627564, 0.000000, -0.000029, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2240.404052, 535.032897, -5.847567, -0.000014, 0.000000, -89.999954, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1776, -2236.833496, 534.543884, -6.481233, -0.000007, 0.000014, -0.000082, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 5443, "glenpark1_lae", "chocolate1", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 2221, "donut_tray", "rustycoffeerap_rb", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 5443, "glenpark1_lae", "chocolate1", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2244.013427, 535.032897, -5.847567, -0.000014, 0.000000, -89.999954, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2240.367431, 535.023620, -5.627564, -0.000007, 0.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2250.138427, 535.026794, -5.627638, 0.000029, 0.000000, 89.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2243.808837, 535.023620, -5.627564, -0.000007, 0.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2253.304687, 535.028137, -5.623045, -0.000029, -0.000007, -89.999969, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2254.990478, 517.538391, -5.623046, 0.000000, -0.000014, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2254.990478, 527.678344, -5.623046, 0.000000, -0.000014, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2254.990478, 529.468383, -5.623046, 0.000000, -0.000014, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2254.968261, 532.476074, -5.617633, 0.000000, 0.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2254.990478, 535.658325, -5.623046, 0.000000, -0.000014, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2252.416503, 534.460144, -7.592323, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2253.347412, 534.430114, -7.582324, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2253.397460, 534.490173, -4.522321, 0.000000, 179.999969, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2252.396484, 534.490173, -4.522321, 0.000000, 179.999969, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2251.415527, 534.460144, -4.522321, 0.000000, 179.999969, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19479, -2254.890136, 527.937561, -5.542328, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(lsgmcxas, 0, "PILLBOX\nHOSPITAL", 130, "Arial", 22, 1, 0xFFFFFFFF, 0x00000000, 1);
    lsgmcxas = CreateDynamicObject(18981, -2248.044189, 504.472839, -8.062323, 0.000000, 90.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2242.710205, 504.029510, -7.643051, 0.000000, 89.999969, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2269.010253, 529.382385, -8.082324, 0.000000, 90.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2270.553955, 518.570922, -8.070322, 89.999992, 450.000000, -89.999969, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2270.553955, 523.570251, -8.070322, 89.999992, 450.000000, -89.999961, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2265.804687, 521.660217, -8.060323, 89.999992, 450.000000, -89.999961, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2115, -2253.297119, 537.271789, -7.582324, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 2635, "pizza_furn", "CJ_TART_TABLE", 0x00000000);
    lsgmcxas = CreateDynamicObject(1811, -2253.468994, 535.684143, -6.992321, 0.000000, 0.000000, -117.599990, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1811, -2252.001464, 535.598144, -6.992321, 0.000000, 0.000000, -75.800018, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1811, -2253.783203, 538.971252, -6.992321, 0.000000, 0.000000, 116.800041, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1811, -2252.344482, 538.847717, -6.992321, 0.000000, 0.000000, 88.699989, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2255.003417, 537.286804, -5.833051, 0.000000, -0.000045, 179.999725, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2254.990478, 537.297607, -5.623046, 0.000000, -0.000029, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2254.998291, 540.496459, -5.847638, 0.000000, 0.000037, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2241.937011, 535.790588, -7.602275, 0.000000, -0.000029, 449.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2254.968261, 540.486694, -5.617633, 0.000000, 0.000037, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2244.122558, 554.282531, -8.082324, 0.000000, 90.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2269.010253, 554.362304, -8.082324, 0.000000, 90.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2250.125488, 542.012756, -5.847567, -0.000029, 0.000000, -89.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2250.088867, 542.003479, -5.627564, -0.000022, 0.000000, -89.999931, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(3034, -2252.679199, 541.910522, -5.792327, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "mcstraps_window", 0x00000000);
    lsgmcxas = CreateDynamicObject(2131, -2248.703613, 541.423461, -7.582324, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2131, -2246.703369, 541.423461, -7.582324, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2139, -2244.238769, 541.414367, -7.592325, 0.000000, 0.000029, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2139, -2243.249023, 541.414367, -7.592325, 0.000000, 0.000029, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2139, -2242.568359, 541.444702, -7.592325, -0.000029, 0.000000, -89.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2139, -2242.568359, 540.464782, -7.592325, -0.000029, 0.000000, -89.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2139, -2242.568359, 539.484802, -7.592325, -0.000029, 0.000000, -89.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2242.506347, 542.012756, -5.847567, -0.000045, 0.000000, -89.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2242.469726, 542.003479, -5.627564, -0.000037, 0.000000, -89.999885, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2244.142578, 541.748840, -4.296710, 0.000000, 180.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2243.142333, 541.748840, -4.296710, 0.000000, 180.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2241.952880, 542.225891, -5.847567, -0.000029, -0.000007, -179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2241.962158, 542.189270, -5.627564, -0.000022, -0.000007, -179.999847, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2242.381347, 540.588684, -4.296710, -0.000022, 180.000000, -89.999969, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2242.381347, 539.588684, -4.296710, -0.000022, 180.000000, -89.999969, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2241.957275, 536.557373, -5.847638, 0.000029, 0.000000, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2241.967285, 536.567382, -5.627636, 0.000029, 0.000000, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2237.728759, 536.557373, -5.847638, 0.000029, -0.000014, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2237.738769, 536.567382, -5.627636, 0.000029, -0.000014, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1744, -2239.854248, 540.373229, -7.613386, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    lsgmcxas = CreateDynamicObject(1744, -2239.863037, 541.323242, -7.613386, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    lsgmcxas = CreateDynamicObject(1744, -2239.854248, 538.443481, -7.613386, 0.000007, 0.000000, 89.999977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    lsgmcxas = CreateDynamicObject(1744, -2239.863037, 539.393493, -7.613386, -0.000007, 0.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2232.864501, 542.012756, -5.847566, -0.000045, 0.000000, -89.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2234.356933, 538.972778, -5.847567, -0.000022, 0.000000, 0.000068, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2232.825927, 535.052917, -5.847567, -0.000022, 0.000000, -89.999931, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2230.768310, 535.023620, -5.627564, -0.000014, 0.000000, -89.999954, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2237.707763, 542.122863, -5.847568, -0.000022, 0.000000, 0.000068, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    lsgmcxas = CreateDynamicObject(2132, -2234.957031, 539.149291, -7.591300, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 18646, "matcolours", "grey-60-percent", 0x00000000);
    lsgmcxas = CreateDynamicObject(2132, -2234.957031, 537.159423, -7.591300, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 18646, "matcolours", "grey-60-percent", 0x00000000);
    lsgmcxas = CreateDynamicObject(2262, -2234.940429, 538.615112, -5.949923, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 19325, "lsmall_shops", "lsmall_window01", 0x00000000);
    lsgmcxas = CreateDynamicObject(2262, -2234.940429, 536.604919, -5.949923, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 19325, "lsmall_shops", "lsmall_window01", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2235.897705, 539.863098, -7.667568, -0.000022, 90.000000, 0.000068, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2276, -2241.143554, 541.407836, -4.805393, 0.000000, 90.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 4833, "airprtrunway_las", "policeha02black_128", 0x00000000);
    lsgmcxas = CreateDynamicObject(2276, -2239.462890, 541.407836, -4.805393, 0.000000, 90.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 4833, "airprtrunway_las", "policeha02black_128", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2259.899414, 535.041381, -5.847567, -0.000045, -0.000022, -89.999710, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2259.862792, 535.032104, -5.627564, -0.000037, -0.000022, -89.999740, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2269.510986, 535.041381, -5.847567, -0.000052, -0.000022, -89.999679, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2269.474365, 535.032104, -5.627564, -0.000045, -0.000022, -89.999710, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1776, -2269.560302, 534.554077, -6.481233, -0.000007, -0.000022, -0.000128, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 5443, "glenpark1_lae", "chocolate1", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 2221, "donut_tray", "rustycoffeerap_rb", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 5443, "glenpark1_lae", "chocolate1", 0x00000000);
    lsgmcxas = CreateDynamicObject(1817, -2260.987060, 529.631958, -7.592328, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19598, "sfbuilding1", "darkwood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2270.728271, 530.117614, -5.847567, -0.000060, -0.000007, 0.000311, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2270.718994, 530.154235, -5.627564, -0.000052, -0.000007, 0.000281, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2270.728271, 496.008178, -5.847567, -0.000060, 0.000045, 0.000311, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2270.718994, 496.024810, -5.627564, -0.000052, 0.000045, 0.000281, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1523, -2270.732177, 522.220825, -7.592586, -0.000029, -0.000007, -89.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(1523, -2270.672119, 519.180297, -7.592586, 0.000029, 0.000007, 89.999877, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2267.128173, 515.389465, -7.643044, 0.000000, 89.999984, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1523, -2268.680664, 517.000244, -7.592586, -0.000029, 0.000022, 0.000075, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(1523, -2265.640136, 517.060302, -7.592586, 0.000029, -0.000022, 179.999664, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2260.835449, 517.003234, -5.847567, 0.000014, 0.000000, 89.999885, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2260.842773, 517.023254, -5.617561, 0.000014, 0.000000, 89.999885, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2273.484619, 517.003234, -5.847567, 0.000022, 0.000000, 89.999931, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2273.471923, 517.023254, -5.617562, 0.000022, 0.000000, 89.999931, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2270.728271, 527.036804, -5.847567, -0.000059, 0.000000, 0.000311, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2270.718994, 527.023376, -5.627564, -0.000052, 0.000000, 0.000281, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2272.550537, 520.698791, -7.653042, 0.000000, 89.999984, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2276.039306, 520.698791, -7.653040, 0.000000, 89.999984, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2274.528076, 523.888427, -7.653040, 0.000000, 89.999984, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2274.528076, 527.078002, -7.653040, 0.000000, 89.999984, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2276.039306, 527.079162, -7.653040, 0.000000, 89.999984, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2274.528076, 530.288024, -7.653040, 0.000000, 89.999984, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2278.032958, 522.080261, -8.070322, 89.999992, 450.000000, 0.000029, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2278.032958, 532.020446, -8.070322, 89.999992, 450.000000, 0.000029, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2273.161865, 531.020446, -8.070322, 89.999992, 450.000000, 0.000029, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2277.957763, 521.877990, -5.847567, 0.000022, 0.000007, -0.000114, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2277.937744, 521.885314, -5.617562, 0.000022, 0.000007, -0.000114, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2277.957763, 531.507690, -5.847567, 0.000022, 0.000014, -0.000114, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2277.937744, 531.515014, -5.617562, 0.000022, 0.000014, -0.000114, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2275.621337, 532.041625, -5.847567, -0.000075, -0.000022, -89.999610, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2275.584716, 532.032348, -5.627564, -0.000068, -0.000022, -89.999641, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2271.689941, 528.271606, -7.600088, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1369, -2276.849365, 523.194824, -6.996789, 0.000000, 0.000000, -106.999992, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 7103, "vgnplantgen", "metalwheel4_128", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 4, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2270.749023, 527.024047, -5.627563, -0.000051, -0.000007, 0.000280, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2270.719970, 514.436096, -5.847637, 0.000000, 0.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1369, -2269.898437, 530.231201, -6.996789, 0.000000, 0.000000, 79.400009, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 7103, "vgnplantgen", "metalwheel4_128", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 4, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2261.920654, 517.721679, -7.570087, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2273.032470, 503.642852, -8.062323, 0.000000, 90.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2267.128173, 512.179504, -7.643044, 0.000000, 89.999984, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2270.722167, 517.608825, -5.833051, 0.000000, -0.000045, 179.999725, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2270.712158, 517.618835, -5.623046, 0.000000, -0.000045, 179.999725, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2270.719970, 511.226257, -5.847637, 0.000000, 0.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2270.709960, 510.489715, -7.582270, -0.000022, 0.000000, 90.000068, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2270.709960, 511.236236, -5.627634, 0.000000, 0.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2270.709960, 513.699707, -7.582270, -0.000022, 0.000000, 90.000068, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2270.709960, 514.426696, -5.627634, 0.000000, 0.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2270.722167, 508.839141, -5.833051, 0.000000, -0.000052, 179.999679, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2270.712158, 508.819122, -5.623046, 0.000000, -0.000052, 179.999679, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2267.128173, 508.969543, -7.643044, 0.000000, 89.999977, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2267.128173, 505.759582, -7.643044, 0.000000, 89.999977, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2267.128173, 502.599609, -7.643044, 0.000000, 89.999977, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2267.128173, 499.389648, -7.643044, 0.000000, 89.999977, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2267.128173, 496.179687, -7.643044, 0.000000, 89.999969, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2267.128173, 492.969726, -7.643044, 0.000000, 89.999969, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2270.606689, 504.029510, -7.643044, 0.000000, 89.999977, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2263.637207, 504.029510, -7.643047, 0.000000, 89.999977, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2260.146728, 504.029510, -7.643047, 0.000000, 89.999977, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2256.656982, 504.029510, -7.643048, 0.000000, 89.999977, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2253.179443, 504.029510, -7.643047, 0.000000, 89.999969, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2249.688964, 504.029510, -7.643047, 0.000000, 89.999969, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2246.199218, 504.029510, -7.643048, 0.000000, 89.999969, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2263.597167, 512.125000, -5.847567, -0.000052, 0.000029, -179.999542, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2263.606445, 512.088378, -5.627564, -0.000045, 0.000029, -179.999572, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2272.408935, 500.734069, -5.837050, -0.000022, -0.000052, -90.000190, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2272.388916, 500.744079, -5.627048, -0.000022, -0.000052, -90.000190, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2275.593750, 500.731567, -5.847637, 0.000037, 0.000029, 89.999855, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2275.583740, 500.741577, -5.627634, 0.000037, 0.000029, 89.999855, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2278.798583, 500.734069, -5.837050, -0.000029, -0.000052, -90.000167, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2278.778564, 500.744079, -5.627048, -0.000029, -0.000052, -90.000167, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2281.983886, 500.731567, -5.847637, 0.000051, 0.000029, 89.999809, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2281.973876, 500.741577, -5.627634, 0.000050, 0.000029, 89.999809, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2285.188720, 500.734069, -5.837050, -0.000037, -0.000052, -90.000144, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2285.168701, 500.744079, -5.627048, -0.000037, -0.000052, -90.000144, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2288.374511, 500.731567, -5.847637, 0.000052, 0.000029, 89.999809, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2288.364501, 500.741577, -5.627634, 0.000051, 0.000029, 89.999809, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2277.565185, 504.029510, -7.643047, 0.000000, 89.999969, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2274.074707, 504.029510, -7.643047, 0.000000, 89.999969, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2281.059082, 504.029510, -7.643044, 0.000000, 89.999969, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2288.017578, 504.029510, -7.643047, 0.000000, 89.999961, 179.999771, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2284.527099, 504.029510, -7.643047, 0.000000, 89.999961, 179.999771, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2297.922851, 491.642791, -8.062322, 0.000000, 90.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2274.850341, 500.739654, -7.582273, -0.000022, 0.000000, 180.000061, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2281.230224, 500.739654, -7.582273, -0.000022, -0.000007, -179.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2287.639892, 500.739654, -7.582273, -0.000022, 0.000000, 180.000061, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2281.990966, 507.331176, -5.847637, 0.000022, 0.000022, -90.000106, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2281.980957, 507.321411, -5.627634, 0.000022, 0.000022, -90.000106, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2282.724609, 507.323120, -7.582273, -0.000029, 0.000022, 0.000068, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2275.599853, 507.331207, -5.847567, -0.000068, 0.000022, -89.999481, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2275.563232, 507.321929, -5.627564, -0.000060, 0.000022, -89.999511, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2285.168701, 507.333892, -5.837050, -0.000029, -0.000045, 89.999771, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2285.188720, 507.324127, -5.627048, -0.000029, -0.000045, 89.999771, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2288.359863, 507.333892, -5.837050, -0.000022, -0.000045, 89.999748, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2288.379882, 507.324127, -5.627048, -0.000022, -0.000045, 89.999748, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1523, -2263.600097, 502.510284, -7.592586, -0.000014, 0.000029, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(1523, -2263.660156, 505.550811, -7.592586, 0.000014, -0.000029, -90.000274, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2263.597167, 510.374908, -5.847567, -0.000052, 0.000022, -179.999496, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2263.606445, 510.338287, -5.627564, -0.000045, 0.000022, -179.999526, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2263.597167, 497.715179, -5.847567, -0.000052, 0.000007, -179.999404, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2263.606445, 497.718566, -5.627564, -0.000045, 0.000007, -179.999435, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.007568, 504.029510, -7.643047, 0.000000, 89.999954, 179.999725, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2291.517089, 504.029510, -7.643047, 0.000000, 89.999954, 179.999725, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1523, -2295.435058, 502.510284, -7.592586, 0.000007, 0.000029, 89.999931, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(1523, -2295.495117, 505.550811, -7.592586, -0.000007, -0.000029, -90.000205, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(1776, -2273.506591, 506.843872, -6.481233, -0.000007, -0.000022, -0.000128, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 5443, "glenpark1_lae", "chocolate1", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 2221, "donut_tray", "rustycoffeerap_rb", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 5443, "glenpark1_lae", "chocolate1", 0x00000000);
    lsgmcxas = CreateDynamicObject(2257, -2272.495361, 500.871124, -6.008354, 0.000000, -0.000007, 179.999954, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 10023, "bigwhitesfe", "zombotech2", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2264.270996, 497.101898, -7.570086, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2267.164794, 491.352020, -5.847567, -0.000045, 0.000052, 90.000236, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2267.181396, 491.361297, -5.627564, -0.000037, 0.000052, 90.000205, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2263.597167, 488.095123, -5.847567, -0.000052, 0.000000, -179.999359, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2263.606445, 488.098510, -5.627564, -0.000045, 0.000000, -179.999389, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1369, -2264.359619, 507.785278, -6.966790, 0.000000, 0.000000, -176.399993, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 7103, "vgnplantgen", "metalwheel4_128", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 4, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(1369, -2264.446289, 509.162536, -6.966790, 0.000000, 0.000000, -176.399993, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 7103, "vgnplantgen", "metalwheel4_128", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 4, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(1369, -2264.532958, 510.539733, -6.966790, 0.000000, 0.000000, -176.399993, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 7103, "vgnplantgen", "metalwheel4_128", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 4, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2289.878173, 499.063903, -5.837056, -0.000037, -0.000052, -0.000144, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2289.878173, 495.864074, -5.837053, -0.000037, -0.000052, -0.000144, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19399, -2284.132568, 496.813995, -5.837273, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2284.135986, 494.204010, -5.837051, -0.000037, -0.000045, -0.000144, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2285.957519, 499.063903, -7.647061, -0.000007, 89.999961, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19517, "noncolored", "gen_white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2285.957519, 495.864074, -7.647061, -0.000007, 89.999961, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19517, "noncolored", "gen_white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2288.207275, 499.063903, -7.637062, -0.000007, 89.999969, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19517, "noncolored", "gen_white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2288.207275, 495.864074, -7.637062, -0.000007, 89.999969, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19517, "noncolored", "gen_white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2288.205810, 494.333953, -5.837056, -0.000022, -0.000037, 89.999778, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2285.426513, 494.333953, -5.837053, -0.000022, -0.000037, 89.999778, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2284.135986, 499.184051, -5.837056, -0.000037, -0.000045, -0.000144, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2282.225585, 494.333953, -5.837056, -0.000014, -0.000037, 89.999755, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2279.025878, 494.333953, -5.837052, -0.000014, -0.000037, 89.999755, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19399, -2278.951171, 496.813995, -5.837273, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2278.954589, 494.204010, -5.837051, -0.000037, -0.000037, -0.000144, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2278.954589, 499.184051, -5.837056, -0.000037, -0.000037, -0.000144, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2280.605468, 499.063903, -7.647060, -0.000006, 89.999977, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2280.605468, 495.864074, -7.647060, -0.000006, 89.999977, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2282.314697, 499.063903, -7.637062, -0.000006, 89.999977, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2282.314697, 495.864074, -7.637062, -0.000006, 89.999977, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(2165, -2283.558593, 496.300445, -7.538342, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 3, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2165, -2279.527587, 497.260437, -7.538342, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 3, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2174, -2279.522460, 500.148101, -7.555517, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 18646, "matcolours", "grey-60-percent", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2279.527832, 495.119750, -7.560781, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2274.857910, 499.063903, -7.647060, -0.000006, 89.999969, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19517, "noncolored", "gen_white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2274.857910, 495.864074, -7.647060, -0.000006, 89.999969, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19517, "noncolored", "gen_white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2277.107666, 499.063903, -7.637062, -0.000006, 89.999977, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19517, "noncolored", "gen_white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2277.107666, 495.864074, -7.637062, -0.000006, 89.999977, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19517, "noncolored", "gen_white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2278.688232, 512.206909, -5.847567, 0.000029, -0.000014, 179.999786, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2278.708251, 512.199584, -5.617561, 0.000029, -0.000014, 179.999786, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2281.970947, 515.251037, -5.847637, 0.000014, 0.000019, -90.000083, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2281.980957, 515.241271, -5.627634, 0.000014, 0.000020, -90.000083, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2282.724609, 515.242980, -7.582273, -0.000029, 0.000029, 0.000068, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(2131, -2284.575439, 507.913574, -7.582327, 0.000000, 0.000007, 540.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2139, -2283.578857, 507.934326, -7.592345, 0.000000, 0.000029, 180.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2283.611816, 507.748718, -5.076715, 0.000000, 180.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2008, -2283.617919, 511.823699, -7.584756, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 15034, "genhotelsave", "walp57S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 4, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2280.605468, 512.063598, -7.647060, -0.000006, 89.999984, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2280.605468, 508.863800, -7.647060, -0.000006, 89.999984, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2282.314697, 512.063598, -7.637062, -0.000006, 89.999984, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2282.314697, 508.863800, -7.637062, -0.000006, 89.999984, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2285.814453, 512.063598, -7.647060, -0.000006, 89.999992, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2285.814453, 508.863800, -7.647060, -0.000006, 89.999992, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2285.954589, 515.382751, -7.637062, 0.000007, 90.000007, 89.999961, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2282.754882, 515.382751, -7.637062, 0.000007, 90.000007, 89.999961, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2279.544921, 515.382751, -7.637062, 0.000007, 90.000007, 89.999961, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2278.770263, 515.253601, -5.837049, 0.000007, -0.000044, 89.999656, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2278.790283, 515.243835, -5.627048, 0.000007, -0.000044, 89.999656, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2285.169677, 515.253601, -5.837049, 0.000015, -0.000044, 89.999633, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2285.189697, 515.243835, -5.627048, 0.000015, -0.000044, 89.999633, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2286.379394, 512.199584, -5.847567, 0.000022, -0.000014, -0.000281, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2286.359375, 512.206909, -5.617561, 0.000022, -0.000014, -0.000281, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2282.722412, 518.533264, -5.847567, 0.000014, -0.000014, -90.000305, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2282.715087, 518.513244, -5.617561, 0.000014, -0.000014, -90.000305, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2278.695312, 517.828857, -5.837049, 0.000015, -0.000044, -0.000403, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2278.705078, 517.848876, -5.627048, 0.000015, -0.000044, -0.000403, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2286.375976, 517.848876, -5.837049, 0.000023, -0.000044, 179.999465, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2286.366210, 517.828857, -5.627048, 0.000023, -0.000044, 179.999465, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2285.954589, 518.862548, -7.637062, 0.000014, 90.000007, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2282.754882, 518.862548, -7.637062, 0.000014, 90.000007, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2279.544921, 518.862548, -7.637062, 0.000014, 90.000007, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(2008, -2280.277832, 516.934082, -7.584756, 0.000000, 0.000000, 450.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 15034, "genhotelsave", "walp57S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 4, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2139, -2285.789306, 517.914428, -7.552350, 0.000007, 0.000029, 89.999977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2139, -2285.789306, 516.944396, -7.552350, 0.000007, 0.000029, 89.999977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2139, -2285.789306, 515.954162, -7.552350, 0.000007, 0.000029, 89.999977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(3034, -2283.280761, 518.420349, -5.792325, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "mcstraps_window", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2298.506347, 504.029510, -7.643046, 0.000000, 89.999954, 179.999725, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2305.496337, 504.029510, -7.643046, 0.000000, 89.999946, 179.999679, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2302.005859, 504.029510, -7.643046, 0.000000, 89.999946, 179.999679, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2299.044677, 500.674713, -7.643045, -0.000029, 89.999946, -90.000167, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2299.044677, 493.684722, -7.643045, -0.000029, 89.999938, -90.000213, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2299.044677, 497.175201, -7.643045, -0.000029, 89.999938, -90.000213, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2299.044677, 514.354370, -7.643045, -0.000037, 89.999946, -90.000144, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2299.044677, 507.364349, -7.643045, -0.000037, 89.999938, -90.000190, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2299.044677, 510.854827, -7.643045, -0.000037, 89.999938, -90.000190, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.458496, 507.158905, -5.837049, -0.000012, -0.000075, 179.999389, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.478759, 507.128875, -5.627048, -0.000012, -0.000075, 179.999389, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.449462, 500.918914, -5.627046, -0.000020, -0.000044, -0.000510, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2302.563720, 486.129211, -5.837049, -0.000012, -0.000134, 179.998672, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.468750, 515.658691, -5.837049, -0.000020, -0.000029, -0.000646, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.478515, 515.638427, -5.627048, -0.000020, -0.000029, -0.000646, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2295.463134, 512.454406, -5.847637, 0.000022, -0.000022, 179.999481, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2295.472900, 512.434387, -5.627634, 0.000022, -0.000022, 179.999481, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2295.471191, 513.198059, -7.582273, -0.000074, 0.000022, -89.999748, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2295.463134, 518.854125, -5.847637, 0.000022, -0.000029, 179.999435, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2295.472900, 518.844116, -5.627634, 0.000022, -0.000029, 179.999435, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2295.471191, 519.597778, -7.582273, -0.000081, 0.000022, -89.999725, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2302.578613, 509.199066, -5.837049, -0.000012, -0.000097, 179.999252, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2302.568847, 509.179046, -5.627048, -0.000012, -0.000097, 179.999252, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2302.563720, 515.569213, -5.837049, -0.000012, -0.000074, 179.999038, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2302.553955, 515.579467, -5.627048, -0.000012, -0.000074, 179.999038, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2302.569580, 518.773315, -5.847637, 0.000012, 0.000022, -0.000418, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2302.559814, 518.783325, -5.627634, 0.000012, 0.000022, -0.000418, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2302.561279, 518.029663, -7.582273, -0.000029, 0.000028, 90.000053, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2302.569580, 512.373657, -5.847637, 0.000012, 0.000014, -0.000464, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2302.559814, 512.383666, -5.627634, 0.000012, 0.000014, -0.000464, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2302.561279, 511.630004, -7.582273, -0.000037, 0.000028, 90.000076, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.468750, 509.338867, -5.837049, -0.000020, -0.000044, -0.000510, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.478515, 509.358886, -5.627048, -0.000020, -0.000044, -0.000510, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.449462, 507.148834, -5.627048, -0.000020, -0.000044, -0.000510, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2302.578613, 498.928619, -5.837049, -0.000012, -0.000159, 179.998886, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2302.568847, 498.908599, -5.627048, -0.000012, -0.000159, 179.998886, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2302.563720, 492.529022, -5.837049, -0.000012, -0.000127, 179.998718, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2302.553955, 492.539276, -5.627048, -0.000012, -0.000127, 179.998718, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2302.569580, 495.723083, -5.847637, 0.000012, 0.000075, -0.000418, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2302.559814, 495.733123, -5.627634, 0.000012, 0.000075, -0.000418, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2302.561279, 494.989471, -7.582273, 0.000024, 0.000028, 89.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2302.569580, 489.333465, -5.847637, 0.000012, 0.000068, -0.000464, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2302.559814, 489.343475, -5.627634, 0.000012, 0.000068, -0.000464, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2302.561279, 488.589813, -7.582273, 0.000015, 0.000028, 89.999916, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2291.559570, 507.333892, -5.837049, -0.000014, -0.000044, 89.999725, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2291.579589, 507.324127, -5.627048, -0.000014, -0.000044, 89.999725, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.850830, 507.333892, -5.837049, -0.000006, -0.000044, 89.999702, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.870849, 507.324127, -5.627048, -0.000006, -0.000044, 89.999702, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2291.578369, 500.734069, -5.837049, -0.000045, -0.000052, -90.000122, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2291.558349, 500.744079, -5.627048, -0.000045, -0.000052, -90.000122, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.829589, 500.734069, -5.837049, -0.000052, -0.000052, -90.000099, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.809570, 500.744079, -5.627048, -0.000052, -0.000052, -90.000099, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(2257, -2287.385742, 507.201080, -6.008354, 0.000000, -0.000007, 359.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 10023, "bigwhitesfe", "zombotech2", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.468750, 492.418762, -5.837049, -0.000020, -0.000020, -0.000646, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.478515, 492.408508, -5.627048, -0.000020, -0.000020, -0.000646, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2295.463134, 489.214477, -5.847637, 0.000022, -0.000029, 179.999435, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2295.472900, 489.204467, -5.627634, 0.000022, -0.000029, 179.999435, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2295.471191, 489.958129, -7.582273, -0.000081, 0.000022, -89.999725, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2295.463134, 495.614196, -5.847637, 0.000022, -0.000037, 179.999389, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2295.472900, 495.604187, -5.627634, 0.000022, -0.000037, 179.999389, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2295.471191, 496.357849, -7.582273, -0.000090, 0.000022, -89.999702, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.458496, 498.729217, -5.837049, -0.000012, -0.000090, 179.999298, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.468750, 500.909179, -5.837049, -0.000020, -0.000029, -0.000510, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.478515, 500.929199, -5.627048, -0.000020, -0.000029, -0.000510, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.488525, 498.719146, -5.627048, -0.000020, -0.000029, -0.000510, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2302.553955, 486.139465, -5.627048, -0.000012, -0.000134, 179.998672, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.468750, 486.018951, -5.837049, -0.000020, -0.000012, -0.000646, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.478515, 486.008697, -5.627048, -0.000020, -0.000012, -0.000646, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.468750, 522.058471, -5.837049, -0.000020, -0.000020, -0.000646, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.478515, 522.048217, -5.627048, -0.000020, -0.000020, -0.000646, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2302.563720, 521.968994, -5.837049, -0.000012, -0.000081, 179.998992, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2302.553955, 521.979248, -5.627048, -0.000012, -0.000081, 179.998992, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2287.512451, 507.343261, -5.617558, 0.000022, 0.000000, 89.999931, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2290.673339, 507.343261, -5.617558, 0.000022, 0.000000, 89.999931, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2276.403320, 507.343261, -5.617562, 0.000022, 0.000000, 89.999931, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2281.920898, 507.341430, -5.627634, 0.000022, 0.000022, 89.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2295.442871, 512.414367, -5.627634, 0.000022, -0.000022, 359.999481, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.448486, 509.358886, -5.627048, -0.000020, -0.000044, 179.999481, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.448486, 515.618408, -5.627048, -0.000020, -0.000029, -0.000646, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2295.432861, 518.824096, -5.627634, 0.000022, -0.000029, 179.999435, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.448486, 522.038208, -5.627048, -0.000020, -0.000020, 179.999359, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.769531, 516.273681, -5.837049, 0.000000, -0.000044, 89.999679, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.789550, 516.263916, -5.627048, 0.000000, -0.000044, 89.999679, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.589111, 516.273681, -5.837049, 0.000007, -0.000044, 89.999656, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.609130, 516.263916, -5.627048, 0.000007, -0.000044, 89.999656, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.769531, 520.363708, -5.837049, 0.000000, -0.000044, 89.999679, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.789550, 520.353942, -5.627048, 0.000000, -0.000044, 89.999679, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.589111, 520.363708, -5.837049, 0.000007, -0.000044, 89.999656, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.609130, 520.353942, -5.627048, 0.000007, -0.000044, 89.999656, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2292.960937, 517.131591, -7.600088, -0.000007, 0.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.769531, 510.593658, -5.837049, 0.000007, -0.000044, 89.999656, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.789550, 510.583892, -5.627048, 0.000007, -0.000044, 89.999656, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.589111, 510.593658, -5.837049, 0.000015, -0.000044, 89.999633, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.609130, 510.583892, -5.627048, 0.000015, -0.000044, 89.999633, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2292.960937, 511.451568, -7.600088, -0.000014, 0.000000, -89.999954, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.789550, 510.613891, -5.627048, 0.000023, -0.000044, 89.999610, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.609130, 510.613891, -5.627048, 0.000030, -0.000044, 89.999588, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.789550, 516.304138, -5.627048, 0.000023, -0.000044, 89.999610, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.609130, 516.304138, -5.627048, 0.000030, -0.000044, 89.999588, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2288.936767, 515.486633, -5.847567, 0.000037, -0.000014, 179.999771, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2288.956787, 515.479309, -5.617561, 0.000037, -0.000014, 179.999771, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2291.780761, 515.351562, -7.600088, -0.000014, 0.000000, 90.000045, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2289.517578, 513.659973, -7.560781, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2289.517578, 514.659790, -7.560781, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2294.877929, 514.049804, -7.560781, 0.000000, 0.000000, 450.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2297.922851, 516.562744, -8.062322, 0.000000, 90.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2289.517578, 518.160095, -7.560781, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2289.517578, 519.150085, -7.560781, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1369, -2296.031494, 507.743011, -6.966790, -0.000000, -0.000022, -178.899856, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 7103, "vgnplantgen", "metalwheel4_128", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 4, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(1369, -2296.058105, 509.122741, -6.966790, -0.000000, -0.000022, -178.899856, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 7103, "vgnplantgen", "metalwheel4_128", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 4, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(1369, -2296.084716, 510.502410, -6.966790, -0.000000, -0.000022, -178.899856, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 7103, "vgnplantgen", "metalwheel4_128", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 4, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2304.254394, 514.704223, -5.837049, 0.000000, -0.000051, -90.000328, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2304.234375, 514.713989, -5.627048, 0.000000, -0.000051, -90.000328, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2307.435058, 514.704223, -5.837049, 0.000007, -0.000051, -90.000358, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2307.415039, 514.713989, -5.627048, 0.000007, -0.000051, -90.000358, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2304.254394, 510.614135, -5.837049, 0.000000, -0.000051, -90.000328, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2304.234375, 510.623901, -5.627048, 0.000000, -0.000051, -90.000328, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2307.435058, 510.614135, -5.837049, 0.000007, -0.000051, -90.000358, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2307.415039, 510.623901, -5.627048, 0.000007, -0.000051, -90.000358, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2305.062988, 513.846313, -7.600088, -0.000007, 0.000007, 89.999961, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2304.254394, 520.384155, -5.837049, 0.000007, -0.000051, -90.000358, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2304.234375, 520.393920, -5.627048, 0.000007, -0.000051, -90.000358, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2307.435058, 520.384155, -5.837049, 0.000015, -0.000051, -90.000373, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2307.415039, 520.393920, -5.627048, 0.000015, -0.000051, -90.000373, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2305.062988, 519.526245, -7.600088, -0.000014, 0.000007, 89.999984, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2304.234375, 520.363891, -5.627048, 0.000023, -0.000051, -90.000404, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2307.415039, 520.363891, -5.627048, 0.000030, -0.000051, -90.000419, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2304.234375, 514.673706, -5.627048, 0.000023, -0.000051, -90.000404, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2307.415039, 514.673706, -5.627048, 0.000030, -0.000051, -90.000419, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2309.087402, 515.491333, -5.847567, 0.000029, -0.000014, -0.000220, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2309.067382, 515.498657, -5.617561, 0.000029, -0.000014, -0.000220, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2306.243164, 515.626342, -7.600088, -0.000014, -0.000007, -89.999885, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2308.506347, 517.317993, -7.560781, 0.000000, 0.000007, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2308.506347, 516.317993, -7.560781, 0.000000, 0.000007, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2303.145996, 516.928100, -7.560781, 0.000000, -0.000007, -90.000007, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2308.506347, 512.817749, -7.560781, 0.000000, 0.000007, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2308.506347, 511.827758, -7.560781, 0.000000, 0.000007, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2304.254394, 491.734100, -5.837049, -0.000014, -0.000051, -90.000282, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2304.234375, 491.743865, -5.627048, -0.000014, -0.000051, -90.000282, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2307.435058, 491.734100, -5.837049, -0.000006, -0.000051, -90.000312, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2307.415039, 491.743865, -5.627048, -0.000006, -0.000051, -90.000312, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2304.254394, 487.644012, -5.837049, -0.000014, -0.000051, -90.000282, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2304.234375, 487.653778, -5.627048, -0.000014, -0.000051, -90.000282, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2307.435058, 487.644012, -5.837049, -0.000006, -0.000051, -90.000312, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2307.415039, 487.653778, -5.627048, -0.000006, -0.000051, -90.000312, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2305.062988, 490.876190, -7.600088, 0.000007, 0.000007, 89.999916, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2304.254394, 497.414031, -5.837049, -0.000006, -0.000051, -90.000312, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2304.234375, 497.423797, -5.627048, -0.000006, -0.000051, -90.000312, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2307.435058, 497.414031, -5.837049, 0.000000, -0.000051, -90.000328, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2307.415039, 497.423797, -5.627048, 0.000000, -0.000051, -90.000328, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2305.062988, 496.556121, -7.600088, 0.000000, 0.000007, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2304.234375, 497.393768, -5.627048, 0.000007, -0.000051, -90.000358, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2307.415039, 497.393768, -5.627048, 0.000015, -0.000051, -90.000373, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2304.234375, 491.703582, -5.627048, 0.000007, -0.000051, -90.000358, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2307.415039, 491.703582, -5.627048, 0.000015, -0.000051, -90.000373, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2309.087402, 492.521209, -5.847567, 0.000029, 0.000000, -0.000220, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2309.067382, 492.528533, -5.617561, 0.000029, 0.000000, -0.000220, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2306.243164, 492.656219, -7.600088, -0.000029, -0.000007, -89.999839, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2308.506347, 494.347869, -7.560781, 0.000014, 0.000007, 89.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2308.506347, 493.347869, -7.560781, 0.000014, 0.000007, 89.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2303.145996, 493.957977, -7.560781, -0.000014, -0.000007, -89.999961, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2308.506347, 489.847625, -7.560781, 0.000014, 0.000007, 89.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2308.506347, 488.857635, -7.560781, 0.000014, 0.000007, 89.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2302.579833, 489.343444, -5.627634, 0.000012, 0.000068, -0.000464, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2302.579833, 495.733154, -5.627634, 0.000012, 0.000075, -0.000418, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2302.583984, 492.539276, -5.627048, -0.000012, -0.000127, 179.998718, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.634521, 492.534088, -5.837049, -0.000029, -0.000051, -90.000236, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.614501, 492.543853, -5.627048, -0.000029, -0.000051, -90.000236, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.815185, 492.534088, -5.837049, -0.000020, -0.000051, -90.000267, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.795166, 492.543853, -5.627048, -0.000020, -0.000051, -90.000267, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.614501, 492.503570, -5.627048, -0.000006, -0.000051, -90.000312, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.795166, 492.503570, -5.627048, 0.000000, -0.000051, -90.000328, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2289.875976, 489.438537, -5.847567, 0.000037, -0.000007, 179.999603, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2289.895996, 489.431213, -5.617561, 0.000037, -0.000007, 179.999603, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2289.895996, 495.861114, -5.617566, 0.000037, -0.000007, 179.999603, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2291.558349, 500.714019, -5.627048, -0.000059, -0.000052, -90.000076, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.809570, 500.714019, -5.627048, -0.000068, -0.000052, -90.000053, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.634521, 484.724060, -5.837049, -0.000037, -0.000051, -90.000213, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.614501, 484.733825, -5.627048, -0.000037, -0.000051, -90.000213, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.815185, 484.724060, -5.837049, -0.000029, -0.000051, -90.000244, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.795166, 484.733825, -5.627048, -0.000029, -0.000051, -90.000244, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.614501, 484.693542, -5.627048, -0.000014, -0.000051, -90.000289, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.795166, 484.693542, -5.627048, -0.000006, -0.000051, -90.000305, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.438476, 498.719146, -5.627048, -0.000020, -0.000029, -0.000510, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2295.442871, 495.604187, -5.627634, 0.000022, -0.000037, 179.999389, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.458496, 492.408508, -5.627048, -0.000020, -0.000020, -0.000646, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2295.452880, 489.204528, -5.627634, 0.000022, -0.000029, 179.999435, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.448486, 486.038879, -5.627048, -0.000020, -0.000012, -0.000646, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.918457, 498.918487, -7.627059, 0.000009, 90.000000, 89.999961, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2291.549560, 498.918487, -7.637060, 0.000015, 90.000000, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.918457, 494.369201, -7.617061, 0.000000, 90.000000, 89.999984, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2291.549560, 494.369140, -7.627060, 0.000009, 90.000000, 89.999961, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.918457, 496.678466, -7.627059, 0.000015, 90.000000, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2291.549560, 496.678466, -7.637060, 0.000024, 90.000000, 89.999916, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2291.549560, 490.698516, -7.637060, 0.000024, 90.000000, 89.999916, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2291.549560, 486.149169, -7.627060, 0.000015, 90.000000, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2291.549560, 488.458496, -7.637060, 0.000031, 90.000000, 89.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.890136, 490.698516, -7.627059, 0.000031, 90.000000, 89.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.890136, 486.149169, -7.617061, 0.000024, 90.000000, 89.999916, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.890136, 488.458496, -7.627059, 0.000038, 90.000000, 89.999870, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.638671, 518.548767, -7.637060, 0.000015, 90.000000, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.638671, 514.458679, -7.637060, 0.000015, 90.000000, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2290.638671, 510.958679, -7.637060, 0.000015, 90.000000, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.789550, 518.548767, -7.637060, 0.000024, 90.000000, 89.999916, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.789550, 514.458679, -7.637060, 0.000024, 90.000000, 89.999916, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.789550, 510.958679, -7.637060, 0.000024, 90.000000, 89.999916, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2293.798828, 516.818725, -7.647059, 0.000015, 90.000000, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2289.158691, 516.818725, -7.647059, 0.000015, 90.000000, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2293.480468, 499.241546, -7.600088, -0.000014, 0.000000, -89.999954, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2293.480468, 496.661590, -7.600088, -0.000014, 0.000000, -89.999954, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2293.480468, 494.131561, -7.600088, -0.000014, 0.000000, -89.999954, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2293.480468, 491.261566, -7.600088, -0.000022, 0.000000, -89.999931, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2293.480468, 488.681610, -7.600088, -0.000022, 0.000000, -89.999931, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2293.480468, 486.151580, -7.600088, -0.000022, 0.000000, -89.999931, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2294.865966, 498.657318, -7.560781, 0.000000, 0.000007, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2294.865966, 497.657562, -7.560781, 0.000000, 0.000007, 89.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2294.865966, 486.497375, -7.560781, 0.000007, 0.000007, 89.999916, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2294.865966, 485.497619, -7.560781, 0.000007, 0.000007, 89.999916, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(3034, -2289.049560, 518.230285, -5.512324, 0.000000, 0.000007, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "mcstraps_window", 0x00000000);
    lsgmcxas = CreateDynamicObject(3034, -2289.989501, 496.470397, -5.332324, 0.000000, 0.000007, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "mcstraps_window", 0x00000000);
    lsgmcxas = CreateDynamicObject(3034, -2289.989501, 488.480407, -5.332324, 0.000000, 0.000007, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "mcstraps_window", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2299.044677, 490.214599, -7.643045, -0.000037, 89.999946, -90.000144, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2299.044677, 483.224609, -7.643045, -0.000037, 89.999938, -90.000190, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2299.044677, 486.715087, -7.643045, -0.000037, 89.999938, -90.000190, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2299.044677, 524.834411, -7.643045, -0.000045, 89.999946, -90.000122, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2299.044677, 517.844421, -7.643045, -0.000045, 89.999938, -90.000167, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2299.044677, 521.334899, -7.643045, -0.000045, 89.999938, -90.000167, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2308.985351, 504.029510, -7.643045, 0.000000, 89.999946, 179.999679, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2315.975341, 504.029510, -7.643045, 0.000000, 89.999938, 179.999633, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2312.484863, 504.029510, -7.643045, 0.000000, 89.999938, 179.999633, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19696, -2299.118652, 483.434234, -8.935729, 0.000000, 90.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 6522, "cuntclub_law2", "marinawindow1_256", 0x00000000);
    lsgmcxas = CreateDynamicObject(19696, -2299.118652, 524.933837, -8.965728, 0.000000, 90.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 6522, "cuntclub_law2", "marinawindow1_256", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.075195, 484.724060, -5.837049, -0.000044, -0.000050, -90.000190, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.085205, 484.733825, -5.627048, -0.000044, -0.000051, -90.000190, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2303.214599, 484.724060, -5.837049, -0.000051, -0.000051, -90.000167, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2303.194580, 484.733825, -5.627048, -0.000051, -0.000051, -90.000167, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2303.194580, 523.643493, -5.837049, -0.000051, -0.000045, 89.999771, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2303.184570, 523.633728, -5.627048, -0.000051, -0.000045, 89.999771, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.063964, 523.643493, -5.837049, -0.000044, -0.000045, 89.999748, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.083984, 523.633728, -5.627048, -0.000044, -0.000045, 89.999748, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2307.461425, 507.683288, -5.847567, 0.000014, -0.000007, -90.000061, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2307.454101, 507.663269, -5.617561, 0.000014, -0.000007, -90.000061, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2307.484130, 500.443298, -5.847567, 0.000029, 0.000000, 89.999832, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2307.291259, 500.463317, -5.617561, 0.000029, 0.000000, 89.999832, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2322.841796, 501.972717, -8.062322, 0.000000, 90.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2313.045898, 500.446563, -5.847637, 0.000067, 0.000082, 89.999389, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2313.055908, 500.456329, -5.627634, 0.000067, 0.000082, 89.999389, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2312.302246, 500.454864, -7.582273, 0.000030, -0.000024, 179.999526, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2313.042968, 507.676330, -5.847637, 0.000044, 0.000075, -90.000549, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2313.052978, 507.666564, -5.627634, 0.000044, 0.000075, -90.000549, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2313.786621, 507.668029, -7.582273, 0.000024, -0.000000, -0.000372, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2316.253417, 500.444152, -5.837049, -0.000082, -0.000051, -90.000076, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2316.233398, 500.453918, -5.627048, -0.000082, -0.000051, -90.000076, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2316.233398, 507.674133, -5.857050, -0.000082, -0.000045, 89.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2316.253417, 507.664367, -5.647047, -0.000082, -0.000045, 89.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19696, -2319.188720, 504.044219, -8.935729, 0.000000, 90.000000, 360.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 6522, "cuntclub_law2", "marinawindow1_256", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2317.859863, 508.099365, -5.857050, -0.000075, -0.000059, 179.999725, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2317.850097, 508.079345, -5.647047, -0.000075, -0.000059, 179.999725, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2317.859863, 499.979370, -5.857050, -0.000075, -0.000067, 179.999679, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2317.850097, 499.989379, -5.647047, -0.000075, -0.000067, 179.999679, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2319.435546, 504.029510, -7.643044, 0.000000, 89.999938, 179.999633, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1776, -2315.456542, 507.193908, -6.481233, -0.000007, -0.000014, -0.000127, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 5443, "glenpark1_lae", "chocolate1", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 2221, "donut_tray", "rustycoffeerap_rb", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 5443, "glenpark1_lae", "chocolate1", 0x00000000);
    lsgmcxas = CreateDynamicObject(2257, -2310.834960, 507.541107, -6.008356, 0.000000, -0.000007, 359.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 10023, "bigwhitesfe", "zombotech2", 0x00000000);
    lsgmcxas = CreateDynamicObject(1369, -2302.065917, 487.362487, -6.966790, -0.000007, -0.000022, 1.100196, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 7103, "vgnplantgen", "metalwheel4_128", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 4, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(1369, -2302.039306, 485.982849, -6.966790, -0.000007, -0.000022, 1.100196, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 7103, "vgnplantgen", "metalwheel4_128", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 4, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(1776, -2302.078125, 515.462951, -6.481233, 0.000007, -0.000006, 89.999794, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 5443, "glenpark1_lae", "chocolate1", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 2221, "donut_tray", "rustycoffeerap_rb", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 5443, "glenpark1_lae", "chocolate1", 0x00000000);
    lsgmcxas = CreateDynamicObject(2257, -2302.435302, 498.361083, -6.008347, 0.000000, -0.000007, 809.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 10023, "bigwhitesfe", "zombotech1", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2311.483154, 509.349121, -5.837049, 0.000000, -0.000051, 179.999542, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2311.473388, 509.329101, -5.627048, 0.000000, -0.000051, 179.999542, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2311.483154, 512.529785, -5.837049, 0.000007, -0.000051, 179.999511, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2311.473388, 512.509765, -5.627048, 0.000007, -0.000051, 179.999511, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2315.572998, 509.349121, -5.837049, 0.000000, -0.000051, 179.999542, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2315.563232, 509.329101, -5.627048, 0.000000, -0.000051, 179.999542, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2315.572998, 512.529785, -5.837049, 0.000007, -0.000051, 179.999511, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2315.563232, 512.509765, -5.627048, 0.000007, -0.000051, 179.999511, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2312.341064, 508.297698, -7.600088, -0.000007, 0.000007, -0.000082, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2316.086914, 514.182128, -5.847567, 0.000022, -0.000014, -90.000244, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2316.079589, 514.162109, -5.617561, 0.000022, -0.000014, -90.000244, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2313.719726, 513.601074, -7.560781, 0.000000, 0.000014, -0.000098, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2314.709960, 513.601074, -7.560781, 0.000000, 0.000014, -0.000098, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2165, -2315.001464, 510.197998, -7.564627, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 3, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2311.493408, 509.329101, -5.627048, 0.000000, -0.000067, 179.999450, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2311.493408, 512.509765, -5.627048, 0.000007, -0.000067, 179.999420, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2313.052978, 507.696594, -5.627634, 0.000044, 0.000075, -90.000549, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2316.253417, 507.674407, -5.647047, -0.000082, -0.000045, 89.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(2131, -2312.104980, 513.573547, -7.582317, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(1806, -2313.667480, 510.606231, -7.574872, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2267, -2315.453613, 509.929840, -5.451159, 0.000014, 0.000000, 89.999954, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2267, -2315.453613, 511.419738, -5.451159, 0.000014, 0.000000, 89.999954, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2316.758300, 495.569580, -5.847567, 0.000006, 0.000014, -0.000068, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2316.738281, 495.576904, -5.617561, 0.000006, 0.000014, -0.000068, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2313.121093, 497.857696, -7.590085, 0.000000, 0.000007, 89.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2313.121093, 494.537719, -7.590085, 0.000000, 0.000007, 89.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2313.121093, 490.897827, -7.590085, 0.000000, 0.000007, 89.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2316.758300, 486.219512, -5.847567, 0.000006, 0.000022, -0.000068, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2316.738281, 486.226837, -5.617561, 0.000006, 0.000022, -0.000068, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2316.169921, 496.371093, -7.560776, 0.000000, 0.000014, 89.999900, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2316.169921, 492.831237, -7.560774, 0.000000, 0.000014, 89.999900, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2190, -2316.867187, 495.310821, -5.674265, 0.000006, 0.000003, 65.500045, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 14392, "dr_gsstudio", "monitors_128", 0x00000000);
    lsgmcxas = CreateDynamicObject(2190, -2316.868652, 498.564147, -5.674265, 0.000006, 0.000003, 65.500045, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 14392, "dr_gsstudio", "monitors_128", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2313.843994, 489.453033, -5.847567, 0.000014, 0.000029, 89.999877, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2190, -2316.868652, 491.654113, -5.674265, 0.000012, 0.000006, 65.500022, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 14392, "dr_gsstudio", "monitors_128", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2313.851318, 489.473052, -5.617561, 0.000014, 0.000029, 89.999877, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    lsgmcxas = CreateDynamicObject(2267, -2313.548339, 489.584320, -5.451159, 0.000022, -0.000022, 179.999771, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2267, -2315.038085, 489.584320, -5.451159, 0.000022, -0.000022, 179.999771, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2311.438232, 495.556610, -5.847567, 0.000022, 0.000014, 179.999740, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2311.458251, 495.549285, -5.617561, 0.000022, 0.000014, 179.999740, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2311.438232, 485.936553, -5.847567, 0.000022, 0.000007, 179.999694, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2311.458251, 485.929229, -5.617561, 0.000022, 0.000007, 179.999694, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2316.233398, 500.433929, -5.627048, -0.000082, -0.000051, -90.000076, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2313.055908, 500.426300, -5.627634, 0.000067, 0.000082, 89.999389, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 9583, "bigshap_sfw", "bridge_walls2_sfw", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2302.589843, 512.383666, -5.627634, 0.000012, 0.000014, -0.000464, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2302.578857, 509.179046, -5.627048, -0.000012, -0.000097, 179.999252, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2302.593994, 515.579467, -5.627048, -0.000012, -0.000074, 179.999038, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2302.589599, 518.783325, -5.627634, 0.000012, 0.000022, -0.000418, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2277.106201, 494.333953, -5.837056, -0.000014, -0.000036, 89.999755, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2274.326904, 494.333953, -5.837052, -0.000014, -0.000037, 89.999755, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2273.028320, 496.008178, -5.847566, -0.000059, 0.000045, 0.000311, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2278.338134, 494.929748, -7.560781, 0.000000, 0.000000, 540.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2277.337890, 494.929748, -7.560781, 0.000000, 0.000000, 540.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2274.065185, 512.103332, -5.847565, 0.000022, -0.000007, 179.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2272.454589, 512.103332, -7.647562, 0.000022, 90.000000, 179.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19004, "roundbuilding1", "stonewalltile4", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2272.409912, 512.838562, -5.797080, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    lsgmcxas = CreateDynamicObject(2132, -2273.469970, 510.177032, -7.591300, 0.000007, 0.000007, 89.999916, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 18646, "matcolours", "grey-60-percent", 0x00000000);
    lsgmcxas = CreateDynamicObject(2262, -2273.486572, 510.711212, -5.949923, 0.000007, 0.000007, 89.999916, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 19325, "lsmall_shops", "lsmall_window01", 0x00000000);
    lsgmcxas = CreateDynamicObject(2132, -2273.469970, 514.297058, -7.591300, 0.000014, 0.000007, 89.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 18646, "matcolours", "grey-60-percent", 0x00000000);
    lsgmcxas = CreateDynamicObject(2262, -2273.486572, 514.831237, -5.949923, 0.000014, 0.000007, 89.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 19325, "lsmall_shops", "lsmall_window01", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2272.409912, 509.068511, -5.797080, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19595, "lsappartments1", "ceilingtiles3-128x128", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2261.902099, 507.333892, -5.837049, 0.000000, -0.000043, 89.999679, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2261.922119, 507.324127, -5.627048, 0.000000, -0.000043, 89.999679, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2258.731201, 507.333892, -5.837049, 0.000009, -0.000043, 89.999656, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2258.721191, 507.324127, -5.627048, 0.000009, -0.000043, 89.999656, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1523, -2254.090820, 507.370147, -7.592586, 0.000000, -0.000014, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(1523, -2257.131347, 507.310089, -7.592586, 0.000000, 0.000014, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(1523, -2257.131347, 500.720092, -7.592586, -0.000007, -0.000007, -0.000082, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(1523, -2254.090820, 500.780151, -7.592586, 0.000007, 0.000007, 179.999832, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2261.928710, 500.734069, -5.837049, -0.000029, -0.000052, -90.000167, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2261.908691, 500.744079, -5.627048, -0.000029, -0.000052, -90.000167, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2258.728271, 500.734069, -5.837049, -0.000044, -0.000052, -90.000122, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2258.708251, 500.744079, -5.627048, -0.000044, -0.000052, -90.000122, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2252.498779, 500.734069, -5.837049, -0.000059, -0.000052, -90.000076, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2252.508789, 500.744079, -5.627048, -0.000059, -0.000052, -90.000076, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1523, -2250.921386, 500.720092, -7.592586, -0.000007, 0.000000, -0.000082, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(1523, -2247.880859, 500.780151, -7.592586, 0.000007, 0.000000, 179.999786, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14577, "casinovault01", "cof_wood1", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2249.302978, 507.333099, -5.847567, -0.000029, -0.000007, -89.999916, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2249.305664, 507.313079, -5.617561, -0.000029, -0.000007, -89.999916, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2239.559570, 500.749786, -7.582273, -0.000022, -0.000007, -179.999893, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2240.290039, 504.029510, -7.643052, 0.000000, 89.999969, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2240.290039, 517.569763, -7.643052, 0.000000, 89.999969, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2240.290039, 514.359497, -7.643052, 0.000000, 89.999969, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2240.290039, 511.160888, -7.643052, 0.000000, 89.999961, 179.999771, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2240.290039, 507.950622, -7.643052, 0.000000, 89.999961, 179.999771, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2240.290039, 506.880126, -7.643052, 0.000000, 89.999961, 179.999771, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2243.246337, 500.734069, -5.837049, -0.000090, -0.000052, -89.999984, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2243.256347, 500.744079, -5.627048, -0.000090, -0.000052, -89.999984, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2246.297851, 500.734069, -5.837049, -0.000090, -0.000052, -89.999984, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2246.307861, 500.744079, -5.627048, -0.000090, -0.000052, -89.999984, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2240.300781, 500.736206, -5.847637, 0.000029, 0.000029, 89.999877, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2240.291503, 500.746215, -5.627634, 0.000029, 0.000029, 89.999877, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2243.795166, 512.088378, -5.847567, -0.000059, 0.000014, 0.000510, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2243.785888, 512.125000, -5.627563, -0.000051, 0.000014, 0.000479, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2245.329589, 507.333892, -5.837049, 0.000015, -0.000043, 89.999633, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2245.309570, 507.324127, -5.627048, 0.000015, -0.000043, 89.999633, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2236.803955, 505.723602, -7.582273, -0.000044, -0.000014, -89.999809, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2236.790283, 504.982391, -5.847637, 0.000037, 0.000006, 179.999694, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2236.800292, 504.991668, -5.627634, 0.000037, 0.000006, 179.999694, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2237.106689, 500.734069, -5.837049, -0.000097, -0.000052, -89.999961, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2237.116699, 500.744079, -5.627048, -0.000097, -0.000052, -89.999961, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2236.786621, 501.814483, -5.837049, -0.000104, -0.000029, 0.000029, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2236.796386, 501.804473, -5.627048, -0.000104, -0.000029, 0.000029, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2236.794433, 511.404785, -5.847567, -0.000052, 0.000000, -179.999343, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2236.803710, 511.368164, -5.627563, -0.000045, 0.000000, -179.999374, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2236.801513, 515.337646, -5.833051, 0.000007, -0.000014, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2236.811279, 515.347656, -5.623046, 0.000007, -0.000014, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2253.769042, 514.405212, -8.003335, 90.000000, 360.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19004, "roundbuilding1", "stonewalltile4", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2253.769042, 509.785003, -8.023337, 90.000000, 360.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19004, "roundbuilding1", "stonewalltile4", 0x00000000);
    lsgmcxas = CreateDynamicObject(2046, -2260.150390, 515.204650, -4.918230, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 16322, "a51_stores", "metalic128", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2260.167480, 514.889648, -7.560781, 0.000000, 0.000000, 360.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2257.107421, 514.889648, -7.560781, 0.000000, 0.000000, 360.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2254.137207, 514.889648, -7.560781, 0.000000, 0.000000, 360.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2251.257324, 514.889648, -7.560781, 0.000000, 0.000000, 360.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2190, -2259.948974, 515.249816, -6.592391, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 14392, "dr_gsstudio", "monitors_128", 0x00000000);
    lsgmcxas = CreateDynamicObject(2190, -2256.858642, 515.249816, -6.592391, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 14392, "dr_gsstudio", "monitors_128", 0x00000000);
    lsgmcxas = CreateDynamicObject(2190, -2253.949707, 515.249816, -6.592391, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 14392, "dr_gsstudio", "monitors_128", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2248.716308, 512.125000, -5.847567, -0.000052, 0.000007, -179.999389, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2248.725585, 512.088378, -5.627563, -0.000045, 0.000007, -179.999420, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2262.596435, 512.088378, -5.847567, -0.000059, 0.000007, 0.000616, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2262.587158, 512.125000, -5.627563, -0.000051, 0.000007, 0.000586, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2257.729492, 515.461608, -5.847567, -0.000068, 0.000007, -89.999313, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2257.692871, 515.452331, -5.627563, -0.000059, 0.000007, -89.999343, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2248.729248, 515.461608, -5.847567, -0.000075, 0.000007, -89.999290, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2248.692626, 515.452331, -5.627563, -0.000067, 0.000007, -89.999320, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2258.580078, 514.405212, -8.013337, 89.999992, 450.000000, -89.999992, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19004, "roundbuilding1", "stonewalltile4", 0x00000000);
    lsgmcxas = CreateDynamicObject(18766, -2258.580078, 509.785003, -8.003335, 89.999992, 450.000000, -89.999992, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19004, "roundbuilding1", "stonewalltile4", 0x00000000);
    lsgmcxas = CreateDynamicObject(2190, -2251.009521, 515.249816, -6.592391, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 2, 14392, "dr_gsstudio", "monitors_128", 0x00000000);
    lsgmcxas = CreateDynamicObject(2046, -2257.088867, 515.204650, -4.918230, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 16322, "a51_stores", "metalic128", 0x00000000);
    lsgmcxas = CreateDynamicObject(2046, -2254.147216, 515.204650, -4.918230, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 16322, "a51_stores", "metalic128", 0x00000000);
    lsgmcxas = CreateDynamicObject(2046, -2251.255371, 515.204650, -4.918230, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 16322, "a51_stores", "metalic128", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2249.305664, 507.343078, -5.617561, -0.000029, -0.000007, -89.999916, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2261.902099, 507.374145, -5.627048, 0.000015, -0.000043, 89.999633, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2258.701171, 507.374145, -5.627048, 0.000024, -0.000043, 89.999610, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2263.576416, 497.718566, -5.627563, -0.000045, 0.000007, -179.999435, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2263.586425, 510.338287, -5.627563, -0.000045, 0.000022, -179.999526, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2246.603027, 516.983215, -5.617561, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2237.191650, 516.978088, -5.623046, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2270.749023, 536.404174, -5.627573, -0.000051, -0.000007, 0.000280, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2270.742187, 517.618835, -5.623045, 0.000000, -0.000045, 179.999725, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2270.729980, 514.426696, -5.627634, 0.000000, 0.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2270.729980, 511.236236, -5.627634, 0.000000, 0.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2270.732177, 508.819122, -5.623045, 0.000000, -0.000051, 179.999679, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2281.980957, 515.271301, -5.627634, 0.000014, 0.000019, -90.000083, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2285.189697, 515.263854, -5.627048, 0.000014, -0.000044, 89.999633, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2278.790283, 515.263854, -5.627048, 0.000007, -0.000044, 89.999656, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2255.020507, 517.568420, -5.623045, 0.000000, -0.000014, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2255.020507, 529.468383, -5.623045, 0.000000, -0.000014, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2255.010498, 527.668334, -5.623045, 0.000000, -0.000014, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2255.018310, 532.476074, -5.617632, 0.000000, 0.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2255.020507, 535.658325, -5.623045, 0.000000, -0.000014, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2242.648437, 535.043579, -5.627563, -0.000007, 0.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2250.138427, 535.056823, -5.627637, 0.000029, 0.000000, 89.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2253.304687, 535.048156, -5.623044, -0.000029, -0.000007, -89.999969, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2243.758544, 535.043579, -5.627563, -0.000007, 0.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19383, -2241.947265, 536.567382, -5.627635, 0.000029, 0.000000, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2241.942138, 542.189270, -5.627563, -0.000022, -0.000007, -179.999847, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2258.738525, 495.988128, -5.847558, -0.000059, 0.000067, 0.000311, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2254.648437, 499.123901, -7.647060, -0.000006, 89.999992, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19517, "noncolored", "gen_white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2254.648437, 495.924072, -7.647060, -0.000006, 89.999992, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19517, "noncolored", "gen_white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2256.898193, 499.123901, -7.637062, -0.000006, 90.000000, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19517, "noncolored", "gen_white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2256.898193, 495.924072, -7.637062, -0.000006, 90.000000, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19517, "noncolored", "gen_white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2251.108886, 494.398284, -5.847567, -0.000059, 0.000075, 90.000312, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2258.809570, 494.398284, -5.847572, -0.000059, 0.000075, 90.000312, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2252.818847, 495.988128, -5.847566, -0.000059, 0.000067, 0.000311, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2258.128662, 494.989746, -7.560781, 0.000000, -0.000022, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2257.128417, 494.989746, -7.560781, 0.000000, -0.000022, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2252.198486, 495.988128, -5.847558, -0.000059, 0.000075, 0.000311, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2248.108398, 499.123901, -7.647060, -0.000006, 90.000000, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19517, "noncolored", "gen_white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2248.108398, 495.924072, -7.647060, -0.000006, 90.000000, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19517, "noncolored", "gen_white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2250.358154, 499.123901, -7.637062, -0.000006, 90.000007, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19517, "noncolored", "gen_white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2250.358154, 495.924072, -7.637062, -0.000006, 90.000007, 0.000037, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19517, "noncolored", "gen_white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19376, -2307.789794, 515.510925, -7.650176, 0.000000, 90.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19376, -2314.289550, 512.520507, -7.650176, 0.000000, 90.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2246.278808, 495.988128, -5.847566, -0.000059, 0.000075, 0.000311, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2251.608642, 494.989746, -7.560781, 0.000000, -0.000029, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2250.608398, 494.989746, -7.560781, 0.000000, -0.000029, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19376, -2314.289550, 495.670867, -7.640175, 0.000000, 90.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19376, -2314.289550, 486.061126, -7.640175, 0.000000, 90.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19376, -2307.777587, 492.541015, -7.640175, 0.000000, 90.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2260.842773, 516.993225, -5.617561, 0.000014, 0.000000, 89.999885, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2273.471923, 516.983215, -5.617561, 0.000022, 0.000000, 89.999931, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2237.958251, 495.988159, -5.847575, -0.000059, 0.000075, 0.000311, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2242.988525, 495.988159, -5.847568, -0.000059, 0.000075, 0.000311, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19376, -2240.733398, 495.953613, -7.627874, 0.000000, 90.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 19004, "roundbuilding1", "stonewalltile4", 0x00000000);
    lsgmcxas = CreateDynamicObject(2165, -2238.527343, 496.770477, -7.538341, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 3, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2131, -2238.526367, 494.773712, -7.532327, 0.000000, 0.000007, 630.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2238.527587, 492.769714, -7.560781, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2238.527587, 491.779907, -7.560781, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2239.218261, 491.779907, -7.560781, 0.000000, 0.000000, 540.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2174, -2240.202392, 491.768066, -7.535515, 0.000000, 0.000000, 540.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 18646, "matcolours", "grey-60-percent", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2242.407958, 492.769714, -7.560781, 0.000000, 0.000000, 450.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2242.187988, 491.779907, -7.560781, 0.000000, 0.000000, 540.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2242.407958, 491.769622, -7.560781, 0.000000, 0.000000, 450.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2339, -2242.407958, 493.769653, -7.560781, 0.000000, 0.000000, 450.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(2131, -2242.407226, 494.763671, -7.532327, 0.000000, 0.000007, 810.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2174, -2242.413085, 496.758148, -7.535515, 0.000000, 0.000000, 810.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 18646, "matcolours", "grey-60-percent", 0x00000000);
    lsgmcxas = CreateDynamicObject(2131, -2242.407226, 498.723693, -7.532327, 0.000000, 0.000007, 810.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2267, -2238.081787, 496.229858, -5.781144, 0.000014, 0.000000, 269.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2240.158447, 491.188201, -5.847570, -0.000059, 0.000075, 90.000312, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1776, -2246.717529, 506.843872, -6.481233, -0.000007, -0.000014, -0.000127, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 5443, "glenpark1_lae", "chocolate1", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 2221, "donut_tray", "rustycoffeerap_rb", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 3, 5443, "glenpark1_lae", "chocolate1", 0x00000000);
    lsgmcxas = CreateDynamicObject(2257, -2236.934814, 508.841094, -6.008354, 0.000000, -0.000007, 269.999938, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 1, 10023, "bigwhitesfe", "zombotech2", 0x00000000);
    lsgmcxas = CreateDynamicObject(1800, -2236.886962, 510.611694, -7.075498, 360.000000, -90.500045, 720.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2231.907958, 503.238098, -5.847579, -0.000059, 0.000075, 90.000312, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2231.907958, 506.458160, -5.847582, -0.000059, 0.000075, 90.000312, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2233.648681, 506.458160, -5.847585, -0.000059, 0.000067, -179.999649, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2235.069091, 506.458160, -7.647583, -0.000059, 90.000076, 180.000305, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(9131, -2241.611328, 531.011657, -6.478885, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3850, "carshowglass_sfsx", "ws_glass_balustrade_better", 0x00000000);
    lsgmcxas = CreateDynamicObject(9131, -2241.611328, 531.011657, -4.438888, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3850, "carshowglass_sfsx", "ws_glass_balustrade_better", 0x00000000);
    lsgmcxas = CreateDynamicObject(19935, -2241.765136, 531.013305, -7.431879, 0.000000, 90.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19935, -2242.125488, 531.013916, -4.331881, 0.000000, 90.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19935, -2241.765136, 531.413818, -7.421878, 0.000000, 90.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19935, -2241.765136, 530.623962, -7.421878, 0.000000, 90.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19935, -2242.515136, 531.013305, -7.461878, 0.000000, 90.000015, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19935, -2242.515136, 531.413818, -7.411879, 0.000000, 90.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19935, -2242.515136, 530.623962, -7.411879, 0.000000, 90.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(9131, -2264.645019, 530.220947, -6.478885, 0.000000, 0.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3850, "carshowglass_sfsx", "ws_glass_balustrade_better", 0x00000000);
    lsgmcxas = CreateDynamicObject(9131, -2264.645019, 530.220947, -4.438888, 0.000000, 0.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3850, "carshowglass_sfsx", "ws_glass_balustrade_better", 0x00000000);
    lsgmcxas = CreateDynamicObject(19935, -2264.798828, 530.222595, -7.431879, 0.000000, 90.000015, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19935, -2265.159179, 530.223205, -4.331881, 0.000000, 90.000038, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19935, -2264.798828, 530.623107, -7.421878, 0.000000, 90.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19935, -2264.798828, 529.833251, -7.421878, 0.000000, 90.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19935, -2265.548828, 530.222595, -7.461878, 0.000000, 90.000030, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19935, -2265.548828, 530.623107, -7.411879, 0.000000, 90.000038, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19935, -2265.548828, 529.833251, -7.411879, 0.000000, 90.000038, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2246.435546, 529.820068, -3.632796, 0.000000, 90.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2271.364746, 529.820068, -3.632796, 0.000000, 90.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2246.435546, 504.810424, -3.632796, 0.000000, 90.000015, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2271.424804, 504.810424, -3.632796, 0.000000, 90.000015, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2296.374023, 515.121032, -3.632788, 0.000000, 90.000015, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2296.374023, 490.210876, -3.632788, 0.000000, 90.000015, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2321.051513, 502.340881, -3.632781, 0.000000, 90.000015, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2321.051513, 477.370941, -3.632781, 0.000000, 90.000015, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2246.435546, 479.830780, -3.632796, 0.000000, 90.000030, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(18981, -2271.454833, 479.830780, -3.632796, 0.000000, 90.000030, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2267.128173, 517.559631, -7.643043, 0.000000, 89.999984, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2240.343261, 517.008117, -3.343048, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2239.534179, 517.122436, -4.596460, 0.000000, 90.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2241.013916, 517.122436, -4.596460, 0.000000, 90.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2241.013671, 516.892089, -4.596460, -0.000007, 90.000000, 0.000007, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2239.534179, 516.892089, -4.596460, -0.000007, 90.000000, 0.000007, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2266.385009, 517.132446, -4.596460, 0.000000, 89.999984, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2267.864746, 517.132446, -4.596460, 0.000000, 89.999984, 179.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2267.864501, 516.902099, -4.596460, -0.000007, 90.000015, 0.000007, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2266.385009, 516.902099, -4.596460, -0.000007, 90.000015, 0.000007, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2265.283203, 517.023254, -3.327560, 0.000014, 0.000000, 89.999885, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19445, -2263.606445, 502.728546, -3.337562, -0.000045, 0.000007, -179.999435, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2263.719726, 504.787109, -4.596460, -0.000015, 89.999977, -90.000030, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2263.719726, 503.307373, -4.596460, -0.000015, 89.999977, -90.000030, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2263.489257, 503.307617, -4.596460, 0.000007, 90.000022, 89.999931, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2263.489257, 504.787109, -4.596460, 0.000007, 90.000022, 89.999931, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2295.478515, 504.009246, -3.347049, -0.000020, -0.000029, -0.000510, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2295.599609, 504.787109, -4.596460, -0.000022, 89.999977, -90.000007, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2295.599609, 503.307373, -4.596460, -0.000022, 89.999977, -90.000007, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2295.369140, 503.307617, -4.596460, 0.000015, 90.000022, 89.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2295.369140, 504.787109, -4.596460, 0.000015, 90.000022, 89.999908, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2270.712158, 520.689086, -3.333046, 0.000000, -0.000052, 179.999679, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2270.830078, 521.437377, -4.596460, -0.000015, 89.999977, -90.000030, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2266.424804, 491.472595, -4.466457, 0.000022, 90.000000, 179.999725, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2267.904296, 491.472595, -4.466457, 0.000022, 90.000000, 179.999725, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2270.830078, 519.957641, -4.596460, -0.000015, 89.999977, -90.000030, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2270.599609, 519.957885, -4.596460, 0.000007, 90.000022, 89.999931, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(2658, -2270.599609, 521.437377, -4.596460, 0.000007, 90.000022, 89.999931, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    lsgmcxas = CreateDynamicObject(19476, -2270.613769, 520.741149, -4.602801, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(lsgmcxas, 0, "WARD D", 40, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    lsgmcxas = CreateDynamicObject(19476, -2270.813964, 520.611022, -4.602801, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(lsgmcxas, 0, "MAIN HALL", 40, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    lsgmcxas = CreateDynamicObject(19476, -2267.172119, 517.120300, -4.602801, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(lsgmcxas, 0, "WARD B", 40, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    lsgmcxas = CreateDynamicObject(19476, -2240.371093, 517.110290, -4.602801, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(lsgmcxas, 0, "WARD A", 40, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    lsgmcxas = CreateDynamicObject(19476, -2240.210937, 516.910339, -4.602801, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(lsgmcxas, 0, "MAIN HALL", 80, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    lsgmcxas = CreateDynamicObject(19476, -2263.503906, 504.060455, -4.602801, 0.000000, 0.000000, 720.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(lsgmcxas, 0, "WARD B", 40, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    lsgmcxas = CreateDynamicObject(19476, -2263.714111, 503.980377, -4.602801, 0.000000, 0.000000, 900.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(lsgmcxas, 0, "WARD A", 40, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    lsgmcxas = CreateDynamicObject(19476, -2267.214599, 491.460449, -4.472799, 0.000000, 0.000000, 1170.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(lsgmcxas, 0, "EXIT", 40, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    lsgmcxas = CreateDynamicObject(19476, -2295.377197, 504.050445, -4.612802, 0.000000, 0.000000, 1080.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(lsgmcxas, 0, "WARD C", 40, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    lsgmcxas = CreateDynamicObject(19476, -2295.587402, 504.000396, -4.612802, 0.000000, 0.000000, 1260.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(lsgmcxas, 0, "WARD B", 40, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    lsgmcxas = CreateDynamicObject(19476, -2267.092773, 516.900329, -4.602801, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(lsgmcxas, 0, "MAIN HALL", 80, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    lsgmcxas = CreateDynamicObject(19353, -2249.439208, 500.744079, -3.337051, -0.000059, -0.000052, -90.000076, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2255.579833, 500.744079, -3.337044, -0.000059, -0.000052, -90.000076, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2255.579833, 507.314178, -3.357050, -0.000059, -0.000052, -90.000076, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2260.778564, 511.449493, -7.583048, 0.000000, 89.999977, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2257.308837, 511.449493, -7.583049, 0.000000, 89.999977, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2253.837890, 511.449493, -7.583048, 0.000000, 89.999969, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2250.368164, 511.449493, -7.583049, 0.000000, 89.999969, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2255.625976, 508.249572, -7.573047, 0.000000, 89.999977, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2255.625976, 505.109375, -7.613048, 0.000000, 89.999977, 179.999862, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    lsgmcxas = CreateDynamicObject(1499, -2237.726318, 535.790588, -7.602275, 0.000000, -0.000029, 449.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(lsgmcxas, 1, 15034, "genhotelsave", "walp57S", 0x00000000);
    lsgmcxas = CreateDynamicObject(19353, -2255.040527, 541.557739, -5.623046, 0.000000, -0.000029, 179.999816, 77, 77, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(lsgmcxas, 0, 18646, "matcolours", "red", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(1209, -2246.584472, 517.405761, -7.589511, 0.000000, -0.000007, 179.999954, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1775, -2247.746826, 517.590942, -6.472476, 0.000000, -0.000007, 179.999954, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1776, -2248.945068, 517.500610, -6.481233, 0.000000, -0.000007, 179.999954, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2258, -2242.906494, 517.139221, -5.490705, 0.000000, -0.000007, 179.999954, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, -2254.596679, 517.303771, -7.584980, 0.000000, -0.000014, 179.999908, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1708, -2236.224609, 529.103271, -7.592325, -0.000007, 0.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1708, -2236.224609, 531.953308, -7.592325, -0.000007, 0.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2083, -2236.717529, 530.524108, -7.592325, -0.000007, 0.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2236.290039, 529.787292, -6.992321, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2236.400146, 530.257507, -6.992321, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1708, -2239.686523, 529.653747, -7.592325, 0.000007, 0.000000, 89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1708, -2239.686523, 531.333862, -7.592325, 0.000007, 0.000000, 89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1708, -2241.447509, 534.404052, -7.592325, 0.000007, 0.000029, -0.000068, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1708, -2239.767333, 534.404052, -7.592325, 0.000007, 0.000029, -0.000068, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, -2235.984619, 534.740661, -7.584980, -0.000007, 0.000022, -0.000037, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11706, -2237.882080, 534.608276, -7.598731, 0.000000, 0.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2083, -2242.713867, 534.071594, -7.592325, -0.000007, 0.000037, -0.000007, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2241.977050, 534.499084, -6.992321, 0.000037, 0.000007, 89.999855, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2242.447265, 534.388977, -6.992321, 0.000037, 0.000007, 89.999855, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1708, -2245.678710, 534.404052, -7.592325, 0.000007, 0.000037, -0.000068, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1708, -2243.998535, 534.404052, -7.592325, 0.000007, 0.000037, -0.000068, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2319, -2252.172607, 517.555358, -7.547727, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2855, -2251.584472, 517.504699, -7.040321, 0.000000, 0.000000, 108.499954, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, -2240.223632, 534.427551, -5.482326, 0.000000, 0.000037, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2262, -2239.080810, 534.450805, -5.402322, 0.000000, 0.000037, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2264, -2241.231933, 534.424865, -5.432321, 0.000000, 0.000037, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, -2244.505615, 534.427551, -5.482326, 0.000000, 0.000045, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2262, -2243.362792, 534.450805, -5.402322, 0.000000, 0.000045, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2264, -2245.513916, 534.424865, -5.432321, 0.000000, 0.000045, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2270, -2246.712890, 534.446289, -6.732324, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2270, -2247.233398, 534.446289, -6.732324, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, -2247.768066, 534.571166, -7.582324, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, -2236.236083, 530.064636, -5.482326, -0.000014, 0.000029, -89.999992, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2262, -2236.212890, 528.921569, -5.402322, -0.000014, 0.000029, -89.999992, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2264, -2236.238769, 531.072937, -5.432321, -0.000014, 0.000029, -89.999992, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19825, -2242.265136, 534.897949, -4.502322, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, -2250.695312, 530.717529, -7.602324, 0.000006, 0.000003, 61.099979, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1714, -2253.547851, 529.525573, -7.602324, 0.000000, 0.000007, -2.400007, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19893, -2249.536621, 530.003417, -6.672327, -0.000006, -0.000003, -117.999984, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19893, -2253.563964, 528.228942, -6.682325, 0.000000, -0.000007, -179.699966, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19807, -2254.056884, 528.251159, -6.612325, 0.000003, -0.000006, 146.999984, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19807, -2249.794433, 529.501708, -6.612321, -0.000003, -0.000006, -148.800018, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11728, -2255.091308, 531.052612, -5.932321, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19993, -2251.610595, 534.702880, -6.562325, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2855, -2253.226806, 534.528991, -6.542322, -0.000003, 0.000006, -36.199985, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2690, -2254.781738, 526.475036, -7.240322, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2961, -2254.871337, 527.052673, -7.060320, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2274, -2254.416259, 536.342712, -5.451312, 0.000007, 0.000000, 89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2275, -2254.410644, 537.132141, -5.555451, 0.000007, 0.000000, 89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2282, -2254.411376, 538.173461, -5.578179, 0.000007, 0.000000, 89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2202, -2250.575439, 541.465515, -7.582324, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, -2253.219726, 541.959960, -6.862326, 0.000007, 180.000000, 89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, -2244.965576, 541.720520, -7.584980, -0.000007, 0.000029, -0.000037, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2149, -2244.349121, 541.538635, -6.376399, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2865, -2243.251953, 541.576110, -6.512776, 0.000000, 0.000007, -0.299998, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11722, -2242.216796, 541.166870, -6.404794, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11723, -2242.223632, 541.017944, -6.390692, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2515, -2242.422851, 539.896850, -6.400905, -0.000007, 0.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, -2238.326171, 541.398437, -7.590016, -0.000007, 0.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, -2238.326171, 540.408630, -7.590016, -0.000007, 0.000000, -89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, -2238.326171, 539.418823, -7.590016, -0.000014, 0.000000, -89.999954, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, -2238.326171, 538.429016, -7.590016, -0.000014, 0.000000, -89.999954, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, -2241.366455, 538.428833, -7.590016, -0.000007, 0.000007, 89.999961, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, -2241.366455, 539.418823, -7.590016, -0.000007, 0.000007, 89.999961, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, -2241.366455, 540.408569, -7.590016, -0.000014, 0.000007, 89.999984, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2007, -2241.366455, 541.398315, -7.590016, -0.000014, 0.000007, 89.999984, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(371, -2239.503906, 538.191284, -7.180969, 90.000000, 90.000000, -128.600006, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, -2241.618164, 541.590148, -6.060232, 0.000000, 0.000000, -4.399999, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2855, -2241.657470, 540.467102, -6.186611, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(18645, -2241.586914, 539.700256, -6.113584, 0.000000, -29.099998, -71.300003, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11738, -2238.079833, 538.833557, -6.142322, 0.000000, 0.000000, 86.900009, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11745, -2237.961425, 539.579589, -6.042885, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19805, -2239.829589, 535.119018, -5.832323, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2517, -2235.050781, 540.445678, -7.589640, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2517, -2236.121093, 540.445678, -7.589640, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2517, -2237.191650, 540.445678, -7.589640, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19873, -2234.743164, 537.306823, -6.462562, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2741, -2234.538330, 537.616821, -5.851049, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2514, -2234.947021, 540.206420, -7.583755, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -2234.386230, 539.511474, -5.564939, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -2234.386230, 535.721679, -5.564939, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2373, -2235.182128, 534.938171, -8.035600, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2986, -2236.757568, 539.926696, -7.581144, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2986, -2236.757568, 536.506591, -7.581144, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1776, -2256.585937, 534.551025, -6.481233, 0.000000, -0.000007, 359.999938, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2258.447509, 534.406127, -7.600969, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2261.008300, 534.406127, -7.600969, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2262.349121, 534.406127, -7.600969, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, -2259.737548, 533.850463, -7.586746, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2259.206298, 534.562377, -6.997667, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2259.396484, 534.162048, -6.997667, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, -2263.619384, 533.850463, -7.586746, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2263.088134, 534.562377, -6.997667, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2263.278320, 534.162048, -6.997667, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2264.890136, 534.406127, -7.600969, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2266.230957, 534.406127, -7.600969, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2267.591796, 534.406127, -7.600969, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, -2268.741455, 534.750854, -7.584980, -0.000007, -0.000014, -0.000082, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, -2261.256835, 534.427551, -5.482326, 0.000000, 0.000052, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2262, -2260.114013, 534.450805, -5.402322, 0.000000, 0.000052, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2264, -2262.265136, 534.424865, -5.432321, 0.000000, 0.000052, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, -2265.837158, 534.427551, -5.482326, 0.000000, 0.000060, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2262, -2264.694335, 534.450805, -5.402322, 0.000000, 0.000060, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2264, -2266.845458, 534.424865, -5.432321, 0.000000, 0.000060, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19825, -2263.406250, 534.897949, -4.502322, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2261.351318, 528.175842, -7.600969, 0.000007, -0.000007, 179.999786, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2260.010498, 528.175842, -7.600969, 0.000007, -0.000007, 179.999786, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2258.649658, 528.175842, -7.600969, 0.000007, -0.000007, 179.999786, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2263.018798, 530.416076, -7.600969, 0.000022, 0.000007, 89.999900, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2263.018798, 529.075256, -7.600969, 0.000022, 0.000007, 89.999900, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2260.206787, 530.142028, -6.997667, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2260.696533, 530.322204, -6.997667, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2260.836669, 529.912048, -6.997667, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2855, -2260.542968, 530.171081, -7.480326, 0.000007, -0.000001, 108.499931, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, -2255.538818, 534.571166, -7.582324, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(18755, -2279.829345, 520.665222, -5.631196, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(18756, -2275.979248, 520.613159, -5.657104, 0.000000, 0.000000, 360.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(18757, -2276.007568, 520.641967, -5.637101, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(18755, -2279.829345, 527.066223, -5.631196, 0.000000, -0.000007, 179.999954, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(18756, -2275.979248, 527.014160, -5.657104, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(18757, -2276.007568, 527.042968, -5.637101, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(18755, -2274.546386, 533.915527, -5.631196, 0.000007, -0.000007, 89.999885, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(18756, -2274.598144, 530.065429, -5.657104, -0.000007, 0.000007, -90.000015, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(18757, -2274.569335, 530.093750, -5.637101, -0.000007, 0.000007, -90.000015, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1778, -2271.087646, 524.339111, -7.572196, 0.000000, 0.000000, -33.700004, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2690, -2270.941650, 523.005249, -7.240322, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11706, -2277.411621, 531.628112, -7.568717, 0.000000, 0.000022, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19164, -2277.810791, 523.862121, -5.740133, 450.000000, 450.000000, 360.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2271.302490, 518.313659, -7.600968, 0.000007, -0.000007, -125.500190, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, -2270.578857, 532.195007, -5.520741, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1775, -2270.147460, 529.041320, -6.472476, 0.000000, -0.000007, 449.999938, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1209, -2270.344970, 527.896240, -7.589511, 0.000000, -0.000007, 449.999938, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11706, -2270.352539, 526.848327, -7.598729, 0.000000, 0.000022, 90.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, -2255.277099, 518.483886, -7.584980, 0.000000, -0.000014, 269.999908, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11706, -2255.601318, 517.368164, -7.598725, 0.000000, 0.000022, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, -2259.059082, 517.351257, -7.582324, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1216, -2257.496093, 517.382202, -6.926885, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1216, -2260.826904, 517.382202, -6.926885, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2263, -2269.651367, 517.614257, -5.794556, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2690, -2269.632324, 517.224731, -7.210320, 0.000000, 0.000000, 540.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2279.577636, 501.406616, -7.600969, 0.000007, -0.000007, 179.999832, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2277.017089, 501.406616, -7.600969, 0.000007, -0.000007, 179.999832, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, -2278.287597, 501.962280, -7.586746, 0.000007, -0.000007, 179.999832, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2278.818847, 501.250366, -6.997667, 0.000007, -0.000007, 179.999832, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2278.628906, 501.650756, -6.997667, 0.000007, -0.000007, 179.999832, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2285.907714, 501.406616, -7.600969, 0.000007, -0.000014, 179.999786, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2283.347167, 501.406616, -7.600969, 0.000007, -0.000014, 179.999786, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, -2284.617675, 501.962280, -7.586746, 0.000007, -0.000014, 179.999786, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2285.148925, 501.250366, -6.997667, 0.000007, -0.000014, 179.999786, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2284.958984, 501.650756, -6.997667, 0.000007, -0.000014, 179.999786, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, -2272.687744, 507.040649, -7.584980, -0.000007, -0.000014, -0.000082, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2275.636962, 506.706390, -7.600969, 0.000000, 0.000000, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2278.197509, 506.706390, -7.600969, 0.000000, 0.000000, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, -2276.927001, 506.150726, -7.586746, 0.000000, 0.000000, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2276.395751, 506.862640, -6.997667, 0.000000, 0.000000, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2276.585693, 506.462249, -6.997667, 0.000000, 0.000000, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2279.697998, 506.706390, -7.600969, 0.000000, 0.000000, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11707, -2236.817871, 535.208740, -6.411633, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2319, -2285.503662, 506.725433, -7.547727, 0.000000, 0.000014, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2855, -2284.915527, 506.674774, -7.040321, 0.000014, -0.000003, 108.499908, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2270, -2271.600097, 506.741821, -6.065639, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, -2277.407226, 506.717468, -5.482326, 0.000000, 0.000068, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2262, -2276.264404, 506.740722, -5.402322, 0.000000, 0.000068, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2264, -2278.415527, 506.714782, -5.432321, 0.000000, 0.000068, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, -2278.583740, 501.348114, -5.482326, 0.000007, 0.000068, 179.999832, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2262, -2279.726806, 501.324920, -5.402322, 0.000007, 0.000068, 179.999832, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2264, -2277.575439, 501.350799, -5.432321, 0.000007, 0.000068, 179.999832, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1209, -2263.973632, 500.678283, -7.589511, -0.000014, -0.000014, -89.999984, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1775, -2264.158691, 499.515930, -6.472476, -0.000014, -0.000014, -89.999984, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1776, -2264.068359, 498.317687, -6.481233, -0.000014, -0.000014, -89.999984, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1437, -2264.592529, 501.796020, -9.103598, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(3089, -2268.648681, 491.465240, -6.210992, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(3089, -2265.668212, 491.465240, -6.210992, 0.000000, -0.000007, 179.999954, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2685, -2270.601318, 494.186370, -5.873284, 0.000000, 0.000000, 450.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2717, -2270.585449, 495.766571, -5.452907, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2687, -2270.599121, 497.376220, -5.915030, 0.000000, 0.000000, 450.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2690, -2270.502929, 498.544738, -7.210320, 0.000000, 0.000000, 810.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, -2287.010986, 497.376708, -7.568736, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2285.265625, 494.777679, -7.558160, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2333, -2283.576660, 496.912353, -7.586246, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1513, -2289.657958, 497.501647, -6.251930, 0.000000, 0.000000, 450.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1513, -2289.657958, 498.081542, -6.251930, 0.000000, 0.000000, 450.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1671, -2280.836914, 496.534942, -7.119371, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1671, -2282.257080, 497.044952, -7.119371, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11713, -2284.018798, 499.972198, -6.011847, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2994, -2283.646728, 495.106567, -7.043474, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2994, -2283.646728, 498.716674, -7.043474, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2994, -2287.846679, 497.206878, -7.043474, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19809, -2287.766357, 497.055847, -6.621211, 0.000000, 0.000000, 89.899986, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11716, -2287.819824, 497.339294, -6.627893, 0.000000, 0.000000, -121.199981, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1330, -2287.873535, 496.251617, -7.541196, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2690, -2283.908691, 499.965087, -5.938484, 0.000000, 0.000000, 450.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11707, -2280.517822, 500.588256, -6.345148, 0.000000, 0.000000, 360.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, -2285.839355, 514.811340, -7.582321, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2279.328857, 514.426086, -7.600968, -0.000030, -0.000014, -90.000038, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2279.328857, 513.136596, -7.600968, -0.000030, -0.000014, -90.000038, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, -2279.884521, 511.866088, -7.586746, -0.000030, -0.000014, -90.000038, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2279.172607, 511.334838, -6.997666, -0.000030, -0.000014, -90.000038, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2279.572998, 511.524780, -6.997666, -0.000030, -0.000014, -90.000038, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2279.328857, 510.626647, -7.600968, -0.000030, -0.000014, -90.000038, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2279.328857, 509.386657, -7.600968, -0.000030, -0.000014, -90.000038, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, -2280.167236, 507.604003, -7.584974, 0.000000, -0.000022, 179.999832, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2593, -2279.248535, 507.722442, -6.718138, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11706, -2280.811767, 507.678192, -7.598734, 0.000000, 0.000022, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2654, -2285.333496, 507.550994, -4.866765, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19999, -2285.017578, 511.011169, -7.591126, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2267, -2284.070068, 515.127746, -5.444354, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, -2279.310546, 509.465332, -5.482326, -0.000007, 0.000059, -90.000015, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2262, -2279.287353, 508.322265, -5.402321, -0.000007, 0.000059, -90.000015, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2264, -2279.313232, 510.473632, -5.432321, -0.000007, 0.000059, -90.000015, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, -2279.310546, 513.435241, -5.482326, -0.000014, 0.000059, -89.999992, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2262, -2279.287353, 512.292175, -5.402321, -0.000014, 0.000059, -89.999992, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2264, -2279.313232, 514.443542, -5.432321, -0.000014, 0.000059, -89.999992, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, -2285.740234, 511.360321, -5.482326, -0.000007, 0.000068, 89.999923, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2262, -2285.763427, 512.503417, -5.402321, -0.000007, 0.000068, 89.999923, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2264, -2285.737548, 510.352020, -5.432321, -0.000007, 0.000068, 89.999923, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1671, -2282.167968, 511.274993, -7.119371, 0.000000, 0.000000, -68.600006, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2319, -2284.303955, 518.165527, -7.547727, 0.000000, 0.000014, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19899, -2284.250244, 514.839599, -6.862320, 0.000007, 180.000000, 89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1671, -2279.187744, 517.554931, -7.119371, 0.000000, 0.000000, 630.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2267, -2286.249511, 516.937683, -5.284351, 0.000000, 0.000000, 90.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2282.089599, 517.926330, -7.600969, -0.000030, -0.000014, -0.000037, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, -2278.958984, 514.811340, -7.582322, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, -2278.958984, 515.781433, -7.582322, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19825, -2286.247314, 511.387756, -4.392321, 0.000000, 0.000007, 90.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1209, -2290.775878, 501.115417, -7.589510, -0.000014, -0.000014, 179.999877, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1775, -2291.938476, 501.300476, -6.472476, -0.000014, -0.000014, 179.999877, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1776, -2293.136718, 501.210144, -6.481233, -0.000014, -0.000014, 179.999877, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2289.766601, 506.706390, -7.600968, 0.000000, 0.000007, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2292.327148, 506.706390, -7.600968, 0.000000, 0.000007, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, -2291.056640, 506.150726, -7.586746, 0.000000, 0.000007, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2290.525390, 506.862640, -6.997666, 0.000000, 0.000007, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2290.715332, 506.462249, -6.997666, 0.000000, 0.000007, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2293.827636, 506.706390, -7.600968, 0.000000, 0.000007, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2292.436035, 515.837707, -7.558159, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2289.375976, 512.737548, -7.558159, 0.000000, 0.000000, 540.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2292.295898, 516.707702, -7.558159, 0.000000, 0.000000, 450.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2273, -2292.637207, 519.773681, -5.533455, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2273, -2293.376464, 511.193725, -5.533455, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1789, -2291.009765, 515.824096, -6.996188, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1789, -2291.009765, 519.994750, -6.996188, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2994, -2296.026855, 500.666931, -7.043474, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2994, -2296.026855, 499.206909, -7.043474, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2305.587890, 515.140258, -7.558159, 0.000000, 0.000007, 89.999938, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2308.647949, 518.240356, -7.558159, -0.000007, 0.000000, 0.000007, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2305.728027, 514.270141, -7.558159, 0.000000, -0.000007, -90.000007, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2273, -2305.386718, 511.204223, -5.533455, 0.000007, 0.000000, 179.999877, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2273, -2304.647460, 519.784057, -5.533455, -0.000007, 0.000000, 0.000007, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1789, -2307.014160, 515.153686, -6.996188, 0.000007, 0.000000, 179.999877, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1789, -2307.014160, 510.983032, -6.996188, 0.000007, 0.000000, 179.999877, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2305.587890, 492.170135, -7.558159, 0.000014, 0.000007, 89.999893, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2308.647949, 495.270233, -7.558159, -0.000007, 0.000014, 0.000007, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2305.728027, 491.300018, -7.558159, -0.000014, -0.000007, -89.999961, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2273, -2305.386718, 488.234100, -5.533455, 0.000007, -0.000014, 179.999786, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2273, -2304.647460, 496.813934, -5.533455, -0.000007, 0.000014, 0.000007, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1789, -2307.014160, 492.183563, -6.996188, 0.000007, -0.000014, 179.999786, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1789, -2307.014160, 488.012908, -6.996188, 0.000007, -0.000014, 179.999786, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2295.007568, 499.579925, -7.558159, -0.000007, 0.000000, 0.000007, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2295.007568, 487.419982, -7.558159, -0.000007, 0.000007, 0.000007, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2273, -2292.637207, 493.123809, -5.533455, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2273, -2292.637207, 485.313781, -5.533455, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, -2289.418212, 515.741394, -7.582321, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, -2289.418212, 519.921508, -7.582321, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, -2289.418212, 519.921508, -7.582321, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, -2294.988281, 492.971527, -7.582321, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, -2294.988281, 491.991607, -7.582321, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2304.936767, 507.036682, -7.600968, 0.000000, 0.000014, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2307.497314, 507.036682, -7.600968, 0.000000, 0.000014, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, -2306.226806, 506.481018, -7.586746, 0.000000, 0.000014, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2305.695556, 507.192932, -6.997666, 0.000000, 0.000014, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2305.885498, 506.792541, -6.997666, 0.000000, 0.000014, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2308.997802, 507.036682, -7.600968, 0.000000, 0.000014, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2308.997802, 501.067108, -7.600968, 0.000007, 0.000014, 179.999710, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2306.437255, 501.067108, -7.600968, 0.000007, 0.000014, 179.999710, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, -2307.707763, 501.622772, -7.586746, 0.000007, 0.000014, 179.999710, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2308.239013, 500.910858, -6.997666, 0.000007, 0.000014, 179.999710, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2308.049072, 501.311248, -6.997666, 0.000007, 0.000014, 179.999710, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2304.936767, 501.067108, -7.600968, 0.000007, 0.000014, 179.999710, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1209, -2314.586181, 500.805511, -7.589510, -0.000014, -0.000022, 179.999832, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1775, -2315.748779, 500.990570, -6.472476, -0.000014, -0.000022, 179.999832, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1776, -2316.947021, 500.900238, -6.481233, -0.000014, -0.000022, 179.999832, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, -2314.637695, 507.390686, -7.584980, -0.000007, -0.000007, -0.000081, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, -2306.648681, 507.067474, -5.482326, 0.000000, 0.000075, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2262, -2305.505859, 507.090728, -5.402321, 0.000000, 0.000075, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2264, -2307.656982, 507.064788, -5.432321, 0.000000, 0.000075, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2690, -2302.333251, 499.934753, -7.210319, 0.000000, 0.000000, 810.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2296.106445, 491.746002, -7.600968, -0.000007, -0.000022, -90.000152, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2296.106445, 494.306549, -7.600968, -0.000007, -0.000022, -90.000152, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, -2296.662109, 493.036041, -7.586746, -0.000007, -0.000022, -90.000152, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2295.950195, 492.504791, -6.997666, -0.000007, -0.000022, -90.000152, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2296.350585, 492.694732, -6.997666, -0.000007, -0.000022, -90.000152, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2301.936767, 493.336608, -7.600968, 0.000000, -0.000014, 89.999763, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2301.936767, 490.776062, -7.600968, 0.000000, -0.000014, 89.999763, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, -2301.381103, 492.046569, -7.586746, 0.000000, -0.000014, 89.999763, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2302.093017, 492.577819, -6.997666, 0.000000, -0.000014, 89.999763, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2301.692626, 492.387878, -6.997666, 0.000000, -0.000014, 89.999763, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2301.936767, 510.426605, -7.600968, 0.000007, -0.000014, 89.999740, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2301.936767, 507.866058, -7.600968, 0.000007, -0.000014, 89.999740, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, -2301.381103, 509.136566, -7.586746, 0.000007, -0.000014, 89.999740, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2302.093017, 509.667816, -6.997666, 0.000007, -0.000014, 89.999740, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2301.692626, 509.477874, -6.997666, 0.000007, -0.000014, 89.999740, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, -2302.274902, 516.281799, -7.584980, 0.000007, 0.000000, 89.999839, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2258, -2292.056396, 507.199066, -5.490708, 0.000000, -0.000007, 359.999938, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2258, -2307.476806, 500.579162, -5.490711, 0.000000, -0.000007, 539.999938, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2258, -2302.446533, 509.629272, -5.490711, 0.000000, -0.000007, 809.999938, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2273, -2313.863281, 513.591613, -5.533455, 0.000007, 0.000000, 359.999816, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1789, -2315.204345, 512.498962, -6.996188, 0.000007, 0.000000, 89.999832, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, -2312.887451, 513.880554, -7.584980, -0.000007, -0.000007, -0.000081, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2314.968017, 508.286682, -7.600968, 0.000000, 0.000014, 89.999839, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2001, -2316.321044, 499.991638, -7.582320, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2245, -2316.295410, 496.412414, -6.263237, 0.000000, 0.000000, -74.599998, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2245, -2316.316894, 492.847839, -6.263237, 0.000000, 0.000000, 62.399997, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, -2275.911376, 497.376708, -7.568736, 0.000000, -0.000007, 179.999954, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2274.166015, 494.777679, -7.558159, 0.000007, 0.000000, 89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2333, -2272.477050, 496.912353, -7.586246, 0.000007, 0.000000, 89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2994, -2276.747070, 497.206878, -7.043474, 0.000000, -0.000007, 179.999954, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19809, -2276.666748, 497.055847, -6.621211, 0.000007, 0.000000, 89.899963, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11716, -2276.720214, 497.339294, -6.627892, -0.000006, -0.000003, -121.199958, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1330, -2276.773925, 496.251617, -7.541195, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1513, -2278.728027, 498.161682, -6.251930, 0.000014, 0.000000, 89.999954, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1513, -2278.728027, 498.741577, -6.251930, 0.000014, 0.000000, 89.999954, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2514, -2273.490966, 512.438537, -7.591209, 0.000007, 0.000000, 89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2514, -2273.490234, 516.548217, -7.581213, 0.000007, 0.000000, 89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2741, -2273.888916, 511.979278, -5.851048, 0.000007, 0.000007, 89.999916, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2986, -2272.268310, 510.706634, -7.551144, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2741, -2273.888916, 516.099304, -5.851048, 0.000014, 0.000007, 89.999893, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2986, -2272.268310, 514.826660, -7.551144, 0.000000, -0.000007, 179.999954, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1778, -2270.968750, 516.553955, -7.572196, 0.000000, 0.000000, -33.700004, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1778, -2271.000976, 509.531555, -7.572196, 0.000000, 0.000000, -33.700004, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11707, -2271.978271, 512.988647, -6.411633, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11707, -2271.978271, 512.678649, -6.411633, 0.000000, 0.000000, 360.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2994, -2251.227050, 507.766754, -7.043475, 0.000000, -0.000007, 449.999938, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2994, -2260.126953, 507.806732, -7.043475, 0.000000, -0.000007, 449.999938, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, -2258.630859, 514.366760, -7.538734, 0.000000, 0.000000, 360.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, -2261.609863, 514.366760, -7.518735, 0.000000, 0.000000, 360.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, -2255.630126, 514.366760, -7.548735, 0.000000, 0.000000, 360.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, -2252.759277, 514.366760, -7.548735, 0.000000, 0.000000, 360.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, -2249.809082, 514.366760, -7.548735, 0.000000, 0.000000, 360.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, -2249.809082, 508.506622, -7.528735, 0.000007, 0.000000, 179.999877, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, -2252.759277, 508.506622, -7.528735, 0.000007, 0.000000, 179.999877, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, -2261.609863, 508.456726, -7.528735, 0.000007, 0.000000, 179.999877, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, -2258.630859, 508.456726, -7.528735, 0.000007, 0.000000, 179.999877, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, -2255.701904, 497.436706, -7.568736, 0.000000, -0.000029, 179.999816, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2253.956542, 494.837677, -7.558159, 0.000029, 0.000000, 89.999908, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2333, -2252.267578, 496.972351, -7.586246, 0.000029, 0.000000, 89.999908, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2994, -2256.537597, 497.266876, -7.043474, 0.000000, -0.000029, 179.999816, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19809, -2256.457275, 497.115844, -6.621211, 0.000029, 0.000000, 89.899894, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11716, -2256.510742, 497.399291, -6.627892, -0.000025, -0.000014, -121.199890, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1330, -2256.564453, 496.311614, -7.541195, 0.000000, 0.000029, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1513, -2258.518554, 499.051635, -6.251930, 0.000045, 0.000000, 89.999862, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1513, -2258.518554, 499.631530, -6.251930, 0.000045, 0.000000, 89.999862, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19805, -2258.626953, 497.054473, -5.911495, 0.000000, 0.000000, 450.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1997, -2249.161865, 497.436706, -7.568736, 0.000000, -0.000037, 179.999771, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19903, -2247.416503, 494.837677, -7.558159, 0.000037, 0.000000, 89.999885, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2333, -2245.727539, 496.972351, -7.586246, 0.000037, 0.000000, 89.999885, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2994, -2249.997558, 497.266876, -7.043474, 0.000000, -0.000037, 179.999771, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19809, -2249.917236, 497.115844, -6.621211, 0.000037, 0.000000, 89.899871, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11716, -2249.970703, 497.399291, -6.627892, -0.000031, -0.000018, -121.199867, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1330, -2250.024414, 496.311614, -7.541195, 0.000000, 0.000037, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1513, -2251.978515, 499.051635, -6.251930, 0.000052, 0.000000, 89.999839, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1513, -2251.978515, 499.631530, -6.251930, 0.000052, 0.000000, 89.999839, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19805, -2252.086914, 497.054473, -5.911495, 0.000007, 0.000000, 89.999977, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2333, -2237.397216, 498.402221, -7.586250, 0.000037, 0.000000, 89.999885, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2690, -2238.158447, 497.612487, -7.190048, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, -2242.397460, 500.430572, -7.584972, -0.000007, -0.000014, 449.999908, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2690, -2253.828369, 507.552459, -7.150046, 0.000000, 0.000000, 540.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2855, -2238.444580, 498.507476, -6.620148, 0.000000, 0.000000, -63.800022, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11707, -2238.809082, 500.587921, -6.472922, 0.000000, 0.000000, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1663, -2239.724121, 496.476898, -7.119496, 0.000000, 0.000000, 73.299972, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, -2245.898681, 507.040649, -7.584980, -0.000007, -0.000007, -0.000081, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2248.847900, 506.706390, -7.600968, 0.000000, 0.000007, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2251.408447, 506.706390, -7.600968, 0.000000, 0.000007, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, -2250.137939, 506.150726, -7.586746, 0.000000, 0.000007, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2249.606689, 506.862640, -6.997666, 0.000000, 0.000007, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2249.796630, 506.462249, -6.997666, 0.000000, 0.000007, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2252.908935, 506.706390, -7.600968, 0.000000, 0.000007, -0.000159, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, -2250.618164, 506.717468, -5.482326, 0.000000, 0.000075, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2262, -2249.475341, 506.740722, -5.402321, 0.000000, 0.000075, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2264, -2251.626464, 506.714782, -5.432321, 0.000000, 0.000075, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1209, -2261.456298, 506.970397, -7.589510, -0.000022, -0.000007, 0.000007, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1775, -2260.293701, 506.785339, -6.472476, -0.000022, -0.000007, 0.000007, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1776, -2259.095458, 506.875671, -6.481233, -0.000022, -0.000007, 0.000007, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2260.898193, 501.347015, -7.600968, 0.000007, 0.000007, 179.999710, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2258.337646, 501.347015, -7.600968, 0.000007, 0.000007, 179.999710, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, -2259.608154, 501.902679, -7.586746, 0.000007, 0.000007, 179.999710, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2260.139404, 501.190765, -6.997666, 0.000007, 0.000007, 179.999710, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2259.949462, 501.591156, -6.997666, 0.000007, 0.000007, 179.999710, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2245.197265, 501.347015, -7.600968, 0.000007, -0.000007, 179.999618, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2242.636718, 501.347015, -7.600968, 0.000007, -0.000007, 179.999618, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, -2243.907226, 501.902679, -7.586746, 0.000007, -0.000007, 179.999618, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2244.438476, 501.190765, -6.997666, 0.000007, -0.000007, 179.999618, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2244.248535, 501.591156, -6.997666, 0.000007, -0.000007, 179.999618, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2241.416503, 501.347015, -7.600968, 0.000007, -0.000007, 179.999618, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2246.396240, 501.347015, -7.600968, 0.000007, -0.000007, 179.999618, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, -2244.322998, 501.347839, -5.482326, 0.000007, 0.000075, 179.999877, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2262, -2245.466064, 501.324645, -5.402321, 0.000007, 0.000075, 179.999877, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2264, -2243.314697, 501.350524, -5.432321, 0.000007, 0.000075, 179.999877, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1778, -2236.311767, 503.421356, -7.561651, 0.000000, 0.000000, -168.800018, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11730, -2234.107421, 506.100097, -7.561649, 0.000000, 0.000007, 0.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11707, -2235.456298, 503.395355, -6.421650, 0.000000, 0.000000, 180.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, -2233.978027, 503.661651, -7.576024, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, -2233.978027, 504.341674, -7.576024, 0.000000, 0.000000, 270.000000, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(3089, -2235.727783, 520.225036, -6.230992, 0.000022, 0.000014, 89.999900, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(3089, -2235.727783, 523.205505, -6.230992, -0.000022, -0.000014, -89.999961, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1727, -2251.434570, 501.347015, -7.600968, 0.000007, -0.000008, 179.999618, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(1822, -2252.705078, 501.902679, -7.586746, 0.000007, -0.000008, 179.999618, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2253.236328, 501.190765, -6.997666, 0.000007, -0.000008, 179.999618, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(19835, -2253.046386, 501.591156, -6.997666, 0.000007, -0.000008, 179.999618, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(3089, -2254.948486, 541.245788, -6.360988, -0.000022, -0.000014, -89.999961, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, -2245.731201, 535.648681, -5.482326, 0.000007, 0.000037, 179.999832, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2262, -2246.874267, 535.625488, -5.402322, 0.000007, 0.000037, 179.999832, 77, 77, -1, 200.00, 200.00); 
    CreateDynamicObject(2264, -2244.722900, 535.651367, -5.432321, 0.000007, 0.000037, 179.999832, 77, 77, -1, 200.00, 200.00);
}