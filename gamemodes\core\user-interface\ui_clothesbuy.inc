new Text:ClothesTD[13];

CreateClothesBuyTextdraw()
{
    ClothesTD[0] = TextDrawCreate(147.000000, 172.000000, "_");
    TextDrawFont(ClothesTD[0], 1);
    TextDrawLetterSize(ClothesTD[0], 0.600000, 20.800003);
    TextDrawTextSize(ClothesTD[0], 298.500000, 180.000000);
    TextDrawSetOutline(ClothesTD[0], 1);
    TextDrawSetShadow(ClothesTD[0], 0);
    TextDrawAlignment(ClothesTD[0], 2);
    TextDrawColor(ClothesTD[0], -1);
    TextDrawBackgroundColor(ClothesTD[0], 255);
    TextDrawBoxColor(ClothesTD[0], -7232257);
    TextDrawUseBox(ClothesTD[0], 1);
    TextDrawSetProportional(ClothesTD[0], 1);
    TextDrawSetSelectable(ClothesTD[0], 0);

    ClothesTD[1] = TextDrawCreate(65.000000, 182.000000, "Preview_Model");
    TextDrawFont(ClothesTD[1], 5);
    TextDrawLetterSize(ClothesTD[1], 0.600000, 2.000000);
    TextDrawTextSize(ClothesTD[1], 46.500000, 58.500000);
    TextDrawSetOutline(ClothesTD[1], 0);
    TextDrawSetShadow(ClothesTD[1], 0);
    TextDrawAlignment(ClothesTD[1], 1);
    TextDrawColor(ClothesTD[1], -1);
    TextDrawBackgroundColor(ClothesTD[1], 255);
    TextDrawBoxColor(ClothesTD[1], 255);
    TextDrawUseBox(ClothesTD[1], 0);
    TextDrawSetProportional(ClothesTD[1], 1);
    TextDrawSetSelectable(ClothesTD[1], 1);
    TextDrawSetPreviewModel(ClothesTD[1], 0);
    TextDrawSetPreviewRot(ClothesTD[1], -10.000000, 0.000000, -20.000000, 1.000000);
    TextDrawSetPreviewVehCol(ClothesTD[1], 1, 1);

    ClothesTD[2] = TextDrawCreate(123.500000, 182.000000, "Preview_Model");
    TextDrawFont(ClothesTD[2], 5);
    TextDrawLetterSize(ClothesTD[2], 0.600000, 2.000000);
    TextDrawTextSize(ClothesTD[2], 46.500000, 58.500000);
    TextDrawSetOutline(ClothesTD[2], 0);
    TextDrawSetShadow(ClothesTD[2], 0);
    TextDrawAlignment(ClothesTD[2], 1);
    TextDrawColor(ClothesTD[2], -1);
    TextDrawBackgroundColor(ClothesTD[2], 255);
    TextDrawBoxColor(ClothesTD[2], 255);
    TextDrawUseBox(ClothesTD[2], 0);
    TextDrawSetProportional(ClothesTD[2], 1);
    TextDrawSetSelectable(ClothesTD[2], 1);
    TextDrawSetPreviewModel(ClothesTD[2], 0);
    TextDrawSetPreviewRot(ClothesTD[2], -10.000000, 0.000000, -20.000000, 1.000000);
    TextDrawSetPreviewVehCol(ClothesTD[2], 1, 1);

    ClothesTD[3] = TextDrawCreate(182.000000, 182.000000, "Preview_Model");
    TextDrawFont(ClothesTD[3], 5);
    TextDrawLetterSize(ClothesTD[3], 0.600000, 2.000000);
    TextDrawTextSize(ClothesTD[3], 46.500000, 58.500000);
    TextDrawSetOutline(ClothesTD[3], 0);
    TextDrawSetShadow(ClothesTD[3], 0);
    TextDrawAlignment(ClothesTD[3], 1);
    TextDrawColor(ClothesTD[3], -1);
    TextDrawBackgroundColor(ClothesTD[3], 255);
    TextDrawBoxColor(ClothesTD[3], 255);
    TextDrawUseBox(ClothesTD[3], 0);
    TextDrawSetProportional(ClothesTD[3], 1);
    TextDrawSetSelectable(ClothesTD[3], 1);
    TextDrawSetPreviewModel(ClothesTD[3], 0);
    TextDrawSetPreviewRot(ClothesTD[3], -10.000000, 0.000000, -20.000000, 1.000000);
    TextDrawSetPreviewVehCol(ClothesTD[3], 1, 1);

    ClothesTD[4] = TextDrawCreate(65.000000, 268.000000, "Preview_Model");
    PlayerTextDrawFont(ClothesTD[4], 5);
    PlayerTextDrawLetterSize(ClothesTD[4], 0.600000, 2.000000);
    PlayerTextDrawTextSize(ClothesTD[4], 46.500000, 58.500000);
    PlayerTextDrawSetOutline(ClothesTD[4], 0);
    PlayerTextDrawSetShadow(ClothesTD[4], 0);
    PlayerTextDrawAlignment(ClothesTD[4], 1);
    PlayerTextDrawColor(ClothesTD[4], -1);
    PlayerTextDrawBackgroundColor(ClothesTD[4], 255);
    PlayerTextDrawBoxColor(ClothesTD[4], 255);
    PlayerTextDrawUseBox(ClothesTD[4], 0);
    PlayerTextDrawSetProportional(ClothesTD[4], 1);
    PlayerTextDrawSetSelectable(ClothesTD[4], 1);
    PlayerTextDrawSetPreviewModel(ClothesTD[4], 0);
    PlayerTextDrawSetPreviewRot(ClothesTD[4], -10.000000, 0.000000, -20.000000, 1.000000);
    PlayerTextDrawSetPreviewVehCol(ClothesTD[4], 1, 1);

    ClothesTD[5] = TextDrawCreate(123.500000, 268.000000, "Preview_Model");
    TextDrawFont(ClothesTD[5], 5);
    TextDrawLetterSize(ClothesTD[5], 0.600000, 2.000000);
    TextDrawTextSize(ClothesTD[5], 46.500000, 58.500000);
    TextDrawSetOutline(ClothesTD[5], 0);
    TextDrawSetShadow(ClothesTD[5], 0);
    TextDrawAlignment(ClothesTD[5], 1);
    TextDrawColor(ClothesTD[5], -1);
    TextDrawBackgroundColor(ClothesTD[5], 255);
    TextDrawBoxColor(ClothesTD[5], 255);
    TextDrawUseBox(ClothesTD[5], 0);
    TextDrawSetProportional(ClothesTD[5], 1);
    TextDrawSetSelectable(ClothesTD[5], 1);
    TextDrawSetPreviewModel(ClothesTD[5], 0);
    TextDrawSetPreviewRot(ClothesTD[5], -10.000000, 0.000000, -20.000000, 1.000000);
    TextDrawSetPreviewVehCol(ClothesTD[5], 1, 1);

    ClothesTD[6] = TextDrawCreate(182.000000, 268.000000, "Preview_Model");
    TextDrawFont(ClothesTD[6], 5);
    TextDrawLetterSize(ClothesTD[6], 0.600000, 2.000000);
    TextDrawTextSize(ClothesTD[6], 46.500000, 58.500000);
    TextDrawSetOutline(ClothesTD[6], 0);
    TextDrawSetShadow(ClothesTD[6], 0);
    TextDrawAlignment(ClothesTD[6], 1);
    TextDrawColor(ClothesTD[6], -1);
    TextDrawBackgroundColor(ClothesTD[6], 255);
    TextDrawBoxColor(ClothesTD[6], 255);
    TextDrawUseBox(ClothesTD[6], 0);
    TextDrawSetProportional(ClothesTD[6], 1);
    TextDrawSetSelectable(ClothesTD[6], 1);
    TextDrawSetPreviewModel(ClothesTD[6], 0);
    TextDrawSetPreviewRot(ClothesTD[6], -10.000000, 0.000000, -20.000000, 1.000000);
    TextDrawSetPreviewVehCol(ClothesTD[6], 1, 1);

    ClothesTD[7] = TextDrawCreate(218.000000, 352.000000, ">>");
    TextDrawFont(ClothesTD[7], 2);
    TextDrawLetterSize(ClothesTD[7], 0.145833, 1.700000);
    TextDrawTextSize(ClothesTD[7], 16.500000, 38.000000);
    TextDrawSetOutline(ClothesTD[7], 1);
    TextDrawSetShadow(ClothesTD[7], 0);
    TextDrawAlignment(ClothesTD[7], 2);
    TextDrawColor(ClothesTD[7], -1);
    TextDrawBackgroundColor(ClothesTD[7], 255);
    TextDrawBoxColor(ClothesTD[7], 255);
    TextDrawUseBox(ClothesTD[7], 1);
    TextDrawSetProportional(ClothesTD[7], 1);
    TextDrawSetSelectable(ClothesTD[7], 1);

    ClothesTD[8] = TextDrawCreate(76.000000, 352.000000, "<<");
    TextDrawFont(ClothesTD[8], 2);
    TextDrawLetterSize(ClothesTD[8], 0.145833, 1.700000);
    TextDrawTextSize(ClothesTD[8], 16.500000, 38.000000);
    TextDrawSetOutline(ClothesTD[8], 1);
    TextDrawSetShadow(ClothesTD[8], 0);
    TextDrawAlignment(ClothesTD[8], 2);
    TextDrawColor(ClothesTD[8], -1);
    TextDrawBackgroundColor(ClothesTD[8], 255);
    TextDrawBoxColor(ClothesTD[8], 255);
    TextDrawUseBox(ClothesTD[8], 1);
    TextDrawSetProportional(ClothesTD[8], 1);
    TextDrawSetSelectable(ClothesTD[8], 1);

    ClothesTD[9] = TextDrawCreate(147.000000, 352.000000, "Buy");
    TextDrawFont(ClothesTD[9], 2);
    TextDrawLetterSize(ClothesTD[9], 0.145833, 1.700000);
    TextDrawTextSize(ClothesTD[9], 16.500000, 44.000000);
    TextDrawSetOutline(ClothesTD[9], 1);
    TextDrawSetShadow(ClothesTD[9], 0);
    TextDrawAlignment(ClothesTD[9], 2);
    TextDrawColor(ClothesTD[9], -1);
    TextDrawBackgroundColor(ClothesTD[9], 255);
    TextDrawBoxColor(ClothesTD[9], 255);
    TextDrawUseBox(ClothesTD[9], 1);
    TextDrawSetProportional(ClothesTD[9], 1);
    TextDrawSetSelectable(ClothesTD[9], 1);

    ClothesTD[10] = TextDrawCreate(231.000000, 163.000000, "X");
    TextDrawFont(ClothesTD[10], 1);
    TextDrawLetterSize(ClothesTD[10], 0.479166, 1.600000);
    TextDrawTextSize(ClothesTD[10], 244.000000, 17.000000);
    TextDrawSetOutline(ClothesTD[10], 1);
    TextDrawSetShadow(ClothesTD[10], 0);
    TextDrawAlignment(ClothesTD[10], 1);
    TextDrawColor(ClothesTD[10], -1);
    TextDrawBackgroundColor(ClothesTD[10], 255);
    TextDrawBoxColor(ClothesTD[10], 50);
    TextDrawUseBox(ClothesTD[10], 0);
    TextDrawSetProportional(ClothesTD[10], 1);
    TextDrawSetSelectable(ClothesTD[10], 1);

    ClothesTD[11] = TextDrawCreate(47.000000, 157.000000, "Beli Pakaian");
    TextDrawFont(ClothesTD[11], 0);
    TextDrawLetterSize(ClothesTD[11], 0.600000, 2.000000);
    TextDrawTextSize(ClothesTD[11], 216.000000, 17.000000);
    TextDrawSetOutline(ClothesTD[11], 1);
    TextDrawSetShadow(ClothesTD[11], 0);
    TextDrawAlignment(ClothesTD[11], 1);
    TextDrawColor(ClothesTD[11], -1);
    TextDrawBackgroundColor(ClothesTD[11], 255);
    TextDrawBoxColor(ClothesTD[11], 50);
    TextDrawUseBox(ClothesTD[11], 0);
    TextDrawSetProportional(ClothesTD[11], 1);
    TextDrawSetSelectable(ClothesTD[11], 0);

    ClothesTD[12] = TextDrawCreate(195.000000, 332.000000, "10/10");
    TextDrawFont(ClothesTD[12], 1);
    TextDrawLetterSize(ClothesTD[12], 0.325000, 1.450000);
    TextDrawTextSize(ClothesTD[12], 236.500000, 17.000000);
    TextDrawSetOutline(ClothesTD[12], 0);
    TextDrawSetShadow(ClothesTD[12], 0);
    TextDrawAlignment(ClothesTD[12], 1);
    TextDrawColor(ClothesTD[12], 255);
    TextDrawBackgroundColor(ClothesTD[12], 255);
    TextDrawBoxColor(ClothesTD[12], 50);
    TextDrawUseBox(ClothesTD[12], 0);
    TextDrawSetProportional(ClothesTD[12], 1);
    TextDrawSetSelectable(ClothesTD[12], 0);
}

ShowClothesTD(playerid)
{
    for(new x=0; x < 13; x++)
    {
        TextDrawShowForPlayer(playerid, ClothesTD[x]);
    }
    SelectTextDraw(playerid, 0xff91a4cc);
}

HideClothesTD(playerid)
{
    for(new x=0; x < 13; x++)
    {
        TextDrawHideForPlayer(playerid, ClothesTD[x]);
    }
    CancelSelectTextDraw(playerid);
}