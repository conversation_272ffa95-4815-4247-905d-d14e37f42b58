#
# rules.input-after
#
# Rules that should be run after the ufw command line added rules. Custom
# rules should be added to one of these chains:
#   ufw6-after-input
#   ufw6-after-output
#   ufw6-after-forward
#

# Don't delete these required lines, otherwise there will be errors
*filter
:ufw6-after-input - [0:0]
:ufw6-after-output - [0:0]
:ufw6-after-forward - [0:0]
# End required lines

# don't log noisy services by default
-A ufw6-after-input -p udp --dport 137 -j ufw6-skip-to-policy-input
-A ufw6-after-input -p udp --dport 138 -j ufw6-skip-to-policy-input
-A ufw6-after-input -p tcp --dport 139 -j ufw6-skip-to-policy-input
-A ufw6-after-input -p tcp --dport 445 -j ufw6-skip-to-policy-input
-A ufw6-after-input -p udp --dport 546 -j ufw6-skip-to-policy-input
-A ufw6-after-input -p udp --dport 547 -j ufw6-skip-to-policy-input

# don't delete the 'COMMIT' line or these rules won't be processed
COMMIT
