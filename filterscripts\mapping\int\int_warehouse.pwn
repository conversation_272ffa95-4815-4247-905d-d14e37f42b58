CreateWarehouseInt()
{
    new STREAMER_TAG_OBJECT:wrsxt;
    wrsxt = CreateDynamicObject(18981, -1503.539306, 2635.039306, 27.401493, 0.000000, 90.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1478.538940, 2635.039306, 27.401493, 0.000000, 90.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1453.559570, 2635.039306, 27.401493, 0.000000, 90.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1428.560546, 2635.039306, 27.401493, 0.000000, 90.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1503.539306, 2610.061035, 27.401493, 0.000000, 90.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1478.538940, 2610.061035, 27.401493, 0.000000, 90.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1453.559570, 2610.061035, 27.401493, 0.000000, 90.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1428.560546, 2610.061035, 27.401493, 0.000000, 90.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 18008, "intclothesa", "mp_cloth_vicfloor", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1478.538940, 2585.066894, 27.401493, 0.000000, 90.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 19004, "roundbuilding1", "stonewalltile4", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1453.559570, 2585.066894, 27.401493, 0.000000, 90.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 19004, "roundbuilding1", "stonewalltile4", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1516.538330, 2635.039306, 33.631523, 0.000000, 180.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16093, "a51_ext", "corugwall_sandy", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1516.538330, 2610.039062, 33.631523, 0.000000, 180.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16093, "a51_ext", "corugwall_sandy", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1513.806274, 2604.752685, 25.802347, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1513.806274, 2604.752685, 29.852350, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1513.806274, 2609.364013, 25.802347, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1513.806274, 2609.364013, 29.852350, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1513.806274, 2613.984619, 25.802347, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1514.168090, 2607.606689, 27.725046, 90.000000, 90.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1513.806274, 2613.984619, 29.852350, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1513.806274, 2618.595947, 25.802347, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1513.806274, 2618.595947, 29.852350, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1514.168090, 2617.606933, 27.725046, 90.000000, 90.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1513.806274, 2623.262451, 25.802347, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1513.806274, 2623.262451, 29.852350, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1513.806274, 2627.873779, 25.802347, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1513.806274, 2627.873779, 29.852350, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1514.168090, 2627.604492, 27.725046, 89.999992, 180.000000, -89.999992, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1513.806274, 2632.489746, 25.802347, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1513.806274, 2632.489746, 29.852350, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1513.806274, 2637.101074, 25.802347, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1513.806274, 2637.101074, 29.852350, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1514.168090, 2637.601806, 27.725046, 89.999992, 180.000000, -89.999977, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1513.891357, 2607.642822, 32.461585, 0.000000, 90.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1513.891357, 2617.272705, 32.461585, 0.000000, 90.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1513.891357, 2626.903320, 32.461585, 0.000000, 90.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1513.901367, 2634.261230, 32.451587, 0.000000, 90.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1503.654541, 2640.518798, 25.802347, 0.000007, 0.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1503.654541, 2640.518798, 29.852350, 0.000007, 0.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1503.654541, 2635.907470, 25.802347, 0.000007, 0.000022, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1503.654541, 2635.907470, 29.852350, 0.000007, 0.000022, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1503.654541, 2631.286865, 25.802347, 0.000007, 0.000022, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1503.292724, 2637.664794, 27.725046, 89.999992, 225.000000, 44.999973, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1503.654541, 2631.286865, 29.852350, 0.000007, 0.000022, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1503.654541, 2626.675537, 25.802347, 0.000007, 0.000030, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1503.654541, 2626.675537, 29.852350, 0.000007, 0.000030, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1503.292724, 2627.664550, 27.725046, 89.999992, 225.000000, 44.999973, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1503.654541, 2622.009033, 25.802347, 0.000007, 0.000030, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1503.654541, 2622.009033, 29.852350, 0.000007, 0.000030, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1503.654541, 2617.397705, 25.802347, 0.000007, 0.000038, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1503.654541, 2617.397705, 29.852350, 0.000007, 0.000038, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1503.292724, 2617.666992, 27.725046, 89.999992, 225.000015, 44.999984, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1503.654541, 2612.781738, 25.802347, 0.000007, 0.000038, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1503.654541, 2612.781738, 29.852350, 0.000007, 0.000038, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1503.654541, 2608.170410, 25.802347, 0.000007, 0.000045, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1503.654541, 2608.170410, 29.852350, 0.000007, 0.000045, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1503.292724, 2607.669677, 27.725046, 89.999992, 225.000030, 44.999984, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1503.569335, 2637.628662, 32.461585, 0.000007, 90.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1503.569335, 2627.998779, 32.461585, 0.000007, 90.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1503.569335, 2618.368164, 32.461585, 0.000007, 90.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1503.559326, 2611.010253, 32.451587, 0.000007, 90.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1492.464355, 2604.752685, 25.802347, 0.000000, -0.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1492.464355, 2604.752685, 29.852350, 0.000000, -0.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1492.464355, 2609.364013, 25.802347, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1492.464355, 2609.364013, 29.852350, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1492.464355, 2613.984619, 25.802347, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1492.826171, 2607.606689, 27.725046, 89.999992, 376.533325, 73.466598, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1492.464355, 2613.984619, 29.852350, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1492.464355, 2618.595947, 25.802347, 0.000000, 0.000015, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1492.464355, 2618.595947, 29.852350, 0.000000, 0.000015, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1492.826171, 2617.606933, 27.725046, 89.999992, 376.533325, 73.466598, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1492.464355, 2623.262451, 25.802347, 0.000000, 0.000015, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1492.464355, 2623.262451, 29.852350, 0.000000, 0.000015, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1492.464355, 2627.873779, 25.802347, 0.000000, 0.000022, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1492.464355, 2627.873779, 29.852350, 0.000000, 0.000022, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1492.826171, 2627.604492, 27.725046, 89.999992, 376.533325, 73.466621, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1492.464355, 2632.489746, 25.802347, 0.000000, 0.000022, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1492.464355, 2632.489746, 29.852350, 0.000000, 0.000022, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1492.464355, 2637.101074, 25.802347, 0.000000, 0.000030, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1492.464355, 2637.101074, 29.852350, 0.000000, 0.000030, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1492.826171, 2637.601806, 27.725046, 89.999992, 376.533355, 73.466621, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1492.549560, 2607.642822, 32.461585, 0.000000, 90.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1492.549560, 2617.272705, 32.461585, 0.000000, 90.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1492.549560, 2626.903320, 32.461585, 0.000000, 90.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1492.559570, 2634.261230, 32.451587, 0.000000, 90.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1481.822021, 2640.518798, 25.802347, 0.000007, 0.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1481.822021, 2640.518798, 29.852350, 0.000007, 0.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1481.822021, 2635.907470, 25.802347, 0.000007, 0.000022, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1481.822021, 2635.907470, 29.852350, 0.000007, 0.000022, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1481.822021, 2631.286865, 25.802347, 0.000007, 0.000022, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1481.460205, 2637.664794, 27.725046, 89.999992, 551.309448, 78.690444, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1481.822021, 2631.286865, 29.852350, 0.000007, 0.000022, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1481.822021, 2626.675537, 25.802347, 0.000007, 0.000030, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1481.822021, 2626.675537, 29.852350, 0.000007, 0.000030, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1481.460205, 2627.664550, 27.725046, 89.999992, 551.309448, 78.690444, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1481.822021, 2622.009033, 25.802347, 0.000007, 0.000030, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1481.822021, 2622.009033, 29.852350, 0.000007, 0.000030, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1481.822021, 2617.397705, 25.802347, 0.000007, 0.000038, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1481.822021, 2617.397705, 29.852350, 0.000007, 0.000038, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1481.460205, 2617.666992, 27.725046, 89.999992, 551.309448, 78.690467, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1481.822021, 2612.781738, 25.802347, 0.000007, 0.000038, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1481.822021, 2612.781738, 29.852350, 0.000007, 0.000038, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1481.822021, 2608.170410, 25.802347, 0.000007, 0.000045, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1481.822021, 2608.170410, 29.852350, 0.000007, 0.000045, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1481.460205, 2607.669677, 27.725046, 89.999992, 551.309448, 78.690467, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1481.736816, 2637.628662, 32.461585, 0.000007, 90.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1481.736816, 2627.998779, 32.461585, 0.000007, 90.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1481.736816, 2618.368164, 32.461585, 0.000007, 90.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1481.726806, 2611.010253, 32.451587, 0.000007, 90.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1470.515014, 2604.752685, 25.802347, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1470.515014, 2604.752685, 29.852350, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1470.515014, 2609.364013, 25.802347, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1470.515014, 2609.364013, 29.852350, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1470.515014, 2613.984619, 25.802347, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1470.876831, 2607.606689, 27.725046, 89.999992, 180.000000, -89.999992, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1470.515014, 2613.984619, 29.852350, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1470.515014, 2618.595947, 25.802347, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1470.515014, 2618.595947, 29.852350, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1470.876831, 2617.606933, 27.725046, 89.999992, 180.000000, -89.999992, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1470.515014, 2623.262451, 25.802347, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1470.515014, 2623.262451, 29.852350, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1470.515014, 2627.873779, 25.802347, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1470.515014, 2627.873779, 29.852350, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1470.876831, 2627.604492, 27.725046, 89.999992, 180.000000, -89.999977, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1470.515014, 2632.489746, 25.802347, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1470.515014, 2632.489746, 29.852350, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1470.515014, 2637.101074, 25.802347, 0.000000, 0.000038, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1470.515014, 2637.101074, 29.852350, 0.000000, 0.000038, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1470.876831, 2637.601806, 27.725046, 89.999992, 180.000015, -89.999969, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1470.600097, 2607.642822, 32.461585, 0.000000, 90.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1470.600097, 2617.272705, 32.461585, 0.000000, 90.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1470.600097, 2626.903320, 32.461585, 0.000000, 90.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1470.610107, 2634.261230, 32.451587, 0.000000, 90.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1460.363281, 2640.518798, 25.802347, 0.000007, 0.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1460.363281, 2640.518798, 29.852350, 0.000007, 0.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1460.363281, 2635.907470, 25.802347, 0.000007, 0.000015, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1460.363281, 2635.907470, 29.852350, 0.000007, 0.000015, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1460.363281, 2631.286865, 25.802347, 0.000007, 0.000015, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1460.001464, 2637.664794, 27.725046, 89.999992, 315.000000, -45.000030, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1460.363281, 2631.286865, 29.852350, 0.000007, 0.000015, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1460.363281, 2626.675537, 25.802347, 0.000007, 0.000022, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1460.363281, 2626.675537, 29.852350, 0.000007, 0.000022, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1460.001464, 2627.664550, 27.725046, 89.999992, 315.000000, -45.000030, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1460.363281, 2622.009033, 25.802347, 0.000007, 0.000022, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1460.363281, 2622.009033, 29.852350, 0.000007, 0.000022, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1460.363281, 2617.397705, 25.802347, 0.000007, 0.000030, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1460.363281, 2617.397705, 29.852350, 0.000007, 0.000030, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1460.001464, 2617.666992, 27.725046, 89.999992, 315.000000, -45.000011, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1460.363281, 2612.781738, 25.802347, 0.000007, 0.000030, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1460.363281, 2612.781738, 29.852350, 0.000007, 0.000030, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1460.363281, 2608.170410, 25.802347, 0.000007, 0.000038, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1460.363281, 2608.170410, 29.852350, 0.000007, 0.000038, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1460.001464, 2607.669677, 27.725046, 89.999992, 315.000030, -45.000011, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1460.278076, 2637.628662, 32.461585, 0.000007, 90.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1460.278076, 2627.998779, 32.461585, 0.000007, 90.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1460.278076, 2618.368164, 32.461585, 0.000007, 90.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1460.268066, 2611.010253, 32.451587, 0.000007, 90.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1449.173095, 2604.752685, 25.802347, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1449.173095, 2604.752685, 29.852350, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1449.173095, 2609.364013, 25.802347, 0.000000, 0.000015, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1449.173095, 2609.364013, 29.852350, 0.000000, 0.000015, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1449.173095, 2613.984619, 25.802347, 0.000000, 0.000015, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1449.534912, 2607.606689, 27.725046, 89.999992, 466.533325, -16.533414, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1449.173095, 2613.984619, 29.852350, 0.000000, 0.000015, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1449.173095, 2618.595947, 25.802347, 0.000000, 0.000022, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1449.173095, 2618.595947, 29.852350, 0.000000, 0.000022, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1449.534912, 2617.606933, 27.725046, 89.999992, 466.533325, -16.533414, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1449.173095, 2623.262451, 25.802347, 0.000000, 0.000022, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1449.173095, 2623.262451, 29.852350, 0.000000, 0.000022, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1449.173095, 2627.873779, 25.802347, 0.000000, 0.000030, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1449.173095, 2627.873779, 29.852350, 0.000000, 0.000030, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1449.534912, 2627.604492, 27.725046, 89.999992, 466.533325, -16.533384, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1449.173095, 2632.489746, 25.802347, 0.000000, 0.000030, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1449.173095, 2632.489746, 29.852350, 0.000000, 0.000030, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1449.173095, 2637.101074, 25.802347, 0.000000, 0.000038, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1449.173095, 2637.101074, 29.852350, 0.000000, 0.000038, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1449.534912, 2637.601806, 27.725046, 89.999992, 466.533355, -16.533384, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1449.258300, 2607.642822, 32.461585, 0.000000, 90.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1449.258300, 2617.272705, 32.461585, 0.000000, 90.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1449.258300, 2626.903320, 32.461585, 0.000000, 90.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1449.268310, 2634.261230, 32.451587, 0.000000, 90.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1438.530761, 2640.518798, 25.802347, 0.000007, 0.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1438.530761, 2640.518798, 29.852350, 0.000007, 0.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1438.530761, 2635.907470, 25.802347, 0.000007, 0.000015, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1438.530761, 2635.907470, 29.852350, 0.000007, 0.000015, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1438.530761, 2631.286865, 25.802347, 0.000007, 0.000015, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1438.168945, 2637.664794, 27.725046, 89.999992, 641.309448, -11.309569, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1438.530761, 2631.286865, 29.852350, 0.000007, 0.000015, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1438.530761, 2626.675537, 25.802347, 0.000007, 0.000022, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1438.530761, 2626.675537, 29.852350, 0.000007, 0.000022, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1438.168945, 2627.664550, 27.725046, 89.999992, 641.309448, -11.309569, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1438.530761, 2622.009033, 25.802347, 0.000007, 0.000022, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1438.530761, 2622.009033, 29.852350, 0.000007, 0.000022, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1438.530761, 2617.397705, 25.802347, 0.000007, 0.000030, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1438.530761, 2617.397705, 29.852350, 0.000007, 0.000030, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1438.168945, 2617.666992, 27.725046, 89.999992, 641.309448, -11.309546, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1438.530761, 2612.781738, 25.802347, 0.000007, 0.000030, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1438.530761, 2612.781738, 29.852350, 0.000007, 0.000030, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1438.530761, 2608.170410, 25.802347, 0.000007, 0.000038, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1438.530761, 2608.170410, 29.852350, 0.000007, 0.000038, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1438.168945, 2607.669677, 27.725046, 89.999992, 641.309448, -11.309546, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1438.445556, 2637.628662, 32.461585, 0.000007, 90.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1438.445556, 2627.998779, 32.461585, 0.000007, 90.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1438.445556, 2618.368164, 32.461585, 0.000007, 90.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1438.435546, 2611.010253, 32.451587, 0.000007, 90.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1415.587646, 2610.039062, 33.631523, 0.000000, 180.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16093, "a51_ext", "corugwall_sandy", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1415.586914, 2635.039306, 33.631523, 0.000000, 180.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16093, "a51_ext", "corugwall_sandy", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1428.542724, 2604.752685, 25.802347, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1428.542724, 2604.752685, 29.852350, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1428.542724, 2609.364013, 25.802347, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1428.542724, 2609.364013, 29.852350, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1428.542724, 2613.984619, 25.802347, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1428.904541, 2607.606689, 27.725046, 89.999992, 180.000000, -89.999977, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1428.542724, 2613.984619, 29.852350, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1428.542724, 2618.595947, 25.802347, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1428.542724, 2618.595947, 29.852350, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1428.904541, 2617.606933, 27.725046, 89.999992, 180.000000, -89.999977, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1428.542724, 2623.262451, 25.802347, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1428.542724, 2623.262451, 29.852350, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1428.542724, 2627.873779, 25.802347, 0.000000, 0.000038, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1428.542724, 2627.873779, 29.852350, 0.000000, 0.000038, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1428.904541, 2627.604492, 27.725046, 89.999992, 180.000015, -89.999969, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1428.542724, 2632.489746, 25.802347, 0.000000, 0.000038, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1428.542724, 2632.489746, 29.852350, 0.000000, 0.000038, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1428.542724, 2637.101074, 25.802347, 0.000000, 0.000045, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1428.542724, 2637.101074, 29.852350, 0.000000, 0.000045, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1428.904541, 2637.601806, 27.725046, 89.999992, 180.000030, -89.999961, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1428.627807, 2607.642822, 32.461585, 0.000000, 90.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1428.627807, 2617.272705, 32.461585, 0.000000, 90.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1428.627807, 2626.903320, 32.461585, 0.000000, 90.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1428.637817, 2634.261230, 32.451587, 0.000000, 90.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1418.390991, 2640.518798, 25.802347, 0.000007, 0.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1418.390991, 2640.518798, 29.852350, 0.000007, 0.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1418.390991, 2635.907470, 25.802347, 0.000007, 0.000007, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1418.390991, 2635.907470, 29.852350, 0.000007, 0.000007, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1418.390991, 2631.286865, 25.802347, 0.000007, 0.000007, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1418.029174, 2637.664794, 27.725046, 89.999992, 334.471191, -64.471237, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1418.390991, 2631.286865, 29.852350, 0.000007, 0.000007, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1418.390991, 2626.675537, 25.802347, 0.000007, 0.000015, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1418.390991, 2626.675537, 29.852350, 0.000007, 0.000015, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1418.029174, 2627.664550, 27.725046, 89.999992, 334.471191, -64.471237, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1418.390991, 2622.009033, 25.802347, 0.000007, 0.000015, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1418.390991, 2622.009033, 29.852350, 0.000007, 0.000015, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1418.390991, 2617.397705, 25.802347, 0.000007, 0.000022, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1418.390991, 2617.397705, 29.852350, 0.000007, 0.000022, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1418.029174, 2617.666992, 27.725046, 89.999992, 334.471221, -64.471221, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1418.390991, 2612.781738, 25.802347, 0.000007, 0.000022, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1418.390991, 2612.781738, 29.852350, 0.000007, 0.000022, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1418.390991, 2608.170410, 25.802347, 0.000007, 0.000030, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 1948, "kbslotnu", "slot_steel", 0x00000000);
    wrsxt = CreateDynamicObject(19157, -1418.390991, 2608.170410, 29.852350, 0.000007, 0.000030, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    wrsxt = CreateDynamicObject(18766, -1418.029174, 2607.669677, 27.725046, 89.999992, 334.471252, -64.471221, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1418.305786, 2637.628662, 32.461585, 0.000007, 90.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1418.305786, 2627.998779, 32.461585, 0.000007, 90.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1418.305786, 2618.368164, 32.461585, 0.000007, 90.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(19445, -1418.295776, 2611.010253, 32.451587, 0.000007, 90.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1503.539306, 2635.039306, 45.601390, 0.000000, 90.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 18835, "mickytextures", "metal061", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1478.538940, 2635.039306, 45.601390, 0.000000, 90.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 18835, "mickytextures", "metal061", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1453.559570, 2635.039306, 45.601390, 0.000000, 90.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 18835, "mickytextures", "metal061", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1428.560546, 2635.039306, 45.601390, 0.000000, 90.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 18835, "mickytextures", "metal061", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1503.539306, 2610.061035, 45.601390, 0.000000, 90.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 18835, "mickytextures", "metal061", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1478.538940, 2610.061035, 45.601390, 0.000000, 90.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 18835, "mickytextures", "metal061", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1453.559570, 2610.061035, 45.601390, 0.000000, 90.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 18835, "mickytextures", "metal061", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1428.560546, 2610.061035, 45.601390, 0.000000, 90.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 18835, "mickytextures", "metal061", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1503.588500, 2597.059082, 33.631523, 0.000000, 180.000000, 90.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16093, "a51_ext", "corugwall_sandy", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1428.556396, 2597.059082, 33.631523, 0.000000, 180.000000, 90.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 16093, "a51_ext", "corugwall_sandy", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1440.677612, 2584.521240, 33.631523, 0.000000, 180.000000, 180.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1491.569091, 2585.030761, 33.631523, 0.000000, 180.000000, 180.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1447.817382, 2597.040283, 40.331546, 0.000000, 180.000000, 270.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1484.348022, 2597.040283, 40.331546, 0.000000, 180.000000, 270.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    wrsxt = CreateDynamicObject(16074, -1484.233276, 2570.107421, 31.168300, 0.000000, 0.000000, 119.199966, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    SetDynamicObjectMaterial(wrsxt, 3, 16271, "des_factory", "sm_quarry_conv_belt_empty", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1478.610717, 2572.040527, 33.631523, 0.000000, 180.000000, 270.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1453.641235, 2572.040527, 33.631523, 0.000000, 180.000000, 270.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    wrsxt = CreateDynamicObject(16074, -1470.885498, 2570.086669, 31.168300, 0.000000, 0.000000, 119.199966, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    SetDynamicObjectMaterial(wrsxt, 3, 16271, "des_factory", "sm_quarry_conv_belt_empty", 0x00000000);
    wrsxt = CreateDynamicObject(16074, -1460.272827, 2570.107421, 31.168300, 0.000006, -0.000003, 119.199943, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    SetDynamicObjectMaterial(wrsxt, 3, 16271, "des_factory", "sm_quarry_conv_belt_empty", 0x00000000);
    wrsxt = CreateDynamicObject(16074, -1446.925048, 2570.086669, 31.168300, 0.000006, -0.000003, 119.199943, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    SetDynamicObjectMaterial(wrsxt, 3, 16271, "des_factory", "sm_quarry_conv_belt_empty", 0x00000000);
    wrsxt = CreateDynamicObject(3032, -1446.891113, 2572.568603, 31.933860, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wrsxt = CreateDynamicObject(19411, -1446.882446, 2572.495361, 31.687086, 0.000000, 0.000000, 90.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 8391, "ballys01", "CJ_blackplastic", 0x00000000);
    wrsxt = CreateDynamicObject(3032, -1460.252685, 2572.558593, 31.933860, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wrsxt = CreateDynamicObject(19411, -1460.244018, 2572.495361, 31.687086, 0.000007, 0.000000, 89.999977, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 8391, "ballys01", "CJ_blackplastic", 0x00000000);
    wrsxt = CreateDynamicObject(3032, -1470.882446, 2572.558593, 31.933860, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wrsxt = CreateDynamicObject(19411, -1470.873779, 2572.495361, 31.687086, 0.000015, 0.000000, 89.999954, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 8391, "ballys01", "CJ_blackplastic", 0x00000000);
    wrsxt = CreateDynamicObject(3032, -1484.193481, 2572.558593, 31.933860, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wrsxt = CreateDynamicObject(19411, -1484.184814, 2572.495361, 31.687086, 0.000030, 0.000000, 89.999908, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 8391, "ballys01", "CJ_blackplastic", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1478.538940, 2585.066894, 45.651432, 0.000000, 90.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1453.559570, 2585.066894, 45.651432, 0.000000, 90.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1503.559326, 2647.973388, 34.071498, 89.999992, 514.471130, -64.471199, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1478.558959, 2647.973388, 34.071498, 89.999992, 514.471130, -64.471199, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1453.579589, 2647.973388, 34.071498, 89.999992, 514.471130, -64.471199, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    wrsxt = CreateDynamicObject(18981, -1428.580566, 2647.973388, 34.071498, 89.999992, 514.471130, -64.471199, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    wrsxt = CreateDynamicObject(3037, -1465.418823, 2647.488037, 30.069505, 0.000000, 0.000000, 90.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 4829, "airport_las", "Bow_Loadingbay_Door", 0x00000000);
    wrsxt = CreateDynamicObject(18980, -1453.469604, 2579.253173, 33.288909, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 3474, "freightcrane", "yellowcabchev_256", 0x00000000);
    wrsxt = CreateDynamicObject(18980, -1465.539672, 2579.253173, 33.288909, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 3474, "freightcrane", "yellowcabchev_256", 0x00000000);
    wrsxt = CreateDynamicObject(18980, -1477.259765, 2579.253173, 33.288909, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wrsxt, 0, 3474, "freightcrane", "yellowcabchev_256", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(3577, -1513.787597, 2604.755615, 28.951898, 0.000000, 0.000000, 270.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1513.787597, 2609.436523, 28.951898, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1513.787597, 2613.987548, 28.951898, -0.000007, -0.000000, -89.999977, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1513.787597, 2618.668457, 28.951898, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1513.787597, 2613.987548, 30.461893, -0.000007, -0.000000, 0.000022, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1513.787597, 2623.265380, 28.951898, -0.000015, 0.000000, -89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1513.787597, 2627.946289, 28.951898, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1513.787597, 2632.492675, 28.951898, -0.000022, 0.000000, -89.999931, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1513.787597, 2637.173583, 28.951898, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(2977, -1513.841430, 2627.436767, 29.564228, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(964, -1514.023559, 2619.237792, 29.666856, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1513.916503, 2609.154052, 34.036560, 0.000000, 0.000000, 90.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1513.916503, 2627.745361, 34.036560, 0.000000, 0.000000, 180.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18260, -1514.000366, 2619.724365, 34.118003, 0.000000, 0.000000, 270.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1513.152465, 2604.941406, 30.163858, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1513.863037, 2603.570312, 33.013889, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1513.863037, 2604.830566, 33.013889, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1513.892333, 2606.310058, 33.013889, 0.000000, 0.000000, 135.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1513.152465, 2623.540527, 30.163858, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1514.533569, 2632.460693, 33.013885, 0.000000, 0.000000, 45.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1513.564331, 2631.490966, 33.013885, 0.000000, 0.000000, 45.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1513.168579, 2632.920410, 33.013885, 0.000000, 0.000000, 45.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1513.918823, 2636.436279, 34.026573, 0.000000, 0.000000, 270.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1513.028808, 2617.198730, 33.013889, 0.000000, 0.000000, 135.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1514.671997, 2640.390869, 28.703857, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1513.351928, 2640.390869, 28.703857, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1513.351928, 2641.701171, 28.703857, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1514.771850, 2641.701171, 28.703857, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1503.673095, 2640.515869, 28.951898, -0.000015, 0.000007, 89.999984, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1503.673095, 2635.834960, 28.951898, 0.000007, 0.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1503.673095, 2631.283935, 28.951898, -0.000022, 0.000007, 90.000007, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1503.673095, 2626.603027, 28.951898, 0.000007, 0.000022, 179.999923, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1503.673095, 2631.283935, 30.461893, 0.000000, 0.000015, 179.999938, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1503.673095, 2622.006103, 28.951898, -0.000030, 0.000007, 90.000030, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1503.673095, 2617.325195, 28.951898, 0.000007, 0.000030, 179.999923, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1503.673095, 2612.778808, 28.951898, -0.000038, 0.000007, 90.000053, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1503.673095, 2608.097900, 28.951898, 0.000007, 0.000038, 179.999923, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(2977, -1503.619384, 2617.834716, 29.564228, 0.000007, 0.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(964, -1503.437255, 2626.033691, 29.666856, 0.000007, 0.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1503.544189, 2636.117431, 34.036560, 0.000015, -0.000007, -90.000061, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1503.544189, 2617.526123, 34.036560, -0.000007, -0.000015, -0.000038, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18260, -1503.460205, 2625.547119, 34.118003, -0.000015, 0.000007, 89.999984, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1504.308349, 2640.330078, 30.163858, 0.000007, 0.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1503.597656, 2641.701171, 33.013889, 0.000007, 0.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1503.597656, 2640.440917, 33.013889, 0.000007, 0.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1503.568359, 2638.961425, 33.013889, 0.000005, -0.000016, -45.000003, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1504.308349, 2621.730957, 30.163858, 0.000007, 0.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1502.927001, 2612.810791, 33.013885, 0.000016, 0.000005, -135.000045, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1503.896240, 2613.780517, 33.013885, 0.000016, 0.000005, -135.000045, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1504.292236, 2612.351074, 33.013885, 0.000016, 0.000005, -135.000045, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1503.541748, 2608.835205, 34.026573, -0.000015, 0.000007, 89.999984, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1504.431884, 2628.072753, 33.013889, 0.000005, -0.000016, -45.000003, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1502.788818, 2604.880615, 28.703857, 0.000007, 0.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1504.108642, 2604.880615, 28.703857, 0.000007, 0.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1504.108642, 2603.570312, 28.703857, 0.000007, 0.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1502.688720, 2603.570312, 28.703857, 0.000007, 0.000015, 179.999923, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1492.445800, 2604.755615, 28.951898, 0.000000, 0.000000, -90.000076, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1492.445800, 2609.436523, 28.951898, 0.000000, -0.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1492.445800, 2613.987548, 28.951898, -0.000007, 0.000000, -90.000045, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1492.445800, 2618.668457, 28.951898, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1492.445800, 2613.987548, 30.461893, -0.000007, -0.000000, -0.000099, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1492.445800, 2623.265380, 28.951898, -0.000015, 0.000000, -90.000030, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1492.445800, 2627.946289, 28.951898, 0.000000, 0.000015, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1492.445800, 2632.492675, 28.951898, -0.000022, 0.000000, -89.999923, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1492.445800, 2637.173583, 28.951898, 0.000000, 0.000022, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(2977, -1492.499511, 2627.436767, 29.564228, 0.000000, -0.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(964, -1492.681640, 2619.237792, 29.666856, 0.000000, -0.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1492.574707, 2609.154052, 34.036560, 0.000000, -0.000000, 89.999923, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1492.574707, 2627.745361, 34.036560, -0.000000, 0.000000, 179.999877, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18260, -1492.658691, 2619.724365, 34.118003, 0.000000, 0.000000, -90.000076, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1491.810546, 2604.941406, 30.163858, 0.000000, -0.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1492.521240, 2603.570312, 33.013889, 0.000000, -0.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1492.521240, 2604.830566, 33.013889, 0.000000, -0.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1492.550537, 2606.310058, 33.013889, -0.000000, 0.000000, 134.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1491.810546, 2623.540527, 30.163858, 0.000000, -0.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1493.191894, 2632.460693, 33.013885, 0.000000, -0.000000, 44.999938, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1492.222656, 2631.490966, 33.013885, 0.000000, -0.000000, 44.999938, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1491.826660, 2632.920410, 33.013885, 0.000000, -0.000000, 44.999938, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1492.577148, 2636.436279, 34.026573, 0.000000, 0.000000, -90.000076, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1491.687011, 2617.198730, 33.013889, -0.000000, 0.000000, 134.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1493.330078, 2640.390869, 28.703857, 0.000000, -0.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1492.010253, 2640.390869, 28.703857, 0.000000, -0.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1492.010253, 2641.701171, 28.703857, 0.000000, -0.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1493.430175, 2641.701171, 28.703857, 0.000000, -0.000000, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1481.840576, 2640.515869, 28.951898, -0.000015, 0.000007, 89.999908, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1481.840576, 2635.834960, 28.951898, 0.000007, 0.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1481.840576, 2631.283935, 28.951898, -0.000022, 0.000007, 89.999938, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1481.840576, 2626.603027, 28.951898, 0.000007, 0.000022, 179.999801, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1481.840576, 2631.283935, 30.461893, 0.000000, 0.000015, 179.999816, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1481.840576, 2622.006103, 28.951898, -0.000030, 0.000007, 89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1481.840576, 2617.325195, 28.951898, 0.000007, 0.000030, 179.999801, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1481.840576, 2612.778808, 28.951898, -0.000038, 0.000007, 90.000061, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1481.840576, 2608.097900, 28.951898, 0.000007, 0.000038, 179.999801, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(2977, -1481.786865, 2617.834716, 29.564228, 0.000007, 0.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(964, -1481.604736, 2626.033691, 29.666856, 0.000007, 0.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1481.711669, 2636.117431, 34.036560, 0.000015, -0.000007, -90.000137, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1481.711669, 2617.526123, 34.036560, -0.000007, -0.000015, -0.000160, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18260, -1481.627685, 2625.547119, 34.118003, -0.000015, 0.000007, 89.999908, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1482.475830, 2640.330078, 30.163858, 0.000007, 0.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1481.765136, 2641.701171, 33.013889, 0.000007, 0.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1481.765136, 2640.440917, 33.013889, 0.000007, 0.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1481.735839, 2638.961425, 33.013889, 0.000005, -0.000016, -45.000045, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1482.475830, 2621.730957, 30.163858, 0.000007, 0.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1481.094482, 2612.810791, 33.013885, 0.000016, 0.000005, -135.000091, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1482.063720, 2613.780517, 33.013885, 0.000016, 0.000005, -135.000091, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1482.459716, 2612.351074, 33.013885, 0.000016, 0.000005, -135.000091, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1481.709228, 2608.835205, 34.026573, -0.000015, 0.000007, 89.999908, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1482.599365, 2628.072753, 33.013889, 0.000005, -0.000016, -45.000045, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1480.956298, 2604.880615, 28.703857, 0.000007, 0.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1482.276123, 2604.880615, 28.703857, 0.000007, 0.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1482.276123, 2603.570312, 28.703857, 0.000007, 0.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1480.856201, 2603.570312, 28.703857, 0.000007, 0.000015, 179.999801, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1470.496337, 2604.755615, 28.951898, -0.000007, -0.000000, -89.999977, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1470.496337, 2609.436523, 28.951898, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1470.496337, 2613.987548, 28.951898, -0.000015, 0.000000, -89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1470.496337, 2618.668457, 28.951898, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1470.496337, 2613.987548, 30.461893, -0.000007, 0.000007, 0.000022, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1470.496337, 2623.265380, 28.951898, -0.000022, 0.000000, -89.999931, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1470.496337, 2627.946289, 28.951898, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1470.496337, 2632.492675, 28.951898, -0.000030, 0.000000, -89.999908, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1470.496337, 2637.173583, 28.951898, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(2977, -1470.550170, 2627.436767, 29.564228, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(964, -1470.732299, 2619.237792, 29.666856, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1470.625244, 2609.154052, 34.036560, 0.000007, 0.000000, 89.999977, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1470.625244, 2627.745361, 34.036560, 0.000000, -0.000007, 179.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18260, -1470.709106, 2619.724365, 34.118003, -0.000007, -0.000000, -89.999977, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1469.861206, 2604.941406, 30.163858, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1470.571777, 2603.570312, 33.013889, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1470.571777, 2604.830566, 33.013889, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1470.601074, 2606.310058, 33.013889, 0.000005, -0.000005, 135.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1469.861206, 2623.540527, 30.163858, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1471.242309, 2632.460693, 33.013885, 0.000005, 0.000005, 44.999988, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1470.273071, 2631.490966, 33.013885, 0.000005, 0.000005, 44.999988, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1469.877319, 2632.920410, 33.013885, 0.000005, 0.000005, 44.999988, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1470.627563, 2636.436279, 34.026573, -0.000007, -0.000000, -89.999977, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1469.737548, 2617.198730, 33.013889, 0.000005, -0.000005, 135.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1471.380737, 2640.390869, 28.703857, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1470.060668, 2640.390869, 28.703857, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1470.060668, 2641.701171, 28.703857, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1471.480590, 2641.701171, 28.703857, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1460.381835, 2640.515869, 28.951898, -0.000007, 0.000007, 89.999961, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1460.381835, 2635.834960, 28.951898, 0.000007, 0.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1460.381835, 2631.283935, 28.951898, -0.000015, 0.000007, 89.999984, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1460.381835, 2626.603027, 28.951898, 0.000007, 0.000015, 179.999877, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1460.381835, 2631.283935, 30.461893, 0.000000, 0.000007, 179.999893, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1460.381835, 2622.006103, 28.951898, -0.000022, 0.000007, 90.000007, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1460.381835, 2617.325195, 28.951898, 0.000007, 0.000022, 179.999877, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1460.381835, 2612.778808, 28.951898, -0.000030, 0.000007, 90.000030, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1460.381835, 2608.097900, 28.951898, 0.000007, 0.000030, 179.999877, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(2977, -1460.328125, 2617.834716, 29.564228, 0.000007, 0.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(964, -1460.145996, 2626.033691, 29.666856, 0.000007, 0.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1460.252929, 2636.117431, 34.036560, 0.000007, -0.000007, -90.000038, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1460.252929, 2617.526123, 34.036560, -0.000007, -0.000007, -0.000038, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18260, -1460.168945, 2625.547119, 34.118003, -0.000007, 0.000007, 89.999961, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1461.017089, 2640.330078, 30.163858, 0.000007, 0.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1460.306396, 2641.701171, 33.013889, 0.000007, 0.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1460.306396, 2640.440917, 33.013889, 0.000007, 0.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1460.277099, 2638.961425, 33.013889, -0.000000, -0.000010, -45.000003, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1461.017089, 2621.730957, 30.163858, 0.000007, 0.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1459.635742, 2612.810791, 33.013885, 0.000010, 0.000000, -135.000045, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1460.604980, 2613.780517, 33.013885, 0.000010, 0.000000, -135.000045, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1461.000976, 2612.351074, 33.013885, 0.000010, 0.000000, -135.000045, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1460.250488, 2608.835205, 34.026573, -0.000007, 0.000007, 89.999961, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1461.140625, 2628.072753, 33.013889, -0.000000, -0.000010, -45.000003, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1459.497558, 2604.880615, 28.703857, 0.000007, 0.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1460.817382, 2604.880615, 28.703857, 0.000007, 0.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1460.817382, 2603.570312, 28.703857, 0.000007, 0.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1459.397460, 2603.570312, 28.703857, 0.000007, 0.000007, 179.999877, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1449.154541, 2604.755615, 28.951898, -0.000007, 0.000000, -90.000053, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1449.154541, 2609.436523, 28.951898, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1449.154541, 2613.987548, 28.951898, -0.000015, 0.000000, -90.000022, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1449.154541, 2618.668457, 28.951898, 0.000000, 0.000015, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1449.154541, 2613.987548, 30.461893, -0.000007, 0.000007, -0.000099, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1449.154541, 2623.265380, 28.951898, -0.000022, 0.000000, -90.000007, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1449.154541, 2627.946289, 28.951898, 0.000000, 0.000022, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1449.154541, 2632.492675, 28.951898, -0.000030, 0.000000, -89.999900, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1449.154541, 2637.173583, 28.951898, 0.000000, 0.000030, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(2977, -1449.208251, 2627.436767, 29.564228, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(964, -1449.390380, 2619.237792, 29.666856, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1449.283447, 2609.154052, 34.036560, 0.000007, -0.000000, 89.999900, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1449.283447, 2627.745361, 34.036560, 0.000000, -0.000007, 179.999832, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18260, -1449.367431, 2619.724365, 34.118003, -0.000007, 0.000000, -90.000053, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1448.519287, 2604.941406, 30.163858, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1449.229980, 2603.570312, 33.013889, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1449.229980, 2604.830566, 33.013889, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1449.259277, 2606.310058, 33.013889, 0.000005, -0.000005, 134.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1448.519287, 2623.540527, 30.163858, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1449.900634, 2632.460693, 33.013885, 0.000005, 0.000005, 44.999938, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1448.931396, 2631.490966, 33.013885, 0.000005, 0.000005, 44.999938, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1448.535400, 2632.920410, 33.013885, 0.000005, 0.000005, 44.999938, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1449.285888, 2636.436279, 34.026573, -0.000007, 0.000000, -90.000053, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1448.395751, 2617.198730, 33.013889, 0.000005, -0.000005, 134.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1450.038818, 2640.390869, 28.703857, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1448.718994, 2640.390869, 28.703857, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1448.718994, 2641.701171, 28.703857, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1450.138916, 2641.701171, 28.703857, 0.000000, 0.000007, -0.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1438.549316, 2640.515869, 28.951898, -0.000007, 0.000007, 89.999885, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1438.549316, 2635.834960, 28.951898, 0.000007, 0.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1438.549316, 2631.283935, 28.951898, -0.000015, 0.000007, 89.999916, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1438.549316, 2626.603027, 28.951898, 0.000007, 0.000015, 179.999755, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1438.549316, 2631.283935, 30.461893, 0.000000, 0.000007, 179.999771, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1438.549316, 2622.006103, 28.951898, -0.000022, 0.000007, 89.999931, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1438.549316, 2617.325195, 28.951898, 0.000007, 0.000022, 179.999755, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1438.549316, 2612.778808, 28.951898, -0.000030, 0.000007, 90.000038, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1438.549316, 2608.097900, 28.951898, 0.000007, 0.000030, 179.999755, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(2977, -1438.495605, 2617.834716, 29.564228, 0.000007, 0.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(964, -1438.313476, 2626.033691, 29.666856, 0.000007, 0.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1438.420410, 2636.117431, 34.036560, 0.000007, -0.000007, -90.000114, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1438.420410, 2617.526123, 34.036560, -0.000007, -0.000007, -0.000160, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18260, -1438.336425, 2625.547119, 34.118003, -0.000007, 0.000007, 89.999885, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1439.184570, 2640.330078, 30.163858, 0.000007, 0.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1438.473876, 2641.701171, 33.013889, 0.000007, 0.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1438.473876, 2640.440917, 33.013889, 0.000007, 0.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1438.444580, 2638.961425, 33.013889, 0.000000, -0.000010, -45.000034, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1439.184570, 2621.730957, 30.163858, 0.000007, 0.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1437.803222, 2612.810791, 33.013885, 0.000010, 0.000000, -135.000091, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1438.772460, 2613.780517, 33.013885, 0.000010, 0.000000, -135.000091, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1439.168457, 2612.351074, 33.013885, 0.000010, 0.000000, -135.000091, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1438.417968, 2608.835205, 34.026573, -0.000007, 0.000007, 89.999885, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1439.308105, 2628.072753, 33.013889, 0.000000, -0.000010, -45.000034, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1437.665039, 2604.880615, 28.703857, 0.000007, 0.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1438.984863, 2604.880615, 28.703857, 0.000007, 0.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1438.984863, 2603.570312, 28.703857, 0.000007, 0.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1437.564941, 2603.570312, 28.703857, 0.000007, 0.000007, 179.999755, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1428.524047, 2604.755615, 28.951898, -0.000015, 0.000000, -89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1428.524047, 2609.436523, 28.951898, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1428.524047, 2613.987548, 28.951898, -0.000022, 0.000000, -89.999931, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1428.524047, 2618.668457, 28.951898, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1428.524047, 2613.987548, 30.461893, -0.000007, 0.000015, 0.000022, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1428.524047, 2623.265380, 28.951898, -0.000030, 0.000000, -89.999908, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1428.524047, 2627.946289, 28.951898, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1428.524047, 2632.492675, 28.951898, -0.000038, 0.000000, -89.999885, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1428.524047, 2637.173583, 28.951898, 0.000000, 0.000038, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(2977, -1428.577880, 2627.436767, 29.564228, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(964, -1428.760009, 2619.237792, 29.666856, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1428.652954, 2609.154052, 34.036560, 0.000015, 0.000000, 89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1428.652954, 2627.745361, 34.036560, 0.000000, -0.000015, 179.999908, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18260, -1428.736816, 2619.724365, 34.118003, -0.000015, 0.000000, -89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1427.888916, 2604.941406, 30.163858, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1428.599487, 2603.570312, 33.013889, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1428.599487, 2604.830566, 33.013889, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1428.628784, 2606.310058, 33.013889, 0.000010, -0.000010, 135.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1427.888916, 2623.540527, 30.163858, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1429.270019, 2632.460693, 33.013885, 0.000010, 0.000010, 44.999984, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1428.300781, 2631.490966, 33.013885, 0.000010, 0.000010, 44.999984, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1427.905029, 2632.920410, 33.013885, 0.000010, 0.000010, 44.999984, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1428.655273, 2636.436279, 34.026573, -0.000015, 0.000000, -89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1427.765258, 2617.198730, 33.013889, 0.000010, -0.000010, 135.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1429.408447, 2640.390869, 28.703857, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1428.088378, 2640.390869, 28.703857, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1428.088378, 2641.701171, 28.703857, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1429.508300, 2641.701171, 28.703857, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1418.409545, 2640.515869, 28.951898, 0.000000, 0.000007, 89.999938, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1418.409545, 2635.834960, 28.951898, 0.000007, 0.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1418.409545, 2631.283935, 28.951898, -0.000007, 0.000007, 89.999961, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1418.409545, 2626.603027, 28.951898, 0.000007, 0.000007, 179.999832, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1418.409545, 2631.283935, 30.461893, 0.000000, 0.000000, 179.999847, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1418.409545, 2622.006103, 28.951898, -0.000015, 0.000007, 89.999984, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1418.409545, 2617.325195, 28.951898, 0.000007, 0.000015, 179.999832, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1418.409545, 2612.778808, 28.951898, -0.000022, 0.000007, 90.000007, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3577, -1418.409545, 2608.097900, 28.951898, 0.000007, 0.000022, 179.999832, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(2977, -1418.355834, 2617.834716, 29.564228, 0.000007, 0.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(964, -1418.173706, 2626.033691, 29.666856, 0.000007, 0.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1418.280639, 2636.117431, 34.036560, 0.000000, -0.000007, -90.000015, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1418.280639, 2617.526123, 34.036560, -0.000007, -0.000000, -0.000038, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18260, -1418.196655, 2625.547119, 34.118003, 0.000000, 0.000007, 89.999938, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1419.044799, 2640.330078, 30.163858, 0.000007, 0.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1418.334106, 2641.701171, 33.013889, 0.000007, 0.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1418.334106, 2640.440917, 33.013889, 0.000007, 0.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1418.304809, 2638.961425, 33.013889, -0.000005, -0.000005, -45.000003, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1419.044799, 2621.730957, 30.163858, 0.000007, 0.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1417.663452, 2612.810791, 33.013885, 0.000005, -0.000005, -135.000045, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1418.632690, 2613.780517, 33.013885, 0.000005, -0.000005, -135.000045, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1419.028686, 2612.351074, 33.013885, 0.000005, -0.000005, -135.000045, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3576, -1418.278198, 2608.835205, 34.026573, 0.000000, 0.000007, 89.999938, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1419.168334, 2628.072753, 33.013889, -0.000005, -0.000005, -45.000003, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1417.525268, 2604.880615, 28.703857, 0.000007, 0.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1418.845092, 2604.880615, 28.703857, 0.000007, 0.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1418.845092, 2603.570312, 28.703857, 0.000007, 0.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1417.425170, 2603.570312, 28.703857, 0.000007, 0.000000, 179.999832, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1508.576049, 2604.512207, 43.115230, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1491.126464, 2604.512207, 43.115230, 0.000000, 0.000007, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1473.687255, 2604.512207, 43.115230, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1456.237670, 2604.512207, 43.115230, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1438.758178, 2604.512207, 43.115230, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1421.308593, 2604.512207, 43.115230, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1508.576049, 2615.383789, 43.115230, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1491.126464, 2615.383789, 43.115230, 0.000000, 0.000015, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1473.687255, 2615.383789, 43.115230, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1456.237670, 2615.383789, 43.115230, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1438.758178, 2615.383789, 43.115230, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1421.308593, 2615.383789, 43.115230, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1508.576049, 2625.254394, 43.115230, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1491.126464, 2625.254394, 43.115230, 0.000000, 0.000022, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1473.687255, 2625.254394, 43.115230, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1456.237670, 2625.254394, 43.115230, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1438.758178, 2625.254394, 43.115230, 0.000000, 0.000038, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1421.308593, 2625.254394, 43.115230, 0.000000, 0.000038, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1508.576049, 2635.544433, 43.115230, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1491.126464, 2635.544433, 43.115230, 0.000000, 0.000030, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1473.687255, 2635.544433, 43.115230, 0.000000, 0.000038, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1456.237670, 2635.544433, 43.115230, 0.000000, 0.000038, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1438.758178, 2635.544433, 43.115230, 0.000000, 0.000045, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(16644, -1421.308593, 2635.544433, 43.115230, 0.000000, 0.000045, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1499.561279, 2641.765136, 44.705539, 0.000007, 0.000000, 89.999977, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1476.090942, 2641.765136, 44.705539, 0.000007, 0.000000, 89.999977, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1454.658569, 2641.765136, 44.705539, 0.000015, 0.000000, 89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1431.188232, 2641.765136, 44.705539, 0.000015, 0.000000, 89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1499.561279, 2629.683593, 44.705539, 0.000015, 0.000000, 89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1476.090942, 2629.683593, 44.705539, 0.000015, 0.000000, 89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1454.658569, 2629.683593, 44.705539, 0.000022, 0.000000, 89.999931, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1431.188232, 2629.683593, 44.705539, 0.000022, 0.000000, 89.999931, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1499.561279, 2619.602539, 44.705539, 0.000022, 0.000000, 89.999931, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1476.090942, 2619.602539, 44.705539, 0.000022, 0.000000, 89.999931, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1454.658569, 2619.602539, 44.705539, 0.000030, 0.000000, 89.999908, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1431.188232, 2619.602539, 44.705539, 0.000030, 0.000000, 89.999908, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1499.561279, 2609.492187, 44.705539, 0.000030, 0.000000, 89.999908, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1476.090942, 2609.492187, 44.705539, 0.000030, 0.000000, 89.999908, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1454.658569, 2609.492187, 44.705539, 0.000038, 0.000000, 89.999885, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1431.188232, 2609.492187, 44.705539, 0.000038, 0.000000, 89.999885, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1499.561279, 2600.262939, 44.705539, 0.000038, 0.000000, 89.999885, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1476.090942, 2600.262939, 44.705539, 0.000038, 0.000000, 89.999885, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1454.658569, 2600.262939, 44.705539, 0.000045, 0.000000, 89.999862, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, -1431.188232, 2600.262939, 44.705539, 0.000045, 0.000000, 89.999862, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18652, -1496.875244, 2627.406494, 45.801670, 0.000000, 0.000000, 90.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(18652, -1445.344970, 2627.406494, 45.901657, 0.000000, 0.000000, 90.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(19459, -1445.874267, 2597.482910, 29.618740, 0.000000, 0.000000, 90.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(19459, -1455.503784, 2597.482910, 29.618740, 0.000000, 0.000000, 90.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(19459, -1476.655639, 2597.482910, 29.618740, 0.000007, 0.000000, 89.999977, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(19459, -1486.285156, 2597.482910, 29.618740, 0.000007, 0.000000, 89.999977, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(19459, -1445.874267, 2596.602050, 29.618740, 0.000007, 0.000000, 89.999977, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(19459, -1455.503784, 2596.602050, 29.618740, 0.000007, 0.000000, 89.999977, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(19459, -1476.655639, 2596.602050, 29.618740, 0.000015, 0.000000, 89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(19459, -1486.285156, 2596.602050, 29.618740, 0.000015, 0.000000, 89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3761, -1479.463500, 2595.698242, 29.878253, 0.000000, 0.000000, 270.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(3761, -1452.082641, 2595.698242, 29.878253, 0.000000, 0.000000, 270.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(929, -1458.893798, 2581.315673, 28.792253, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(929, -1445.543090, 2581.315673, 28.792253, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(929, -1469.513427, 2581.315673, 28.792253, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(929, -1485.334106, 2581.315673, 28.792253, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(19459, -1445.874267, 2597.482910, 43.358741, 0.000007, 0.000000, 89.999977, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(19459, -1455.503784, 2597.482910, 43.358741, 0.000007, 0.000000, 89.999977, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(19459, -1476.655639, 2597.482910, 43.358741, 0.000015, 0.000000, 89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(19459, -1486.285156, 2597.482910, 43.358741, 0.000015, 0.000000, 89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(19459, -1445.874267, 2596.592041, 43.358741, 0.000015, 0.000000, 89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(19459, -1455.503784, 2596.592041, 43.358741, 0.000015, 0.000000, 89.999954, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(19459, -1476.655639, 2596.592041, 43.358741, 0.000022, 0.000000, 89.999931, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(19459, -1486.285156, 2596.592041, 43.358741, 0.000022, 0.000000, 89.999931, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(2008, -1487.226196, 2596.010498, 27.891164, 0.000000, 0.000000, 0.000000, 51, 51, -1, 200.00, 200.00); 
    CreateDynamicObject(2356, -1486.567016, 2594.914306, 27.887693, 0.000000, 0.000000, -22.299997, 51, 51, -1, 200.00, 200.00);
}