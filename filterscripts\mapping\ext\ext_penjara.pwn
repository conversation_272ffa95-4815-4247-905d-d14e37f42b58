RemovePenjaraBuilding(playerid)
{
    RemoveBuildingForPlayer(playerid, 3383, 289.859, 1830.699, 7.039, 0.250);
    RemoveBuildingForPlayer(playerid, 3393, 291.726, 1835.439, 7.031, 0.250);
    RemoveBuildingForPlayer(playerid, 3389, 284.882, 1831.729, 7.039, 0.250);
    RemoveBuildingForPlayer(playerid, 3387, 284.882, 1834.479, 7.039, 0.250);
}

CreatePenjaraExt()
{
    new STREAMER_TAG_OBJECT: PNJSRXT;
    PNJSRXT = CreateDynamicObject(18981, 316.016723, 1824.340454, 6.506443, 0.000000, 90.000000, 0.000000, 69, 0, -1, 200.00, 200.00);
    SetDynamicObjectMaterial(PNJSRXT, 0, 11100, "bendytunnel_sfse", "Bow_sub_walltiles", 0x00000000);
    PNJSRXT = CreateDynamicObject(18766, 303.262451, 1831.823852, 6.509909, 90.000000, 360.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 11100, "bendytunnel_sfse", "Bow_sub_walltiles", 0x00000000);
    PNJSRXT = CreateDynamicObject(18766, 303.272460, 1829.963500, 6.519908, 90.000000, 360.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 11100, "bendytunnel_sfse", "Bow_sub_walltiles", 0x00000000);
    PNJSRXT = CreateDynamicObject(19383, 297.696594, 1821.720092, 8.567426, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 14881, "gf5", "mp_jail_wall", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 294.482299, 1821.717285, 8.564182, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 14881, "gf5", "mp_jail_wall", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 300.882019, 1821.717285, 8.564182, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 14881, "gf5", "mp_jail_wall", 0x00000000);
    PNJSRXT = CreateDynamicObject(18766, 277.398406, 1822.380737, 5.469822, 0.000000, 90.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 2951, "a51_labdoor", "washapartwall1_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19383, 322.466491, 1830.273925, 8.576457, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 14842, "genintintpolicea", "copcell_bars", 0x00000000);
    PNJSRXT = CreateDynamicObject(1497, 321.698516, 1830.284301, 6.816088, 0.000000, 0.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 1, 14842, "genintintpolicea", "copcell_bars", 0x00000000);
    PNJSRXT = CreateDynamicObject(19383, 319.186584, 1830.273925, 8.576457, 0.000007, 0.000000, 89.999977, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 14842, "genintintpolicea", "copcell_bars", 0x00000000);
    PNJSRXT = CreateDynamicObject(1497, 318.418609, 1830.284301, 6.816088, 0.000000, 0.000007, 0.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 1, 14842, "genintintpolicea", "copcell_bars", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 317.612030, 1828.106811, 8.564182, 0.000000, -0.000022, 179.999862, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 320.722198, 1828.106811, 8.564182, 0.000000, -0.000014, 179.999908, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 320.891937, 1828.106811, 8.564182, 0.000000, -0.000014, 179.999908, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 324.002105, 1828.106811, 8.564182, 0.000000, -0.000007, 179.999954, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 317.622039, 1828.788696, 8.564182, 0.000000, -0.000037, 179.999771, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 320.732208, 1828.788696, 8.564182, 0.000000, -0.000029, 179.999816, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19383, 315.907135, 1830.273925, 8.576457, 0.000014, 0.000000, 89.999954, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 14842, "genintintpolicea", "copcell_bars", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 320.901947, 1828.788696, 8.564182, 0.000000, -0.000029, 179.999816, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 324.012115, 1828.788696, 8.564182, 0.000000, -0.000022, 179.999862, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(1497, 315.139160, 1830.284301, 6.816088, 0.000000, 0.000014, 0.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 1, 14842, "genintintpolicea", "copcell_bars", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 314.332580, 1828.106811, 8.564182, 0.000000, -0.000029, 179.999816, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 317.442749, 1828.106811, 8.564182, 0.000000, -0.000022, 179.999862, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 314.342590, 1828.788696, 8.564182, 0.000000, -0.000045, 179.999725, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 317.452758, 1828.788696, 8.564182, 0.000000, -0.000037, 179.999771, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19383, 312.617156, 1830.273925, 8.576457, 0.000022, 0.000000, 89.999931, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 14842, "genintintpolicea", "copcell_bars", 0x00000000);
    PNJSRXT = CreateDynamicObject(1497, 311.849182, 1830.284301, 6.816088, 0.000000, 0.000022, 0.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 1, 14842, "genintintpolicea", "copcell_bars", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 311.042602, 1828.106811, 8.564182, 0.000000, -0.000037, 179.999771, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 314.152770, 1828.106811, 8.564182, 0.000000, -0.000029, 179.999816, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 311.052612, 1828.788696, 8.564182, 0.000000, -0.000052, 179.999679, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 314.162780, 1828.788696, 8.564182, 0.000000, -0.000045, 179.999725, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19383, 309.337219, 1830.273925, 8.576457, 0.000029, 0.000000, 89.999908, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 14842, "genintintpolicea", "copcell_bars", 0x00000000);
    PNJSRXT = CreateDynamicObject(1497, 308.569244, 1830.284301, 6.816088, 0.000000, 0.000029, 0.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 1, 14842, "genintintpolicea", "copcell_bars", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 307.762664, 1828.106811, 8.564182, 0.000000, -0.000045, 179.999725, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 310.872833, 1828.106811, 8.564182, 0.000000, -0.000037, 179.999771, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 307.772674, 1828.788696, 8.564182, 0.000000, -0.000060, 179.999633, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 310.882843, 1828.788696, 8.564182, 0.000000, -0.000052, 179.999679, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19383, 306.067291, 1830.273925, 8.576457, 0.000037, 0.000000, 89.999885, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 14842, "genintintpolicea", "copcell_bars", 0x00000000);
    PNJSRXT = CreateDynamicObject(1497, 305.299316, 1830.284301, 6.816088, 0.000000, 0.000037, 0.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 1, 14842, "genintintpolicea", "copcell_bars", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 304.492736, 1828.106811, 8.564182, 0.000000, -0.000052, 179.999679, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 307.602905, 1828.106811, 8.564182, 0.000000, -0.000045, 179.999725, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 304.502746, 1828.788696, 8.564182, 0.000000, -0.000068, 179.999588, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 307.612915, 1828.788696, 8.564182, 0.000000, -0.000060, 179.999633, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 5154, "dkcargoshp_las2", "mp_cellwall_256", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 291.783172, 1828.728637, 8.624193, 0.000000, -0.000068, 179.999588, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 10101, "2notherbuildsfe", "sl_vicwall02", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 291.783172, 1831.938964, 8.634199, 0.000000, -0.000068, 179.999588, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 10101, "2notherbuildsfe", "sl_vicwall02", 0x00000000);
    PNJSRXT = CreateDynamicObject(19353, 291.783172, 1835.149414, 8.634199, 0.000000, -0.000068, 179.999588, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 10101, "2notherbuildsfe", "sl_vicwall02", 0x00000000);
    PNJSRXT = CreateDynamicObject(1812, 292.383300, 1833.661865, 7.036452, 0.000000, -0.000037, 359.999755, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    PNJSRXT = CreateDynamicObject(1812, 294.283355, 1833.673095, 7.036452, 0.000000, -0.000037, 359.999755, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    PNJSRXT = CreateDynamicObject(2339, 294.487762, 1828.597290, 7.033366, 0.000000, 0.000000, 630.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    SetDynamicObjectMaterial(PNJSRXT, 2, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    PNJSRXT = CreateDynamicObject(1806, 293.150604, 1830.910156, 7.022055, 0.000000, 0.000000, 226.699981, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    PNJSRXT = CreateDynamicObject(1498, 292.508666, 1826.667480, 6.908482, 0.000000, 0.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    PNJSRXT = CreateDynamicObject(1498, 292.508666, 1827.097900, 6.908482, 0.000000, 0.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    PNJSRXT = CreateDynamicObject(19929, 285.796112, 1834.723754, 7.020601, 0.000000, 0.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    SetDynamicObjectMaterial(PNJSRXT, 1, 18021, "genintintfastd", "tile_test3red", 0x00000000);
    PNJSRXT = CreateDynamicObject(2362, 283.189239, 1833.520629, 7.884151, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 1, 10056, "bigoldbuild_sfe", "cluckbell01_law", 0x00000000);
    SetDynamicObjectMaterial(PNJSRXT, 2, 10056, "bigoldbuild_sfe", "cluckbell01_law", 0x00000000);
    PNJSRXT = CreateDynamicObject(2362, 283.189239, 1832.960205, 7.884151, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 1, 10056, "bigoldbuild_sfe", "cluckbell01_law", 0x00000000);
    SetDynamicObjectMaterial(PNJSRXT, 2, 10056, "bigoldbuild_sfe", "cluckbell01_law", 0x00000000);
    PNJSRXT = CreateDynamicObject(2362, 283.189239, 1832.960205, 8.534151, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 1, 10056, "bigoldbuild_sfe", "cluckbell01_law", 0x00000000);
    SetDynamicObjectMaterial(PNJSRXT, 2, 10056, "bigoldbuild_sfe", "cluckbell01_law", 0x00000000);
    PNJSRXT = CreateDynamicObject(2362, 283.189239, 1833.520751, 8.534151, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 1, 10056, "bigoldbuild_sfe", "cluckbell01_law", 0x00000000);
    SetDynamicObjectMaterial(PNJSRXT, 2, 10056, "bigoldbuild_sfe", "cluckbell01_law", 0x00000000);
    PNJSRXT = CreateDynamicObject(1498, 286.998657, 1837.178222, 6.898481, 0.000000, 0.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    PNJSRXT = CreateDynamicObject(1498, 286.998657, 1836.169067, 7.028484, 0.000000, 0.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    PNJSRXT = CreateDynamicObject(2094, 243.002838, 1858.975219, 13.056597, 0.000000, 0.000000, 180.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 3922, "bistro", "Cabinet", 0x00000000);
    PNJSRXT = CreateDynamicObject(19383, 238.463714, 1872.392944, 11.829554, 0.000000, 0.000000, 180.000000, -1, -1, -1, 200.00, 200.00); // pintu terluar ext
    SetDynamicObjectMaterial(PNJSRXT, 0, 16640, "a51", "Metal3_128", 0x00000000);
    PNJSRXT = CreateDynamicObject(19383, 248.853744, 1841.901245, 9.449536, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 16640, "a51", "Metal3_128", 0x00000000);
    PNJSRXT = CreateDynamicObject(19383, 247.843826, 1805.790161, 8.259522, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 16640, "a51", "Metal3_128", 0x00000000);
    PNJSRXT = CreateDynamicObject(19383, 238.493927, 1803.398681, 8.119520, 0.000000, 0.000000, 180.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 16640, "a51", "Metal3_128", 0x00000000);
    PNJSRXT = CreateDynamicObject(19383, 233.473968, 1822.760253, 8.109519, 0.000000, 0.000000, 180.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 16640, "a51", "Metal3_128", 0x00000000);
    PNJSRXT = CreateDynamicObject(19383, 239.573623, 1862.702392, 14.819567, 0.000000, 0.000000, 270.000000, 69, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 16640, "a51", "Metal3_128", 0x00000000);
    PNJSRXT = CreateDynamicObject(19379, 268.876312, 1884.081420, 16.537908, 0.000000, 90.000000, 0.000000, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(PNJSRXT, 0, 11013, "crackdrive_sfse", "ws_asphalt2", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(18981, 316.016723, 1826.021240, 3.296437, 0.000000, 180.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18981, 312.857025, 1826.021240, 3.296437, 0.000000, 180.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18981, 314.017395, 1824.340454, 6.496444, 0.000000, 90.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18981, 329.046447, 1824.770263, 3.296437, 0.000000, 180.000000, 180.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18981, 316.016723, 1826.291259, 11.266442, 0.000000, 90.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18981, 312.856964, 1826.281250, 11.256442, 0.000000, 90.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1812, 320.132995, 1829.020874, 7.006443, 0.000000, -0.000007, 179.999954, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2514, 318.108581, 1827.036254, 7.006443, 0.000000, -0.000007, 179.999954, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 318.219787, 1828.656860, 7.006443, 0.000007, 0.000000, 89.999977, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1812, 323.412902, 1829.020874, 7.006443, 0.000000, 0.000000, 180.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2514, 321.388488, 1827.036254, 7.006443, 0.000000, 0.000000, 180.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 321.499694, 1828.656860, 7.006443, 0.000000, 0.000000, 450.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1812, 316.853546, 1829.020874, 7.006443, 0.000000, -0.000014, 179.999908, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2514, 314.829132, 1827.036254, 7.006443, 0.000000, -0.000014, 179.999908, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 314.940338, 1828.656860, 7.006443, 0.000014, 0.000000, 89.999954, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1812, 313.563568, 1829.020874, 7.006443, 0.000000, -0.000022, 179.999862, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2514, 311.539154, 1827.036254, 7.006443, 0.000000, -0.000022, 179.999862, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 311.650360, 1828.656860, 7.006443, 0.000022, 0.000000, 89.999931, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1812, 310.283630, 1829.020874, 7.006443, 0.000000, -0.000029, 179.999816, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2514, 308.259216, 1827.036254, 7.006443, 0.000000, -0.000029, 179.999816, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 308.370422, 1828.656860, 7.006443, 0.000029, 0.000000, 89.999908, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1812, 307.013702, 1829.020874, 7.006443, 0.000000, -0.000037, 179.999771, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2514, 304.989288, 1827.036254, 7.006443, 0.000000, -0.000037, 179.999771, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2518, 305.100494, 1828.656860, 7.006443, 0.000037, 0.000000, 89.999885, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3029, 311.847564, 1837.239501, 6.828125, 0.000000, 0.000000, 270.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3029, 315.347381, 1837.319580, 6.828125, 0.000000, 0.000000, 450.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3029, 311.847564, 1836.779174, 6.828125, -0.000007, 0.000000, -89.999977, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3029, 315.347381, 1836.859252, 6.828125, 0.000007, 0.000000, 89.999977, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2855, 323.025482, 1828.465942, 7.006691, 0.000000, 0.000000, -126.400001, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 325.464355, 1832.221923, 10.381564, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 318.024230, 1832.221923, 10.381564, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 310.344207, 1832.221923, 10.381564, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, 302.734130, 1832.221923, 10.381564, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(932, 292.406005, 1832.574951, 7.024925, 0.000000, 0.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(932, 292.385986, 1832.355102, 7.024925, 0.000000, 0.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(932, 292.365966, 1832.114868, 7.024925, 0.000000, 0.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2165, 294.266815, 1830.860229, 7.033627, 0.000000, 0.000000, 270.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19379, 289.600341, 1831.473510, 6.946639, 360.000000, 90.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19379, 285.280181, 1831.473510, 6.936639, 360.000000, 90.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19379, 289.600341, 1831.473510, 10.446663, -0.000000, 90.000007, 0.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19379, 285.280181, 1831.473510, 10.436663, -0.000000, 90.000007, 0.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2257, 291.888549, 1830.145629, 8.721893, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19996, 291.288055, 1829.599487, 7.004542, -0.000007, -0.000000, -89.999977, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19922, 289.811950, 1830.641845, 7.047789, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19996, 291.288055, 1830.580078, 7.004542, -0.000007, -0.000000, -89.999977, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19996, 291.288055, 1831.590942, 7.004542, -0.000007, -0.000000, -89.999977, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19996, 288.217895, 1831.590942, 7.004542, -0.000007, 0.000007, 89.999961, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19996, 288.217895, 1830.610351, 7.004542, -0.000007, 0.000007, 89.999961, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19996, 288.217895, 1829.599487, 7.004542, -0.000007, 0.000007, 89.999961, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2131, 283.941741, 1834.292236, 7.032238, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16779, 287.523193, 1831.635864, 10.462679, 0.000000, 0.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2771, 285.741149, 1833.620605, 8.149239, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2275, 283.944915, 1829.625122, 8.480129, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2267, 291.663421, 1830.530517, 8.899135, 0.000000, 0.000000, 270.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19379, 283.339996, 1831.473510, 6.936639, 360.000000, 180.000000, 0.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 249.401641, 1857.704467, 13.042211, 0.000000, 0.000000, 180.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 248.731567, 1857.704467, 13.042211, 0.000000, 0.000000, 180.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 248.071517, 1857.704467, 13.042211, 0.000000, 0.000000, 180.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 247.411560, 1857.704467, 13.042211, 0.000000, -0.000007, 179.999954, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 246.741485, 1857.704467, 13.042211, 0.000000, -0.000007, 179.999954, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11729, 246.081436, 1857.704467, 13.042211, 0.000000, -0.000007, 179.999954, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2951, 256.281158, 1835.609741, 3.229019, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2951, 256.281158, 1845.909545, 7.289016, 0.000000, 0.000000, 90.000000, 69, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3109, 238.488983, 1871.625488, 11.448797, 0.000000, 0.000000, 0.000000, -1, -1, -1, 200.00, 200.00);
}