include(FindPackageHandleStandardArgs)

find_package(SAMPGDK QUIET CONFIG NAMES SAMPGDK)

set(SAMPGDK_INCLUDE_DIRS ${SAMPGDK_INCLUDE_DIR})
set(SAMPGDK_LIBRARY_DIRS ${SAMPGDK_LIBRARY_DIR})

mark_as_advanced(
  SAMPGDK_VERSION
  SAMPGDK_INCLUDE_DIR
  SAMPGDK_LIBRARY_DIR
)

find_package_handle_standard_args(SAMPGDK
  REQUIRED_VARS SAMPGDK_INCLUDE_DIRS SAMPGDK_LIBRARY_DIRS
  VERSION_VAR SAMPGDK_VERSION
)
