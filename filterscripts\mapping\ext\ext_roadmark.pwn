CreateRoadMarkExt()
{
    new STREAMER_TAG_OBJECT:rdmxtark;
    rdmxtark = CreateDynamicObject(19861, 663.163940, -1405.499023, 12.399533, -90.099975, -0.001457, -90.001251, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SLOW", 130, "Arial", 180, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19861, 700.434265, -1405.499023, 12.364499, -90.099975, -0.001457, -90.001251, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SLOW", 130, "Arial", 180, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19861, 731.364257, -1395.527709, 12.408467, -89.699981, 0.598541, 89.998748, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SLOW", 130, "Arial", 180, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19861, 766.532653, -1395.895629, 12.414299, -89.299987, 0.598541, 89.998748, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SLOW", 130, "Arial", 180, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 188.559829, -1224.620239, 18.366544, -86.800025, 0.000000, -59.699981, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SLOW", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 221.424224, -1207.977661, 20.266305, -87.000030, 0.000000, -63.200035, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SLOW", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 186.013565, -1220.260009, 18.336589, -87.000022, 0.000000, -59.699981, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SLOW", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 219.193176, -1203.558227, 20.246332, -87.000030, 0.000000, -63.200035, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SLOW", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 254.328842, -1192.688964, 22.127178, -87.300025, 0.000000, -68.300025, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SIGNAL", 130, "Arial", 120, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 252.432495, -1187.922363, 22.117189, -87.300025, 0.000000, -68.300025, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SIGNAL", 130, "Arial", 120, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 294.790588, -1171.541137, 24.348995, -87.200027, 0.000000, -69.500030, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "AHEAD", 130, "Arial", 120, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 296.564605, -1176.290771, 24.378961, -87.200027, 0.000000, -69.500030, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "AHEAD", 130, "Arial", 120, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 1951.800415, -2054.954833, 12.364502, 630.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "STOP", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 1971.810913, -2107.475341, 12.354504, 630.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "STOP", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 2508.622314, -2302.116210, 23.335083, -88.500015, 0.000000, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SLOW", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 2479.837890, -2273.331787, 24.020866, -89.300003, -0.099999, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SLOW", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 2458.687255, -2252.057861, 24.057420, -89.699996, -0.099999, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SIGNAL", 130, "Arial", 130, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 2428.456298, -2221.931640, 23.950891, -90.599983, -0.099999, 45.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "AHEAD", 130, "Arial", 130, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 2477.944580, -2278.618408, 23.985136, -90.399986, -0.099999, 225.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SLOW", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 2506.769775, -2307.346923, 23.281002, -90.999977, -0.099999, 225.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SLOW", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 2456.696289, -2257.442382, 24.034557, -90.399986, -0.099999, 225.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SLOW", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 2426.597412, -2227.306396, 23.981901, -89.500000, -0.099999, 225.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SLOW", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 2394.889404, -2195.566894, 23.110336, -87.800025, -0.099999, 225.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "SLOW", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 2190.741943, -1896.984008, 12.628518, -87.600051, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "RXR", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 2210.251708, -1892.334228, 12.431399, -89.300025, 0.000000, 810.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "RXR", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 2248.124267, -1653.848144, 14.282239, -89.700019, 0.000000, 74.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "RXR", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 2164.884765, -1636.660156, 13.201086, -87.300056, 0.000000, -108.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "RXR", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 2250.298095, -1486.453125, 22.136085, -91.600006, 0.000000, -91.300033, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "RXR", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 2297.892822, -1481.491210, 22.186519, -91.500007, 0.000000, 88.699966, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "RXR", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 1811.467041, -1834.860351, 12.385274, 270.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "STOP", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 1811.467529, -1857.640258, 12.395276, 270.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "STOP", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 1811.467529, -1734.880493, 12.365275, 270.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "STOP", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    rdmxtark = CreateDynamicObject(19864, 1964.237915, -1762.320312, 12.375273, 270.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(rdmxtark, 0, "STOP", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);

    //RODEO REAL LA VIBES
    rdmxtark = CreateDynamicObject(18980, 535.439758, -1553.468139, 14.226403, 88.200004, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(rdmxtark, 0, 14391, "dr_gsmix", "chromecabinet01side_128", 0x00000000);
    rdmxtark = CreateDynamicObject(18980, 535.439758, -1528.488647, 13.531406, 88.599998, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(rdmxtark, 0, 14391, "dr_gsmix", "chromecabinet01side_128", 0x00000000);
    rdmxtark = CreateDynamicObject(18980, 535.439758, -1503.477661, 13.167641, 89.699981, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(rdmxtark, 0, 14391, "dr_gsmix", "chromecabinet01side_128", 0x00000000);
    rdmxtark = CreateDynamicObject(18980, 534.260131, -1453.529907, 13.975275, 91.699951, 2.599998, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(rdmxtark, 0, 14391, "dr_gsmix", "chromecabinet01side_128", 0x00000000);
    rdmxtark = CreateDynamicObject(18980, 533.779052, -1443.817260, 14.303542, 91.699951, 3.099998, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(rdmxtark, 0, 14391, "dr_gsmix", "chromecabinet01side_128", 0x00000000);
    rdmxtark = CreateDynamicObject(18980, 535.429748, -1553.458129, 14.236403, 88.199943, 0.000241, -0.000241, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(rdmxtark, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    rdmxtark = CreateDynamicObject(18980, 535.429748, -1528.478637, 13.541405, 88.600021, 0.000311, -0.000311, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(rdmxtark, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    rdmxtark = CreateDynamicObject(18980, 535.429748, -1503.467651, 13.177641, 89.699981, 0.001457, -0.001457, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(rdmxtark, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    rdmxtark = CreateDynamicObject(18980, 535.419738, -1553.458129, 14.226403, 88.200004, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(rdmxtark, 0, 14391, "dr_gsmix", "chromecabinet01side_128", 0x00000000);
    rdmxtark = CreateDynamicObject(18980, 535.419738, -1528.478637, 13.531406, 88.599998, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(rdmxtark, 0, 14391, "dr_gsmix", "chromecabinet01side_128", 0x00000000);
    rdmxtark = CreateDynamicObject(18980, 535.419738, -1503.427612, 13.167431, 89.699981, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(rdmxtark, 0, 14391, "dr_gsmix", "chromecabinet01side_128", 0x00000000);
    rdmxtark = CreateDynamicObject(18980, 534.249633, -1453.519897, 13.985569, 91.699951, 2.599998, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(rdmxtark, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    rdmxtark = CreateDynamicObject(18980, 533.769042, -1443.817260, 14.313542, 91.699951, 3.099998, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(rdmxtark, 0, 4835, "airoads_las", "grassdry_128HV", 0x00000000);
    rdmxtark = CreateDynamicObject(18980, 534.240112, -1453.519897, 13.975275, 91.699951, 2.599998, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(rdmxtark, 0, 14391, "dr_gsmix", "chromecabinet01side_128", 0x00000000);
    rdmxtark = CreateDynamicObject(18980, 533.759033, -1443.807250, 14.303542, 91.699951, 3.099998, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(rdmxtark, 0, 14391, "dr_gsmix", "chromecabinet01side_128", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(18014, 534.730346, -1463.628173, 14.407769, 2.000000, 0.000000, 1.700000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14400, 535.409484, -1562.712402, 13.782574, -89.999992, 179.999984, -90.000015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14400, 535.409484, -1557.532104, 13.782574, -89.999992, 179.999984, -90.000015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14400, 535.409484, -1551.992553, 13.622571, -89.999992, 179.999984, -90.000015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14400, 535.409484, -1546.223266, 13.332568, -89.999992, 179.999984, -90.000015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18014, 535.430725, -1541.011596, 14.589197, -1.500000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2743, 535.425415, -1564.499755, 14.441078, -2.299998, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2743, 535.425415, -1564.391357, 17.118917, -2.299998, 360.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18014, 535.430725, -1536.373413, 14.467726, -1.500000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18014, 535.430725, -1531.735839, 14.346248, -1.500000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14400, 535.439514, -1526.762207, 12.632554, -89.999992, 90.098915, -179.901062, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14400, 535.439514, -1521.581909, 12.632554, -89.999992, 90.098915, -179.901062, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14400, 535.439514, -1516.042358, 12.472551, -89.999992, 90.098915, -179.901062, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14400, 535.439514, -1510.273071, 12.182545, -89.999992, 90.098915, -179.901062, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18014, 535.430725, -1505.869140, 13.918957, -0.299998, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18014, 535.430725, -1501.230468, 13.894672, -0.299998, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18014, 535.430725, -1496.591186, 13.870384, -0.299998, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18014, 534.563476, -1458.995849, 14.569692, 2.000000, 0.000000, 2.399998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18014, 534.329650, -1454.365356, 14.731619, 2.000000, 0.000000, 2.999999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14400, 533.886047, -1450.034057, 13.303400, -86.600326, 268.823089, 0.824020, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14400, 533.705322, -1444.866088, 13.610516, -86.600326, 268.823089, 0.824020, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14400, 533.511901, -1439.330200, 13.779211, -86.600326, 268.823089, 0.824020, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14400, 533.351745, -1434.744140, 13.761193, -86.600326, 268.823089, 180.824020, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19958, 534.179504, -1466.114624, 13.692388, 0.000000, 0.000000, -177.800109, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19958, 536.028808, -1490.891967, 13.137404, 0.000000, 0.000000, 3.299998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 523.068481, -1559.482543, 15.402426, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 523.068481, -1562.032226, 15.402426, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 523.068481, -1564.471923, 15.552431, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 522.378723, -1566.942382, 15.652429, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 520.318359, -1568.162841, 15.762431, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 517.908203, -1567.632324, 15.732432, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 504.675201, -1571.355468, 15.882429, 0.000000, 0.000000, -51.199989, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 506.398376, -1573.499389, 15.802427, 0.000000, 0.000000, -51.199989, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 507.023376, -1575.539428, 15.662418, 0.000000, 0.000000, -51.199989, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 504.916961, -1576.925292, 15.512413, 0.000000, 0.000000, -51.199989, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19979, 518.812133, -1568.714477, 17.044189, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19979, 523.212524, -1555.174682, 14.604169, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19979, 547.352844, -1555.174682, 14.574170, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(649, 535.437194, -1561.517700, 14.924883, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(649, 535.437194, -1556.318237, 14.784879, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(649, 535.437194, -1550.928100, 14.604877, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(649, 535.567321, -1544.997070, 14.374874, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(629, 522.832885, -1556.812988, 14.831847, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(629, 521.013061, -1548.072875, 14.651842, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(641, 535.672668, -1528.420532, 11.279520, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(641, 535.202392, -1520.309814, 11.119520, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(641, 535.202392, -1511.990966, 11.239520, 0.000000, 0.000000, 135.899978, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(629, 535.423522, -1491.862670, 13.421840, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(641, 534.593994, -1460.687377, 11.859499, 0.000000, 0.000000, 135.899978, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(649, 534.407226, -1456.276367, 14.304877, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(649, 534.117065, -1451.496337, 14.504874, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(649, 533.797058, -1446.676879, 14.634867, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(649, 533.837036, -1441.396484, 14.754863, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(649, 533.152465, -1435.337036, 15.044865, 0.000000, 0.000000, 67.200004, 0, 0, -1, 200.00, 200.00); 
}