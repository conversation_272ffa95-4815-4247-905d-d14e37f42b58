CreateUberExt()
{
    new STREAMER_TAG_OBJECT:uberasdt;
    uberasdt = CreateDynamicObject(18762, -2000.964599, -959.874755, 34.155937, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -937.614746, 34.155937, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -935.034729, 34.155937, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -957.294677, 34.155937, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2000.868652, -958.545288, 31.796810, 89.999992, 90.000030, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -959.328186, 34.103649, 89.999992, 180.000030, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -957.828186, 34.103649, 89.999992, 180.000030, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.375000, -957.818176, 32.023628, 0.000000, 90.000045, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -957.874694, 36.155967, 89.999992, 90.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.375000, -957.818176, 35.633609, 0.000000, 90.000045, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2000.868652, -956.145263, 31.796810, 89.999992, 90.000030, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -955.544677, 34.155937, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -956.788146, 34.103649, 89.999992, 180.000030, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -956.058166, 34.103649, 89.999992, 180.000030, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -957.874694, 31.545982, 89.999992, 90.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -953.004577, 34.155937, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2000.868652, -952.645141, 31.796810, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -955.038085, 34.103649, 89.999992, 180.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -953.538024, 34.103649, 89.999992, 180.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.375000, -953.528076, 32.023628, 0.000000, 90.000053, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -953.584533, 36.155967, 89.999992, 90.000030, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -953.584533, 31.545982, 89.999992, 90.000030, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.375000, -953.848205, 35.633609, 0.000000, 90.000045, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -952.024536, 34.155937, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2006.463867, -943.584594, 34.155937, 0.000000, 0.000051, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964355, -952.064758, 34.155937, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964355, -949.484741, 34.155937, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2000.868408, -950.735290, 31.796810, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372558, -951.518188, 34.103649, 89.999992, 180.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372558, -950.018188, 34.103649, 89.999992, 180.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.374755, -950.008178, 32.023628, 0.000000, 90.000061, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964355, -950.064697, 36.155967, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.374755, -950.008178, 35.633609, 0.000000, 90.000061, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2000.868408, -948.335266, 31.796810, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964355, -947.734741, 34.155937, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372558, -948.978210, 34.103649, 89.999992, 180.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372558, -948.248229, 34.103649, 89.999992, 180.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964355, -950.064697, 31.545982, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964355, -945.194580, 34.155937, 0.000000, 0.000051, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2000.868164, -944.875183, 31.796810, 89.999992, 90.000076, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372558, -947.228088, 34.103649, 89.999992, 180.000076, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372558, -945.728088, 34.103649, 89.999992, 180.000076, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.374755, -945.718078, 32.023628, 0.000000, 90.000068, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964355, -945.774597, 36.155967, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964355, -945.774597, 31.545982, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.374755, -946.038208, 35.633609, 0.000000, 90.000061, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964355, -944.214599, 34.155937, 0.000000, 0.000051, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964355, -943.584594, 34.155937, 0.000000, 0.000051, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2004.832275, -940.584594, 31.395938, 89.999992, 90.000053, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2005.261962, -940.584594, 31.535926, 89.999992, 90.000053, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2006.463867, -937.614562, 34.155937, 0.000000, 0.000067, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2000.868652, -936.285278, 31.796810, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(18980, -2002.792480, -938.154541, 34.155937, 0.000000, 0.000081, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 3922, "bistro", "mottled_grey_64HV", 0x00000000);
    uberasdt = CreateDynamicObject(18980, -2002.792480, -943.094543, 34.155937, 0.000000, 0.000081, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 3922, "bistro", "mottled_grey_64HV", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2002.926513, -942.328979, 41.745761, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2002.926513, -938.838928, 41.745761, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2002.926513, -942.328979, 32.115760, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2002.926513, -938.838928, 32.115760, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    uberasdt = CreateDynamicObject(18880, -2002.793945, -938.770874, 31.905796, 0.000029, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 3, 19297, "matlights", "invisible", 0x00000000);
    uberasdt = CreateDynamicObject(18880, -2002.793945, -938.770874, 36.795814, 0.000029, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 3, 19297, "matlights", "invisible", 0x00000000);
    uberasdt = CreateDynamicObject(18880, -2002.793945, -938.770874, 41.485813, 0.000029, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 3, 19297, "matlights", "invisible", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2002.889160, -940.578552, 33.695938, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 8391, "ballys01", "vgncorpdoor1_512", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2002.889160, -943.728576, 33.695938, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 8391, "ballys01", "vgncorpdoor1_512", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2002.889160, -937.408569, 33.695938, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 8391, "ballys01", "vgncorpdoor1_512", 0x00000000);
    uberasdt = CreateDynamicObject(18880, -2002.793945, -942.480957, 31.905796, -0.000029, -0.000004, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 3, 19297, "matlights", "invisible", 0x00000000);
    uberasdt = CreateDynamicObject(18880, -2002.793945, -942.480957, 36.795814, -0.000029, -0.000004, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 3, 19297, "matlights", "invisible", 0x00000000);
    uberasdt = CreateDynamicObject(18880, -2002.793945, -942.480957, 41.485813, -0.000029, -0.000004, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 3, 19297, "matlights", "invisible", 0x00000000);
    uberasdt = CreateDynamicObject(18880, -2002.793945, -938.590942, 35.495815, -0.000029, 89.999992, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 3, 19297, "matlights", "invisible", 0x00000000);
    uberasdt = CreateDynamicObject(18880, -2002.793945, -938.590942, 36.925788, -0.000029, 89.999992, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 3, 19297, "matlights", "invisible", 0x00000000);
    uberasdt = CreateDynamicObject(18880, -2002.793945, -938.590942, 40.245777, -0.000029, 89.999992, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 3, 19297, "matlights", "invisible", 0x00000000);
    uberasdt = CreateDynamicObject(18880, -2002.793945, -938.590942, 43.465774, -0.000029, 89.999992, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 3, 19297, "matlights", "invisible", 0x00000000);
    uberasdt = CreateDynamicObject(18880, -2002.793945, -938.590942, 46.535793, -0.000029, 89.999992, -89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 3, 19297, "matlights", "invisible", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -937.068176, 34.103649, 89.999992, 180.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18880, -2002.793945, -940.610900, 36.795814, -0.000014, -0.000044, -179.999710, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 3, 19297, "matlights", "invisible", 0x00000000);
    uberasdt = CreateDynamicObject(18880, -2002.793945, -940.610900, 41.485813, -0.000014, -0.000044, -179.999710, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 3, 19297, "matlights", "invisible", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -935.568176, 34.103649, 89.999992, 180.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.375000, -935.558227, 32.023628, 0.000000, 90.000053, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -935.614685, 36.155967, 89.999992, 90.000030, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.375000, -935.558227, 35.633609, 0.000000, 90.000053, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -935.614685, 31.545982, 89.999992, 90.000030, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -934.488220, 34.103649, 89.999992, 180.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -933.148193, 34.103649, 89.999992, 180.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -932.624694, 34.155937, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -929.814697, 31.545982, 89.999992, 90.000030, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.375000, -929.738220, 35.633609, 0.000000, 90.000053, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.375000, -929.698181, 32.023628, 0.000000, 90.000053, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -930.614685, 36.155967, 89.999992, 90.000030, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2000.868652, -932.785278, 31.796810, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2000.868652, -929.305297, 31.796810, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -932.098205, 34.103649, 89.999992, 180.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -931.278198, 34.103649, 89.999992, 180.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -930.754699, 34.155937, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -930.208190, 34.103649, 89.999992, 180.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -929.388183, 34.103649, 89.999992, 180.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -928.864685, 34.155937, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -927.894775, 34.155937, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -925.314697, 34.155937, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2000.868652, -926.565307, 31.796810, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -927.348205, 34.103649, 89.999992, 180.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -925.848205, 34.103649, 89.999992, 180.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.375000, -925.838195, 32.023628, 0.000000, 90.000061, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -925.894714, 36.155967, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.375000, -925.838195, 35.633609, 0.000000, 90.000061, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -925.894714, 31.545982, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -924.768188, 34.103649, 89.999992, 180.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -923.428161, 34.103649, 89.999992, 180.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -922.904724, 34.155937, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -920.094726, 31.545982, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.375000, -920.018188, 35.633609, 0.000000, 90.000061, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.375000, -919.978210, 32.023628, 0.000000, 90.000061, -0.000029, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -920.894714, 36.155967, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2000.868652, -923.065307, 31.796810, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2000.868652, -919.585327, 31.796810, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -922.378173, 34.103649, 89.999992, 180.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -921.558166, 34.103649, 89.999992, 180.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -921.034667, 34.155937, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -920.488159, 34.103649, 89.999992, 180.000076, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.372802, -919.668151, 34.103649, 89.999992, 180.000076, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.964599, -919.144714, 34.155937, 0.000000, 0.000044, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.957763, -917.972473, 34.155937, 0.000044, 0.000022, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2003.537841, -917.972473, 34.155937, 0.000044, 0.000022, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2002.287109, -917.876525, 31.796810, 89.999992, 178.299163, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.504394, -918.380676, 34.103649, 89.999992, 268.299163, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2003.004394, -918.380676, 34.103649, 89.999992, 268.299163, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2003.014404, -918.382873, 32.023628, 0.000044, 90.000038, 89.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2002.957763, -917.972473, 36.155967, 89.999992, 178.299148, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2003.014404, -918.382873, 35.633609, 0.000044, 90.000038, 89.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2002.957763, -917.972473, 31.545982, 89.999992, 178.299148, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2004.084228, -918.380676, 34.103649, 89.999992, 268.299163, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2005.424316, -918.380676, 34.103649, 89.999992, 268.299163, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2005.947753, -917.972473, 34.155937, 0.000044, 0.000022, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2008.757812, -917.972473, 31.545982, 89.999992, 178.299148, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2008.834228, -918.382873, 35.633609, 0.000044, 90.000038, 89.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2008.874267, -918.382873, 32.023628, 0.000044, 90.000038, 89.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2007.957763, -917.972473, 36.155967, 89.999992, 178.299148, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2005.787109, -917.876525, 31.796810, 89.999992, 178.299163, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2009.267089, -917.876525, 31.796810, 89.999992, 178.299163, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2006.474365, -918.380676, 34.103649, 89.999992, 268.299163, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2007.294189, -918.380676, 34.103649, 89.999992, 268.299163, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2007.817871, -917.972473, 34.155937, 0.000044, 0.000022, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2008.364257, -918.380676, 34.103649, 89.999992, 268.299163, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2009.184326, -918.380676, 34.103649, 89.999992, 268.299163, -88.299072, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2009.707763, -917.972473, 34.155937, 0.000044, 0.000022, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.957763, -918.562500, 34.155937, 0.000044, 0.000022, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2010.888916, -959.875976, 34.155937, -0.000023, 0.000012, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2008.308837, -959.875976, 34.155937, -0.000023, 0.000012, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2009.559570, -959.971923, 31.796810, 89.999992, 357.459716, -87.459648, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2010.342285, -959.467773, 34.103649, 89.999992, 447.459716, -87.459648, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2008.842285, -959.467773, 34.103649, 89.999992, 447.459716, -87.459648, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2008.832275, -959.465576, 32.023628, -0.000023, 90.000030, -90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2008.888916, -959.875976, 36.155967, 89.999992, 357.459716, -87.459648, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2008.832275, -959.465576, 35.633609, -0.000023, 90.000030, -90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2008.888916, -959.875976, 31.545982, 89.999992, 357.459716, -87.459648, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2007.762451, -959.467773, 34.103649, 89.999992, 447.459716, -87.459648, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2006.422363, -959.467773, 34.103649, 89.999992, 447.459716, -87.459648, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2005.898925, -959.875976, 34.155937, -0.000023, 0.000012, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2003.088867, -959.875976, 31.545982, 89.999992, 357.459716, -87.459648, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2003.012451, -959.465576, 35.633609, -0.000023, 90.000030, -90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2002.972412, -959.465576, 32.023628, -0.000023, 90.000030, -90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2003.888916, -959.875976, 36.155967, 89.999992, 357.459716, -87.459648, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2006.059570, -959.971923, 31.796810, 89.999992, 357.459716, -87.459648, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2002.579589, -959.971923, 31.796810, 89.999992, 357.459716, -87.459648, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 7978, "vgssairport", "airportwindow02_128", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2005.372314, -959.467773, 34.103649, 89.999992, 447.459716, -87.459648, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2004.552490, -959.467773, 34.103649, 89.999992, 447.459716, -87.459648, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2004.028808, -959.875976, 34.155937, -0.000023, 0.000012, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2003.482421, -959.467773, 34.103649, 89.999992, 447.459716, -87.459648, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2002.662353, -959.467773, 34.103649, 89.999992, 447.459716, -87.459648, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2002.138916, -959.875976, 34.155937, -0.000023, 0.000012, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2001.354980, -959.874755, 34.155937, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2010.673339, -922.472961, 34.155937, 0.000029, 0.000037, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2010.903564, -922.472961, 34.155937, 0.000029, 0.000037, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2013.891357, -919.472961, 42.045883, -0.000007, 90.000091, -0.000004, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2013.891357, -919.472961, 32.045883, -0.000007, 90.000091, -0.000004, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.994384, -935.034729, 37.145965, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2001.994140, -938.034729, 37.145965, 89.999992, 173.225997, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.994384, -930.034729, 37.145965, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.994384, -925.034729, 37.145965, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.994384, -920.064697, 37.145965, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994384, -936.034729, 37.145965, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994384, -931.034729, 37.145965, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994384, -926.034729, 37.145965, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994384, -921.064758, 37.145965, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994140, -920.064758, 37.146163, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.994384, -917.064758, 37.146163, 89.999992, 173.226013, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2005.974365, -917.064758, 37.146163, 89.999992, 173.226013, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2008.914550, -917.064575, 37.146362, 89.999992, 173.226013, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2011.914794, -918.064575, 37.146362, 89.999992, -76.612197, -103.387779, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.994384, -916.064758, 37.146163, 89.999992, 173.226013, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2005.974365, -916.064758, 37.146163, 89.999992, 173.226013, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2008.914550, -916.064575, 37.146362, 89.999992, 173.226013, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2012.914794, -918.064575, 37.146362, 89.999992, -76.612197, -103.387779, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.994384, -935.034729, 42.176013, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2001.994140, -938.034729, 42.176013, 89.999992, 176.601211, -86.601173, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.994384, -930.034729, 42.176013, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.994384, -925.034729, 42.176013, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.994384, -920.064697, 42.176013, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994384, -936.034729, 42.176013, 89.999992, 90.000076, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994384, -931.034729, 42.176013, 89.999992, 90.000076, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994384, -926.034729, 42.176013, 89.999992, 90.000076, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994384, -921.064758, 42.176013, 89.999992, 90.000076, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994140, -920.064758, 42.176212, 89.999992, 90.000076, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.994384, -917.064758, 42.176212, 89.999992, 176.601226, -86.601173, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2005.974365, -917.064758, 42.176212, 89.999992, 176.601226, -86.601173, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2008.914550, -917.064575, 42.176410, 89.999992, 176.601226, -86.601173, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2011.914794, -918.064575, 42.176410, 89.999992, -83.215942, -96.784011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.994384, -916.064758, 42.176212, 89.999992, 176.601226, -86.601173, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2005.974365, -916.064758, 42.176212, 89.999992, 176.601226, -86.601173, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2008.914550, -916.064575, 42.176410, 89.999992, 176.601226, -86.601173, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2012.914794, -918.064575, 42.176410, 89.999992, -83.215942, -96.784011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2003.772460, -937.764587, 39.665943, 0.000000, 0.000067, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.272705, -936.774597, 39.665943, 0.000000, 0.000067, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.272705, -935.774597, 39.665943, 0.000000, 0.000067, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.272705, -934.774597, 39.665943, 0.000000, 0.000067, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.272705, -916.314575, 39.665943, 0.000000, 0.000067, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.272705, -917.314575, 39.665943, 0.000000, 0.000067, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.272705, -918.304626, 39.665943, 0.000000, 0.000067, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.272460, -916.314575, 39.665943, 0.000000, 0.000067, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2001.262451, -916.314575, 39.665943, 0.000000, 0.000067, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2012.673095, -916.314575, 39.665943, 0.000000, 0.000067, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2011.682128, -916.314575, 39.665943, 0.000000, 0.000067, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2012.673095, -917.314575, 39.665943, 0.000000, 0.000067, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2012.673095, -918.314575, 39.665943, 0.000000, 0.000067, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2012.673095, -919.314575, 39.665943, 0.000000, 0.000067, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1999.640625, -934.201538, 39.448406, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1999.640625, -918.891540, 39.448406, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1998.889892, -931.651550, 37.728416, 0.000000, 450.000000, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1998.889892, -926.731567, 37.728416, 0.000000, 450.000000, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1998.889892, -921.901550, 37.728416, 0.000000, 450.000000, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1998.889892, -921.081542, 37.728416, 0.000000, 450.000000, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1998.889892, -931.651550, 41.598358, 0.000000, 450.000000, 179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1998.889892, -926.731567, 41.598358, 0.000000, 450.000000, 179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1998.889892, -921.901550, 41.598358, 0.000000, 450.000000, 179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1998.889892, -921.081542, 41.598358, 0.000000, 450.000000, 179.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -1999.612548, -929.144592, 39.665943, 0.000029, 0.000037, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -1999.613525, -922.114562, 39.665943, 0.000029, 0.000037, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1999.640625, -926.681579, 39.448406, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1999.640625, -930.581542, 39.448406, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1999.640625, -922.601562, 39.448406, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1999.640625, -924.631591, 39.448406, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1999.640625, -920.591552, 39.448406, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1999.640625, -928.581542, 39.448406, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1999.640625, -932.371582, 39.448406, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.859863, -916.741577, 39.448406, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2011.090087, -916.741577, 39.448406, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2004.401367, -915.991577, 37.728416, -0.000022, 450.000000, -89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2009.311035, -915.991577, 37.728416, -0.000022, 450.000000, -89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2009.060791, -915.991577, 41.578395, -0.000022, 450.000000, -89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2004.130859, -915.991577, 41.578395, -0.000022, 450.000000, -89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2006.992919, -916.674560, 39.665943, 0.000000, 0.000004, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2006.540039, -916.741577, 39.448406, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2004.309814, -916.741577, 39.448406, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2008.840332, -916.741577, 39.448406, 89.999992, 263.225952, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2018.891357, -919.472961, 42.045883, -0.000007, 90.000091, -0.000004, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2018.891357, -919.472961, 32.045883, -0.000007, 90.000091, -0.000004, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2013.891357, -956.692932, 42.045883, -0.000007, 90.000099, -0.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2013.891357, -956.692932, 32.045883, -0.000007, 90.000099, -0.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2018.891357, -956.692932, 42.045883, -0.000007, 90.000099, -0.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2018.891357, -956.692932, 32.045883, -0.000007, 90.000099, -0.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2010.903564, -955.372924, 34.155941, 0.000029, 0.000037, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2001.994140, -943.254699, 37.145965, 89.999992, 173.225997, -83.225975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.994384, -946.254699, 37.145965, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.994384, -951.244750, 37.145965, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.994384, -956.244750, 37.145965, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.994140, -958.254699, 37.146163, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994384, -945.244689, 37.145965, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994384, -950.244689, 37.145965, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994384, -955.244750, 37.145965, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994140, -958.254699, 37.146163, 89.999992, 90.000045, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2012.883911, -952.509521, 30.688404, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18800, "mroadhelix1", "road1-3", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2012.883911, -927.510192, 30.688404, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18800, "mroadhelix1", "road1-3", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2012.883911, -902.510498, 30.688404, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18800, "mroadhelix1", "road1-3", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2012.883911, -889.840515, 20.438390, 0.000014, 180.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 12855, "cunte_cop", "sw_copgrass01", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2035.574218, -889.840332, 20.438591, 0.000014, 180.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 12855, "cunte_cop", "sw_copgrass01", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2035.532958, -952.509521, 30.687406, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18800, "mroadhelix1", "road1-3", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2035.532958, -927.510192, 30.687406, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18800, "mroadhelix1", "road1-3", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2035.532958, -902.510498, 30.687406, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18800, "mroadhelix1", "road1-3", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2047.574707, -902.819885, 20.438591, 0.000007, 180.000000, 179.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 12855, "cunte_cop", "sw_copgrass01", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2047.574707, -927.819274, 20.438591, 0.000007, 180.000000, 179.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 12855, "cunte_cop", "sw_copgrass01", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2047.574462, -952.519165, 20.438791, 0.000007, 180.000000, 179.999923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 12855, "cunte_cop", "sw_copgrass01", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2011.914794, -958.274780, 37.146362, 89.999992, -83.215942, -96.784011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2012.914794, -958.274780, 37.146362, 89.999992, -83.215942, -96.784011, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.994750, -960.854248, 37.146560, 89.999992, -11.603158, -78.396812, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2005.994384, -960.854248, 37.146560, 89.999992, -11.603158, -78.396812, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2010.924682, -960.854248, 37.146560, 89.999992, -11.603158, -78.396812, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.994750, -961.854248, 37.146560, 89.999992, -11.603158, -78.396812, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2005.994384, -961.854248, 37.146560, 89.999992, -11.603158, -78.396812, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2010.924682, -961.854248, 37.146560, 89.999992, -11.603158, -78.396812, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2001.994140, -943.254699, 42.175937, 89.999992, 176.601211, -86.601173, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.994384, -946.254699, 42.175937, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.994384, -951.244750, 42.175937, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.994384, -956.244750, 42.175937, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.994140, -958.254699, 42.176136, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994384, -945.244689, 42.175937, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994384, -950.244689, 42.175937, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994384, -955.244750, 42.175937, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1998.994140, -958.254699, 42.176136, 89.999992, 90.000061, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2011.914794, -958.274780, 42.176334, 89.999992, -86.596099, -93.403831, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2012.914794, -958.274780, 42.176334, 89.999992, -86.596099, -93.403831, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.994750, -960.854248, 42.176532, 89.999992, -5.860448, -84.139503, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2005.994384, -960.854248, 42.176532, 89.999992, -5.860448, -84.139503, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2010.924682, -960.854248, 42.176532, 89.999992, -5.860448, -84.139503, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.994750, -961.854248, 42.176532, 89.999992, -5.860448, -84.139503, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2005.994384, -961.854248, 42.176532, 89.999992, -5.860448, -84.139503, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2010.924682, -961.854248, 42.176532, 89.999992, -5.860448, -84.139503, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18065, "ab_sfammumain", "plywood_gym", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2003.772460, -943.494201, 39.665943, 0.000000, 0.000067, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2012.632324, -957.095397, 39.665943, 0.000014, 0.000051, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -1999.252319, -948.024230, 39.665943, 0.000000, 0.000067, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -1999.252075, -948.984558, 39.665943, 0.000000, 0.000067, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18244, -1998.911743, -949.835754, 39.664047, 89.999992, 154.471191, -64.471237, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2009.653808, -961.596740, 39.665943, 0.000022, 0.000075, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2010.653808, -961.596740, 39.665943, 0.000022, 0.000075, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2011.653808, -961.596740, 39.665943, 0.000022, 0.000075, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.272827, -961.596740, 39.665943, 0.000045, 0.000075, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2000.272827, -961.596740, 39.665943, 0.000045, 0.000075, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2001.272827, -961.596740, 39.665943, 0.000045, 0.000075, 89.999832, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.272705, -960.595947, 39.665943, 0.000000, 0.000090, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.272705, -959.595947, 39.665943, 0.000000, 0.000090, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -1999.272705, -958.595947, 39.665943, 0.000000, 0.000090, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 6284, "bev_law2", "pierwall02_law", 0x00000000);
    uberasdt = CreateDynamicObject(2736, -1998.461059, -951.094299, 39.624290, 0.000007, 0.000007, 89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(2736, -1998.350952, -951.094299, 39.624290, 0.000007, 0.000007, 89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(2643, -1998.370483, -951.081604, 39.614326, 0.000007, 0.000007, 89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18066, -1998.320678, -951.607727, 39.624469, 0.000007, 0.000007, 89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(11711, -1998.320678, -951.018127, 39.624469, 0.000007, 90.000007, 89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1999.640625, -954.032409, 39.448406, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1999.640625, -958.002807, 39.448406, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(18244, -1998.911987, -947.137023, 39.664249, 89.999992, 154.471191, -64.471237, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1998.889892, -956.402343, 37.728416, 0.000000, 450.000000, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1998.889892, -956.402343, 41.598388, 0.000000, 450.000000, 179.999847, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(18244, -1998.541625, -947.137023, 39.664249, 89.999992, 334.444030, -64.444129, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18244, -1998.541381, -949.835998, 39.664447, 89.999992, 334.444030, -64.444129, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2001.879882, -961.202026, 39.448406, 89.999992, 107.413024, -107.413032, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2008.778808, -961.952026, 37.728416, 0.000007, 450.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2003.869140, -961.952026, 37.728416, 0.000007, 450.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2004.119384, -961.952026, 41.578395, 0.000007, 450.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2009.049316, -961.952026, 41.578395, 0.000007, 450.000000, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2006.187255, -961.269042, 39.665943, 0.000000, 0.000036, -0.000104, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2006.640136, -961.202026, 39.448406, 89.999992, 107.413024, -107.413032, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2009.060546, -961.202026, 39.448406, 89.999992, 107.413024, -107.413032, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -2004.339843, -961.202026, 39.448406, 89.999992, 107.413024, -107.413032, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -1999.612548, -957.003967, 39.665943, 0.000029, 0.000037, 89.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    uberasdt = CreateDynamicObject(19866, -1999.640625, -956.043029, 39.448406, 89.999992, 180.000015, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19482, -1998.494995, -947.296936, 39.633289, -0.000007, 0.000007, 0.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(uberasdt, 0, "Uber", 130, "Arial", 199, 1, 0xFF000000, 0x00000000, 1);
    uberasdt = CreateDynamicObject(18981, -2008.900634, -927.864379, 47.387111, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2008.900634, -950.104492, 47.387111, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2021.898925, -927.864379, 35.387054, 0.000000, 180.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2021.898925, -950.105590, 35.387054, 0.000000, 180.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2007.660888, -919.472961, 44.475872, -0.000007, 180.000076, -0.000004, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2002.430541, -932.683044, 44.475879, 0.000007, 180.000076, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2002.430541, -923.971801, 44.475879, 0.000007, 180.000076, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2001.489990, -921.052062, 42.235897, 89.999992, 314.802032, -44.801990, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2007.931152, -918.561828, 42.235698, 89.999992, 115.528846, -115.528800, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2001.489990, -931.022094, 42.235897, 89.999992, 314.802032, -44.801990, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2001.489746, -933.042602, 42.236095, 89.999992, 314.802032, -44.801990, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    uberasdt = CreateDynamicObject(970, -2000.753295, -938.275573, 43.171791, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -1998.652709, -936.185913, 43.171791, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -1998.652709, -932.006103, 43.171791, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -1998.652709, -927.816772, 43.171791, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -1998.652709, -923.616638, 43.171791, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -1998.652709, -919.436584, 43.171791, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -1998.652709, -917.855834, 43.171791, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -2000.732543, -915.735656, 43.171791, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -2004.932373, -915.735656, 43.171791, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -2009.132446, -915.735656, 43.171791, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -2011.202270, -915.735656, 43.171791, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -2013.282348, -917.845520, 43.171791, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2001.968139, -931.495117, 44.524818, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 8391, "ballys01", "vgncorpdoor1_512", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2001.967895, -925.125732, 44.524818, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 8391, "ballys01", "vgncorpdoor1_512", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2004.668823, -919.046875, 44.786167, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 14534, "ab_wooziea", "mcstraps_window", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2010.228271, -919.046875, 44.786167, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 14534, "ab_wooziea", "mcstraps_window", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2002.430541, -948.593444, 44.475894, 0.000007, 180.000076, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2002.430541, -951.973327, 44.475894, 0.000007, 180.000076, 89.999946, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2006.931518, -956.692932, 44.465869, -0.000007, 180.000091, -0.000003, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 18646, "matcolours", "grey-70-percent", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2001.489746, -948.242675, 42.236095, 89.999992, 314.802032, -44.801990, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2001.489501, -956.773315, 42.236293, 89.999992, 314.802032, -44.801990, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2007.738769, -959.263610, 42.236492, 89.999992, 115.528846, -115.528800, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 16640, "a51", "ws_metalpanel1", 0x00000000);
    uberasdt = CreateDynamicObject(970, -2000.753295, -943.025756, 43.171791, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -1998.672729, -945.125671, 43.171791, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -1998.672729, -949.306274, 43.171791, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -1998.672729, -953.486511, 43.171791, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -1998.672729, -957.666381, 43.171791, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -1998.672729, -959.786926, 43.171791, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -2000.773193, -961.867370, 43.171791, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -2004.953491, -961.867370, 43.171791, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -2009.143554, -961.867370, 43.171791, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -2010.904052, -961.867370, 43.171791, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -2012.994140, -959.767639, 43.171791, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(970, -2012.994140, -955.597167, 43.171791, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(19445, -2001.968139, -950.406066, 44.524818, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 8391, "ballys01", "vgncorpdoor1_512", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2004.668823, -957.186340, 44.786167, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 14534, "ab_wooziea", "mcstraps_window", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2010.228271, -957.186340, 44.786167, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 14534, "ab_wooziea", "mcstraps_window", 0x00000000);
    uberasdt = CreateDynamicObject(18980, -2002.792480, -938.154541, 47.165885, 89.999992, 90.000061, -89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 14534, "ab_wooziea", "mcstraps_window", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2012.633911, -951.120361, 31.208393, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2012.634643, -927.080200, 31.208393, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2012.414428, -926.689819, 31.078390, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2012.414428, -951.360473, 31.078390, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2012.974609, -951.360473, 31.078390, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2012.974609, -926.679443, 31.078390, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2020.919189, -931.384948, 34.897102, 0.000000, 180.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2014.778808, -931.384948, 34.897102, 0.000000, 180.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2017.889038, -931.384948, 34.897102, 0.000000, 180.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2020.919189, -944.854309, 34.897102, 0.000000, 180.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2014.778808, -944.854309, 34.897102, 0.000000, 180.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2017.889038, -944.854309, 34.897102, 0.000000, 180.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2034.615112, -964.529724, 20.438791, 0.000000, 180.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 12855, "cunte_cop", "sw_copgrass01", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2012.875366, -964.529724, 20.438791, 0.000000, 180.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 12855, "cunte_cop", "sw_copgrass01", 0x00000000);
    uberasdt = CreateDynamicObject(18766, -2000.882446, -895.320739, 30.438375, 0.000014, 180.000000, 269.999938, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 12855, "cunte_cop", "sw_copgrass01", 0x00000000);
    uberasdt = CreateDynamicObject(3881, -2004.816406, -897.160949, 32.948421, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 2, 6522, "cuntclub_law2", "marinawindow1_256", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 3, 18027, "cj_barb2", "interiordoor1_256", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 4, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 6, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2012.883911, -890.840393, 19.198392, 0.000022, 180.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2034.694458, -890.840393, 19.198392, 0.000022, 180.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2012.883911, -891.840393, 19.198392, 0.000022, 180.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2034.694458, -891.840393, 19.198392, 0.000022, 180.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2046.604125, -904.650207, 19.198392, 0.000014, 180.000000, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2046.604125, -929.620178, 19.198392, 0.000014, 180.000000, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2046.604125, -951.720275, 19.198392, 0.000014, 180.000000, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2045.604125, -904.650207, 19.198392, 0.000014, 180.000000, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2045.604125, -929.620178, 19.198392, 0.000014, 180.000000, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2045.604125, -951.720275, 19.198392, 0.000014, 180.000000, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2043.949096, -963.535461, 31.197408, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2038.949096, -963.535461, 31.197408, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2033.949096, -963.535461, 31.197408, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2043.949096, -962.535461, 31.197408, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2038.949096, -962.535461, 31.197408, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2033.949096, -962.535461, 31.197408, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2043.718872, -962.335266, 30.967403, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2038.718872, -962.335266, 30.967403, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2033.718872, -962.335266, 30.967403, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2033.718872, -963.335266, 30.967403, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    uberasdt = CreateDynamicObject(18762, -2033.718872, -964.335266, 30.967403, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2045.383911, -904.650207, 18.958387, 0.000014, 180.000000, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2045.383911, -929.620178, 18.958387, 0.000014, 180.000000, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2045.383911, -951.720275, 18.958387, 0.000014, 180.000000, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2032.404663, -892.120300, 18.958387, 0.000014, 180.000000, 269.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2013.354858, -892.120300, 18.958387, 0.000014, 180.000000, 269.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2014.473388, -894.369873, 29.588390, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2018.473388, -894.369873, 29.588390, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2022.473388, -894.369873, 29.588390, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2026.473388, -894.369873, 29.588390, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2030.473388, -894.369873, 29.588390, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2034.473388, -894.369873, 29.588390, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2038.473388, -894.369873, 29.588390, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2042.473388, -894.369873, 29.588390, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2043.173950, -913.890625, 29.588390, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2043.173950, -917.890625, 29.588390, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2043.173950, -921.890625, 29.588390, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2043.173950, -925.890625, 29.588390, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2043.173950, -929.890625, 29.588390, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2043.173950, -933.890625, 29.588390, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2043.173950, -937.890625, 29.588390, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2043.173950, -941.890625, 29.588390, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2043.173950, -945.890625, 29.588390, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2043.173950, -949.890625, 29.588390, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2043.173950, -953.890625, 29.588390, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2043.173950, -957.890625, 29.588390, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 1239, "icons6", "lyellow32", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2027.223144, -914.661193, 29.588390, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2027.223144, -918.661193, 29.588390, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2027.223144, -922.661193, 29.588390, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2027.223144, -926.661193, 29.588390, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2027.223144, -930.661193, 29.588390, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2027.223144, -934.661193, 29.588390, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2027.223144, -938.661193, 29.588390, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2027.223144, -942.661193, 29.588390, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2027.223144, -946.661193, 29.588390, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2027.223144, -950.661193, 29.588390, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2027.223144, -954.661193, 29.588390, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(19353, -2027.223144, -958.661193, 29.588390, 89.999992, 179.999984, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18980, -2022.041992, -960.884521, 35.125930, 0.000000, 0.000081, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18980, -2022.041992, -958.824523, 35.125930, 0.000000, 0.000081, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18980, -2022.041992, -956.794433, 35.125930, 0.000000, 0.000081, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18980, -2022.041992, -954.564453, 35.125930, 0.000000, 0.000081, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18980, -2022.041992, -952.404235, 35.125930, 0.000000, 0.000081, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2022.039306, -947.057800, 30.600143, -29.100048, 180.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18981, -2022.039306, -938.966796, 26.096670, -29.100048, 180.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18980, -2022.071533, -948.904113, 31.665939, 90.000000, 0.000081, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(18980, -2022.061401, -935.743652, 31.665939, 90.000000, 0.000081, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(2736, -2000.249145, -915.898498, 39.587417, 0.000000, -0.000007, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(2736, -2000.249145, -915.788391, 39.587417, 0.000000, -0.000007, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(2643, -2000.261718, -915.808044, 39.577457, 0.000000, -0.000007, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    uberasdt = CreateDynamicObject(18066, -1999.735595, -915.758117, 39.587596, 0.000000, -0.000007, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    uberasdt = CreateDynamicObject(11711, -2000.325195, -915.758117, 39.587596, 0.000000, 89.999992, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(uberasdt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(uberasdt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(638, -2012.938110, -918.364501, 32.388389, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, -2019.748291, -918.364501, 32.388389, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, -2016.368408, -918.364501, 32.388389, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, -2012.938110, -958.026245, 32.388389, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, -2019.748291, -958.026245, 32.388389, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, -2016.368408, -958.026245, 32.388389, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1597, -2007.531738, -906.899780, 33.818405, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1597, -2018.301513, -906.899780, 33.818405, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1231, -2012.824096, -906.609741, 33.808414, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1231, -2002.084106, -906.609741, 33.808414, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1231, -2023.895507, -906.609741, 33.808414, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1617, -2022.391967, -919.047302, 45.375446, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1617, -2022.391967, -922.647033, 45.375446, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1617, -2022.391967, -926.417053, 45.375446, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1617, -2022.391967, -919.047302, 42.375530, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1617, -2022.391967, -922.647033, 42.375530, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1617, -2022.391967, -926.417053, 42.375530, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2649, -2023.042724, -918.580688, 33.367412, 360.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2649, -2023.042724, -921.340393, 33.367412, 360.000000, 270.000000, 0.000000, 0, 0, -1, 200.00, 200.00);
}