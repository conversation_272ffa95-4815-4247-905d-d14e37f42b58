RemoveTerminalBuilding(playerid)
{
    RemoveBuildingForPlayer(playerid, 12956, 96.328, -261.195, 3.859, 0.250);
    RemoveBuildingForPlayer(playerid, 12861, 36.828, -256.226, 0.468, 0.250);
    RemoveBuildingForPlayer(playerid, 13195, 36.828, -256.226, 0.468, 0.250);
    RemoveBuildingForPlayer(playerid, 12805, 65.257, -303.984, 14.453, 0.250);
    RemoveBuildingForPlayer(playerid, 13191, 65.257, -303.984, 14.453, 0.250);
    RemoveBuildingForPlayer(playerid, 1431, 36.429, -291.062, 1.570, 0.250);
    RemoveBuildingForPlayer(playerid, 1440, 32.406, -289.218, 1.648, 0.250);
    RemoveBuildingForPlayer(playerid, 1438, 29.234, -286.054, 1.218, 0.250);
    RemoveBuildingForPlayer(playerid, 1426, 29.171, -292.273, 1.406, 0.250);
    RemoveBuildingForPlayer(playerid, 1426, 24.593, -291.757, 1.406, 0.250);
    RemoveBuildingForPlayer(playerid, 1438, 33.601, -279.351, 1.117, 0.250);
    RemoveBuildingForPlayer(playerid, 1449, 43.109, -254.960, 1.218, 0.250);
    RemoveBuildingForPlayer(playerid, 1450, 43.484, -252.570, 1.203, 0.250);
}

CreateTerminalExt()
{
    new STREAMER_TAG_OBJECT:trmnxlsbust;
    trmnxlsbust = CreateDynamicObject(19482, 69.515426, -315.514953, 7.387405, -0.000007, -0.000007, -90.000053, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "POLICE STATION", 80, "Arial", 30, 1, 0xFFFFFFFF, 0xFF4D9586, 1);
    trmnxlsbust = CreateDynamicObject(18980, 103.654914, -304.040496, -5.718217, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 103.649284, -309.509582, -1.813441, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 7419, "vegasbuild", "vgnwoodenwall_256", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 103.654914, -315.000457, -5.718217, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 96.074935, -304.040496, -5.718217, 0.000000, 0.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 96.069305, -309.509582, -1.813441, 0.000045, 0.000000, 89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 7419, "vegasbuild", "vgnwoodenwall_256", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 96.074935, -315.000457, -5.718217, 0.000000, 0.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 88.494995, -304.040496, -5.718217, 0.000000, 0.000052, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 88.489364, -309.509582, -1.813441, 0.000052, 0.000000, 89.999839, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 7419, "vegasbuild", "vgnwoodenwall_256", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 88.494995, -315.000457, -5.718217, 0.000000, 0.000052, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 80.914932, -304.040496, -5.718217, 0.000000, 0.000052, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 80.909301, -309.509582, -1.813441, 0.000052, 0.000000, 89.999839, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 7419, "vegasbuild", "vgnwoodenwall_256", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 80.914932, -315.000457, -5.718217, 0.000000, 0.000052, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 73.334991, -304.040496, -5.718217, 0.000000, 0.000060, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 73.329360, -309.509582, -1.813441, 0.000060, 0.000000, 89.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 7419, "vegasbuild", "vgnwoodenwall_256", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 73.334991, -315.000457, -5.718217, 0.000000, 0.000060, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 65.754989, -304.040496, -5.718217, 0.000000, 0.000068, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 65.749359, -309.509582, -1.813441, 0.000068, 0.000000, 89.999794, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 7419, "vegasbuild", "vgnwoodenwall_256", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 65.754989, -315.000457, -5.718217, 0.000000, 0.000068, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 91.654876, -304.040496, 7.271781, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 77.754890, -304.030487, 7.281786, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 91.654876, -315.010589, 7.271781, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 77.754890, -315.000579, 7.281786, 0.000000, 90.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18762, 103.652687, -307.029937, 7.272799, 0.000000, 90.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18762, 103.652687, -312.029937, 7.272799, 0.000000, 90.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18762, 65.752708, -307.029937, 7.272799, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18762, 65.752708, -312.029937, 7.272799, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 16640, "a51", "redmetal", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 71.231941, -312.018035, 7.266253, 90.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 71.231941, -307.017944, 7.266253, 90.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 81.231903, -312.018035, 7.266253, 89.999992, 270.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 81.231903, -307.017944, 7.266253, 89.999992, 270.000000, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 91.221847, -312.018035, 7.266253, 89.999992, 270.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 91.221847, -307.017944, 7.266253, 89.999992, 270.000000, -89.999969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 98.161842, -312.018035, 7.266252, 89.999992, 270.000030, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 98.161842, -307.017944, 7.266252, 89.999992, 270.000030, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19482, 99.925445, -303.524658, 7.387405, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "AIRPORT", 50, "Arial", 25, 1, 0xFFFFFFFF, 0xFF4D9586, 1);
    trmnxlsbust = CreateDynamicObject(19482, 92.195426, -303.524658, 7.387405, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "PALETO BAY", 50, "Arial", 21, 1, 0xFFFFFFFF, 0xFF4D9586, 1);
    trmnxlsbust = CreateDynamicObject(19482, 84.655410, -303.524658, 7.387405, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "GALILEO\nOBSERVATORY", 80, "Arial", 30, 1, 0xFFFFFFFF, 0xFF4D9586, 1);
    trmnxlsbust = CreateDynamicObject(19482, 77.145416, -303.524658, 7.387405, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "PORT OF LS", 80, "Arial", 40, 1, 0xFFFFFFFF, 0xFF4D9586, 1);
    trmnxlsbust = CreateDynamicObject(19482, 69.515434, -303.524658, 7.387405, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "POLICE STATION", 80, "Arial", 30, 1, 0xFFFFFFFF, 0xFF4D9586, 1);
    trmnxlsbust = CreateDynamicObject(19482, 92.315452, -315.514953, 7.387405, -0.000007, -0.000007, -90.000053, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "PALETO BAY", 50, "Arial", 21, 1, 0xFFFFFFFF, 0xFF4D9586, 1);
    trmnxlsbust = CreateDynamicObject(19482, 84.785476, -315.514953, 7.387405, -0.000007, -0.000007, -90.000053, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "GALILEO\nOBSERVATORY", 80, "Arial", 30, 1, 0xFFFFFFFF, 0xFF4D9586, 1);
    trmnxlsbust = CreateDynamicObject(19482, 77.195434, -315.514953, 7.387405, -0.000007, -0.000007, -90.000053, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "PORT OF LS", 80, "Arial", 40, 1, 0xFFFFFFFF, 0xFF4D9586, 1);
    trmnxlsbust = CreateDynamicObject(19482, 99.925460, -315.514953, 7.387405, -0.000007, -0.000007, -90.000053, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "AIRPORT", 50, "Arial", 25, 1, 0xFFFFFFFF, 0xFF4D9586, 1);
    trmnxlsbust = CreateDynamicObject(18981, 20.181512, -309.677215, -4.704214, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 19517, "noncolored", "gen_white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18981, 32.161529, -321.667175, -4.704214, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 19517, "noncolored", "gen_white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18981, 32.211540, -297.747131, -4.704214, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 19517, "noncolored", "gen_white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18981, 32.107429, -309.787445, 0.738415, 1.399999, 90.000000, 90.099983, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 18065, "ab_sfammumain", "gun_floor1", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19447, 39.843185, -322.088287, 7.555988, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19447, 30.233184, -322.088287, 7.555988, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19447, 24.483173, -322.118316, 7.555988, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19447, 19.698139, -302.023376, 7.555988, 0.000007, -0.000007, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19447, 19.698139, -311.633392, 7.555988, 0.000007, -0.000007, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19447, 19.728168, -317.383392, 7.555988, 0.000007, -0.000007, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19447, 24.433151, -297.238220, 7.555988, -0.000014, -0.000014, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19447, 34.043167, -297.238220, 7.555988, -0.000014, -0.000014, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19447, 39.793167, -297.208190, 7.555988, -0.000014, -0.000014, -90.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18981, 32.146026, -309.657775, 8.283241, 0.000000, 90.000000, 90.099975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 18065, "ab_sfammumain", "gun_floor1", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19447, 44.648136, -301.953277, 7.555988, -0.000014, -0.000029, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19447, 44.648136, -311.563262, 7.555988, -0.000014, -0.000029, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19447, 44.678165, -317.353302, 7.555988, -0.000014, -0.000029, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10850, "bakerybit2_sfse", "frate64_blue", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 44.173027, -316.183715, 3.343810, 0.000000, 0.000000, 90.199996, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 19517, "noncolored", "gen_white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 44.127784, -303.223937, 3.343810, 0.000000, 0.000000, 90.199996, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 19517, "noncolored", "gen_white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(1499, 44.200172, -311.209747, 0.925599, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 6284, "bev_law2", "glass_fence_64hv", 0x00000000);
    trmnxlsbust = CreateDynamicObject(1499, 44.200172, -308.209747, 0.925599, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 6284, "bev_law2", "glass_fence_64hv", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 44.097789, -303.224029, 6.123808, 0.000000, 0.000000, 90.199996, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 19517, "noncolored", "gen_white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 44.143043, -316.183959, 6.123808, 0.000000, 0.000000, 90.199996, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 19517, "noncolored", "gen_white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 44.132438, -310.283905, 5.933807, 0.000000, 0.000000, 90.199996, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 19517, "noncolored", "gen_white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19379, 49.919033, -309.620269, 8.629197, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    trmnxlsbust = CreateDynamicObject(3875, 53.284469, -309.564361, 1.172196, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 8391, "ballys01", "greyground256128", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 8391, "ballys01", "greyground256128", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 2, 8391, "ballys01", "greyground256128", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 53.309383, -309.509582, -1.753440, 0.000082, 0.000000, 89.999748, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 7419, "vegasbuild", "vgnwoodenwall_256", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19379, 55.209037, -309.620269, 8.619195, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 111.589065, -309.534973, 0.575092, 90.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 116.589057, -309.534973, 0.575092, 90.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 111.589065, -319.534973, 0.575092, 90.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 116.589057, -319.534973, 0.575092, 90.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 111.589065, -299.535064, 0.575093, 90.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 116.589057, -299.535034, 0.575093, 90.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18763, 114.220436, -332.648773, 3.451850, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18763, 117.190429, -332.648773, 3.451850, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19353, 115.644660, -331.092773, 2.788235, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 9514, "711_sfw", "rebrckwall_128", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19353, 114.554664, -325.742797, 2.718235, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 9514, "711_sfw", "rebrckwall_128", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19353, 112.864692, -331.072662, 0.858237, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 9514, "711_sfw", "rebrckwall_128", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19353, 112.864692, -325.832672, 0.858237, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 9514, "711_sfw", "rebrckwall_128", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19353, 116.724655, -325.752807, 2.708235, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 9514, "711_sfw", "rebrckwall_128", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19353, 118.414695, -331.072662, 0.858237, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 9514, "711_sfw", "rebrckwall_128", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19353, 118.394683, -325.832672, 0.858237, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 9514, "711_sfw", "rebrckwall_128", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18763, 117.058944, -318.992401, 3.451850, 0.000007, -0.000014, 179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18763, 114.088973, -318.992401, 3.451850, 0.000007, -0.000014, 179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19353, 115.634719, -320.548400, 2.788235, 0.000007, -0.000014, 179.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 9514, "711_sfw", "rebrckwall_128", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19353, 118.414688, -320.568511, 0.858237, -0.000007, 0.000014, 0.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 9514, "711_sfw", "rebrckwall_128", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19353, 112.864700, -320.568511, 0.858237, -0.000007, 0.000014, 0.000007, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 9514, "711_sfw", "rebrckwall_128", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 111.589065, -329.534912, 0.575092, 90.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 116.589057, -329.534973, 0.575092, 90.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 120.098999, -319.534973, 0.565092, 89.999992, 180.000000, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 120.098999, -329.534912, 0.565092, 89.999992, 180.000000, -89.999992, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 114.069068, -296.615142, 0.315093, 90.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 4830, "airport2", "bathtile01_int", 0x00000000);
    trmnxlsbust = CreateDynamicObject(19480, 45.247467, -219.994964, 5.886171, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "T", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 43.659229, -219.920135, 5.746170, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "E", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 41.961120, -219.840042, 5.916172, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "R", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 39.993312, -219.747360, 5.746170, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "M", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 38.674781, -219.685134, 5.916172, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "I", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 37.486103, -219.629165, 5.746170, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "N", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 35.608196, -219.540618, 5.916172, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "A", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 33.940044, -219.461959, 5.746170, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "L", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 30.613746, -219.305068, 5.746170, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "A", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 28.775774, -219.218276, 5.916172, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "R", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 27.437278, -219.155227, 5.746170, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "I", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 26.238597, -219.098632, 5.916172, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "V", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 24.540477, -219.018402, 5.746170, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "E", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 22.832376, -218.937973, 5.916172, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "N", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 20.924501, -218.847946, 5.746170, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "A", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 45.146945, -220.094436, 5.876172, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "T", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 43.558708, -220.019622, 5.736170, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "E", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 41.860599, -219.939529, 5.906171, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "R", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 39.892791, -219.846847, 5.736170, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "M", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 38.574260, -219.784622, 5.906171, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "I", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 37.385581, -219.728652, 5.736170, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "N", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 35.507675, -219.640090, 5.906171, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "A", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 33.839523, -219.561447, 5.736170, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "L", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 30.513225, -219.404541, 5.736170, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "A", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 28.675251, -219.317764, 5.906171, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "R", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 27.336755, -219.254714, 5.736170, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "I", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 26.138071, -219.198120, 5.906171, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "V", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 24.439954, -219.117889, 5.736170, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "E", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 22.731853, -219.037460, 5.906171, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "N", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(19480, 20.823978, -218.947418, 5.736170, 0.000037, 0.000000, 87.299888, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(trmnxlsbust, 0, "A", 80, "Arial", 35, 1, 0xFFFFFFFF, 0x00000000, 1);
    trmnxlsbust = CreateDynamicObject(18981, 33.178874, -239.356719, 1.011937, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18981, 33.178874, -239.356719, 8.761938, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 45.624439, -249.737899, 9.791024, 0.000000, 0.000000, 89.900001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 45.631717, -245.567901, 9.791024, 0.000000, 0.000000, 89.900001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 45.639007, -241.387878, 9.791024, 0.000000, 0.000000, 89.900001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 45.630649, -229.007858, 9.791024, 0.000000, 0.000000, 89.900001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 45.623405, -233.167922, 9.791024, 0.000000, 0.000000, 89.900001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 45.616199, -237.287841, 9.791024, 0.000000, 0.000000, 89.900001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 43.552532, -226.919998, 9.791024, 0.000007, -0.000022, -179.900009, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 39.382530, -226.927276, 9.791024, 0.000007, -0.000022, -179.900009, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 35.202510, -226.934585, 9.791024, 0.000007, -0.000022, -179.900009, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 22.822591, -226.986145, 9.791024, 0.000007, -0.000022, -179.900009, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 26.982658, -226.978866, 9.791024, 0.000007, -0.000022, -179.900009, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 31.102577, -226.971694, 9.791024, 0.000007, -0.000022, -179.900009, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 20.734415, -249.737899, 9.791024, 0.000007, 0.000000, 89.899978, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 20.741693, -245.567901, 9.791024, 0.000007, 0.000000, 89.899978, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 20.748983, -241.387878, 9.791024, 0.000007, 0.000000, 89.899978, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 20.740625, -229.007858, 9.791024, 0.000007, 0.000000, 89.899978, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 20.733381, -233.167922, 9.791024, 0.000007, 0.000000, 89.899978, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 20.726175, -237.287841, 9.791024, 0.000007, 0.000000, 89.899978, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 43.552532, -251.779846, 9.791024, 0.000007, -0.000029, -179.899963, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 39.382530, -251.787124, 9.791024, 0.000007, -0.000029, -179.899963, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 35.202510, -251.794433, 9.791024, 0.000007, -0.000029, -179.899963, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 26.982658, -251.838714, 9.791024, 0.000007, -0.000029, -179.899963, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(970, 31.102577, -251.831542, 9.791024, 0.000007, -0.000029, -179.899963, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 17588, "lae2coast_alpha", "plainglass", 0x00000000);
    SetDynamicObjectMaterial(trmnxlsbust, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 45.036602, -227.532363, -3.190892, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 45.036602, -251.192337, -3.190892, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 25.566608, -251.192337, -3.190892, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 21.296588, -227.532363, -3.190892, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 45.036602, -252.876968, -1.543473, -36.499996, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 45.036602, -226.040893, -1.328721, -36.499996, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 46.265869, -227.530883, -1.140653, -36.499996, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18980, 46.565509, -251.196945, -1.407341, -36.499996, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 43.607742, -239.279891, 0.836413, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 43.847732, -239.279891, 0.646413, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 44.077724, -239.279891, 0.516412, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 44.377727, -239.279891, 0.336412, 90.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 115.719116, -329.145172, 6.425090, 90.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 115.709121, -322.485076, 6.415090, 90.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    trmnxlsbust = CreateDynamicObject(18766, 198.119873, -321.010345, 1.491018, 0.000000, 0.000000, 95.799987, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(trmnxlsbust, 0, 10101, "2notherbuildsfe", "sl_vicwall02", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(19425, 101.510452, -304.035339, 0.568974, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 98.210449, -304.035339, 0.568974, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 93.930511, -304.035339, 0.568974, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 96.629486, -307.975585, 0.578891, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 96.619483, -311.075408, 0.588891, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 103.069366, -311.075439, 0.578891, 0.000007, -0.000022, 179.999740, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 103.079376, -307.975585, 0.588891, 0.000007, -0.000022, 179.999740, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 104.219406, -307.975585, 0.578891, 0.000000, 0.000022, -0.000114, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 104.209396, -311.075439, 0.588891, 0.000000, 0.000022, -0.000114, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 95.499473, -311.075439, 0.578891, 0.000007, -0.000029, 179.999572, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 95.509483, -307.975585, 0.588891, 0.000007, -0.000029, 179.999572, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 90.630508, -304.035339, 0.568974, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 89.049545, -307.975585, 0.578891, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 89.039543, -311.075408, 0.588891, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 87.919532, -311.075439, 0.578891, 0.000007, -0.000037, 179.999526, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 87.929542, -307.975585, 0.588891, 0.000007, -0.000037, 179.999526, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 86.350448, -304.035339, 0.568974, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 83.050445, -304.035339, 0.568974, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 78.770507, -304.035339, 0.568974, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 81.469482, -307.975585, 0.578891, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 81.459480, -311.075408, 0.588891, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 80.339469, -311.075439, 0.578891, 0.000007, -0.000037, 179.999526, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 80.349479, -307.975585, 0.588891, 0.000007, -0.000037, 179.999526, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 75.470504, -304.035339, 0.568974, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 73.889541, -307.975585, 0.578891, 0.000000, 0.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 73.879539, -311.075408, 0.588891, 0.000000, 0.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 72.759529, -311.075439, 0.578891, 0.000007, -0.000045, 179.999481, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 72.769538, -307.975585, 0.588891, 0.000007, -0.000045, 179.999481, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 71.190505, -304.035339, 0.568974, 0.000000, 0.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 67.890502, -304.035339, 0.568974, 0.000000, 0.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 66.309539, -307.975585, 0.578891, 0.000000, 0.000052, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 66.299537, -311.075408, 0.588891, 0.000000, 0.000052, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 65.179527, -311.075439, 0.578891, 0.000007, -0.000052, 179.999435, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 65.189537, -307.975585, 0.588891, 0.000007, -0.000052, 179.999435, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 101.510452, -315.015228, 0.568974, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 98.210449, -315.015228, 0.568974, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 93.930511, -315.015228, 0.568974, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 90.630508, -315.015228, 0.568974, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 86.350448, -315.015228, 0.568974, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 83.050445, -315.015228, 0.568974, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 78.770507, -315.015228, 0.568974, 0.000000, 0.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 75.470504, -315.015228, 0.568974, 0.000000, 0.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 71.190505, -315.015228, 0.568974, 0.000000, 0.000052, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 67.890502, -315.015228, 0.568974, 0.000000, 0.000052, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1285, 96.058609, -314.284057, 1.210623, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1285, 103.648605, -314.284057, 1.210623, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1285, 88.488594, -314.284057, 1.210623, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1285, 80.958587, -314.284057, 1.210623, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1285, 73.288589, -314.284057, 1.210623, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1285, 65.718589, -314.284057, 1.210623, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1696, 101.450073, -306.970916, 8.770774, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1696, 100.850059, -311.830871, 8.770774, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1696, 95.960060, -306.970916, 8.770774, 0.000000, -0.000022, 179.999862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1696, 95.360046, -311.830871, 8.770774, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1696, 90.490119, -306.970916, 8.770774, 0.000000, -0.000022, 179.999862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1696, 89.890106, -311.830871, 8.770774, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1696, 85.000106, -306.970916, 8.770774, 0.000000, -0.000029, 179.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1696, 84.400093, -311.830871, 8.770774, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1696, 79.510086, -306.970916, 8.770774, 0.000000, -0.000029, 179.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1696, 78.910072, -311.830871, 8.770774, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1696, 74.020072, -306.970916, 8.770774, 0.000000, -0.000037, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1696, 73.420059, -311.830871, 8.770774, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1696, 68.540107, -306.970916, 8.770774, 0.000000, -0.000037, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1696, 67.940093, -311.830871, 8.770774, 0.000000, 0.000037, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 103.483497, -302.012268, 4.345142, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 95.923515, -302.012268, 4.345142, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 88.363548, -302.012268, 4.345142, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 80.773567, -302.012268, 4.345142, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 73.173538, -302.012268, 4.345142, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 65.583557, -302.012268, 4.345142, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1622, 21.196441, -298.619293, 7.755414, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1622, 21.066438, -320.629089, 7.755414, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1622, 43.066440, -320.749084, 7.755414, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 53.869564, -307.975585, 0.638891, 0.000000, 0.000068, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 53.859561, -311.075408, 0.648891, 0.000000, 0.000068, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 52.739551, -311.075439, 0.638891, 0.000007, -0.000068, 179.999343, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1251, 52.749561, -307.975585, 0.648891, 0.000007, -0.000068, 179.999343, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1244, 53.311046, -312.125457, 1.515753, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1244, 53.311046, -306.565490, 1.515753, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 53.961685, -312.139556, 1.189854, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 53.961685, -306.829559, 1.189854, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 52.691677, -306.829559, 1.189854, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 52.691677, -312.119537, 1.189854, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 53.307609, -313.915740, 1.272382, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 53.307609, -310.245849, 1.272382, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 53.307609, -308.705871, 1.272382, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 53.307609, -304.985839, 1.272382, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 65.983581, -317.002441, 4.345142, 0.000007, 0.000007, 89.999916, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 73.543579, -317.002441, 4.345142, 0.000007, 0.000007, 89.999916, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 81.103546, -317.002441, 4.345142, 0.000007, 0.000007, 89.999916, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 88.693511, -317.002441, 4.345142, 0.000007, 0.000007, 89.999916, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 96.293548, -317.002441, 4.345142, 0.000007, 0.000007, 89.999916, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 103.883514, -317.002441, 4.345142, 0.000007, 0.000007, 89.999916, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1844, 21.197225, -314.914459, 1.461459, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1844, 21.197225, -311.914520, 1.461459, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1844, 21.197225, -308.914550, 1.461459, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1844, 21.197225, -305.914459, 1.461459, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2012, 25.393196, -298.752777, 1.349375, 0.000000, 1.200000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2012, 28.402536, -298.752777, 1.286339, 0.000000, 1.200000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2012, 31.381885, -298.752777, 1.223930, 0.000000, 1.200000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2585, 33.775997, -298.409149, 2.771128, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2585, 35.285987, -298.409149, 2.771128, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1847, 38.790714, -298.687805, 1.081334, 0.000000, 1.799999, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2361, 21.226287, -303.812347, 1.451974, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2361, 21.226287, -301.832305, 1.451974, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2360, 26.161832, -320.663208, 1.318992, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2360, 27.741825, -320.663208, 1.298992, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2360, 29.321823, -320.663208, 1.258991, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1209, 24.863895, -320.886901, 1.409842, 0.000000, -1.799999, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1209, 22.324195, -320.886901, 1.459640, 0.000000, -1.799999, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1776, 23.627109, -320.990509, 2.523773, 0.000000, -1.899999, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1776, 21.074552, -319.897125, 2.556588, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1776, 21.074552, -317.187042, 2.556588, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1775, 21.061573, -318.549591, 2.568676, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1984, 38.538074, -317.599517, 1.046465, 0.000000, 0.699999, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2582, 42.350811, -320.913452, 1.864138, 0.000000, -0.099999, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2582, 40.850814, -320.913452, 1.866757, 0.000000, -0.099999, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1848, 32.985160, -320.745544, 1.176241, 0.000000, -1.299998, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1888, 37.590072, -320.642211, 1.096037, 0.000000, -1.299999, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1776, 23.157909, -298.640441, 2.536163, 0.000000, 0.999998, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1775, 21.131574, -299.769561, 2.598676, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1889, 39.656024, -303.459625, 0.979831, 0.000000, 1.600000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1889, 36.637180, -303.459625, 1.064156, 0.000000, 1.600000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1889, 33.686218, -303.459625, 1.157734, 0.000000, 1.299980, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1889, 30.666971, -303.459625, 1.226251, 0.000000, 1.299980, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1889, 27.667747, -303.459625, 1.294312, 0.000000, 1.299980, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1889, 39.656024, -307.669708, 0.979831, 0.000000, 1.600007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1889, 36.637180, -307.669708, 1.064156, 0.000000, 1.600007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1889, 33.686218, -307.669708, 1.157734, 0.000000, 1.299988, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1889, 30.666971, -307.669708, 1.226251, 0.000000, 1.299988, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1889, 27.667747, -307.669708, 1.294312, 0.000000, 1.299988, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1891, 39.645427, -313.359039, 0.940064, 0.000000, 1.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1889, 36.625602, -313.349700, 1.001610, 0.000000, 1.600007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1891, 33.645416, -313.359039, 1.090064, 0.000000, 1.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1889, 30.625591, -313.349700, 1.151610, 0.000000, 1.600023, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1891, 27.646509, -313.359039, 1.204777, 0.000000, 1.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2365, 43.652446, -313.868865, 0.934388, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1885, 43.386379, -312.936553, 0.938198, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1889, 30.625591, -317.819763, 1.151610, 0.000000, 1.600023, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1891, 33.635242, -317.819091, 1.080240, 0.000000, 1.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1891, 27.646509, -317.819061, 1.204777, 0.000000, 1.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3034, 44.691936, -319.355010, 3.977786, 0.000000, 0.000000, 90.099998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3034, 44.683330, -314.425140, 3.977786, 0.000000, 0.000000, 90.099998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3034, 44.667148, -305.155181, 3.977786, 0.000000, 0.000000, 90.099998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3034, 44.658214, -300.035186, 3.977786, 0.000000, 0.000000, 90.099998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1438, 41.815406, -296.379272, 0.885204, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1450, 38.868850, -296.575042, 1.603772, 0.000000, -1.799998, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 109.184173, -307.045684, 1.059986, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 109.184173, -312.015594, 1.059986, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 109.184173, -317.015411, 1.059986, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 109.184173, -321.985321, 1.059986, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 111.678428, -306.996459, 1.722219, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 112.488433, -306.996459, 1.722219, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 111.678428, -312.346496, 1.722219, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 112.488433, -312.346496, 1.722219, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 109.184173, -297.075531, 1.059986, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 109.184173, -302.045440, 1.059986, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1362, 112.059844, -309.669738, 1.656937, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1498, 113.459480, -331.181396, 1.077152, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1498, 117.939491, -331.171386, 1.077152, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1498, 117.819900, -320.459777, 1.077152, 0.000007, -0.000014, 179.999786, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1498, 113.339889, -320.469787, 1.077152, -0.000007, 0.000014, 0.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 109.184173, -326.985290, 1.059986, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 109.184173, -331.985382, 1.059986, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 111.764190, -334.395568, 1.059986, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 116.554183, -334.405578, 1.059986, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 122.494140, -321.985321, 1.059986, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 122.494140, -326.985290, 1.059986, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 122.494140, -331.985382, 1.059986, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 119.894210, -334.415588, 1.069987, 0.000000, 0.000007, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 122.494140, -316.985565, 1.059986, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 119.894142, -314.585601, 1.059986, 0.000000, 0.000014, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 118.974151, -311.995605, 1.059985, 0.000000, 0.000014, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 118.974151, -306.995574, 1.059985, 0.000000, 0.000014, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 118.974151, -301.995635, 1.059985, 0.000000, 0.000014, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19866, 118.974151, -296.995666, 1.059985, 0.000000, 0.000014, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 115.248420, -306.996459, 1.722219, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 116.058425, -306.996459, 1.722219, 0.000000, -0.000014, 179.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 115.248420, -312.346496, 1.722219, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 116.058425, -312.346496, 1.722219, 0.000000, -0.000022, 179.999862, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1362, 115.629837, -309.669738, 1.656937, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 118.448417, -297.636474, 1.722218, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 108.838630, -334.624572, 1.032297, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 118.478424, -302.986511, 1.722218, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 122.818611, -334.624572, 1.032297, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 121.538444, -299.866699, 4.319217, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 109.688423, -297.636474, 1.722221, 0.000000, -0.000029, 179.999816, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1256, 109.688430, -302.986511, 1.722221, 0.000000, -0.000037, 179.999771, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 121.538444, -310.046752, 4.319217, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3785, 112.614715, -332.741973, 3.855771, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3785, 118.794731, -332.741973, 3.855771, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18652, 31.700378, -210.903167, -0.243397, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(998, 35.796188, -219.730163, 4.947316, 90.000000, 177.399978, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(998, 46.075634, -220.197097, 4.947316, 90.000000, 177.399978, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(998, 27.284965, -219.343795, 4.947316, 90.000000, 177.399978, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(998, 46.075634, -220.197097, 6.007314, 90.000000, 177.399978, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(998, 35.816200, -219.731201, 6.007314, 90.000000, 177.399978, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(998, 27.285009, -219.343841, 6.007314, 90.000000, 177.399978, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, 25.967439, -309.580230, 7.379627, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, 37.067440, -309.580230, 7.379627, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, 76.767433, -309.160247, 7.659625, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18608, 91.597526, -309.160247, 7.659625, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 41.812587, -219.387512, 1.253774, 0.000007, 0.000000, 87.399963, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 35.918666, -219.119903, 1.253774, 0.000007, 0.000000, 87.399963, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 29.984775, -218.850433, 1.253774, 0.000007, 0.000000, 87.399963, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 23.931011, -218.575546, 1.253774, 0.000007, 0.000000, 87.399963, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 26.913476, -218.589492, 1.127519, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 32.923458, -218.899475, 1.127519, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 38.843452, -219.239456, 1.127519, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1368, 35.944095, -218.307952, 1.262642, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1368, 29.900787, -218.117904, 1.252642, 0.000000, 0.000000, 178.099960, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3361, 33.612686, -252.913192, 3.203676, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3361, 25.572687, -252.912994, 7.203678, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 45.036041, -251.215957, 9.780503, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 45.036041, -227.545928, 9.780503, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 21.276044, -227.545928, 9.780503, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 25.546041, -251.225952, 9.780503, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 21.076032, -251.435958, 2.040504, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 21.076032, -228.775985, 2.040504, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 45.036014, -233.205993, 2.040504, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 45.036014, -245.785964, 2.040504, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 39.485988, -251.235992, 2.040504, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 39.485988, -227.475936, 2.040504, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 46.060264, -248.941390, 0.978322, 0.000000, 0.000000, -0.299998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 46.158744, -230.131713, 0.978322, 0.000000, 0.000000, -0.299998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 103.883491, -342.872375, 4.335140, 0.000007, 0.000007, 269.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 91.973510, -342.872375, 4.335139, 0.000007, 0.000007, 269.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 81.183517, -342.872375, 4.335138, 0.000007, 0.000007, 269.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 69.353492, -342.872375, 4.335140, -0.000000, 0.000006, -90.000068, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 57.443511, -342.872375, 4.335139, -0.000000, 0.000006, -90.000068, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 46.653518, -342.872375, 4.565138, -0.000000, 0.000006, -90.000068, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 19.383499, -287.062316, 5.325139, -0.000000, 0.000006, 179.999801, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 19.383499, -275.152343, 5.325139, -0.000000, 0.000006, 179.999801, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 19.383499, -264.362365, 5.555138, -0.000000, 0.000006, 179.999801, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 19.383504, -344.482299, 5.325139, -0.000000, -0.000000, 179.999755, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 19.383499, -333.902404, 5.325139, -0.000000, -0.000000, 179.999755, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, 19.383499, -323.112426, 5.555138, -0.000000, -0.000000, 179.999755, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1702, 22.299381, -249.063156, 1.510425, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1702, 22.989402, -245.853149, 1.510425, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1702, 25.839401, -247.053115, 1.510425, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1702, 25.674402, -241.028182, 1.510425, 0.000015, 0.000000, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1702, 22.464395, -240.338165, 1.510425, -0.000000, 0.000015, 89.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1702, 23.664361, -237.488174, 1.510425, -0.000015, 0.000000, 0.000015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1702, 25.839403, -229.733200, 1.510425, 0.000007, -0.000015, -90.000083, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1702, 25.149385, -232.943222, 1.510425, 0.000015, 0.000007, 179.999847, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1702, 22.299394, -231.743240, 1.510425, -0.000007, 0.000015, 89.999961, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 34.252841, -247.716552, 2.027252, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 34.252841, -239.356597, 2.027252, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 34.252841, -231.456558, 2.027252, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 33.482810, -231.456542, 2.027252, 0.000000, -0.000007, -90.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 33.492813, -239.376495, 2.027252, 0.000000, -0.000007, -90.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 33.462814, -247.716552, 2.027252, 0.000000, -0.000007, -90.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 38.992847, -247.716552, 2.027252, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 38.992847, -239.356597, 2.027252, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 38.992847, -231.456558, 2.027252, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 38.222816, -231.456542, 2.027252, -0.000007, -0.000007, -89.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 38.232818, -239.376495, 2.027252, -0.000007, -0.000007, -89.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 38.202819, -247.716552, 2.027252, -0.000007, -0.000007, -89.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2011, 33.888599, -245.543289, 1.478799, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2011, 38.608596, -241.573257, 1.478799, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2011, 33.878593, -233.673278, 1.488799, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 38.992847, -247.716552, 9.777243, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 38.992847, -239.356597, 9.777243, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 38.992847, -231.456558, 9.777243, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 38.222816, -231.456542, 9.777243, -0.000015, -0.000007, -89.999961, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 38.232818, -239.376495, 9.777243, -0.000015, -0.000007, -89.999961, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 38.202819, -247.716552, 9.777243, -0.000015, -0.000007, -89.999961, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 32.282852, -247.716552, 9.777243, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 32.282852, -239.356597, 9.777243, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 32.282852, -231.456558, 9.777243, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 31.512819, -231.456542, 9.777243, -0.000022, -0.000007, -89.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 31.522821, -239.376495, 9.777243, -0.000022, -0.000007, -89.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3657, 31.492822, -247.716552, 9.777243, -0.000022, -0.000007, -89.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1702, 21.369392, -235.983230, 9.260421, -0.000007, 0.000015, 89.999961, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1702, 21.369388, -245.413284, 9.260421, -0.000007, 0.000015, 89.999961, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1702, 24.809385, -245.973266, 9.260421, -0.000007, 0.000015, 179.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1702, 24.809391, -236.773208, 9.260421, -0.000007, 0.000015, 179.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1702, 22.769392, -242.683227, 9.260421, -0.000007, 0.000015, 359.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1702, 22.799381, -233.253189, 9.260421, -0.000007, 0.000015, 359.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1825, 43.070465, -235.410903, 9.223921, 0.000000, 0.000000, -1.499994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1825, 42.860275, -243.438079, 9.233922, 0.000000, 0.000000, -1.499994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(4641, 81.282623, -223.406402, 2.215773, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(4641, 79.162628, -223.406402, 2.215773, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 80.243469, -220.905502, 1.091969, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 80.243469, -225.535507, 1.091969, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00);
}