CreateMotelInt()
{
    new STREAMER_TAG_OBJECT:mtmtl;
    mtmtl = CreateDynamicObject(18981, -993.157592, -1112.049560, 127.768402, 0.000000, 90.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "tileblue1", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -980.943481, -1104.397338, 133.225128, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 6867, "vgnpwrmainbld", "sw_wallbrick_02", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -980.943481, -1114.026855, 133.225128, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 6867, "vgnpwrmainbld", "sw_wallbrick_02", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -980.943481, -1123.656127, 133.225128, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 6867, "vgnpwrmainbld", "sw_wallbrick_02", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -978.734191, -1109.247436, 133.225128, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 6867, "vgnpwrmainbld", "sw_wallbrick_02", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -978.734191, -1113.427734, 133.225128, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 6867, "vgnpwrmainbld", "sw_wallbrick_02", 0x00000000);
    mtmtl = CreateDynamicObject(18981, -993.157531, -1094.576904, 127.888404, 0.000007, 89.999984, 179.999786, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -983.463317, -1118.167236, 133.225128, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -986.863281, -1131.356323, 133.225128, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(18981, -993.157348, -1128.050170, 127.888404, 0.000000, 90.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    mtmtl = CreateDynamicObject(18981, -993.157348, -1128.290405, 127.978401, 0.000000, 90.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    mtmtl = CreateDynamicObject(18981, -993.157592, -1128.509277, 127.988403, 0.000000, 90.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "tileblue1", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -983.463317, -1127.796752, 133.225128, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -989.350952, -1118.167236, 128.655136, 0.000000, 0.000029, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -989.350952, -1127.796752, 133.225128, 0.000000, 0.000029, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -994.072448, -1113.435791, 125.185134, 0.000014, 0.000000, 89.999954, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -994.083007, -1109.246215, 133.225128, 0.000014, 0.000000, 89.999954, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -989.350952, -1094.716186, 133.225128, 0.000000, 0.000037, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -989.350952, -1104.345703, 133.225128, 0.000000, 0.000037, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -983.463317, -1094.873779, 133.225128, 0.000000, 0.000022, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -983.463317, -1104.503295, 133.225128, 0.000000, 0.000022, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(18981, -993.157531, -1094.336669, 127.978401, 0.000007, 89.999984, 179.999786, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    mtmtl = CreateDynamicObject(18981, -993.157287, -1094.117919, 127.988403, 0.000007, 89.999984, 179.999786, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "tileblue1", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -986.863281, -1089.918090, 133.225128, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(18766, -996.920898, -1113.123535, 129.942855, 89.999992, 180.000000, -89.999992, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 10101, "2notherbuildsfe", "sl_vicwall02", 0x00000000);
    mtmtl = CreateDynamicObject(18981, -976.768188, -1118.870849, 133.898406, 0.000000, 90.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    mtmtl = CreateDynamicObject(18981, -976.758178, -1093.870361, 133.898406, 0.000000, 90.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    mtmtl = CreateDynamicObject(19353, -990.879760, -1113.436035, 132.183441, 0.000014, 0.000000, 89.999954, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(18981, -1001.768310, -1100.902954, 134.898406, 0.000000, 90.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 16640, "a51", "vgs_shopwall01_128", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -994.072448, -1113.435791, 139.165145, 0.000014, 0.000000, 89.999954, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(19353, -992.839233, -1113.446044, 132.183425, 0.000014, 0.000000, 89.999954, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -989.360961, -1122.296264, 133.225128, 0.000000, 0.000029, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -994.072448, -1117.555297, 133.705139, 0.000014, 0.000000, 89.999954, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -998.922973, -1113.415771, 135.405136, 0.000007, -0.000007, 179.999923, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(18981, -976.768188, -1119.281616, 138.958389, 0.000000, 90.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    mtmtl = CreateDynamicObject(18981, -976.768188, -1094.282470, 138.958389, 0.000000, 90.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    mtmtl = CreateDynamicObject(18981, -1001.747619, -1112.042724, 138.958389, 0.000000, 90.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 18646, "matcolours", "grey-10-percent", 0x00000000);
    mtmtl = CreateDynamicObject(19376, -989.331542, -1108.675903, 139.665130, 0.000007, -0.000007, 179.999923, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 19853, "mihouse1", "redwall1", 0x00000000);
    mtmtl = CreateDynamicObject(19353, -983.475891, -1107.031616, 136.447509, 0.000000, -0.000007, 179.999954, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 5390, "glenpark7_lae", "ganggraf01_LA", 0x00000000);
    mtmtl = CreateDynamicObject(19353, -996.896606, -1117.532348, 132.877517, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 14801, "lee_bdupsmain", "Bdup_graf1", 0x00000000);
    mtmtl = CreateDynamicObject(18981, -987.865234, -1102.450805, 138.951248, 0.000000, 90.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    mtmtl = CreateDynamicObject(18981, -987.865234, -1127.440307, 138.951248, 0.000000, 90.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(mtmtl, 0, 14668, "711c", "gun_ceiling1128", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(1498, -980.997375, -1112.128784, 128.257339, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(14416, -992.573852, -1111.305419, 127.249130, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -983.517211, -1120.614746, 128.460571, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -983.517211, -1125.175292, 128.460571, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -983.517211, -1129.655029, 128.460571, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -989.266174, -1120.614746, 128.460571, 0.000029, 0.000000, 89.999908, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -989.266174, -1125.175292, 128.460571, 0.000029, 0.000000, 89.999908, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -989.266174, -1129.655029, 128.460571, 0.000029, 0.000000, 89.999908, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1712, -983.946411, -1116.744384, 128.479995, -0.000007, -0.000000, -89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1712, -988.766235, -1127.464721, 128.459991, 0.000014, 0.000000, 89.999954, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -989.266174, -1094.411499, 128.460571, 0.000045, 0.000000, 89.999862, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -989.266174, -1098.972045, 128.460571, 0.000045, 0.000000, 89.999862, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -989.266174, -1103.451782, 128.460571, 0.000045, 0.000000, 89.999862, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -983.517211, -1094.569091, 128.460571, 0.000029, 0.000000, 89.999908, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -983.517211, -1099.129638, 128.460571, 0.000029, 0.000000, 89.999908, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -983.517211, -1103.609375, 128.460571, 0.000029, 0.000000, 89.999908, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(14416, -990.853393, -1115.515991, 131.179122, -0.000007, -0.000000, -89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(3675, -988.011047, -1091.744262, 138.866790, 89.999992, 179.999984, -90.000007, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(3675, -984.601013, -1094.225463, 138.848144, 89.999992, 89.999992, -89.999992, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1712, -981.426330, -1110.434448, 134.369979, -0.000007, -0.000000, -89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1347, -981.414794, -1112.877929, 134.953170, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -982.312988, -1112.962890, 134.402145, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(2286, -989.221008, -1111.331909, 136.990264, 0.000007, -15.700004, 89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(19922, -983.694641, -1115.036743, 134.840560, -72.299957, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -989.266174, -1094.411499, 134.390579, 0.000052, 0.000000, 89.999839, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -989.266174, -1098.972045, 134.390579, 0.000052, 0.000000, 89.999839, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -989.266174, -1103.451782, 134.390579, 0.000052, 0.000000, 89.999839, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -983.517211, -1094.569091, 134.390579, 0.000037, 0.000000, 89.999885, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -983.517211, -1099.129638, 134.390579, 0.000037, 0.000000, 89.999885, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -983.517211, -1103.609375, 134.390579, 0.000037, 0.000000, 89.999885, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -983.517211, -1120.614746, 134.380584, 0.000014, 0.000000, 89.999954, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -983.517211, -1125.175292, 134.380584, 0.000014, 0.000000, 89.999954, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -983.517211, -1129.655029, 134.380584, 0.000014, 0.000000, 89.999954, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -989.266174, -1120.614746, 134.380584, 0.000037, 0.000000, 89.999885, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -989.266174, -1125.175292, 134.380584, 0.000037, 0.000000, 89.999885, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1506, -989.266174, -1129.655029, 134.380584, 0.000037, 0.000000, 89.999885, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1368, -988.846374, -1108.101928, 135.090728, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(17969, -983.585205, -1106.382812, 130.343719, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, -987.047424, -1121.567504, 133.623260, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, -987.047424, -1098.806030, 133.623260, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1327, -985.489013, -1090.190917, 129.258178, 0.000007, 10.799996, 86.699996, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -988.738586, -1109.711914, 128.298568, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(2674, -985.644409, -1118.017700, 128.506942, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(2614, -989.209228, -1115.052124, 130.769332, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(2596, -988.972167, -1116.905883, 128.609024, -26.999992, -0.000000, 95.699974, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(850, -987.707397, -1092.014526, 128.567474, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1778, -989.361267, -1105.493530, 128.474304, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1778, -989.361267, -1118.075439, 134.394287, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(2045, -982.451354, -1109.514282, 134.720031, 74.699981, 0.000028, -0.000027, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(2047, -986.387390, -1131.239135, 136.613784, 0.000000, -0.000007, 179.999954, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(2059, -987.746887, -1122.225097, 134.407669, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1369, -984.218566, -1130.424926, 134.967498, -0.000004, -0.000005, -139.099822, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(18661, -991.833007, -1113.340454, 132.002563, -0.000007, -0.000000, -89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(14863, -985.538452, -1122.260864, 129.080902, -0.000001, 0.000007, -14.999998, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(2844, -985.349060, -1099.127807, 128.471099, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(2373, -989.616271, -1101.336059, 128.481674, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, -987.047424, -1098.806030, 138.803268, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1893, -987.047424, -1124.346801, 138.803268, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(11711, -986.514709, -1111.373168, 133.280197, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(2601, -987.507934, -1090.824462, 134.443313, 89.999992, 89.999992, -89.999992, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(2601, -987.267211, -1090.583740, 134.443313, 89.999992, 144.735610, -99.735618, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(2674, -984.884216, -1107.077636, 134.406951, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(3675, -988.011047, -1128.945068, 132.876800, 89.999992, 179.999984, -89.999969, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(3675, -984.601013, -1128.086181, 132.858139, 89.999992, 0.065948, 179.934051, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(11711, -986.512939, -1115.224243, 138.041351, 0.000000, -25.099973, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(19087, -986.332275, -1115.215209, 140.708114, 0.000000, 0.000015, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(2203, -998.592468, -1112.723999, 130.581451, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(11711, -998.605529, -1112.674072, 130.703567, 0.000007, -62.399993, 89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1778, -998.921752, -1113.495483, 130.414291, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(1347, -981.454833, -1109.716552, 128.833160, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(2241, -988.796936, -1090.577514, 134.557800, 89.999992, 144.735610, -99.735618, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(19556, -986.288208, -1091.952636, 134.447555, -85.500022, 179.999923, 134.999923, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(19556, -986.101989, -1091.730102, 134.447555, -85.500022, 179.999923, -130.000061, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(3005, -988.648803, -1095.499755, 134.395660, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(19867, -988.805664, -1112.105590, 134.298507, 0.000000, -17.699991, 0.000000, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(19878, -989.105224, -1105.854858, 134.450607, 0.000007, 0.000000, 89.999977, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(19878, -988.962341, -1105.854858, 134.782241, 0.000000, -118.299957, -0.000030, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(14863, -985.543090, -1112.991821, 134.980911, -0.000006, 0.000003, -61.799983, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(321, -984.082092, -1126.272094, 134.382858, 89.999992, 158.699981, -89.999992, -1, 171, -1, 200.00, 200.00); 
    CreateDynamicObject(3005, -988.628784, -1126.059936, 134.395660, 0.000000, 0.000007, 0.000000, -1, 171, -1, 200.00, 200.00);
}