new Text:TollTD[26];
CreateTollTD()
{
    TollTD[0] = TextDrawCreate(396.000, 144.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[0], 103.000, 222.000);
    TextDrawAlignment(TollTD[0], 1);
    TextDrawColor(TollTD[0], -2139062017);
    TextDrawSetShadow(TollTD[0], 0);
    TextDrawSetOutline(TollTD[0], 0);
    TextDrawBackgroundColor(TollTD[0], 255);
    TextDrawFont(TollTD[0], 4);
    TextDrawSetProportional(TollTD[0], 1);

    TollTD[1] = TextDrawCreate(443.000, 147.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[1], 53.000, 84.000);
    TextDrawAlignment(TollTD[1], 1);
    TextDrawColor(TollTD[1], 12582911);
    TextDrawSetShadow(TollTD[1], 0);
    TextDrawSetOutline(TollTD[1], 0);
    TextDrawBackgroundColor(TollTD[1], 255);
    TextDrawFont(TollTD[1], 4);
    TextDrawSetProportional(TollTD[1], 1);

    TollTD[2] = TextDrawCreate(399.000, 231.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[2], 97.000, 131.000);
    TextDrawAlignment(TollTD[2], 1);
    TextDrawColor(TollTD[2], 421097727);
    TextDrawSetShadow(TollTD[2], 0);
    TextDrawSetOutline(TollTD[2], 0);
    TextDrawBackgroundColor(TollTD[2], 255);
    TextDrawFont(TollTD[2], 4);
    TextDrawSetProportional(TollTD[2], 1);

    TollTD[3] = TextDrawCreate(399.000, 147.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[3], 50.000, 84.000);
    TextDrawAlignment(TollTD[3], 1);
    TextDrawColor(TollTD[3], 421097727);
    TextDrawSetShadow(TollTD[3], 0);
    TextDrawSetOutline(TollTD[3], 0);
    TextDrawBackgroundColor(TollTD[3], 255);
    TextDrawFont(TollTD[3], 4);
    TextDrawSetProportional(TollTD[3], 1);

    TollTD[4] = TextDrawCreate(402.000, 150.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[4], 3.000, 207.000);
    TextDrawAlignment(TollTD[4], 1);
    TextDrawColor(TollTD[4], -1);
    TextDrawSetShadow(TollTD[4], 0);
    TextDrawSetOutline(TollTD[4], 0);
    TextDrawBackgroundColor(TollTD[4], 255);
    TextDrawFont(TollTD[4], 4);
    TextDrawSetProportional(TollTD[4], 1);

    TollTD[5] = TextDrawCreate(490.000, 150.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[5], 3.000, 207.000);
    TextDrawAlignment(TollTD[5], 1);
    TextDrawColor(TollTD[5], -1);
    TextDrawSetShadow(TollTD[5], 0);
    TextDrawSetOutline(TollTD[5], 0);
    TextDrawBackgroundColor(TollTD[5], 255);
    TextDrawFont(TollTD[5], 4);
    TextDrawSetProportional(TollTD[5], 1);

    TollTD[6] = TextDrawCreate(404.000, 150.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[6], 87.000, 3.000);
    TextDrawAlignment(TollTD[6], 1);
    TextDrawColor(TollTD[6], -1);
    TextDrawSetShadow(TollTD[6], 0);
    TextDrawSetOutline(TollTD[6], 0);
    TextDrawBackgroundColor(TollTD[6], 255);
    TextDrawFont(TollTD[6], 4);
    TextDrawSetProportional(TollTD[6], 1);

    TollTD[7] = TextDrawCreate(411.000, 160.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[7], 73.000, 44.000);
    TextDrawAlignment(TollTD[7], 1);
    TextDrawColor(TollTD[7], 1768516095);
    TextDrawSetShadow(TollTD[7], 0);
    TextDrawSetOutline(TollTD[7], 0);
    TextDrawBackgroundColor(TollTD[7], 255);
    TextDrawFont(TollTD[7], 4);
    TextDrawSetProportional(TollTD[7], 1);

    TollTD[8] = TextDrawCreate(452.000, 235.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[8], 35.000, 65.000);
    TextDrawAlignment(TollTD[8], 1);
    TextDrawColor(TollTD[8], -2686721);
    TextDrawSetShadow(TollTD[8], 0);
    TextDrawSetOutline(TollTD[8], 0);
    TextDrawBackgroundColor(TollTD[8], 255);
    TextDrawFont(TollTD[8], 4);
    TextDrawSetProportional(TollTD[8], 1);

    TollTD[9] = TextDrawCreate(404.000, 354.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[9], 87.000, 3.000);
    TextDrawAlignment(TollTD[9], 1);
    TextDrawColor(TollTD[9], -1);
    TextDrawSetShadow(TollTD[9], 0);
    TextDrawSetOutline(TollTD[9], 0);
    TextDrawBackgroundColor(TollTD[9], 255);
    TextDrawFont(TollTD[9], 4);
    TextDrawSetProportional(TollTD[9], 1);

    TollTD[10] = TextDrawCreate(452.000, 298.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[10], 35.000, 2.000);
    TextDrawAlignment(TollTD[10], 1);
    TextDrawColor(TollTD[10], -65281);
    TextDrawSetShadow(TollTD[10], 0);
    TextDrawSetOutline(TollTD[10], 0);
    TextDrawBackgroundColor(TollTD[10], 255);
    TextDrawFont(TollTD[10], 4);
    TextDrawSetProportional(TollTD[10], 1);

    TollTD[11] = TextDrawCreate(402.000, 235.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[11], 47.000, 122.000);
    TextDrawAlignment(TollTD[11], 1);
    TextDrawColor(TollTD[11], -2686721);
    TextDrawSetShadow(TollTD[11], 0);
    TextDrawSetOutline(TollTD[11], 0);
    TextDrawBackgroundColor(TollTD[11], 255);
    TextDrawFont(TollTD[11], 4);
    TextDrawSetProportional(TollTD[11], 1);

    TollTD[12] = TextDrawCreate(407.000, 248.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[12], 38.000, 53.000);
    TextDrawAlignment(TollTD[12], 1);
    TextDrawColor(TollTD[12], 421097727);
    TextDrawSetShadow(TollTD[12], 0);
    TextDrawSetOutline(TollTD[12], 0);
    TextDrawBackgroundColor(TollTD[12], 255);
    TextDrawFont(TollTD[12], 4);
    TextDrawSetProportional(TollTD[12], 1);

    TollTD[13] = TextDrawCreate(415.000, 267.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[13], 23.000, 5.000);
    TextDrawAlignment(TollTD[13], 1);
    TextDrawColor(TollTD[13], 255);
    TextDrawSetShadow(TollTD[13], 0);
    TextDrawSetOutline(TollTD[13], 0);
    TextDrawBackgroundColor(TollTD[13], 255);
    TextDrawFont(TollTD[13], 4);
    TextDrawSetProportional(TollTD[13], 1);

    TollTD[14] = TextDrawCreate(417.000, 269.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[14], 19.000, 6.000);
    TextDrawAlignment(TollTD[14], 1);
    TextDrawColor(TollTD[14], -1);
    TextDrawSetShadow(TollTD[14], 0);
    TextDrawSetOutline(TollTD[14], 0);
    TextDrawBackgroundColor(TollTD[14], 255);
    TextDrawFont(TollTD[14], 4);
    TextDrawSetProportional(TollTD[14], 1);

    TollTD[15] = TextDrawCreate(418.000, 270.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[15], 17.000, 0.500);
    TextDrawAlignment(TollTD[15], 1);
    TextDrawColor(TollTD[15], -2139062017);
    TextDrawSetShadow(TollTD[15], 0);
    TextDrawSetOutline(TollTD[15], 0);
    TextDrawBackgroundColor(TollTD[15], 255);
    TextDrawFont(TollTD[15], 4);
    TextDrawSetProportional(TollTD[15], 1);

    TollTD[16] = TextDrawCreate(425.500, 238.000, "Tanda terima");
    TextDrawLetterSize(TollTD[16], 0.158, 0.799);
    TextDrawAlignment(TollTD[16], 2);
    TextDrawColor(TollTD[16], -1);
    TextDrawSetShadow(TollTD[16], 1);
    TextDrawSetOutline(TollTD[16], 0);
    TextDrawBackgroundColor(TollTD[16], 255);
    TextDrawFont(TollTD[16], 1);
    TextDrawSetProportional(TollTD[16], 1);

    TollTD[17] = TextDrawCreate(454.000, 258.000, "LD_BEAT:chit");
    TextDrawTextSize(TollTD[17], 32.000, 40.000);
    TextDrawAlignment(TollTD[17], 1);
    TextDrawColor(TollTD[17], 255);
    TextDrawSetShadow(TollTD[17], 0);
    TextDrawSetOutline(TollTD[17], 0);
    TextDrawBackgroundColor(TollTD[17], 255);
    TextDrawFont(TollTD[17], 4);
    TextDrawSetProportional(TollTD[17], 1);

    TollTD[18] = TextDrawCreate(455.000, 259.000, "LD_BEAT:chit");
    TextDrawTextSize(TollTD[18], 30.000, 38.000);
    TextDrawAlignment(TollTD[18], 1);
    TextDrawColor(TollTD[18], -2686721);
    TextDrawSetShadow(TollTD[18], 0);
    TextDrawSetOutline(TollTD[18], 0);
    TextDrawBackgroundColor(TollTD[18], 255);
    TextDrawFont(TollTD[18], 4);
    TextDrawSetProportional(TollTD[18], 1);

    TollTD[19] = TextDrawCreate(458.000, 263.000, "LD_BEAT:chit");
    TextDrawTextSize(TollTD[19], 24.000, 30.000);
    TextDrawAlignment(TollTD[19], 1);
    TextDrawColor(TollTD[19], 255);
    TextDrawSetShadow(TollTD[19], 0);
    TextDrawSetOutline(TollTD[19], 0);
    TextDrawBackgroundColor(TollTD[19], 255);
    TextDrawFont(TollTD[19], 4);
    TextDrawSetProportional(TollTD[19], 1);
    TextDrawSetSelectable(TollTD[19], 1);

    TollTD[20] = TextDrawCreate(470.000, 274.000, "TAP");
    TextDrawLetterSize(TollTD[20], 0.128, 0.699);
    TextDrawAlignment(TollTD[20], 2);
    TextDrawColor(TollTD[20], -2686721);
    TextDrawSetShadow(TollTD[20], 1);
    TextDrawSetOutline(TollTD[20], 1);
    TextDrawBackgroundColor(TollTD[20], 0);
    TextDrawFont(TollTD[20], 1);
    TextDrawSetProportional(TollTD[20], 1);

    TollTD[21] = TextDrawCreate(455.000, 238.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[21], 29.000, 20.000);
    TextDrawAlignment(TollTD[21], 1);
    TextDrawColor(TollTD[21], 255);
    TextDrawSetShadow(TollTD[21], 0);
    TextDrawSetOutline(TollTD[21], 0);
    TextDrawBackgroundColor(TollTD[21], 255);
    TextDrawFont(TollTD[21], 4);
    TextDrawSetProportional(TollTD[21], 1);

    TollTD[22] = TextDrawCreate(456.000, 239.000, "LD_BUM:blkdot");
    TextDrawTextSize(TollTD[22], 27.000, 18.000);
    TextDrawAlignment(TollTD[22], 1);
    TextDrawColor(TollTD[22], 1768516095);
    TextDrawSetShadow(TollTD[22], 0);
    TextDrawSetOutline(TollTD[22], 0);
    TextDrawBackgroundColor(TollTD[22], 255);
    TextDrawFont(TollTD[22], 4);
    TextDrawSetProportional(TollTD[22], 1);

    TollTD[23] = TextDrawCreate(470.000, 244.000, "$80");
    TextDrawLetterSize(TollTD[23], 0.128, 0.699);
    TextDrawAlignment(TollTD[23], 2);
    TextDrawColor(TollTD[23], 3604735);
    TextDrawSetShadow(TollTD[23], 1);
    TextDrawSetOutline(TollTD[23], 0);
    TextDrawBackgroundColor(TollTD[23], 0);
    TextDrawFont(TollTD[23], 1);
    TextDrawSetProportional(TollTD[23], 1);

    TollTD[24] = TextDrawCreate(448.000, 167.000, "GERBANG TOLL");
    TextDrawLetterSize(TollTD[24], 0.179, 0.999);
    TextDrawAlignment(TollTD[24], 2);
    TextDrawColor(TollTD[24], -7601921);
    TextDrawSetShadow(TollTD[24], 1);
    TextDrawSetOutline(TollTD[24], 6);
    TextDrawBackgroundColor(TollTD[24], 0);
    TextDrawFont(TollTD[24], 1);
    TextDrawSetProportional(TollTD[24], 1);

    TollTD[25] = TextDrawCreate(448.000, 179.000, "ARIVENA");
    TextDrawLetterSize(TollTD[25], 0.298, 1.799);
    TextDrawAlignment(TollTD[25], 2);
    TextDrawColor(TollTD[25], -7601921);
    TextDrawSetShadow(TollTD[25], 1);
    TextDrawSetOutline(TollTD[25], 6);
    TextDrawBackgroundColor(TollTD[25], 0);
    TextDrawFont(TollTD[25], 1);
    TextDrawSetProportional(TollTD[25], 1);
}

ShowTollTD(playerid)
{
    HideHBETD(playerid);
    HideServerNameTD(playerid);
    for(new x; x < 26; x++)
    {
        TextDrawShowForPlayer(playerid, TollTD[x]);
    }
}

HideTollTD(playerid)
{
    for(new x; x < 26; x++)
    {
        TextDrawHideForPlayer(playerid, TollTD[x]);
    }
    ShowHBETD(playerid);
    ShowServerNameTD(playerid);
    CancelSelectTextDraw(playerid);
}