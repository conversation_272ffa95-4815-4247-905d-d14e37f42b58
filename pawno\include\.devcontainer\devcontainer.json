{
    // See https://containers.dev/implementors/json_reference/
    // User defined settings
    "containerEnv": {
        "TZ": "Europe/London"
    },
    // Fixed settings
    "name": "samp-plugin-sky-dev",
    "postCreateCommand": "~/.ssh.sh",
    "dockerComposeFile": "docker-compose.yml",
    "overrideCommand": true,
    "service": "vscode",
    "workspaceFolder": "/workspace",
    "mounts": [
        // Source code
        {
            "source": "../",
            "target": "/workspace",
            "type": "bind"
        },
        // Zsh commands history persistence
        {
            "source": "${localEnv:HOME}/.zsh_history",
            "target": "/root/.zsh_history",
            "type": "bind"
        },
        // Git configuration file
        {
            "source": "${localEnv:HOME}/.gitconfig",
            "target": "/root/.gitconfig",
            "type": "bind"
        },
        // SSH directory for Linux, OSX and WSL
        // On Linux and OSX, a symlink /mnt/ssh <-> ~/.ssh is
        // created in the container. On Windows, files are copied
        // from /mnt/ssh to ~/.ssh to fix permissions.
        {
            "source": "${localEnv:HOME}/.ssh",
            "target": "/mnt/ssh",
            "type": "bind"
        },
        // Docker socket to access the host Docker server
        {
            "source": "/var/run/docker.sock",
            "target": "/var/run/docker.sock",
            "type": "bind"
        }
    ],
    "customizations": {
        "vscode": {
            "extensions": [
                "IBM.output-colorizer",
                "eamodio.gitlens",
                "mhutchie.git-graph",
                "davidanson.vscode-markdownlint",
                "shardulm94.trailing-spaces",
                "alefragnani.Bookmarks",
                "Gruntfuggly.todo-tree",
                "mohsen1.prettify-json",
                "quicktype.quicktype",
                "spikespaz.vscode-smoothtype",
                "stkb.rewrap",
                "vscode-icons-team.vscode-icons",
                "ms-azuretools.vscode-docker",
                "github.copilot",
                "ms-vscode.cpptools-extension-pack",
                "ms-vscode.cmake-tools"
            ],
            "settings": {
                // General settings
                "files.eol": "\n",
                // Docker
                "remote.extensionKind": {
                    "ms-azuretools.vscode-docker": "workspace"
                }
            }
        }
    }
}