new Text:RadialTD[58],
	Text:RadialFashionTD[28],
	Text:RadialVehTD[44],
	Text:RadialCardTD[26];

CreateRadialTD()
{
    RadialTD[0] = TextDrawCreate(95.000, 194.000, "LD_BUM:blkdot"); //PhoneB
	TextDrawTextSize(RadialTD[0], 34.000, 35.000);
	TextDrawAlignment(RadialTD[0], 1);
	TextDrawColor(RadialTD[0], 0x00000066);
	TextDrawSetShadow(RadialTD[0], 0);
	TextDrawSetOutline(RadialTD[0], 0);
	TextDrawBackgroundColor(RadialTD[0], 255);
	TextDrawFont(RadialTD[0], 4);
	TextDrawSetProportional(RadialTD[0], 1);
	TextDrawSetSelectable(RadialTD[0], 1);

	RadialTD[1] = TextDrawCreate(141.000, 194.000, "LD_BUM:blkdot"); //InvB
	TextDrawTextSize(RadialTD[1], 34.000, 35.000);
	TextDrawAlignment(RadialTD[1], 1);
	TextDrawColor(RadialTD[1], 0x00000066);
	TextDrawSetShadow(RadialTD[1], 0);
	TextDrawSetOutline(RadialTD[1], 0);
	TextDrawBackgroundColor(RadialTD[1], 255);
	TextDrawFont(RadialTD[1], 4);
	TextDrawSetProportional(RadialTD[1], 1);
	TextDrawSetSelectable(RadialTD[1], 1);

	RadialTD[2] = TextDrawCreate(186.000, 194.000, "LD_BUM:blkdot"); //ClothB
	TextDrawTextSize(RadialTD[2], 34.000, 35.000);
	TextDrawAlignment(RadialTD[2], 1);
	TextDrawColor(RadialTD[2], 0x00000066);
	TextDrawSetShadow(RadialTD[2], 0);
	TextDrawSetOutline(RadialTD[2], 0);
	TextDrawBackgroundColor(RadialTD[2], 255);
	TextDrawFont(RadialTD[2], 4);
	TextDrawSetProportional(RadialTD[2], 1);
	TextDrawSetSelectable(RadialTD[2], 1);

	RadialTD[3] = TextDrawCreate(232.000, 194.000, "LD_BUM:blkdot"); //DokumenB
	TextDrawTextSize(RadialTD[3], 34.000, 35.000);
	TextDrawAlignment(RadialTD[3], 1);
	TextDrawColor(RadialTD[3], 0x00000066);
	TextDrawSetShadow(RadialTD[3], 0);
	TextDrawSetOutline(RadialTD[3], 0);
	TextDrawBackgroundColor(RadialTD[3], 255);
	TextDrawFont(RadialTD[3], 4);
	TextDrawSetProportional(RadialTD[3], 1);
	TextDrawSetSelectable(RadialTD[3], 1);

	RadialTD[4] = TextDrawCreate(117.000, 237.000, "LD_BUM:blkdot"); //InvoiceB
	TextDrawTextSize(RadialTD[4], 34.000, 35.000);
	TextDrawAlignment(RadialTD[4], 1);
	TextDrawColor(RadialTD[4], 0x00000066);
	TextDrawSetShadow(RadialTD[4], 0);
	TextDrawSetOutline(RadialTD[4], 0);
	TextDrawBackgroundColor(RadialTD[4], 255);
	TextDrawFont(RadialTD[4], 4);
	TextDrawSetProportional(RadialTD[4], 1);
	TextDrawSetSelectable(RadialTD[4], 1);

	RadialTD[5] = TextDrawCreate(163.000, 237.000, "LD_BUM:blkdot"); //VehicleB
	TextDrawTextSize(RadialTD[5], 34.000, 35.000);
	TextDrawAlignment(RadialTD[5], 1);
	TextDrawColor(RadialTD[5], 0x00000066);
	TextDrawSetShadow(RadialTD[5], 0);
	TextDrawSetOutline(RadialTD[5], 0);
	TextDrawBackgroundColor(RadialTD[5], 255);
	TextDrawFont(RadialTD[5], 4);
	TextDrawSetProportional(RadialTD[5], 1);
	TextDrawSetSelectable(RadialTD[5], 1);

	RadialTD[6] = TextDrawCreate(208.000, 237.000, "LD_BUM:blkdot"); //CardB
	TextDrawTextSize(RadialTD[6], 34.000, 35.000);
	TextDrawAlignment(RadialTD[6], 1);
	TextDrawColor(RadialTD[6], 0x00000066);
	TextDrawSetShadow(RadialTD[6], 0);
	TextDrawSetOutline(RadialTD[6], 0);
	TextDrawBackgroundColor(RadialTD[6], 255);
	TextDrawFont(RadialTD[6], 4);
	TextDrawSetProportional(RadialTD[6], 1);
	TextDrawSetSelectable(RadialTD[6], 1);

	RadialTD[7] = TextDrawCreate(167.000, 278.000, "LD_BEAT:chit"); //CloseB
	TextDrawTextSize(RadialTD[7], 30.000, 30.000);
	TextDrawAlignment(RadialTD[7], 1);
	TextDrawColor(RadialTD[7], 0x00000066);
	TextDrawSetShadow(RadialTD[7], 0);
	TextDrawSetOutline(RadialTD[7], 0);
	TextDrawBackgroundColor(RadialTD[7], 255);
	TextDrawFont(RadialTD[7], 4);
	TextDrawSetProportional(RadialTD[7], 1);
	TextDrawSetSelectable(RadialTD[7], 1);

	RadialTD[8] = TextDrawCreate(105.000, 198.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[8], 13.000, 25.000);
	TextDrawAlignment(RadialTD[8], 1);
	TextDrawColor(RadialTD[8], 0xffffffcc);
	TextDrawSetShadow(RadialTD[8], 0);
	TextDrawSetOutline(RadialTD[8], 0);
	TextDrawBackgroundColor(RadialTD[8], 255);
	TextDrawFont(RadialTD[8], 4);
	TextDrawSetProportional(RadialTD[8], 1);

	RadialTD[9] = TextDrawCreate(106.000, 199.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[9], 11.000, 23.000);
	TextDrawAlignment(RadialTD[9], 1);
	TextDrawColor(RadialTD[9], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[9], 0);
	TextDrawSetOutline(RadialTD[9], 0);
	TextDrawBackgroundColor(RadialTD[9], 255);
	TextDrawFont(RadialTD[9], 4);
	TextDrawSetProportional(RadialTD[9], 1);

	RadialTD[10] = TextDrawCreate(110.000, 216.000, "LD_BEAT:chit");
	TextDrawTextSize(RadialTD[10], 3.000, 4.000);
	TextDrawAlignment(RadialTD[10], 1);
	TextDrawColor(RadialTD[10], 0xffffffcc);
	TextDrawSetShadow(RadialTD[10], 0);
	TextDrawSetOutline(RadialTD[10], 0);
	TextDrawBackgroundColor(RadialTD[10], 255);
	TextDrawFont(RadialTD[10], 4);
	TextDrawSetProportional(RadialTD[10], 1);

	RadialTD[11] = TextDrawCreate(147.000, 191.000, "LD_BEAT:chit");
	TextDrawTextSize(RadialTD[11], 21.000, 29.000);
	TextDrawAlignment(RadialTD[11], 1);
	TextDrawColor(RadialTD[11], 0xffffffcc);
	TextDrawSetShadow(RadialTD[11], 0);
	TextDrawSetOutline(RadialTD[11], 0);
	TextDrawBackgroundColor(RadialTD[11], 255);
	TextDrawFont(RadialTD[11], 4);
	TextDrawSetProportional(RadialTD[11], 1);

	RadialTD[12] = TextDrawCreate(149.000, 192.000, "LD_BEAT:chit");
	TextDrawTextSize(RadialTD[12], 17.000, 30.000);
	TextDrawAlignment(RadialTD[12], 1);
	TextDrawColor(RadialTD[12], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[12], 0);
	TextDrawSetOutline(RadialTD[12], 0);
	TextDrawBackgroundColor(RadialTD[12], 255);
	TextDrawFont(RadialTD[12], 4);
	TextDrawSetProportional(RadialTD[12], 1);

	RadialTD[13] = TextDrawCreate(148.000, 201.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[13], 20.000, 24.000);
	TextDrawAlignment(RadialTD[13], 1);
	TextDrawColor(RadialTD[13], 0xffffffcc);
	TextDrawSetShadow(RadialTD[13], 0);
	TextDrawSetOutline(RadialTD[13], 0);
	TextDrawBackgroundColor(RadialTD[13], 255);
	TextDrawFont(RadialTD[13], 4);
	TextDrawSetProportional(RadialTD[13], 1);

	RadialTD[14] = TextDrawCreate(150.000, 201.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[14], 2.000, 10.000);
	TextDrawAlignment(RadialTD[14], 1);
	TextDrawColor(RadialTD[14], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[14], 0);
	TextDrawSetOutline(RadialTD[14], 0);
	TextDrawBackgroundColor(RadialTD[14], 255);
	TextDrawFont(RadialTD[14], 4);
	TextDrawSetProportional(RadialTD[14], 1);

	RadialTD[15] = TextDrawCreate(150.000, 209.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[15], 16.000, 3.000);
	TextDrawAlignment(RadialTD[15], 1);
	TextDrawColor(RadialTD[15], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[15], 0);
	TextDrawSetOutline(RadialTD[15], 0);
	TextDrawBackgroundColor(RadialTD[15], 255);
	TextDrawFont(RadialTD[15], 4);
	TextDrawSetProportional(RadialTD[15], 1);

	RadialTD[16] = TextDrawCreate(164.000, 201.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[16], 2.000, 10.000);
	TextDrawAlignment(RadialTD[16], 1);
	TextDrawColor(RadialTD[16], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[16], 0);
	TextDrawSetOutline(RadialTD[16], 0);
	TextDrawBackgroundColor(RadialTD[16], 255);
	TextDrawFont(RadialTD[16], 4);
	TextDrawSetProportional(RadialTD[16], 1);

	RadialTD[17] = TextDrawCreate(146.000, 213.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[17], 3.000, 10.000);
	TextDrawAlignment(RadialTD[17], 1);
	TextDrawColor(RadialTD[17], 0xffffffcc);
	TextDrawSetShadow(RadialTD[17], 0);
	TextDrawSetOutline(RadialTD[17], 0);
	TextDrawBackgroundColor(RadialTD[17], 255);
	TextDrawFont(RadialTD[17], 4);
	TextDrawSetProportional(RadialTD[17], 1);

	RadialTD[18] = TextDrawCreate(166.000, 213.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[18], 3.000, 10.000);
	TextDrawAlignment(RadialTD[18], 1);
	TextDrawColor(RadialTD[18], 0xffffffcc);
	TextDrawSetShadow(RadialTD[18], 0);
	TextDrawSetOutline(RadialTD[18], 0);
	TextDrawBackgroundColor(RadialTD[18], 255);
	TextDrawFont(RadialTD[18], 4);
	TextDrawSetProportional(RadialTD[18], 1);

	RadialTD[19] = TextDrawCreate(157.000, 207.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[19], 2.000, 7.000);
	TextDrawAlignment(RadialTD[19], 1);
	TextDrawColor(RadialTD[19], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[19], 0);
	TextDrawSetOutline(RadialTD[19], 0);
	TextDrawBackgroundColor(RadialTD[19], 255);
	TextDrawFont(RadialTD[19], 4);
	TextDrawSetProportional(RadialTD[19], 1);

	RadialTD[20] = TextDrawCreate(203.000, 198.000, "U");
	TextDrawLetterSize(RadialTD[20], 0.388, 1.100);
	TextDrawTextSize(RadialTD[20], 400.000, 17.000);
	TextDrawAlignment(RadialTD[20], 2);
	TextDrawColor(RadialTD[20], 0xffffffcc);
	TextDrawSetShadow(RadialTD[20], 0);
	TextDrawSetOutline(RadialTD[20], 0);
	TextDrawBackgroundColor(RadialTD[20], 255);
	TextDrawFont(RadialTD[20], 1);
	TextDrawSetProportional(RadialTD[20], 1);

	RadialTD[21] = TextDrawCreate(208.000, 195.000, "-");
	TextDrawLetterSize(RadialTD[21], 0.388, 1.100);
	TextDrawTextSize(RadialTD[21], 400.000, 17.000);
	TextDrawAlignment(RadialTD[21], 2);
	TextDrawColor(RadialTD[21], 0xffffffcc);
	TextDrawSetShadow(RadialTD[21], 0);
	TextDrawSetOutline(RadialTD[21], 0);
	TextDrawBackgroundColor(RadialTD[21], 255);
	TextDrawFont(RadialTD[21], 1);
	TextDrawSetProportional(RadialTD[21], 1);

	RadialTD[22] = TextDrawCreate(198.000, 195.000, "-");
	TextDrawLetterSize(RadialTD[22], 0.388, 1.100);
	TextDrawTextSize(RadialTD[22], 400.000, 17.000);
	TextDrawAlignment(RadialTD[22], 2);
	TextDrawColor(RadialTD[22], 0xffffffcc);
	TextDrawSetShadow(RadialTD[22], 0);
	TextDrawSetOutline(RadialTD[22], 0);
	TextDrawBackgroundColor(RadialTD[22], 255);
	TextDrawFont(RadialTD[22], 1);
	TextDrawSetProportional(RadialTD[22], 1);

	RadialTD[23] = TextDrawCreate(195.000, 199.000, "/");
	TextDrawLetterSize(RadialTD[23], 0.398, 0.799);
	TextDrawTextSize(RadialTD[23], 400.000, 17.000);
	TextDrawAlignment(RadialTD[23], 2);
	TextDrawColor(RadialTD[23], 0xffffffcc);
	TextDrawSetShadow(RadialTD[23], 0);
	TextDrawSetOutline(RadialTD[15], 0);
	TextDrawBackgroundColor(RadialTD[23], 255);
	TextDrawFont(RadialTD[23], 1);
	TextDrawSetProportional(RadialTD[23], 1);

	RadialTD[24] = TextDrawCreate(211.000, 199.000, "\\");
	TextDrawLetterSize(RadialTD[24], 0.398, 0.799);
	TextDrawTextSize(RadialTD[24], 400.000, 17.000);
	TextDrawAlignment(RadialTD[24], 2);
	TextDrawColor(RadialTD[24], 0xffffffcc);
	TextDrawSetShadow(RadialTD[24], 0);
	TextDrawSetOutline(RadialTD[24], 0);
	TextDrawBackgroundColor(RadialTD[24], 255);
	TextDrawFont(RadialTD[24], 1);
	TextDrawSetProportional(RadialTD[24], 1);

	RadialTD[25] = TextDrawCreate(213.000, 203.000, "\\");
	TextDrawLetterSize(RadialTD[25], 0.398, 0.699);
	TextDrawTextSize(RadialTD[25], 400.000, 17.000);
	TextDrawAlignment(RadialTD[25], 2);
	TextDrawColor(RadialTD[25], 0xffffffcc);
	TextDrawSetShadow(RadialTD[25], 0);
	TextDrawSetOutline(RadialTD[25], 0);
	TextDrawBackgroundColor(RadialTD[25], 255);
	TextDrawFont(RadialTD[25], 1);
	TextDrawSetProportional(RadialTD[25], 1);

	RadialTD[26] = TextDrawCreate(193.000, 203.000, "/");
	TextDrawLetterSize(RadialTD[26], 0.398, 0.699);
	TextDrawTextSize(RadialTD[26], 400.000, 17.000);
	TextDrawAlignment(RadialTD[26], 2);
	TextDrawColor(RadialTD[26], 0xffffffcc);
	TextDrawSetShadow(RadialTD[26], 0);
	TextDrawSetOutline(RadialTD[26], 0);
	TextDrawBackgroundColor(RadialTD[26], 255);
	TextDrawFont(RadialTD[26], 1);
	TextDrawSetProportional(RadialTD[26], 1);

	RadialTD[27] = TextDrawCreate(193.000, 208.000, "\\");
	TextDrawLetterSize(RadialTD[27], 0.469, 0.398);
	TextDrawTextSize(RadialTD[27], 400.000, 17.000);
	TextDrawAlignment(RadialTD[27], 2);
	TextDrawColor(RadialTD[27], 0xffffffcc);
	TextDrawSetShadow(RadialTD[27], 0);
	TextDrawSetOutline(RadialTD[27], 0);
	TextDrawBackgroundColor(RadialTD[27], 255);
	TextDrawFont(RadialTD[27], 1);
	TextDrawSetProportional(RadialTD[27], 1);

	RadialTD[28] = TextDrawCreate(197.000, 205.000, "/");
	TextDrawLetterSize(RadialTD[28], 0.398, 0.699);
	TextDrawTextSize(RadialTD[28], 400.000, 17.000);
	TextDrawAlignment(RadialTD[28], 2);
	TextDrawColor(RadialTD[28], 0xffffffcc);
	TextDrawSetShadow(RadialTD[28], 0);
	TextDrawSetOutline(RadialTD[28], 0);
	TextDrawBackgroundColor(RadialTD[28], 255);
	TextDrawFont(RadialTD[28], 1);
	TextDrawSetProportional(RadialTD[28], 1);

	RadialTD[29] = TextDrawCreate(198.000, 202.000, "I");
	TextDrawLetterSize(RadialTD[29], 0.246, 2.398);
	TextDrawTextSize(RadialTD[29], 400.000, 17.000);
	TextDrawAlignment(RadialTD[29], 2);
	TextDrawColor(RadialTD[29], 0xffffffcc);
	TextDrawSetShadow(RadialTD[29], 0);
	TextDrawSetOutline(RadialTD[29], 0);
	TextDrawBackgroundColor(RadialTD[29], 255);
	TextDrawFont(RadialTD[29], 1);
	TextDrawSetProportional(RadialTD[29], 1);

	RadialTD[30] = TextDrawCreate(213.000, 208.000, "/");
	TextDrawLetterSize(RadialTD[30], 0.469, 0.398);
	TextDrawTextSize(RadialTD[30], 400.000, 17.000);
	TextDrawAlignment(RadialTD[30], 2);
	TextDrawColor(RadialTD[30], 0xffffffcc);
	TextDrawSetShadow(RadialTD[30], 0);
	TextDrawSetOutline(RadialTD[30], 0);
	TextDrawBackgroundColor(RadialTD[30], 255);
	TextDrawFont(RadialTD[30], 1);
	TextDrawSetProportional(RadialTD[30], 1);

	RadialTD[31] = TextDrawCreate(209.000, 205.000, "\\");
	TextDrawLetterSize(RadialTD[31], 0.398, 0.699);
	TextDrawTextSize(RadialTD[31], 400.000, 17.000);
	TextDrawAlignment(RadialTD[31], 2);
	TextDrawColor(RadialTD[31], 0xffffffcc);
	TextDrawSetShadow(RadialTD[31], 0);
	TextDrawSetOutline(RadialTD[31], 0);
	TextDrawBackgroundColor(RadialTD[31], 255);
	TextDrawFont(RadialTD[31], 1);
	TextDrawSetProportional(RadialTD[31], 1);

	RadialTD[32] = TextDrawCreate(207.000, 202.000, "I");
	TextDrawLetterSize(RadialTD[32], 0.229, 2.398);
	TextDrawTextSize(RadialTD[32], 400.000, 17.000);
	TextDrawAlignment(RadialTD[32], 2);
	TextDrawColor(RadialTD[32], 0xffffffcc);
	TextDrawSetShadow(RadialTD[32], 0);
	TextDrawSetOutline(RadialTD[32], 0);
	TextDrawBackgroundColor(RadialTD[32], 255);
	TextDrawFont(RadialTD[32], 1);
	TextDrawSetProportional(RadialTD[32], 1);

	RadialTD[33] = TextDrawCreate(241.000, 198.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[33], 17.000, 26.000);
	TextDrawAlignment(RadialTD[33], 1);
	TextDrawColor(RadialTD[33], 0xffffffcc);
	TextDrawSetShadow(RadialTD[33], 0);
	TextDrawSetOutline(RadialTD[33], 0);
	TextDrawBackgroundColor(RadialTD[33], 255);
	TextDrawFont(RadialTD[33], 4);
	TextDrawSetProportional(RadialTD[33], 1);

	RadialTD[34] = TextDrawCreate(253.000, 198.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[34], 9.000, 6.000);
	TextDrawAlignment(RadialTD[34], 1);
	TextDrawColor(RadialTD[34], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[34], 0);
	TextDrawSetOutline(RadialTD[34], 0);
	TextDrawBackgroundColor(RadialTD[34], 255);
	TextDrawFont(RadialTD[34], 4);
	TextDrawSetProportional(RadialTD[34], 1);

	RadialTD[35] = TextDrawCreate(251.000, 195.000, "\\");
	TextDrawLetterSize(RadialTD[35], 0.509, 1.299);
	TextDrawAlignment(RadialTD[35], 1);
	TextDrawColor(RadialTD[35], 0xffffffcc);
	TextDrawSetShadow(RadialTD[35], 0);
	TextDrawSetOutline(RadialTD[35], 0);
	TextDrawBackgroundColor(RadialTD[35], 150);
	TextDrawFont(RadialTD[35], 2);
	TextDrawSetProportional(RadialTD[35], 1);

	RadialTD[36] = TextDrawCreate(244.000, 208.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[36], 12.000, 2.000);
	TextDrawAlignment(RadialTD[36], 1);
	TextDrawColor(RadialTD[36], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[36], 0);
	TextDrawSetOutline(RadialTD[36], 0);
	TextDrawBackgroundColor(RadialTD[36], 255);
	TextDrawFont(RadialTD[36], 4);
	TextDrawSetProportional(RadialTD[36], 1);

	RadialTD[37] = TextDrawCreate(244.000, 212.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[37], 12.000, 2.000);
	TextDrawAlignment(RadialTD[37], 1);
	TextDrawColor(RadialTD[37], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[37], 0);
	TextDrawSetOutline(RadialTD[37], 0);
	TextDrawBackgroundColor(RadialTD[37], 255);
	TextDrawFont(RadialTD[37], 4);
	TextDrawSetProportional(RadialTD[37], 1);

	RadialTD[38] = TextDrawCreate(244.000, 216.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[38], 12.000, 2.000);
	TextDrawAlignment(RadialTD[38], 1);
	TextDrawColor(RadialTD[38], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[38], 0);
	TextDrawSetOutline(RadialTD[38], 0);
	TextDrawBackgroundColor(RadialTD[38], 255);
	TextDrawFont(RadialTD[38], 4);
	TextDrawSetProportional(RadialTD[38], 1);

	RadialTD[39] = TextDrawCreate(126.000, 241.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[39], 17.000, 26.000);
	TextDrawAlignment(RadialTD[39], 1);
	TextDrawColor(RadialTD[39], 0xffffffcc);
	TextDrawSetShadow(RadialTD[39], 0);
	TextDrawSetOutline(RadialTD[39], 0);
	TextDrawBackgroundColor(RadialTD[39], 255);
	TextDrawFont(RadialTD[39], 4);
	TextDrawSetProportional(RadialTD[39], 1);

	RadialTD[40] = TextDrawCreate(172.000, 238.000, "-");
	TextDrawLetterSize(RadialTD[40], 1.090, 1.199);
	TextDrawAlignment(RadialTD[40], 1);
	TextDrawColor(RadialTD[40], 0xffffffcc);
	TextDrawSetShadow(RadialTD[40], 0);
	TextDrawSetOutline(RadialTD[40], 0);
	TextDrawBackgroundColor(RadialTD[40], 150);
	TextDrawFont(RadialTD[40], 1);
	TextDrawSetProportional(RadialTD[40], 1);

	RadialTD[41] = TextDrawCreate(131.000, 242.000, "$");
	TextDrawLetterSize(RadialTD[41], 0.270, 1.199);
	TextDrawAlignment(RadialTD[41], 1);
	TextDrawColor(RadialTD[41], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[41], 0);
	TextDrawSetOutline(RadialTD[41], 0);
	TextDrawBackgroundColor(RadialTD[41], 150);
	TextDrawFont(RadialTD[41], 1);
	TextDrawSetProportional(RadialTD[41], 1);

	RadialTD[42] = TextDrawCreate(128.000, 254.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[42], 13.000, 2.000);
	TextDrawAlignment(RadialTD[42], 1);
	TextDrawColor(RadialTD[42], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[42], 0);
	TextDrawSetOutline(RadialTD[42], 0);
	TextDrawBackgroundColor(RadialTD[42], 255);
	TextDrawFont(RadialTD[42], 4);
	TextDrawSetProportional(RadialTD[42], 1);

	RadialTD[43] = TextDrawCreate(128.000, 258.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[43], 13.000, 2.000);
	TextDrawAlignment(RadialTD[43], 1);
	TextDrawColor(RadialTD[43], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[43], 0);
	TextDrawSetOutline(RadialTD[43], 0);
	TextDrawBackgroundColor(RadialTD[43], 255);
	TextDrawFont(RadialTD[43], 4);
	TextDrawSetProportional(RadialTD[43], 1);

	RadialTD[44] = TextDrawCreate(128.000, 262.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[44], 13.000, 2.000);
	TextDrawAlignment(RadialTD[44], 1);
	TextDrawColor(RadialTD[44], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[44], 0);
	TextDrawSetOutline(RadialTD[44], 0);
	TextDrawBackgroundColor(RadialTD[44], 255);
	TextDrawFont(RadialTD[44], 4);
	TextDrawSetProportional(RadialTD[44], 1);

	RadialTD[45] = TextDrawCreate(171.000, 242.000, "/");
	TextDrawLetterSize(RadialTD[45], 0.360, 1.199);
	TextDrawAlignment(RadialTD[45], 1);
	TextDrawColor(RadialTD[45], 0xffffffcc);
	TextDrawSetShadow(RadialTD[45], 0);
	TextDrawSetOutline(RadialTD[45], 0);
	TextDrawBackgroundColor(RadialTD[45], 150);
	TextDrawFont(RadialTD[45], 1);
	TextDrawSetProportional(RadialTD[45], 1);

	RadialTD[46] = TextDrawCreate(184.000, 242.000, "\\");
	TextDrawLetterSize(RadialTD[46], 0.360, 1.199);
	TextDrawAlignment(RadialTD[46], 1);
	TextDrawColor(RadialTD[46], 0xffffffcc);
	TextDrawSetShadow(RadialTD[46], 0);
	TextDrawSetOutline(RadialTD[46], 0);
	TextDrawBackgroundColor(RadialTD[46], 150);
	TextDrawFont(RadialTD[46], 1);
	TextDrawSetProportional(RadialTD[46], 1);

	RadialTD[47] = TextDrawCreate(169.000, 253.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[47], 22.000, 9.000);
	TextDrawAlignment(RadialTD[47], 1);
	TextDrawColor(RadialTD[47], 0xffffffcc);
	TextDrawSetShadow(RadialTD[47], 0);
	TextDrawSetOutline(RadialTD[47], 0);
	TextDrawBackgroundColor(RadialTD[47], 255);
	TextDrawFont(RadialTD[47], 4);
	TextDrawSetProportional(RadialTD[47], 1);

	RadialTD[48] = TextDrawCreate(170.000, 255.000, "LD_BEAT:chit");
	TextDrawTextSize(RadialTD[48], 5.000, 6.000);
	TextDrawAlignment(RadialTD[48], 1);
	TextDrawColor(RadialTD[48], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[48], 0);
	TextDrawSetOutline(RadialTD[48], 0);
	TextDrawBackgroundColor(RadialTD[48], 255);
	TextDrawFont(RadialTD[48], 4);
	TextDrawSetProportional(RadialTD[48], 1);

	RadialTD[49] = TextDrawCreate(185.000, 255.000, "LD_BEAT:chit");
	TextDrawTextSize(RadialTD[49], 5.000, 6.000);
	TextDrawAlignment(RadialTD[49], 1);
	TextDrawColor(RadialTD[49], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[49], 0);
	TextDrawSetOutline(RadialTD[49], 0);
	TextDrawBackgroundColor(RadialTD[49], 255);
	TextDrawFont(RadialTD[49], 4);
	TextDrawSetProportional(RadialTD[49], 1);

	RadialTD[50] = TextDrawCreate(171.000, 262.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[50], 3.000, 4.000);
	TextDrawAlignment(RadialTD[50], 1);
	TextDrawColor(RadialTD[50], 0xffffffcc);
	TextDrawSetShadow(RadialTD[50], 0);
	TextDrawSetOutline(RadialTD[50], 0);
	TextDrawBackgroundColor(RadialTD[50], 255);
	TextDrawFont(RadialTD[50], 4);
	TextDrawSetProportional(RadialTD[50], 1);

	RadialTD[51] = TextDrawCreate(186.000, 262.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[51], 3.000, 4.000);
	TextDrawAlignment(RadialTD[51], 1);
	TextDrawColor(RadialTD[51], 0xffffffcc);
	TextDrawSetShadow(RadialTD[51], 0);
	TextDrawSetOutline(RadialTD[51], 0);
	TextDrawBackgroundColor(RadialTD[51], 255);
	TextDrawFont(RadialTD[51], 4);
	TextDrawSetProportional(RadialTD[51], 1);

	RadialTD[52] = TextDrawCreate(215.000, 246.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[52], 22.000, 14.000);
	TextDrawAlignment(RadialTD[52], 1);
	TextDrawColor(RadialTD[52], 0xffffffcc);
	TextDrawSetShadow(RadialTD[52], 0);
	TextDrawSetOutline(RadialTD[52], 0);
	TextDrawBackgroundColor(RadialTD[52], 255);
	TextDrawFont(RadialTD[52], 4);
	TextDrawSetProportional(RadialTD[52], 1);

	RadialTD[53] = TextDrawCreate(216.000, 247.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[53], 7.000, 9.000);
	TextDrawAlignment(RadialTD[53], 1);
	TextDrawColor(RadialTD[53], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[53], 0);
	TextDrawSetOutline(RadialTD[53], 0);
	TextDrawBackgroundColor(RadialTD[53], 255);
	TextDrawFont(RadialTD[53], 4);
	TextDrawSetProportional(RadialTD[53], 1);

	RadialTD[54] = TextDrawCreate(224.000, 247.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[54], 11.000, 2.000);
	TextDrawAlignment(RadialTD[54], 1);
	TextDrawColor(RadialTD[54], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[54], 0);
	TextDrawSetOutline(RadialTD[54], 0);
	TextDrawBackgroundColor(RadialTD[54], 255);
	TextDrawFont(RadialTD[54], 4);
	TextDrawSetProportional(RadialTD[54], 1);

	RadialTD[55] = TextDrawCreate(224.000, 251.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[55], 11.000, 2.000);
	TextDrawAlignment(RadialTD[55], 1);
	TextDrawColor(RadialTD[55], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[55], 0);
	TextDrawSetOutline(RadialTD[55], 0);
	TextDrawBackgroundColor(RadialTD[55], 255);
	TextDrawFont(RadialTD[55], 4);
	TextDrawSetProportional(RadialTD[55], 1);

	RadialTD[56] = TextDrawCreate(224.000, 255.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialTD[56], 11.000, 2.000);
	TextDrawAlignment(RadialTD[56], 1);
	TextDrawColor(RadialTD[56], 0x3f3f3f8c);
	TextDrawSetShadow(RadialTD[56], 0);
	TextDrawSetOutline(RadialTD[56], 0);
	TextDrawBackgroundColor(RadialTD[56], 255);
	TextDrawFont(RadialTD[56], 4);
	TextDrawSetProportional(RadialTD[56], 1);

	RadialTD[57] = TextDrawCreate(178.000, 285.000, "X");
	TextDrawLetterSize(RadialTD[57], 0.358, 1.598);
	TextDrawAlignment(RadialTD[57], 1);
	TextDrawColor(RadialTD[57], 0xffffffcc);
	TextDrawSetShadow(RadialTD[57], 0);
	TextDrawSetOutline(RadialTD[57], 0);
	TextDrawBackgroundColor(RadialTD[57], 150);
	TextDrawFont(RadialTD[57], 1);
	TextDrawSetProportional(RadialTD[57], 1);
}

CreateRadialFashionTD()
{
	RadialFashionTD[23] = TextDrawCreate(96.000, 194.000, "LD_BUM:blkdot"); //helmet
	TextDrawTextSize(RadialFashionTD[23], 34.000, 35.000);
	TextDrawAlignment(RadialFashionTD[23], 1);
	TextDrawColor(RadialFashionTD[23], 0x00000066);
	TextDrawSetShadow(RadialFashionTD[23], 0);
	TextDrawSetOutline(RadialFashionTD[23], 0);
	TextDrawBackgroundColor(RadialFashionTD[23], 255);
	TextDrawFont(RadialFashionTD[23], 4);
	TextDrawSetProportional(RadialFashionTD[23], 1);
	TextDrawSetSelectable(RadialFashionTD[23], 1);

	RadialFashionTD[24] = TextDrawCreate(141.000, 194.000, "LD_BUM:blkdot"); //kacamata
	TextDrawTextSize(RadialFashionTD[24], 34.000, 35.000);
	TextDrawAlignment(RadialFashionTD[24], 1);
	TextDrawColor(RadialFashionTD[24], 0x00000066);
	TextDrawSetShadow(RadialFashionTD[24], 0);
	TextDrawSetOutline(RadialFashionTD[24], 0);
	TextDrawBackgroundColor(RadialFashionTD[24], 255);
	TextDrawFont(RadialFashionTD[24], 4);
	TextDrawSetProportional(RadialFashionTD[24], 1);
	TextDrawSetSelectable(RadialFashionTD[24], 1);

	RadialFashionTD[25] = TextDrawCreate(186.000, 194.000, "LD_BUM:blkdot"); //aksesoris
	TextDrawTextSize(RadialFashionTD[25], 34.000, 35.000);
	TextDrawAlignment(RadialFashionTD[25], 1);
	TextDrawColor(RadialFashionTD[25], 0x00000066);
	TextDrawSetShadow(RadialFashionTD[25], 0);
	TextDrawSetOutline(RadialFashionTD[25], 0);
	TextDrawBackgroundColor(RadialFashionTD[25], 255);
	TextDrawFont(RadialFashionTD[25], 4);
	TextDrawSetProportional(RadialFashionTD[25], 1);
	TextDrawSetSelectable(RadialFashionTD[25], 1);

	RadialFashionTD[26] = TextDrawCreate(232.000, 194.000, "LD_BUM:blkdot"); //koper
	TextDrawTextSize(RadialFashionTD[26], 34.000, 35.000);
	TextDrawAlignment(RadialFashionTD[26], 1);
	TextDrawColor(RadialFashionTD[26], 0x00000066);
	TextDrawSetShadow(RadialFashionTD[26], 0);
	TextDrawSetOutline(RadialFashionTD[26], 0);
	TextDrawBackgroundColor(RadialFashionTD[26], 255);
	TextDrawFont(RadialFashionTD[26], 4);
	TextDrawSetProportional(RadialFashionTD[26], 1);
	TextDrawSetSelectable(RadialFashionTD[26], 1);

	RadialFashionTD[27] = TextDrawCreate(166.000, 238.000, "LD_BEAT:chit"); //CloseB
	TextDrawTextSize(RadialFashionTD[27], 30.000, 30.000);
	TextDrawAlignment(RadialFashionTD[27], 1);
	TextDrawColor(RadialFashionTD[27], 0x00000066);
	TextDrawSetShadow(RadialFashionTD[27], 0);
	TextDrawSetOutline(RadialFashionTD[27], 0);
	TextDrawBackgroundColor(RadialFashionTD[27], 255);
	TextDrawFont(RadialFashionTD[27], 4);
	TextDrawSetProportional(RadialFashionTD[27], 1);
	TextDrawSetSelectable(RadialFashionTD[27], 1);

	RadialFashionTD[0] = TextDrawCreate(177.000, 245.000, "X");
	TextDrawLetterSize(RadialFashionTD[0], 0.349, 1.590);
	TextDrawAlignment(RadialFashionTD[0], 1);
	TextDrawColor(RadialFashionTD[0], 0xffffffcc);
	TextDrawSetShadow(RadialFashionTD[0], 0);
	TextDrawSetOutline(RadialFashionTD[0], 0);
	TextDrawBackgroundColor(RadialFashionTD[0], 150);
	TextDrawFont(RadialFashionTD[0], 1);
	TextDrawSetProportional(RadialFashionTD[0], 1);
	
	RadialFashionTD[1] = TextDrawCreate(101.000, 196.000, "ld_beat:chit");
	TextDrawLetterSize(RadialFashionTD[1], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[1], 28.500, 29.000);
	TextDrawAlignment(RadialFashionTD[1], 1);
	TextDrawColor(RadialFashionTD[1], 0xffffffcc);
	TextDrawUseBox(RadialFashionTD[1], 1);
	TextDrawBoxColor(RadialFashionTD[1], 50);
	TextDrawSetShadow(RadialFashionTD[1], 0);
	TextDrawSetOutline(RadialFashionTD[1], 0);
	TextDrawBackgroundColor(RadialFashionTD[1], 255);
	TextDrawFont(RadialFashionTD[1], 4);

	RadialFashionTD[2] = TextDrawCreate(105.000, 206.000, "ld_dual:white");
	TextDrawLetterSize(RadialFashionTD[2], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[2], 8.000, 12.000);
	TextDrawAlignment(RadialFashionTD[2], 1);
	TextDrawColor(RadialFashionTD[2], 0xffffffcc);
	TextDrawUseBox(RadialFashionTD[2], 1);
	TextDrawBoxColor(RadialFashionTD[2], 50);
	TextDrawSetShadow(RadialFashionTD[2], 0);
	TextDrawSetOutline(RadialFashionTD[2], 0);
	TextDrawBackgroundColor(RadialFashionTD[2], 255);
	TextDrawFont(RadialFashionTD[2], 4);
	TextDrawSetProportional(RadialFashionTD[2], 1);

	RadialFashionTD[3] = TextDrawCreate(99.000, 203.000, "ld_beat:chit");
	TextDrawLetterSize(RadialFashionTD[3], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[3], 21.500, 7.000);
	TextDrawAlignment(RadialFashionTD[3], 1);
	TextDrawColor(RadialFashionTD[3], 0xffffffcc);
	TextDrawUseBox(RadialFashionTD[3], 1);
	TextDrawBoxColor(RadialFashionTD[3], 50);
	TextDrawSetShadow(RadialFashionTD[3], 0);
	TextDrawSetOutline(RadialFashionTD[3], 0);
	TextDrawBackgroundColor(RadialFashionTD[3], 255);
	TextDrawFont(RadialFashionTD[3], 4);
	TextDrawSetProportional(RadialFashionTD[3], 0);

	RadialFashionTD[4] = TextDrawCreate(106.000, 205.000, "/");
	TextDrawLetterSize(RadialFashionTD[4], 0.629, 1.500);
	TextDrawTextSize(RadialFashionTD[4], 400.000, 17.000);
	TextDrawAlignment(RadialFashionTD[4], 1);
	TextDrawColor(RadialFashionTD[4], -1061109505);
	TextDrawSetShadow(RadialFashionTD[4], 0);
	TextDrawSetOutline(RadialFashionTD[4], 0);
	TextDrawBackgroundColor(RadialFashionTD[4], 255);
	TextDrawFont(RadialFashionTD[4], 0);
	TextDrawSetProportional(RadialFashionTD[4], 1);

	RadialFashionTD[5] = TextDrawCreate(105.000, 208.000, "ld_dual:white");
	TextDrawLetterSize(RadialFashionTD[5], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[5], 5.500, 11.000);
	TextDrawAlignment(RadialFashionTD[5], 1);
	TextDrawColor(RadialFashionTD[5], 1296911871);
	TextDrawUseBox(RadialFashionTD[5], 1);
	TextDrawBoxColor(RadialFashionTD[5], 50);
	TextDrawSetShadow(RadialFashionTD[5], 0);
	TextDrawSetOutline(RadialFashionTD[5], 0);
	TextDrawBackgroundColor(RadialFashionTD[5], 255);
	TextDrawFont(RadialFashionTD[5], 4);
	TextDrawSetProportional(RadialFashionTD[5], 1);

	RadialFashionTD[6] = TextDrawCreate(105.000, 207.000, "ld_dual:white");
	TextDrawLetterSize(RadialFashionTD[6], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[6], 9.000, 2.500);
	TextDrawAlignment(RadialFashionTD[6], 1);
	TextDrawColor(RadialFashionTD[6], 0xffffffcc);
	TextDrawUseBox(RadialFashionTD[6], 1);
	TextDrawBoxColor(RadialFashionTD[6], 50);
	TextDrawSetShadow(RadialFashionTD[6], 0);
	TextDrawSetOutline(RadialFashionTD[6], 0);
	TextDrawBackgroundColor(RadialFashionTD[6], 255);
	TextDrawFont(RadialFashionTD[6], 4);
	TextDrawSetProportional(RadialFashionTD[6], 1);

	RadialFashionTD[7] = TextDrawCreate(143.000, 204.000, "ld_beat:chit");
	TextDrawLetterSize(RadialFashionTD[7], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[7], 16.500, 15.500);
	TextDrawAlignment(RadialFashionTD[7], 1);
	TextDrawColor(RadialFashionTD[7], 0xffffffcc);
	TextDrawUseBox(RadialFashionTD[7], 1);
	TextDrawBoxColor(RadialFashionTD[7], 50);
	TextDrawSetShadow(RadialFashionTD[7], 0);
	TextDrawSetOutline(RadialFashionTD[7], 0);
	TextDrawBackgroundColor(RadialFashionTD[7], 255);
	TextDrawFont(RadialFashionTD[7], 4);
	TextDrawSetProportional(RadialFashionTD[7], 0);

	RadialFashionTD[8] = TextDrawCreate(156.000, 204.000, "ld_beat:chit");
	TextDrawLetterSize(RadialFashionTD[8], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[8], 16.500, 15.500);
	TextDrawAlignment(RadialFashionTD[8], 1);
	TextDrawColor(RadialFashionTD[8], 0xffffffcc);
	TextDrawUseBox(RadialFashionTD[8], 1);
	TextDrawBoxColor(RadialFashionTD[8], 50);
	TextDrawSetShadow(RadialFashionTD[8], 0);
	TextDrawSetOutline(RadialFashionTD[8], 0);
	TextDrawBackgroundColor(RadialFashionTD[8], 255);
	TextDrawFont(RadialFashionTD[8], 4);
	TextDrawSetProportional(RadialFashionTD[8], 0);

	RadialFashionTD[9] = TextDrawCreate(153.000, 207.000, "ld_dual:white");
	TextDrawLetterSize(RadialFashionTD[9], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[9], 10.000, 1.500);
	TextDrawAlignment(RadialFashionTD[9], 1);
	TextDrawColor(RadialFashionTD[9], 0xffffffcc);
	TextDrawUseBox(RadialFashionTD[9], 1);
	TextDrawBoxColor(RadialFashionTD[9], 50);
	TextDrawSetShadow(RadialFashionTD[9], 0);
	TextDrawSetOutline(RadialFashionTD[9], 0);
	TextDrawBackgroundColor(RadialFashionTD[9], 255);
	TextDrawFont(RadialFashionTD[9], 4);
	TextDrawSetProportional(RadialFashionTD[9], 1);

	RadialFashionTD[10] = TextDrawCreate(155.000, 209.000, "ld_dual:white");
	TextDrawLetterSize(RadialFashionTD[10], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[10], 7.500, 1.500);
	TextDrawAlignment(RadialFashionTD[10], 1);
	TextDrawColor(RadialFashionTD[10], 0xffffffcc);
	TextDrawUseBox(RadialFashionTD[10], 1);
	TextDrawBoxColor(RadialFashionTD[10], 50);
	TextDrawSetShadow(RadialFashionTD[10], 0);
	TextDrawSetOutline(RadialFashionTD[10], 0);
	TextDrawBackgroundColor(RadialFashionTD[10], 255);
	TextDrawFont(RadialFashionTD[10], 4);
	TextDrawSetProportional(RadialFashionTD[10], 1);

	RadialFashionTD[11] = TextDrawCreate(145.000, 205.000, "ld_beat:chit");
	TextDrawLetterSize(RadialFashionTD[11], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[11], 13.000, 13.500);
	TextDrawAlignment(RadialFashionTD[11], 1);
	TextDrawColor(RadialFashionTD[11], -1094795521);
	TextDrawUseBox(RadialFashionTD[11], 1);
	TextDrawBoxColor(RadialFashionTD[11], 50);
	TextDrawSetShadow(RadialFashionTD[11], 0);
	TextDrawSetOutline(RadialFashionTD[11], 0);
	TextDrawBackgroundColor(RadialFashionTD[11], 255);
	TextDrawFont(RadialFashionTD[11], 4);
	TextDrawSetProportional(RadialFashionTD[12], 0);

	RadialFashionTD[12] = TextDrawCreate(158.000, 205.000, "ld_beat:chit");
	TextDrawLetterSize(RadialFashionTD[12], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[12], 13.000, 13.500);
	TextDrawAlignment(RadialFashionTD[12], 1);
	TextDrawColor(RadialFashionTD[12], -1094795521);
	TextDrawUseBox(RadialFashionTD[12], 1);
	TextDrawBoxColor(RadialFashionTD[12], 50);
	TextDrawSetShadow(RadialFashionTD[12], 0);
	TextDrawSetOutline(RadialFashionTD[12], 0);
	TextDrawBackgroundColor(RadialFashionTD[12], 255);
	TextDrawFont(RadialFashionTD[12], 4);
	TextDrawSetProportional(RadialFashionTD[12], 0);

	RadialFashionTD[13] = TextDrawCreate(201.000, 198.000, "ld_dual:white");
	TextDrawLetterSize(RadialFashionTD[13], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[13], 4.000, 28.000);
	TextDrawAlignment(RadialFashionTD[13], 1);
	TextDrawColor(RadialFashionTD[13], -1);
	TextDrawUseBox(RadialFashionTD[13], 1);
	TextDrawBoxColor(RadialFashionTD[13], 50);
	TextDrawSetShadow(RadialFashionTD[13], 0);
	TextDrawSetOutline(RadialFashionTD[13], 0);
	TextDrawBackgroundColor(RadialFashionTD[13], 255);
	TextDrawFont(RadialFashionTD[13], 4);
	TextDrawSetProportional(RadialFashionTD[13], 1);

	RadialFashionTD[14] = TextDrawCreate(198.000, 207.000, "ld_dual:white");
	TextDrawLetterSize(RadialFashionTD[14], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[14], 10.500, 11.500);
	TextDrawAlignment(RadialFashionTD[14], 1);
	TextDrawColor(RadialFashionTD[14], 1296911871);
	TextDrawUseBox(RadialFashionTD[14], 1);
	TextDrawBoxColor(RadialFashionTD[14], 50);
	TextDrawSetShadow(RadialFashionTD[14], 0);
	TextDrawSetOutline(RadialFashionTD[14], 0);
	TextDrawBackgroundColor(RadialFashionTD[14], 255);
	TextDrawFont(RadialFashionTD[14], 4);
	TextDrawSetProportional(RadialFashionTD[14], 1);

	RadialFashionTD[15] = TextDrawCreate(199.000, 209.000, "ld_dual:white");
	TextDrawLetterSize(RadialFashionTD[15], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[15], 8.500, 8.000);
	TextDrawAlignment(RadialFashionTD[15], 1);
	TextDrawColor(RadialFashionTD[15], -741092353);
	TextDrawUseBox(RadialFashionTD[15], 1);
	TextDrawBoxColor(RadialFashionTD[15], 50);
	TextDrawSetShadow(RadialFashionTD[15], 0);
	TextDrawSetOutline(RadialFashionTD[15], 0);
	TextDrawBackgroundColor(RadialFashionTD[15], 255);
	TextDrawFont(RadialFashionTD[15], 4);
	TextDrawSetProportional(RadialFashionTD[15], 1);

	RadialFashionTD[16] = TextDrawCreate(203.000, 210.000, "ld_dual:white");
	TextDrawLetterSize(RadialFashionTD[16], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[16], 1.000, 3.000);
	TextDrawAlignment(RadialFashionTD[16], 1);
	TextDrawColor(RadialFashionTD[16], 255);
	TextDrawUseBox(RadialFashionTD[16], 1);
	TextDrawBoxColor(RadialFashionTD[16], 50);
	TextDrawSetShadow(RadialFashionTD[16], 0);
	TextDrawSetOutline(RadialFashionTD[16], 0);
	TextDrawBackgroundColor(RadialFashionTD[16], 255);
	TextDrawFont(RadialFashionTD[16], 4);
	TextDrawSetProportional(RadialFashionTD[16], 1);

	RadialFashionTD[17] = TextDrawCreate(200.000, 213.000, "ld_dual:white");
	TextDrawLetterSize(RadialFashionTD[17], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[17], 4.000, 1.500);
	TextDrawAlignment(RadialFashionTD[17], 1);
	TextDrawColor(RadialFashionTD[17], 255);
	TextDrawUseBox(RadialFashionTD[17], 1);
	TextDrawBoxColor(RadialFashionTD[17], 50);
	TextDrawSetShadow(RadialFashionTD[17], 0);
	TextDrawSetOutline(RadialFashionTD[17], 0);
	TextDrawBackgroundColor(RadialFashionTD[17], 255);
	TextDrawFont(RadialFashionTD[17], 4);
	TextDrawSetProportional(RadialFashionTD[17], 1);

	RadialFashionTD[18] = TextDrawCreate(240.000, 205.000, "ld_dual:white");
	TextDrawLetterSize(RadialFashionTD[18], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[18], 19.000, 16.000);
	TextDrawAlignment(RadialFashionTD[18], 1);
	TextDrawColor(RadialFashionTD[18], 0xffffffcc);
	TextDrawUseBox(RadialFashionTD[18], 1);
	TextDrawBoxColor(RadialFashionTD[18], 50);
	TextDrawSetShadow(RadialFashionTD[18], 0);
	TextDrawSetOutline(RadialFashionTD[18], 0);
	TextDrawBackgroundColor(RadialFashionTD[18], 255);
	TextDrawFont(RadialFashionTD[18], 4);
	TextDrawSetProportional(RadialFashionTD[18], 1);

	RadialFashionTD[19] = TextDrawCreate(240.000, 208.000, "ld_dual:white");
	TextDrawLetterSize(RadialFashionTD[19], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[19], 19.000, 2.500);
	TextDrawAlignment(RadialFashionTD[19], 1);
	TextDrawColor(RadialFashionTD[19], 0x3f3f3f8c);
	TextDrawUseBox(RadialFashionTD[19], 1);
	TextDrawBoxColor(RadialFashionTD[19], 50);
	TextDrawSetShadow(RadialFashionTD[19], 0);
	TextDrawSetOutline(RadialFashionTD[19], 0);
	TextDrawBackgroundColor(RadialFashionTD[19], 255);
	TextDrawFont(RadialFashionTD[19], 4);
	TextDrawSetProportional(RadialFashionTD[19], 1);

	RadialFashionTD[20] = TextDrawCreate(246.000, 207.000, "ld_dual:white");
	TextDrawLetterSize(RadialFashionTD[20], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[20], 5.500, 5.000);
	TextDrawAlignment(RadialFashionTD[20], 1);
	TextDrawColor(RadialFashionTD[20], 0x3f3f3f8c);
	TextDrawUseBox(RadialFashionTD[20], 1);
	TextDrawBoxColor(RadialFashionTD[20], 50);
	TextDrawSetShadow(RadialFashionTD[20], 0);
	TextDrawSetOutline(RadialFashionTD[20], 0);
	TextDrawBackgroundColor(RadialFashionTD[20], 255);
	TextDrawFont(RadialFashionTD[20], 4);
	TextDrawSetProportional(RadialFashionTD[20], 1);

	RadialFashionTD[21] = TextDrawCreate(244.000, 201.000, "ld_dual:white");
	TextDrawLetterSize(RadialFashionTD[21], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[21], 10.000, 5.000);
	TextDrawAlignment(RadialFashionTD[21], 1);
	TextDrawColor(RadialFashionTD[21], 0xffffffcc);
	TextDrawUseBox(RadialFashionTD[21], 1);
	TextDrawBoxColor(RadialFashionTD[21], 50);
	TextDrawSetShadow(RadialFashionTD[21], 0);
	TextDrawSetOutline(RadialFashionTD[21], 0);
	TextDrawBackgroundColor(RadialFashionTD[21], 255);
	TextDrawFont(RadialFashionTD[21], 4);
	TextDrawSetProportional(RadialFashionTD[21], 1);

	RadialFashionTD[22] = TextDrawCreate(246.000, 202.000, "ld_dual:white");
	TextDrawLetterSize(RadialFashionTD[22], 0.600, 2.000);
	TextDrawTextSize(RadialFashionTD[22], 5.000, 2.500);
	TextDrawAlignment(RadialFashionTD[22], 1);
	TextDrawColor(RadialFashionTD[22], 0x3f3f3f8c);
	TextDrawUseBox(RadialFashionTD[22], 1);
	TextDrawBoxColor(RadialFashionTD[22], 50);
	TextDrawSetShadow(RadialFashionTD[22], 0);
	TextDrawSetOutline(RadialFashionTD[22], 0);
	TextDrawBackgroundColor(RadialFashionTD[22], 255);
	TextDrawFont(RadialFashionTD[22], 4);
	TextDrawSetProportional(RadialFashionTD[22], 1);
}

CreateRadialVehTD()
{
	RadialVehTD[37] = TextDrawCreate(95.000, 194.000, "LD_BUM:blkdot"); //LockB
	TextDrawTextSize(RadialVehTD[37], 34.000, 35.000);
	TextDrawAlignment(RadialVehTD[37], 1);
	TextDrawColor(RadialVehTD[37], 0x00000066);
	TextDrawSetShadow(RadialVehTD[37], 0);
	TextDrawSetOutline(RadialVehTD[37], 0);
	TextDrawBackgroundColor(RadialVehTD[37], 255);
	TextDrawFont(RadialVehTD[37], 4);
	TextDrawSetProportional(RadialVehTD[37], 1);
	TextDrawSetSelectable(RadialVehTD[37], 1);

	RadialVehTD[38] = TextDrawCreate(141.000, 194.000, "LD_BUM:blkdot"); //LightB
	TextDrawTextSize(RadialVehTD[38], 34.000, 35.000);
	TextDrawAlignment(RadialVehTD[38], 1);
	TextDrawColor(RadialVehTD[38], 0x00000066);
	TextDrawSetShadow(RadialVehTD[38], 0);
	TextDrawSetOutline(RadialVehTD[38], 0);
	TextDrawBackgroundColor(RadialVehTD[38], 255);
	TextDrawFont(RadialVehTD[38], 4);
	TextDrawSetProportional(RadialVehTD[38], 1);
	TextDrawSetSelectable(RadialVehTD[38], 1);

	RadialVehTD[39] = TextDrawCreate(186.000, 194.000, "LD_BUM:blkdot"); //StorageB
	TextDrawTextSize(RadialVehTD[39], 34.000, 35.000);
	TextDrawAlignment(RadialVehTD[39], 1);
	TextDrawColor(RadialVehTD[39], 0x00000066);
	TextDrawSetShadow(RadialVehTD[39], 0);
	TextDrawSetOutline(RadialVehTD[39], 0);
	TextDrawBackgroundColor(RadialVehTD[39], 255);
	TextDrawFont(RadialVehTD[39], 4);
	TextDrawSetProportional(RadialVehTD[39], 1);
	TextDrawSetSelectable(RadialVehTD[39], 1);

	RadialVehTD[40] = TextDrawCreate(232.000, 194.000, "LD_BUM:blkdot"); //WeaponB
	TextDrawTextSize(RadialVehTD[40], 34.000, 35.000);
	TextDrawAlignment(RadialVehTD[40], 1);
	TextDrawColor(RadialVehTD[40], 0x00000066);
	TextDrawSetShadow(RadialVehTD[40], 0);
	TextDrawSetOutline(RadialVehTD[40], 0);
	TextDrawBackgroundColor(RadialVehTD[40], 255);
	TextDrawFont(RadialVehTD[40], 4);
	TextDrawSetProportional(RadialVehTD[40], 1);
	TextDrawSetSelectable(RadialVehTD[40], 1);

	RadialVehTD[41] = TextDrawCreate(119.000, 237.000, "LD_BUM:blkdot"); //KapdepanB
	TextDrawTextSize(RadialVehTD[41], 34.000, 35.000);
	TextDrawAlignment(RadialVehTD[41], 1);
	TextDrawColor(RadialVehTD[41], 0x00000066);
	TextDrawSetShadow(RadialVehTD[41], 0);
	TextDrawSetOutline(RadialVehTD[41], 0);
	TextDrawBackgroundColor(RadialVehTD[41], 255);
	TextDrawFont(RadialVehTD[41], 4);
	TextDrawSetProportional(RadialVehTD[41], 1);
	TextDrawSetSelectable(RadialVehTD[41], 1);

	RadialVehTD[42] = TextDrawCreate(209.000, 237.000, "LD_BUM:blkdot"); //KapbelakangB
	TextDrawTextSize(RadialVehTD[42], 34.000, 35.000);
	TextDrawAlignment(RadialVehTD[42], 1);
	TextDrawColor(RadialVehTD[42], 0x00000066);
	TextDrawSetShadow(RadialVehTD[42], 0);
	TextDrawSetOutline(RadialVehTD[42], 0);
	TextDrawBackgroundColor(RadialVehTD[42], 255);
	TextDrawFont(RadialVehTD[42], 4);
	TextDrawSetProportional(RadialVehTD[42], 1);
	TextDrawSetSelectable(RadialVehTD[42], 1);

	RadialVehTD[43] = TextDrawCreate(165.000, 279.000, "LD_BEAT:chit"); //CloseB
	TextDrawTextSize(RadialVehTD[43], 30.000, 30.000);
	TextDrawAlignment(RadialVehTD[43], 1);
	TextDrawColor(RadialVehTD[43], 0x00000066);
	TextDrawSetShadow(RadialVehTD[43], 0);
	TextDrawSetOutline(RadialVehTD[43], 0);
	TextDrawBackgroundColor(RadialVehTD[43], 255);
	TextDrawFont(RadialVehTD[43], 4);
	TextDrawSetProportional(RadialVehTD[43], 1);
	TextDrawSetSelectable(RadialVehTD[43], 1);

	RadialVehTD[0] = TextDrawCreate(176.000, 286.000, "X");
	TextDrawLetterSize(RadialVehTD[0], 0.349, 1.559);
	TextDrawAlignment(RadialVehTD[0], 1);
	TextDrawColor(RadialVehTD[0], 0xffffffcc);
	TextDrawSetShadow(RadialVehTD[0], 0);
	TextDrawSetOutline(RadialVehTD[0], 0);
	TextDrawBackgroundColor(RadialVehTD[0], 150);
	TextDrawFont(RadialVehTD[0], 1);
	TextDrawSetProportional(RadialVehTD[0], 1);

	RadialVehTD[1] = TextDrawCreate(106.000, 198.000, "0");
	TextDrawLetterSize(RadialVehTD[1], 0.558, 1.559);
	TextDrawAlignment(RadialVehTD[1], 1);
	TextDrawColor(RadialVehTD[1], 0xffffffcc);
	TextDrawSetShadow(RadialVehTD[1], 0);
	TextDrawSetOutline(RadialVehTD[1], 0);
	TextDrawBackgroundColor(RadialVehTD[1], 150);
	TextDrawFont(RadialVehTD[1], 1);
	TextDrawSetProportional(RadialVehTD[1], 1);

	RadialVehTD[2] = TextDrawCreate(105.000, 206.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[2], 14.000, 13.000);
	TextDrawAlignment(RadialVehTD[2], 1);
	TextDrawColor(RadialVehTD[2], 0xffffffcc);
	TextDrawSetShadow(RadialVehTD[2], 0);
	TextDrawSetOutline(RadialVehTD[2], 0);
	TextDrawBackgroundColor(RadialVehTD[2], 255);
	TextDrawFont(RadialVehTD[2], 4);
	TextDrawSetProportional(RadialVehTD[2], 1);

	RadialVehTD[3] = TextDrawCreate(111.000, 211.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[3], 2.000, 6.000);
	TextDrawAlignment(RadialVehTD[3], 1);
	TextDrawColor(RadialVehTD[3], 0x3f3f3f8c);
	TextDrawSetShadow(RadialVehTD[3], 0);
	TextDrawSetOutline(RadialVehTD[3], 0);
	TextDrawBackgroundColor(RadialVehTD[3], 255);
	TextDrawFont(RadialVehTD[3], 4);
	TextDrawSetProportional(RadialVehTD[3], 1);

	RadialVehTD[4] = TextDrawCreate(109.000, 209.000, "LD_BEAT:chit");
	TextDrawTextSize(RadialVehTD[4], 6.000, 6.000);
	TextDrawAlignment(RadialVehTD[4], 1);
	TextDrawColor(RadialVehTD[4], 0x3f3f3f8c);
	TextDrawSetShadow(RadialVehTD[4], 0);
	TextDrawSetOutline(RadialVehTD[4], 0);
	TextDrawBackgroundColor(RadialVehTD[4], 255);
	TextDrawFont(RadialVehTD[4], 4);
	TextDrawSetProportional(RadialVehTD[4], 1);

	RadialVehTD[5] = TextDrawCreate(106.000, 198.000, "0");
	TextDrawLetterSize(RadialVehTD[5], 0.558, 1.559);
	TextDrawAlignment(RadialVehTD[5], 1);
	TextDrawColor(RadialVehTD[5], 0xffffffcc);
	TextDrawSetShadow(RadialVehTD[5], 0);
	TextDrawSetOutline(RadialVehTD[5], 0);
	TextDrawBackgroundColor(RadialVehTD[5], 150);
	TextDrawFont(RadialVehTD[5], 1);
	TextDrawSetProportional(RadialVehTD[5], 1);

	RadialVehTD[6] = TextDrawCreate(161.000, 202.000, "D");
	TextDrawLetterSize(RadialVehTD[6], 0.347, 1.858);
	TextDrawAlignment(RadialVehTD[6], 1);
	TextDrawColor(RadialVehTD[6], 0xffffffcc);
	TextDrawSetShadow(RadialVehTD[6], 0);
	TextDrawSetOutline(RadialVehTD[6], 0);
	TextDrawBackgroundColor(RadialVehTD[6], 150);
	TextDrawFont(RadialVehTD[6], 1);
	TextDrawSetProportional(RadialVehTD[6], 1);

	RadialVehTD[7] = TextDrawCreate(151.000, 206.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[7], 9.000, 2.000);
	TextDrawAlignment(RadialVehTD[7], 1);
	TextDrawColor(RadialVehTD[7], 0xffffffcc);
	TextDrawSetShadow(RadialVehTD[7], 0);
	TextDrawSetOutline(RadialVehTD[7], 0);
	TextDrawBackgroundColor(RadialVehTD[7], 255);
	TextDrawFont(RadialVehTD[7], 4);
	TextDrawSetProportional(RadialVehTD[7], 1);

	RadialVehTD[8] = TextDrawCreate(155.000, 210.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[8], 5.000, 2.000);
	TextDrawAlignment(RadialVehTD[8], 1);
	TextDrawColor(RadialVehTD[8], 0xffffffcc);
	TextDrawSetShadow(RadialVehTD[8], 0);
	TextDrawSetOutline(RadialVehTD[8], 0);
	TextDrawBackgroundColor(RadialVehTD[8], 255);
	TextDrawFont(RadialVehTD[8], 4);
	TextDrawSetProportional(RadialVehTD[8], 1);

	RadialVehTD[9] = TextDrawCreate(151.000, 214.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[9], 9.000, 2.000);
	TextDrawAlignment(RadialVehTD[9], 1);
	TextDrawColor(RadialVehTD[9], 0xffffffcc);
	TextDrawSetShadow(RadialVehTD[9], 0);
	TextDrawSetOutline(RadialVehTD[9], 0);
	TextDrawBackgroundColor(RadialVehTD[9], 255);
	TextDrawFont(RadialVehTD[9], 4);
	TextDrawSetProportional(RadialVehTD[9], 1);

	RadialVehTD[10] = TextDrawCreate(199.000, 197.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[10], 6.000, 6.000);
	TextDrawAlignment(RadialVehTD[10], 1);
	TextDrawColor(RadialVehTD[10], 0xffffffcc);
	TextDrawSetShadow(RadialVehTD[10], 0);
	TextDrawSetOutline(RadialVehTD[10], 0);
	TextDrawBackgroundColor(RadialVehTD[10], 255);
	TextDrawFont(RadialVehTD[10], 4);
	TextDrawSetProportional(RadialVehTD[10], 1);

	RadialVehTD[11] = TextDrawCreate(200.000, 198.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[11], 4.000, 6.000);
	TextDrawAlignment(RadialVehTD[11], 1);
	TextDrawColor(RadialVehTD[11], 0x3f3f3f8c);
	TextDrawSetShadow(RadialVehTD[11], 0);
	TextDrawSetOutline(RadialVehTD[11], 0);
	TextDrawBackgroundColor(RadialVehTD[11], 255);
	TextDrawFont(RadialVehTD[11], 4);
	TextDrawSetProportional(RadialVehTD[11], 1);

	RadialVehTD[12] = TextDrawCreate(195.000, 202.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[12], 14.000, 20.000);
	TextDrawAlignment(RadialVehTD[12], 1);
	TextDrawColor(RadialVehTD[12], 0xffffffcc);
	TextDrawSetShadow(RadialVehTD[12], 0);
	TextDrawSetOutline(RadialVehTD[12], 0);
	TextDrawBackgroundColor(RadialVehTD[12], 255);
	TextDrawFont(RadialVehTD[12], 4);
	TextDrawSetProportional(RadialVehTD[12], 1);

	RadialVehTD[13] = TextDrawCreate(196.000, 203.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[13], 1.000, 18.000);
	TextDrawAlignment(RadialVehTD[13], 1);
	TextDrawColor(RadialVehTD[13], 0x3f3f3f8c);
	TextDrawSetShadow(RadialVehTD[13], 0);
	TextDrawSetOutline(RadialVehTD[13], 0);
	TextDrawBackgroundColor(RadialVehTD[13], 255);
	TextDrawFont(RadialVehTD[13], 4);
	TextDrawSetProportional(RadialVehTD[13], 1);

	RadialVehTD[14] = TextDrawCreate(198.000, 203.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[14], 1.000, 18.000);
	TextDrawAlignment(RadialVehTD[14], 1);
	TextDrawColor(RadialVehTD[14], 0x3f3f3f8c);
	TextDrawSetShadow(RadialVehTD[14], 0);
	TextDrawSetOutline(RadialVehTD[14], 0);
	TextDrawBackgroundColor(RadialVehTD[14], 255);
	TextDrawFont(RadialVehTD[14], 4);
	TextDrawSetProportional(RadialVehTD[14], 1);

	RadialVehTD[15] = TextDrawCreate(207.000, 203.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[15], 1.000, 18.000);
	TextDrawAlignment(RadialVehTD[15], 1);
	TextDrawColor(RadialVehTD[15], 0x3f3f3f8c);
	TextDrawSetShadow(RadialVehTD[15], 0);
	TextDrawSetOutline(RadialVehTD[15], 0);
	TextDrawBackgroundColor(RadialVehTD[15], 255);
	TextDrawFont(RadialVehTD[15], 4);
	TextDrawSetProportional(RadialVehTD[15], 1);

	RadialVehTD[16] = TextDrawCreate(205.000, 203.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[16], 1.000, 18.000);
	TextDrawAlignment(RadialVehTD[16], 1);
	TextDrawColor(RadialVehTD[16], 0x3f3f3f8c);
	TextDrawSetShadow(RadialVehTD[16], 0);
	TextDrawSetOutline(RadialVehTD[16], 0);
	TextDrawBackgroundColor(RadialVehTD[16], 255);
	TextDrawFont(RadialVehTD[16], 4);
	TextDrawSetProportional(RadialVehTD[16], 1);

	RadialVehTD[17] = TextDrawCreate(198.000, 222.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[17], 2.000, 2.000);
	TextDrawAlignment(RadialVehTD[17], 1);
	TextDrawColor(RadialVehTD[17], 0xffffffcc);
	TextDrawSetShadow(RadialVehTD[17], 0);
	TextDrawSetOutline(RadialVehTD[17], 0);
	TextDrawBackgroundColor(RadialVehTD[17], 255);
	TextDrawFont(RadialVehTD[17], 4);
	TextDrawSetProportional(RadialVehTD[17], 1);

	RadialVehTD[18] = TextDrawCreate(205.000, 222.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[18], 2.000, 2.000);
	TextDrawAlignment(RadialVehTD[18], 1);
	TextDrawColor(RadialVehTD[18], 0xffffffcc);
	TextDrawSetShadow(RadialVehTD[18], 0);
	TextDrawSetOutline(RadialVehTD[18], 0);
	TextDrawBackgroundColor(RadialVehTD[18], 255);
	TextDrawFont(RadialVehTD[18], 4);
	TextDrawSetProportional(RadialVehTD[18], 1);

	RadialVehTD[19] = TextDrawCreate(241.000, 203.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[19], 17.000, 3.000);
	TextDrawAlignment(RadialVehTD[19], 1);
	TextDrawColor(RadialVehTD[19], 0xffffffcc);
	TextDrawSetShadow(RadialVehTD[19], 0);
	TextDrawSetOutline(RadialVehTD[19], 0);
	TextDrawBackgroundColor(RadialVehTD[19], 255);
	TextDrawFont(RadialVehTD[19], 4);
	TextDrawSetProportional(RadialVehTD[19], 1);

	RadialVehTD[20] = TextDrawCreate(253.000, 206.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[20], 4.000, 12.000);
	TextDrawAlignment(RadialVehTD[20], 1);
	TextDrawColor(RadialVehTD[20], 0xffffffcc);
	TextDrawSetShadow(RadialVehTD[20], 0);
	TextDrawSetOutline(RadialVehTD[20], 0);
	TextDrawBackgroundColor(RadialVehTD[20], 255);
	TextDrawFont(RadialVehTD[20], 4);
	TextDrawSetProportional(RadialVehTD[20], 1);

	RadialVehTD[21] = TextDrawCreate(248.000, 205.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[21], 6.000, 7.000);
	TextDrawAlignment(RadialVehTD[21], 1);
	TextDrawColor(RadialVehTD[21], 0xffffffcc);
	TextDrawSetShadow(RadialVehTD[21], 0);
	TextDrawSetOutline(RadialVehTD[21], 0);
	TextDrawBackgroundColor(RadialVehTD[21], 255);
	TextDrawFont(RadialVehTD[21], 4);
	TextDrawSetProportional(RadialVehTD[21], 1);

	RadialVehTD[22] = TextDrawCreate(249.000, 206.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialVehTD[22], 4.000, 5.000);
	TextDrawAlignment(RadialVehTD[22], 1);
	TextDrawColor(RadialVehTD[22], 0x3f3f3f8c);
	TextDrawSetShadow(RadialVehTD[22], 0);
	TextDrawSetOutline(RadialVehTD[22], 0);
	TextDrawBackgroundColor(RadialVehTD[22], 255);
	TextDrawFont(RadialVehTD[22], 4);
	TextDrawSetProportional(RadialVehTD[22], 1);

	RadialVehTD[23] = TextDrawCreate(126.000, 256.000, "ld_beat:chit");
	TextDrawLetterSize(RadialVehTD[23], 0.600, 2.000);
	TextDrawTextSize(RadialVehTD[23], 9.000, 11.000);
	TextDrawAlignment(RadialVehTD[23], 1);
	TextDrawColor(RadialVehTD[23], -1);
	TextDrawUseBox(RadialVehTD[23], 1);
	TextDrawBoxColor(RadialVehTD[23], 50);
	TextDrawSetShadow(RadialVehTD[23], 0);
	TextDrawSetOutline(RadialVehTD[23], 0);
	TextDrawBackgroundColor(RadialVehTD[23], 255);
	TextDrawFont(RadialVehTD[23], 4);
	TextDrawSetProportional(RadialVehTD[23], 0);

	RadialVehTD[24] = TextDrawCreate(137.000, 256.000, "ld_beat:chit");
	TextDrawLetterSize(RadialVehTD[24], 0.600, 2.000);
	TextDrawTextSize(RadialVehTD[24], 9.000, 11.000);
	TextDrawAlignment(RadialVehTD[24], 1);
	TextDrawColor(RadialVehTD[24], -1);
	TextDrawUseBox(RadialVehTD[24], 1);
	TextDrawBoxColor(RadialVehTD[24], 50);
	TextDrawSetShadow(RadialVehTD[24], 0);
	TextDrawSetOutline(RadialVehTD[24], 0);
	TextDrawBackgroundColor(RadialVehTD[24], 255);
	TextDrawFont(RadialVehTD[24], 4);
	TextDrawSetProportional(RadialVehTD[24], 0);

	RadialVehTD[25] = TextDrawCreate(128.000, 242.000, "/");
	TextDrawLetterSize(RadialVehTD[25], 0.312, 1.200);
	TextDrawTextSize(RadialVehTD[25], 400.000, 17.000);
	TextDrawAlignment(RadialVehTD[25], 1);
	TextDrawColor(RadialVehTD[25], -1);
	TextDrawSetShadow(RadialVehTD[25], 0);
	TextDrawSetOutline(RadialVehTD[25], 0);
	TextDrawBackgroundColor(RadialVehTD[25], 255);
	TextDrawFont(RadialVehTD[25], 0);
	TextDrawSetProportional(RadialVehTD[25], 1);

	RadialVehTD[26] = TextDrawCreate(143.000, 242.000, "/");
	TextDrawLetterSize(RadialVehTD[26], -0.358, 1.200);
	TextDrawTextSize(RadialVehTD[26], 400.000, 17.000);
	TextDrawAlignment(RadialVehTD[26], 1);
	TextDrawColor(RadialVehTD[26], -1);
	TextDrawSetShadow(RadialVehTD[26], 0);
	TextDrawSetOutline(RadialVehTD[26], 0);
	TextDrawBackgroundColor(RadialVehTD[26], 255);
	TextDrawFont(RadialVehTD[26], 0);
	TextDrawSetProportional(RadialVehTD[26], 1);

	RadialVehTD[27] = TextDrawCreate(124.000, 252.000, "ld_dual:white");
	TextDrawLetterSize(RadialVehTD[27], 0.600, 2.000);
	TextDrawTextSize(RadialVehTD[27], 24.500, 9.500);
	TextDrawAlignment(RadialVehTD[27], 1);
	TextDrawColor(RadialVehTD[27], -1);
	TextDrawUseBox(RadialVehTD[27], 1);
	TextDrawBoxColor(RadialVehTD[27], 50);
	TextDrawSetShadow(RadialVehTD[27], 0);
	TextDrawSetOutline(RadialVehTD[27], 0);
	TextDrawBackgroundColor(RadialVehTD[27], 255);
	TextDrawFont(RadialVehTD[27], 4);
	TextDrawSetProportional(RadialVehTD[27], 1);

	RadialVehTD[28] = TextDrawCreate(141.000, 242.000, "/");
	TextDrawLetterSize(RadialVehTD[28], 0.312, 1.200);
	TextDrawTextSize(RadialVehTD[28], 400.000, 17.000);
	TextDrawAlignment(RadialVehTD[28], 1);
	TextDrawColor(RadialVehTD[28], -1);
	TextDrawSetShadow(RadialVehTD[28], 0);
	TextDrawSetOutline(RadialVehTD[28], 0);
	TextDrawBackgroundColor(RadialVehTD[28], 255);
	TextDrawFont(RadialVehTD[28], 0);
	TextDrawSetProportional(RadialVehTD[28], 1);

	RadialVehTD[29] = TextDrawCreate(130.000, 239.000, "-");
	TextDrawLetterSize(RadialVehTD[29], 0.851, 1.200);
	TextDrawTextSize(RadialVehTD[29], 400.000, 17.000);
	TextDrawAlignment(RadialVehTD[29], 1);
	TextDrawColor(RadialVehTD[29], -1);
	TextDrawSetShadow(RadialVehTD[29], 0);
	TextDrawSetOutline(RadialVehTD[29], 0);
	TextDrawBackgroundColor(RadialVehTD[29], 255);
	TextDrawFont(RadialVehTD[29], 0);
	TextDrawSetProportional(RadialVehTD[29], 1);

	RadialVehTD[30] = TextDrawCreate(216.000, 256.000, "ld_beat:chit");
	TextDrawLetterSize(RadialVehTD[30], 0.600, 2.000);
	TextDrawTextSize(RadialVehTD[30], 9.000, 11.000);
	TextDrawAlignment(RadialVehTD[30], 1);
	TextDrawColor(RadialVehTD[30], -1);
	TextDrawUseBox(RadialVehTD[30], 1);
	TextDrawBoxColor(RadialVehTD[30], 50);
	TextDrawSetShadow(RadialVehTD[30], 0);
	TextDrawSetOutline(RadialVehTD[30], 0);
	TextDrawBackgroundColor(RadialVehTD[30], 255);
	TextDrawFont(RadialVehTD[30], 4);
	TextDrawSetProportional(RadialVehTD[30], 0);

	RadialVehTD[31] = TextDrawCreate(227.000, 256.000, "ld_beat:chit");
	TextDrawLetterSize(RadialVehTD[31], 0.600, 2.000);
	TextDrawTextSize(RadialVehTD[31], 9.000, 11.000);
	TextDrawAlignment(RadialVehTD[31], 1);
	TextDrawColor(RadialVehTD[31], -1);
	TextDrawUseBox(RadialVehTD[31], 1);
	TextDrawBoxColor(RadialVehTD[31], 50);
	TextDrawSetShadow(RadialVehTD[31], 0);
	TextDrawSetOutline(RadialVehTD[31], 0);
	TextDrawBackgroundColor(RadialVehTD[31], 255);
	TextDrawFont(RadialVehTD[31], 4);
	TextDrawSetProportional(RadialVehTD[31], 0);

	RadialVehTD[32] = TextDrawCreate(218.000, 242.000, "/");
	TextDrawLetterSize(RadialVehTD[32], 0.312, 1.200);
	TextDrawTextSize(RadialVehTD[32], 400.000, 17.000);
	TextDrawAlignment(RadialVehTD[32], 1);
	TextDrawColor(RadialVehTD[32], -1);
	TextDrawSetShadow(RadialVehTD[32], 0);
	TextDrawSetOutline(RadialVehTD[32], 0);
	TextDrawBackgroundColor(RadialVehTD[32], 255);
	TextDrawFont(RadialVehTD[32], 0);
	TextDrawSetProportional(RadialVehTD[32], 1);

	RadialVehTD[33] = TextDrawCreate(233.000, 242.000, "/");
	TextDrawLetterSize(RadialVehTD[33], -0.358, 1.200);
	TextDrawTextSize(RadialVehTD[33], 400.000, 17.000);
	TextDrawAlignment(RadialVehTD[33], 1);
	TextDrawColor(RadialVehTD[33], -1);
	TextDrawSetShadow(RadialVehTD[33], 0);
	TextDrawSetOutline(RadialVehTD[33], 0);
	TextDrawBackgroundColor(RadialVehTD[33], 255);
	TextDrawFont(RadialVehTD[33], 0);
	TextDrawSetProportional(RadialVehTD[33], 1);

	RadialVehTD[34] = TextDrawCreate(214.000, 252.000, "ld_dual:white");
	TextDrawLetterSize(RadialVehTD[34], 0.600, 2.000);
	TextDrawTextSize(RadialVehTD[34], 24.500, 9.500);
	TextDrawAlignment(RadialVehTD[34], 1);
	TextDrawColor(RadialVehTD[34], -1);
	TextDrawUseBox(RadialVehTD[34], 1);
	TextDrawBoxColor(RadialVehTD[34], 50);
	TextDrawSetShadow(RadialVehTD[34], 0);
	TextDrawSetOutline(RadialVehTD[34], 0);
	TextDrawBackgroundColor(RadialVehTD[34], 255);
	TextDrawFont(RadialVehTD[34], 4);
	TextDrawSetProportional(RadialVehTD[34], 1);

	RadialVehTD[35] = TextDrawCreate(214.000, 242.000, "\\");
	TextDrawLetterSize(RadialVehTD[35], 0.312, 1.200);
	TextDrawTextSize(RadialVehTD[35], 400.000, 17.000);
	TextDrawAlignment(RadialVehTD[35], 1);
	TextDrawColor(RadialVehTD[35], -1);
	TextDrawSetShadow(RadialVehTD[35], 0);
	TextDrawSetOutline(RadialVehTD[35], 0);
	TextDrawBackgroundColor(RadialVehTD[35], 255);
	TextDrawFont(RadialVehTD[35], 0);
	TextDrawSetProportional(RadialVehTD[35], 1);

	RadialVehTD[36] = TextDrawCreate(220.000, 239.000, "-");
	TextDrawLetterSize(RadialVehTD[36], 0.851, 1.200);
	TextDrawTextSize(RadialVehTD[36], 400.000, 17.000);
	TextDrawAlignment(RadialVehTD[36], 1);
	TextDrawColor(RadialVehTD[36], -1);
	TextDrawSetShadow(RadialVehTD[36], 0);
	TextDrawSetOutline(RadialVehTD[36], 0);
	TextDrawBackgroundColor(RadialVehTD[36], 255);
	TextDrawFont(RadialVehTD[36], 0);
	TextDrawSetProportional(RadialVehTD[36], 1);
}

CreateRadialCardTD()
{
	RadialCardTD[22] = TextDrawCreate(141.000, 194.000, "LD_BUM:blkdot"); //tunjukkan ktp
	TextDrawTextSize(RadialCardTD[22], 34.000, 35.000);
	TextDrawAlignment(RadialCardTD[22], 1);
	TextDrawColor(RadialCardTD[22], 0x00000066);
	TextDrawSetShadow(RadialCardTD[22], 0);
	TextDrawSetOutline(RadialCardTD[22], 0);
	TextDrawBackgroundColor(RadialCardTD[22], 255);
	TextDrawFont(RadialCardTD[22], 4);
	TextDrawSetProportional(RadialCardTD[22], 1);
	TextDrawSetSelectable(RadialCardTD[22], 1);

	RadialCardTD[23] = TextDrawCreate(186.000, 194.000, "LD_BUM:blkdot"); //lihat sim
	TextDrawTextSize(RadialCardTD[23], 34.000, 35.000);
	TextDrawAlignment(RadialCardTD[23], 1);
	TextDrawColor(RadialCardTD[23], 0x00000066);
	TextDrawSetShadow(RadialCardTD[23], 0);
	TextDrawSetOutline(RadialCardTD[23], 0);
	TextDrawBackgroundColor(RadialCardTD[23], 255);
	TextDrawFont(RadialCardTD[23], 4);
	TextDrawSetProportional(RadialCardTD[23], 1);
	TextDrawSetSelectable(RadialCardTD[23], 1);

	RadialCardTD[24] = TextDrawCreate(232.000, 194.000, "LD_BUM:blkdot"); //TunjukansimB
	TextDrawTextSize(RadialCardTD[24], 34.000, 35.000);
	TextDrawAlignment(RadialCardTD[24], 1);
	TextDrawColor(RadialCardTD[24], 0x00000066);
	TextDrawSetShadow(RadialCardTD[24], 0);
	TextDrawSetOutline(RadialCardTD[24], 0);
	TextDrawBackgroundColor(RadialCardTD[24], 255);
	TextDrawFont(RadialCardTD[24], 4);
	TextDrawSetProportional(RadialCardTD[24], 1);
	TextDrawSetSelectable(RadialCardTD[24], 1);

	RadialCardTD[25] = TextDrawCreate(96.000, 194.000, "LD_BUM:blkdot"); //lihat ktp
	TextDrawTextSize(RadialCardTD[25], 34.000, 35.000);
	TextDrawAlignment(RadialCardTD[25], 1);
	TextDrawColor(RadialCardTD[25], 0x00000066);
	TextDrawSetShadow(RadialCardTD[25], 0);
	TextDrawSetOutline(RadialCardTD[25], 0);
	TextDrawBackgroundColor(RadialCardTD[25], 255);
	TextDrawFont(RadialCardTD[25], 4);
	TextDrawSetProportional(RadialCardTD[25], 1);
	TextDrawSetSelectable(RadialCardTD[25], 1);

	RadialCardTD[1] = TextDrawCreate(166.000, 238.000, "LD_BEAT:chit"); //close
	TextDrawTextSize(RadialCardTD[1], 30.000, 30.000);
	TextDrawAlignment(RadialCardTD[1], 1);
	TextDrawColor(RadialCardTD[1], 0x00000066);
	TextDrawSetShadow(RadialCardTD[1], 0);
	TextDrawSetOutline(RadialCardTD[1], 0);
	TextDrawBackgroundColor(RadialCardTD[1], 255);
	TextDrawFont(RadialCardTD[1], 4);
	TextDrawSetProportional(RadialCardTD[1], 1);
	TextDrawSetSelectable(RadialCardTD[1], 1);

	RadialCardTD[0] = TextDrawCreate(177.000, 245.000, "X");
	TextDrawLetterSize(RadialCardTD[0], 0.349, 1.590);
	TextDrawAlignment(RadialCardTD[0], 1);
	TextDrawColor(RadialCardTD[0], 0xffffffcc);
	TextDrawSetShadow(RadialCardTD[0], 0);
	TextDrawSetOutline(RadialCardTD[0], 0);
	TextDrawBackgroundColor(RadialCardTD[0], 150);
	TextDrawFont(RadialCardTD[0], 1);
	TextDrawSetProportional(RadialCardTD[0], 1);

	RadialCardTD[2] = TextDrawCreate(103.000, 205.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialCardTD[2], 21.000, 12.000);
	TextDrawAlignment(RadialCardTD[2], 1);
	TextDrawColor(RadialCardTD[2], 0xffffffcc);
	TextDrawSetShadow(RadialCardTD[2], 0);
	TextDrawSetOutline(RadialCardTD[2], 0);
	TextDrawBackgroundColor(RadialCardTD[2], 255);
	TextDrawFont(RadialCardTD[2], 4);
	TextDrawSetProportional(RadialCardTD[2], 1);

	RadialCardTD[3] = TextDrawCreate(118.000, 206.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialCardTD[3], 5.000, 7.000);
	TextDrawAlignment(RadialCardTD[3], 1);
	TextDrawColor(RadialCardTD[3], 0x3f3f3f8c);
	TextDrawSetShadow(RadialCardTD[3], 0);
	TextDrawSetOutline(RadialCardTD[3], 0);
	TextDrawBackgroundColor(RadialCardTD[3], 255);
	TextDrawFont(RadialCardTD[3], 4);
	TextDrawSetProportional(RadialCardTD[3], 1);

	RadialCardTD[4] = TextDrawCreate(104.000, 206.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialCardTD[4], 13.000, 2.000);
	TextDrawAlignment(RadialCardTD[4], 1);
	TextDrawColor(RadialCardTD[4], 0x3f3f3f8c);
	TextDrawSetShadow(RadialCardTD[4], 0);
	TextDrawSetOutline(RadialCardTD[4], 0);
	TextDrawBackgroundColor(RadialCardTD[4], 255);
	TextDrawFont(RadialCardTD[4], 4);
	TextDrawSetProportional(RadialCardTD[4], 1);

	RadialCardTD[5] = TextDrawCreate(104.000, 209.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialCardTD[5], 13.000, 2.000);
	TextDrawAlignment(RadialCardTD[5], 1);
	TextDrawColor(RadialCardTD[5], 0x3f3f3f8c);
	TextDrawSetShadow(RadialCardTD[5], 0);
	TextDrawSetOutline(RadialCardTD[5], 0);
	TextDrawBackgroundColor(RadialCardTD[5], 255);
	TextDrawFont(RadialCardTD[5], 4);
	TextDrawSetProportional(RadialCardTD[5], 1);

	RadialCardTD[6] = TextDrawCreate(148.000, 205.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialCardTD[6], 21.000, 12.000);
	TextDrawAlignment(RadialCardTD[6], 1);
	TextDrawColor(RadialCardTD[6], 0xffffffcc);
	TextDrawSetShadow(RadialCardTD[6], 0);
	TextDrawSetOutline(RadialCardTD[6], 0);
	TextDrawBackgroundColor(RadialCardTD[6], 255);
	TextDrawFont(RadialCardTD[6], 4);
	TextDrawSetProportional(RadialCardTD[6], 1);

	RadialCardTD[7] = TextDrawCreate(163.000, 206.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialCardTD[7], 5.000, 7.000);
	TextDrawAlignment(RadialCardTD[7], 1);
	TextDrawColor(RadialCardTD[7], 0x3f3f3f8c);
	TextDrawSetShadow(RadialCardTD[7], 0);
	TextDrawSetOutline(RadialCardTD[7], 0);
	TextDrawBackgroundColor(RadialCardTD[7], 255);
	TextDrawFont(RadialCardTD[7], 4);
	TextDrawSetProportional(RadialCardTD[7], 1);

	RadialCardTD[8] = TextDrawCreate(149.000, 206.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialCardTD[8], 13.000, 2.000);
	TextDrawAlignment(RadialCardTD[8], 1);
	TextDrawColor(RadialCardTD[8], 0x3f3f3f8c);
	TextDrawSetShadow(RadialCardTD[8], 0);
	TextDrawSetOutline(RadialCardTD[8], 0);
	TextDrawBackgroundColor(RadialCardTD[8], 255);
	TextDrawFont(RadialCardTD[8], 4);
	TextDrawSetProportional(RadialCardTD[8], 1);

	RadialCardTD[9] = TextDrawCreate(149.000, 209.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialCardTD[9], 13.000, 2.000);
	TextDrawAlignment(RadialCardTD[9], 1);
	TextDrawColor(RadialCardTD[9], 0x3f3f3f8c);
	TextDrawSetShadow(RadialCardTD[9], 0);
	TextDrawSetOutline(RadialCardTD[9], 0);
	TextDrawBackgroundColor(RadialCardTD[9], 255);
	TextDrawFont(RadialCardTD[9], 4);
	TextDrawSetProportional(RadialCardTD[9], 1);

	RadialCardTD[10] = TextDrawCreate(193.000, 205.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialCardTD[10], 21.000, 12.000);
	TextDrawAlignment(RadialCardTD[10], 1);
	TextDrawColor(RadialCardTD[10], 0xffffffcc);
	TextDrawSetShadow(RadialCardTD[10], 0);
	TextDrawSetOutline(RadialCardTD[10], 0);
	TextDrawBackgroundColor(RadialCardTD[10], 255);
	TextDrawFont(RadialCardTD[10], 4);
	TextDrawSetProportional(RadialCardTD[10], 1);

	RadialCardTD[11] = TextDrawCreate(194.000, 208.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialCardTD[11], 5.000, 7.000);
	TextDrawAlignment(RadialCardTD[11], 1);
	TextDrawColor(RadialCardTD[11], 0x3f3f3f8c);
	TextDrawSetShadow(RadialCardTD[11], 0);
	TextDrawSetOutline(RadialCardTD[11], 0);
	TextDrawBackgroundColor(RadialCardTD[11], 255);
	TextDrawFont(RadialCardTD[11], 4);
	TextDrawSetProportional(RadialCardTD[11], 1);

	RadialCardTD[12] = TextDrawCreate(200.000, 209.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialCardTD[12], 11.000, 2.000);
	TextDrawAlignment(RadialCardTD[12], 1);
	TextDrawColor(RadialCardTD[12], 0x3f3f3f8c);
	TextDrawSetShadow(RadialCardTD[12], 0);
	TextDrawSetOutline(RadialCardTD[12], 0);
	TextDrawBackgroundColor(RadialCardTD[12], 255);
	TextDrawFont(RadialCardTD[12], 4);
	TextDrawSetProportional(RadialCardTD[12], 1);

	RadialCardTD[13] = TextDrawCreate(200.000, 212.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialCardTD[13], 11.000, 2.000);
	TextDrawAlignment(RadialCardTD[13], 1);
	TextDrawColor(RadialCardTD[13], 0x3f3f3f8c);
	TextDrawSetShadow(RadialCardTD[13], 0);
	TextDrawSetOutline(RadialCardTD[13], 0);
	TextDrawBackgroundColor(RadialCardTD[13], 255);
	TextDrawFont(RadialCardTD[13], 4);
	TextDrawSetProportional(RadialCardTD[13], 1);

	RadialCardTD[14] = TextDrawCreate(239.000, 205.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialCardTD[14], 21.000, 12.000);
	TextDrawAlignment(RadialCardTD[14], 1);
	TextDrawColor(RadialCardTD[14], 0xffffffcc);
	TextDrawSetShadow(RadialCardTD[14], 0);
	TextDrawSetOutline(RadialCardTD[14], 0);
	TextDrawBackgroundColor(RadialCardTD[14], 255);
	TextDrawFont(RadialCardTD[14], 4);
	TextDrawSetProportional(RadialCardTD[14], 1);

	RadialCardTD[15] = TextDrawCreate(240.000, 208.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialCardTD[15], 5.000, 7.000);
	TextDrawAlignment(RadialCardTD[15], 1);
	TextDrawColor(RadialCardTD[15], 0x3f3f3f8c);
	TextDrawSetShadow(RadialCardTD[15], 0);
	TextDrawSetOutline(RadialCardTD[15], 0);
	TextDrawBackgroundColor(RadialCardTD[15], 255);
	TextDrawFont(RadialCardTD[15], 4);
	TextDrawSetProportional(RadialCardTD[15], 1);

	RadialCardTD[16] = TextDrawCreate(246.000, 209.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialCardTD[16], 11.000, 2.000);
	TextDrawAlignment(RadialCardTD[16], 1);
	TextDrawColor(RadialCardTD[16], 0x3f3f3f8c);
	TextDrawSetShadow(RadialCardTD[16], 0);
	TextDrawSetOutline(RadialCardTD[16], 0);
	TextDrawBackgroundColor(RadialCardTD[16], 255);
	TextDrawFont(RadialCardTD[16], 4);
	TextDrawSetProportional(RadialCardTD[16], 1);

	RadialCardTD[17] = TextDrawCreate(246.000, 212.000, "LD_BUM:blkdot");
	TextDrawTextSize(RadialCardTD[17], 11.000, 2.000);
	TextDrawAlignment(RadialCardTD[17], 1);
	TextDrawColor(RadialCardTD[17], 0x3f3f3f8c);
	TextDrawSetShadow(RadialCardTD[17], 0);
	TextDrawSetOutline(RadialCardTD[17], 0);
	TextDrawBackgroundColor(RadialCardTD[17], 255);
	TextDrawFont(RadialCardTD[17], 4);
	TextDrawSetProportional(RadialCardTD[17], 1);

	RadialCardTD[18] = TextDrawCreate(100.000, 219.000, "LIHAT IDCARD");
	TextDrawLetterSize(RadialCardTD[18], 0.119, 0.699);
	TextDrawAlignment(RadialCardTD[18], 1);
	TextDrawColor(RadialCardTD[18], 0xffffffcc);
	TextDrawSetShadow(RadialCardTD[18], 0);
	TextDrawSetOutline(RadialCardTD[18], 0);
	TextDrawBackgroundColor(RadialCardTD[18], 150);
	TextDrawFont(RadialCardTD[18], 1);
	TextDrawSetProportional(RadialCardTD[18], 1);

	RadialCardTD[19] = TextDrawCreate(145.000, 219.000, "BERI IDCARD");
	TextDrawLetterSize(RadialCardTD[19], 0.129, 0.699);
	TextDrawAlignment(RadialCardTD[19], 1);
	TextDrawColor(RadialCardTD[19], 0xffffffcc);
	TextDrawSetShadow(RadialCardTD[19], 0);
	TextDrawSetOutline(RadialCardTD[19], 0);
	TextDrawBackgroundColor(RadialCardTD[19], 150);
	TextDrawFont(RadialCardTD[19], 1);
	TextDrawSetProportional(RadialCardTD[19], 1);

	RadialCardTD[20] = TextDrawCreate(193.000, 219.000, "LIHAT SIM");
	TextDrawLetterSize(RadialCardTD[20], 0.129, 0.699);
	TextDrawAlignment(RadialCardTD[20], 1);
	TextDrawColor(RadialCardTD[20], 0xffffffcc);
	TextDrawSetShadow(RadialCardTD[20], 0);
	TextDrawSetOutline(RadialCardTD[20], 0);
	TextDrawBackgroundColor(RadialCardTD[20], 150);
	TextDrawFont(RadialCardTD[20], 1);
	TextDrawSetProportional(RadialCardTD[20], 1);

	RadialCardTD[21] = TextDrawCreate(240.000, 219.000, "BERI SIM");
	TextDrawLetterSize(RadialCardTD[21], 0.129, 0.699);
	TextDrawAlignment(RadialCardTD[21], 1);
	TextDrawColor(RadialCardTD[21], 0xffffffcc);
	TextDrawSetShadow(RadialCardTD[21], 0);
	TextDrawSetOutline(RadialCardTD[21], 0);
	TextDrawBackgroundColor(RadialCardTD[21], 150);
	TextDrawFont(RadialCardTD[21], 1);
	TextDrawSetProportional(RadialCardTD[21], 1);
}

ShowRadialTD(playerid)
{
	HideServerNameTD(playerid);
	HideSpeedoTD(playerid);
	HideHBETD(playerid);

    for(new x; x < 58; x++)
    {
        TextDrawShowForPlayer(playerid, RadialTD[x]);
    }
    SelectTextDraw(playerid, 0xff91a4cc);
}

HideRadialTD(playerid)
{
    for(new x; x < 58; x++)
    {
        TextDrawHideForPlayer(playerid, RadialTD[x]);
    }
}

ShowRadialFashionTD(playerid)
{
    for(new x; x < 28; x++)
    {
        TextDrawShowForPlayer(playerid, RadialFashionTD[x]);
    }
    SelectTextDraw(playerid, 0xff91a4cc);
}

HideRadialFashionTD(playerid)
{
    for(new x; x < 28; x++)
    {
        TextDrawHideForPlayer(playerid, RadialFashionTD[x]);
    }
}

ShowRadialVehTD(playerid)
{
    for(new x; x < 44; x++)
    {
        TextDrawShowForPlayer(playerid, RadialVehTD[x]);
    }
    SelectTextDraw(playerid, 0xff91a4cc);
}

HideRadialVehTD(playerid)
{
    for(new x; x < 44; x++)
    {
        TextDrawHideForPlayer(playerid, RadialVehTD[x]);
    }
}

ShowRadialCardTD(playerid)
{
    for(new x; x < 26; x++)
    {
        TextDrawShowForPlayer(playerid, RadialCardTD[x]);
    }
    SelectTextDraw(playerid, 0xff91a4cc);
}

HideRadialCardTD(playerid)
{
    for(new x; x < 26; x++)
    {
        TextDrawHideForPlayer(playerid, RadialCardTD[x]);
    }
}