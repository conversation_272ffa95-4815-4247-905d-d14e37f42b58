CreateWeddingExt()
{
    new STREAMER_TAG_OBJECT:weddingtxt;
    weddingtxt = CreateDynamicObject(19380, 722.551574, -1628.588256, 1.287757, 360.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 6094, "canalsg_law", "ws_sheetwood_clean", 0x00000000);
    weddingtxt = CreateDynamicObject(19380, 722.551574, -1638.037475, 1.287755, 360.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 6094, "canalsg_law", "ws_sheetwood_clean", 0x00000000);
    weddingtxt = CreateDynamicObject(19380, 722.551574, -1647.507324, 1.287755, 360.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 6094, "canalsg_law", "ws_sheetwood_clean", 0x00000000);
    weddingtxt = CreateDynamicObject(19380, 722.551574, -1657.027954, 1.287754, 360.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 6094, "canalsg_law", "ws_sheetwood_clean", 0x00000000);
    weddingtxt = CreateDynamicObject(19380, 732.961486, -1657.027954, 1.287754, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 6094, "canalsg_law", "ws_sheetwood_clean", 0x00000000);
    weddingtxt = CreateDynamicObject(19380, 732.961486, -1647.457153, 1.287755, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 6094, "canalsg_law", "ws_sheetwood_clean", 0x00000000);
    weddingtxt = CreateDynamicObject(19380, 732.961486, -1637.838256, 1.287755, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 6094, "canalsg_law", "ws_sheetwood_clean", 0x00000000);
    weddingtxt = CreateDynamicObject(19380, 732.961486, -1628.626953, 1.287755, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 6094, "canalsg_law", "ws_sheetwood_clean", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 727.759765, -1652.792968, 1.373692, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 726.808715, -1652.957885, 1.373692, 0.000000, 90.000000, 110.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 728.716796, -1652.970703, 1.373692, 0.000000, 90.000000, 70.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 729.553649, -1653.450805, 1.373692, 0.000000, 90.000000, 50.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 725.956481, -1653.450927, 1.373692, 0.000000, 90.000000, 130.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 725.328735, -1654.197875, 1.373692, 0.000000, 90.000000, 150.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 725.004577, -1655.115722, 1.373692, 0.000000, 90.000000, 170.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 725.003234, -1656.079833, 1.373692, 0.000000, 90.000000, 190.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 730.181335, -1654.197753, 1.373692, 0.000000, 90.000000, 30.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 730.501403, -1655.095581, 1.373692, 0.000000, 90.000000, 10.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 730.503601, -1656.060058, 1.373692, 0.000000, 90.000000, -10.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 727.626647, -1655.895385, 1.373692, 0.000000, 90.000000, 1.399999, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 727.602722, -1654.915649, 1.373692, 0.000000, 90.000000, 1.399999, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 730.310791, -1656.439575, 1.373692, 0.000000, 90.000000, 1.399999, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 725.141967, -1656.565795, 1.373692, 0.000000, 90.000000, 1.399999, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 727.281311, -1656.513549, 1.373692, 0.000000, 90.000000, 1.399999, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19445, 727.751525, -1658.888183, 1.389631, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 731.740722, -1657.224243, 1.373692, 0.000000, 89.700004, -86.499923, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 723.747802, -1657.490844, 1.332594, 0.000000, 89.700004, -96.299934, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(18762, 723.277343, -1660.047241, 3.799628, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 8534, "tikimotel", "sa_wood01_128", 0x00000000);
    weddingtxt = CreateDynamicObject(18762, 732.108398, -1660.047241, 3.799628, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 8534, "tikimotel", "sa_wood01_128", 0x00000000);
    weddingtxt = CreateDynamicObject(18762, 730.118591, -1660.047241, 5.849634, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 8534, "tikimotel", "sa_wood01_128", 0x00000000);
    weddingtxt = CreateDynamicObject(18762, 725.276977, -1660.047241, 5.849634, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 8534, "tikimotel", "sa_wood01_128", 0x00000000);
    weddingtxt = CreateDynamicObject(19353, 731.691101, -1659.605834, 4.999629, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 825, "gta_proc_bigbush", "veg_bush1", 0x00000000);
    weddingtxt = CreateDynamicObject(19353, 729.721252, -1659.605834, 4.999629, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 825, "gta_proc_bigbush", "veg_bush1", 0x00000000);
    weddingtxt = CreateDynamicObject(19353, 727.751037, -1659.605834, 4.999629, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 825, "gta_proc_bigbush", "veg_bush1", 0x00000000);
    weddingtxt = CreateDynamicObject(19353, 725.831115, -1659.605834, 4.999629, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 825, "gta_proc_bigbush", "veg_bush1", 0x00000000);
    weddingtxt = CreateDynamicObject(19353, 723.810913, -1659.605834, 4.999629, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 825, "gta_proc_bigbush", "veg_bush1", 0x00000000);
    weddingtxt = CreateDynamicObject(984, 717.439880, -1653.750976, 2.023691, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 1, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 2, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 3, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 4, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 5, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 6, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 7, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 8, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9902, "coastground", "coasty_fencet_sfe", 0x00000000);
    weddingtxt = CreateDynamicObject(984, 717.439880, -1645.732666, 2.023691, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 1, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 2, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 3, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 4, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 5, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 6, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 7, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 8, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9902, "coastground", "coasty_fencet_sfe", 0x00000000);
    weddingtxt = CreateDynamicObject(984, 738.070495, -1630.299072, 2.023691, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 1, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 2, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 3, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 4, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 5, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 6, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 7, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 8, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9902, "coastground", "coasty_fencet_sfe", 0x00000000);
    weddingtxt = CreateDynamicObject(984, 738.070495, -1643.059570, 2.023691, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 1, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 2, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 3, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 4, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 5, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 6, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 7, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 8, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9902, "coastground", "coasty_fencet_sfe", 0x00000000);
    weddingtxt = CreateDynamicObject(984, 738.070495, -1654.231079, 2.023691, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 1, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 2, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 3, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 4, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 5, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 6, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 7, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 8, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9902, "coastground", "coasty_fencet_sfe", 0x00000000);
    weddingtxt = CreateDynamicObject(984, 731.630615, -1623.879272, 2.023691, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 1, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 2, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 3, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 4, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 5, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 6, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 7, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 8, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9902, "coastground", "coasty_fencet_sfe", 0x00000000);
    weddingtxt = CreateDynamicObject(984, 723.619750, -1623.879272, 2.023691, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 1, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 2, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 3, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 4, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 5, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 6, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 7, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 8, 18250, "cw_junkbuildcs_t", "Was_scrpyd_fence_wd_stain", 0x00000000);
    SetDynamicObjectMaterial(weddingtxt, 9, 9902, "coastground", "coasty_fencet_sfe", 0x00000000);
    weddingtxt = CreateDynamicObject(19445, 727.751525, -1646.659057, 1.389631, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19445, 727.751525, -1637.029418, 1.389631, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19445, 727.751525, -1633.248657, 1.389631, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 9259, "presidio01_sfn", "sl_whitewood01", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 730.370483, -1629.523559, 3.093693, 0.000000, 20.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 725.060363, -1629.523559, 3.093693, 0.000000, -20.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 725.060363, -1638.114990, 3.093693, 0.000000, -20.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 730.490844, -1638.114990, 3.093693, 0.000000, 20.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 729.351501, -1629.503540, 5.276418, 0.000000, -68.799987, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 726.040100, -1629.523559, 5.270929, 0.000000, -68.899925, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 729.351501, -1638.097534, 5.276418, 0.000000, -68.799980, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 726.040100, -1638.117553, 5.270929, 0.000000, -68.899932, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 736.950927, -1659.736083, 3.093693, 0.000000, 0.000000, -49.499996, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 734.594970, -1660.117553, 3.093693, 0.000000, 0.000000, -49.499996, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 718.744567, -1660.571899, 3.093693, 0.000000, 0.000000, -129.499969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    weddingtxt = CreateDynamicObject(19426, 720.933593, -1660.290161, 3.093693, 0.000000, 0.000000, -117.100013, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 2755, "ab_dojowall", "ab_trellis", 0x00000000);
    weddingtxt = CreateDynamicObject(1291, 727.820251, -1654.287109, 1.719630, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    weddingtxt = CreateDynamicObject(19445, 733.021423, -1623.778442, 0.789632, 360.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 825, "gta_proc_bigbush", "veg_bush1", 0x00000000);
    weddingtxt = CreateDynamicObject(19445, 723.191406, -1623.778442, 0.789631, 360.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 825, "gta_proc_bigbush", "veg_bush1", 0x00000000);
    weddingtxt = CreateDynamicObject(19445, 717.341491, -1646.558349, 0.679629, 360.000000, 180.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 825, "gta_proc_bigbush", "veg_bush1", 0x00000000);
    weddingtxt = CreateDynamicObject(19445, 717.371520, -1655.848754, 0.679628, 360.000000, 180.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(weddingtxt, 0, 825, "gta_proc_bigbush", "veg_bush1", 0x00000000);
    weddingtxt = CreateDynamicObject(19325, 738.154907, -1633.912109, 4.055568, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(weddingtxt, 0, "Angeles", 130, "Arial", 48, 1, 0xFFFFFFFF, 0x00000000, 1);
    weddingtxt = CreateDynamicObject(19325, 738.154907, -1634.542358, 3.525568, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(weddingtxt, 0, "WeddingOudoor", 130, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    weddingtxt = CreateDynamicObject(19325, 738.154907, -1650.322265, 4.055568, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(weddingtxt, 0, "Angeles", 130, "Arial", 48, 1, 0xFFFFFFFF, 0x00000000, 1);
    weddingtxt = CreateDynamicObject(19325, 738.154907, -1650.952514, 3.525568, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(weddingtxt, 0, "WeddingOutdoor", 130, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    weddingtxt = CreateDynamicObject(19325, 717.536132, -1652.112915, 4.055568, 0.000000, 0.000022, 179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(weddingtxt, 0, "Angeles", 130, "Arial", 48, 1, 0xFFFFFFFF, 0x00000000, 1);
    weddingtxt = CreateDynamicObject(19325, 717.536132, -1651.482666, 3.525568, 0.000000, 0.000022, 179.999893, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(weddingtxt, 0, "WeddingOutdoor", 130, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(14468, 725.165222, -1660.347534, 6.379631, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14468, 729.695251, -1660.347534, 6.379631, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, 724.906311, -1629.545654, 2.063693, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, 730.426086, -1629.545654, 2.063693, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, 730.426086, -1638.208374, 2.063693, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, 725.116271, -1638.208374, 2.063693, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19325, 734.855163, -1661.151733, 3.335567, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19325, 720.704101, -1661.151733, 3.335567, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19610, 727.611816, -1654.475585, 2.625806, 16.400001, 0.000000, -156.300064, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19611, 727.627807, -1654.461425, 0.959631, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(938, 722.713928, -1652.336669, 1.547137, -112.700004, 0.000000, 54.899993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(938, 733.533386, -1653.230224, 1.553174, -112.700004, 0.000000, -51.899990, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2108, 723.449707, -1658.838989, 1.459630, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2108, 732.100158, -1658.838989, 1.459630, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 731.619873, -1644.019165, 1.373692, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 733.430480, -1644.019165, 1.373692, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 735.230468, -1644.019165, 1.373692, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 731.619873, -1640.615966, 1.373692, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 733.430480, -1640.615966, 1.373692, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 735.230468, -1640.615966, 1.373692, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 731.619873, -1637.645141, 1.373692, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 733.430480, -1637.645141, 1.373692, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 735.230468, -1637.645141, 1.373692, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 731.619873, -1630.882812, 1.373692, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 733.430480, -1630.882812, 1.373692, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 735.230468, -1630.882812, 1.373692, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 731.619873, -1627.383178, 1.373692, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 733.430480, -1627.383178, 1.373692, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 735.230468, -1627.383178, 1.373692, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 718.578979, -1644.279418, 1.373692, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 720.389587, -1644.279418, 1.373692, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 722.189575, -1644.279418, 1.373692, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 718.578979, -1640.876220, 1.373692, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 720.389587, -1640.876220, 1.373692, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 722.189575, -1640.876220, 1.373692, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1215, 724.986083, -1627.730224, 1.373695, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1215, 730.447143, -1627.730224, 1.373695, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 722.189575, -1630.632690, 1.373692, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 720.329833, -1630.632690, 1.373692, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 718.498901, -1630.632690, 1.373692, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 718.498901, -1633.902832, 1.373692, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 720.318908, -1633.902832, 1.373692, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1724, 722.178955, -1633.902832, 1.373692, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1215, 722.546081, -1659.879394, 1.373695, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1215, 732.785949, -1659.879394, 1.373695, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19943, 720.189147, -1625.348510, -5.857017, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19943, 728.289245, -1625.348510, -5.857017, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19943, 737.109741, -1625.348510, -5.857017, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19943, 737.109741, -1643.179077, -5.857017, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19943, 737.109741, -1660.158813, -5.857017, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19943, 726.799865, -1660.158813, -5.857017, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19943, 718.360473, -1660.158813, -5.857017, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19325, 738.154907, -1650.361450, 3.335567, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19325, 738.154907, -1634.121582, 3.335567, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19325, 717.404907, -1651.701660, 3.335567, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
}

CreateWeddingRooftop()
{
    static wedrooftxt;
    wedrooftxt = CreateDynamicObject(18980, 1565.559326, -1231.678222, 278.164154, 89.999992, 90.000076, -89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 6882, "vgnland", "hiwaygravel1_256", 0x00000000);
    wedrooftxt = CreateDynamicObject(18980, 1584.760864, -1231.678222, 278.164154, 89.999992, 90.000076, -89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 6882, "vgnland", "hiwaygravel1_256", 0x00000000);
    wedrooftxt = CreateDynamicObject(18981, 1575.058715, -1231.422485, 277.753906, 0.000000, 90.000061, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14581, "ab_mafiasuitea", "cof_wood2", 0x00000000);
    wedrooftxt = CreateDynamicObject(14387, 1568.417358, -1245.044311, 277.260681, -0.000045, 0.000000, -89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    wedrooftxt = CreateDynamicObject(19448, 1565.300781, -1224.003051, 280.293884, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10368, "cathedral_sfs", "ws_woodyhedge", 0x00000000);
    wedrooftxt = CreateDynamicObject(19448, 1565.821289, -1224.003051, 280.293884, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10368, "cathedral_sfs", "ws_woodyhedge", 0x00000000);
    wedrooftxt = CreateDynamicObject(19448, 1565.300781, -1233.613281, 280.293884, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10368, "cathedral_sfs", "ws_woodyhedge", 0x00000000);
    wedrooftxt = CreateDynamicObject(19448, 1565.821289, -1233.613281, 280.293884, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10368, "cathedral_sfs", "ws_woodyhedge", 0x00000000);
    wedrooftxt = CreateDynamicObject(19448, 1565.290771, -1239.354980, 280.293884, 0.000000, 0.000059, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10368, "cathedral_sfs", "ws_woodyhedge", 0x00000000);
    wedrooftxt = CreateDynamicObject(19448, 1565.831298, -1239.354980, 280.293884, 0.000000, 0.000059, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10368, "cathedral_sfs", "ws_woodyhedge", 0x00000000);
    wedrooftxt = CreateDynamicObject(19448, 1584.502441, -1224.003051, 280.293884, 0.000000, 0.000059, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10368, "cathedral_sfs", "ws_woodyhedge", 0x00000000);
    wedrooftxt = CreateDynamicObject(19448, 1585.022949, -1224.003051, 280.293884, 0.000000, 0.000059, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10368, "cathedral_sfs", "ws_woodyhedge", 0x00000000);
    wedrooftxt = CreateDynamicObject(19448, 1584.502441, -1233.613281, 280.293884, 0.000000, 0.000067, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10368, "cathedral_sfs", "ws_woodyhedge", 0x00000000);
    wedrooftxt = CreateDynamicObject(19448, 1585.022949, -1233.613281, 280.293884, 0.000000, 0.000067, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10368, "cathedral_sfs", "ws_woodyhedge", 0x00000000);
    wedrooftxt = CreateDynamicObject(19448, 1584.492431, -1239.354980, 280.293884, 0.000000, 0.000075, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10368, "cathedral_sfs", "ws_woodyhedge", 0x00000000);
    wedrooftxt = CreateDynamicObject(19448, 1585.032958, -1239.354980, 280.293884, 0.000000, 0.000075, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10368, "cathedral_sfs", "ws_woodyhedge", 0x00000000);
    wedrooftxt = CreateDynamicObject(14387, 1573.126220, -1245.044311, 277.260681, -0.000045, 0.000000, -89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    wedrooftxt = CreateDynamicObject(14387, 1577.846801, -1245.044311, 277.260681, -0.000045, 0.000000, -89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    wedrooftxt = CreateDynamicObject(14387, 1581.895507, -1245.044311, 277.260681, -0.000045, 0.000000, -89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    wedrooftxt = CreateDynamicObject(18763, 1586.062988, -1245.418823, 282.924194, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    wedrooftxt = CreateDynamicObject(18763, 1564.062988, -1245.418823, 282.924194, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    wedrooftxt = CreateDynamicObject(18763, 1586.062988, -1245.418823, 277.934204, 0.000000, 0.000059, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    wedrooftxt = CreateDynamicObject(18763, 1564.062988, -1245.418823, 277.934204, 0.000000, 0.000059, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    wedrooftxt = CreateDynamicObject(19376, 1584.381103, -1239.360351, 283.437591, 0.000014, -69.999938, 0.000009, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10023, "bigwhitesfe", "sfe_arch6", 0xFFFFFFFF);
    wedrooftxt = CreateDynamicObject(19376, 1584.381103, -1229.730102, 283.437591, 0.000014, -69.999938, 0.000009, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10023, "bigwhitesfe", "sfe_arch6", 0xFFFFFFFF);
    wedrooftxt = CreateDynamicObject(19376, 1565.953247, -1239.360351, 283.437591, 0.000000, 70.000038, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10023, "bigwhitesfe", "sfe_arch6", 0xFFFFFFFF);
    wedrooftxt = CreateDynamicObject(19376, 1565.953247, -1229.730102, 283.437591, 0.000000, 70.000038, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10023, "bigwhitesfe", "sfe_arch6", 0xFFFFFFFF);
    wedrooftxt = CreateDynamicObject(18763, 1586.613525, -1223.549682, 282.924194, 0.000000, 0.000074, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    wedrooftxt = CreateDynamicObject(18763, 1563.702636, -1223.549682, 282.924194, 0.000000, 0.000074, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    wedrooftxt = CreateDynamicObject(18763, 1586.613525, -1223.549682, 277.934204, 0.000000, 0.000081, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    wedrooftxt = CreateDynamicObject(18763, 1563.702636, -1223.549682, 277.934204, 0.000000, 0.000081, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1575.203979, -1238.929077, 277.759826, 89.999992, 180.000030, -89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(16088, 1575.202392, -1248.219726, 276.947479, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 16640, "a51", "ws_stationfloor", 0xFF505050);
    SetDynamicObjectMaterial(wedrooftxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(970, 1579.745849, -1225.738891, 278.789855, 0.000000, 0.000067, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(970, 1581.806884, -1225.748901, 278.789855, 0.000000, 0.000067, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(970, 1568.634521, -1225.758911, 278.789855, 0.000000, 0.000081, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(970, 1570.695556, -1225.748901, 278.789855, 0.000000, 0.000081, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1579.263916, -1222.185424, 278.539794, 89.999992, 90.000061, -89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10398, "countryclub_sfs", "hc_wall1", 0xFF505050);
    wedrooftxt = CreateDynamicObject(18766, 1571.063110, -1222.185424, 278.539794, 89.999992, 90.000061, -89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10398, "countryclub_sfs", "hc_wall1", 0xFF505050);
    wedrooftxt = CreateDynamicObject(14387, 1575.186645, -1225.803100, 278.040649, -0.000045, 0.000000, -89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    wedrooftxt = CreateDynamicObject(640, 1580.265625, -1225.160278, 278.949829, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 4992, "airportdetail", "bevflower1", 0x00000000);
    wedrooftxt = CreateDynamicObject(640, 1570.156005, -1225.160278, 278.949829, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 4992, "airportdetail", "bevflower1", 0x00000000);
    wedrooftxt = CreateDynamicObject(18981, 1574.121093, -1231.422485, 276.783935, 0.000000, 90.000068, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 3820, "boxhses_sfsx", "stonewall_la", 0x00000000);
    wedrooftxt = CreateDynamicObject(18981, 1576.199707, -1231.422485, 276.783935, 0.000000, 90.000068, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 3820, "boxhses_sfsx", "stonewall_la", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1569.169433, -1219.333251, 277.019836, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1575.158935, -1219.333251, 277.019836, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1581.158325, -1219.333251, 277.019836, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1581.158325, -1219.663574, 277.019836, -0.000045, 0.000000, -89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1575.167968, -1219.663574, 277.019836, -0.000045, 0.000000, -89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1569.178588, -1219.663574, 277.019836, -0.000045, 0.000000, -89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(949, 1572.893920, -1221.095947, 279.679809, 0.000000, 0.000090, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 10756, "airportroads_sfse", "ws_white_wall1", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 4992, "airportdetail", "bevflower1", 0x00000000);
    wedrooftxt = CreateDynamicObject(949, 1577.464355, -1221.095947, 279.679809, 0.000000, 0.000090, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 10756, "airportroads_sfse", "ws_white_wall1", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 4992, "airportdetail", "bevflower1", 0x00000000);
    wedrooftxt = CreateDynamicObject(18275, 1575.190063, -1224.187011, 281.239868, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 4992, "airportdetail", "kb_ivy_256", 0x00000000);
    wedrooftxt = CreateDynamicObject(1726, 1583.204101, -1229.787841, 278.253906, -0.000014, -0.000043, -159.999725, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 8534, "tikimotel", "sa_wood01_128", 0x00000000);
    wedrooftxt = CreateDynamicObject(1726, 1579.893310, -1229.787841, 278.253906, -0.000014, -0.000043, -159.999725, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 8534, "tikimotel", "sa_wood01_128", 0x00000000);
    wedrooftxt = CreateDynamicObject(1726, 1572.402465, -1230.468505, 278.253906, 0.000017, -0.000063, 159.999679, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 8534, "tikimotel", "sa_wood01_128", 0x00000000);
    wedrooftxt = CreateDynamicObject(1726, 1569.091674, -1230.468505, 278.253906, 0.000017, -0.000063, 159.999679, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 8534, "tikimotel", "sa_wood01_128", 0x00000000);
    wedrooftxt = CreateDynamicObject(1726, 1583.204101, -1227.667358, 278.253906, -0.000014, -0.000049, -159.999771, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 8534, "tikimotel", "sa_wood01_128", 0x00000000);
    wedrooftxt = CreateDynamicObject(1726, 1579.893310, -1227.667358, 278.253906, -0.000014, -0.000049, -159.999771, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 8534, "tikimotel", "sa_wood01_128", 0x00000000);
    wedrooftxt = CreateDynamicObject(1726, 1572.402465, -1228.348022, 278.253906, 0.000017, -0.000072, 159.999633, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 8534, "tikimotel", "sa_wood01_128", 0x00000000);
    wedrooftxt = CreateDynamicObject(1726, 1569.091674, -1228.348022, 278.253906, 0.000017, -0.000072, 159.999633, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 8534, "tikimotel", "sa_wood01_128", 0x00000000);
    wedrooftxt = CreateDynamicObject(2122, 1572.492919, -1232.109619, 278.783935, -0.000059, 0.000000, -89.999816, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(2122, 1571.002685, -1232.109619, 278.783935, -0.000059, 0.000000, -89.999816, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(2122, 1569.642211, -1232.109619, 278.783935, -0.000059, 0.000000, -89.999816, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(640, 1566.430786, -1228.483520, 278.919799, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 3946, "bistro_plants", "starflower4", 0x00000000);
    wedrooftxt = CreateDynamicObject(640, 1566.430786, -1232.553100, 278.919799, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 3946, "bistro_plants", "starflower4", 0x00000000);
    wedrooftxt = CreateDynamicObject(640, 1584.021240, -1228.483398, 278.919799, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 3946, "bistro_plants", "starflower4", 0x00000000);
    wedrooftxt = CreateDynamicObject(640, 1584.021240, -1232.553100, 278.919799, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 3946, "bistro_plants", "starflower4", 0x00000000);
    wedrooftxt = CreateDynamicObject(18981, 1575.058715, -1219.192138, 266.733947, 89.999992, 180.000061, -89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14581, "ab_mafiasuitea", "cof_wood2", 0x00000000);
    wedrooftxt = CreateDynamicObject(18981, 1585.053588, -1257.176513, 276.408935, 0.000000, 90.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4829, "airport_las", "Grass_128HV", 0x00000000);
    wedrooftxt = CreateDynamicObject(19482, 1575.097534, -1224.741210, 282.999786, -0.000045, 0.000000, -89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(wedrooftxt, 0, "I LOVE U", 130, "Arial", 80, 1, 0xffffffff, 0x00000000, 1);
    wedrooftxt = CreateDynamicObject(19482, 1575.097534, -1224.761230, 282.999786, -0.000045, 0.000000, -89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(wedrooftxt, 0, "I LOVE U", 130, "Arial", 80, 1, 0xFF0000FF, 0x00000000, 1);
    wedrooftxt = CreateDynamicObject(1726, 1568.042236, -1223.499145, 279.039794, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 13059, "ce_fact03", "GB_truckdepot19", 0x00000000);
    wedrooftxt = CreateDynamicObject(1726, 1582.353271, -1221.498657, 279.039794, -0.000045, 0.000000, -89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 13059, "ce_fact03", "GB_truckdepot19", 0x00000000);
    wedrooftxt = CreateDynamicObject(2767, 1575.279296, -1221.092529, 280.625518, -49.999992, 0.000101, 0.000075, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18064, "ab_sfammuunits", "gun_blackbox", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(2643, 1576.188354, -1222.531005, 279.099792, -89.999992, 90.000106, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    wedrooftxt = CreateDynamicObject(2643, 1574.358398, -1222.531005, 279.099792, -89.999992, 90.000106, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1566.065063, -1245.976684, 276.751831, 65.000000, 179.999893, -179.999664, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1584.075439, -1245.976684, 276.751831, 65.000000, 179.999893, -179.999664, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(949, 1572.893920, -1220.375488, 279.679809, 0.000000, 0.000096, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 10756, "airportroads_sfse", "ws_white_wall1", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 4830, "airport2", "bevflower2", 0x00000000);
    wedrooftxt = CreateDynamicObject(949, 1577.464355, -1220.375488, 279.679809, 0.000000, 0.000096, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 10756, "airportroads_sfse", "ws_white_wall1", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 4830, "airport2", "bevflower2", 0x00000000);
    wedrooftxt = CreateDynamicObject(970, 1562.643066, -1227.205322, 278.793914, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    wedrooftxt = CreateDynamicObject(970, 1562.643066, -1231.375244, 278.793914, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    wedrooftxt = CreateDynamicObject(970, 1562.643066, -1235.545288, 278.793914, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    wedrooftxt = CreateDynamicObject(970, 1562.643066, -1239.715209, 278.793914, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    wedrooftxt = CreateDynamicObject(970, 1562.643066, -1243.875244, 278.793914, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    wedrooftxt = CreateDynamicObject(970, 1587.464355, -1227.205322, 278.793914, 0.000051, 0.000000, 89.999839, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    wedrooftxt = CreateDynamicObject(970, 1587.464355, -1231.375244, 278.793914, 0.000051, 0.000000, 89.999839, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    wedrooftxt = CreateDynamicObject(970, 1587.464355, -1235.545288, 278.793914, 0.000051, 0.000000, 89.999839, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    wedrooftxt = CreateDynamicObject(970, 1587.464355, -1239.715209, 278.793914, 0.000051, 0.000000, 89.999839, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    wedrooftxt = CreateDynamicObject(970, 1587.464355, -1243.875244, 278.793914, 0.000051, 0.000000, 89.999839, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 1717, "cj_tv", "green_glass_64", 0x00000000);
    wedrooftxt = CreateDynamicObject(19376, 1568.122558, -1223.408081, 284.727569, 0.000000, 70.000038, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10023, "bigwhitesfe", "sfe_arch6", 0xFF000000);
    wedrooftxt = CreateDynamicObject(19376, 1582.202392, -1223.408081, 284.727569, 0.000000, -69.999961, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10023, "bigwhitesfe", "sfe_arch6", 0xFF000000);
    wedrooftxt = CreateDynamicObject(18981, 1585.053588, -1232.177856, 276.408935, 0.000000, 90.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4829, "airport_las", "Grass_128HV", 0x00000000);
    wedrooftxt = CreateDynamicObject(18981, 1560.542480, -1232.177856, 276.438934, 0.000000, 90.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4829, "airport_las", "Grass_128HV", 0x00000000);
    wedrooftxt = CreateDynamicObject(11712, 1575.292236, -1221.131469, 280.539794, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(14448, 1575.154174, -1232.232177, 283.489837, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    wedrooftxt = CreateDynamicObject(14448, 1583.381225, -1232.232177, 280.793395, 40.000030, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    wedrooftxt = CreateDynamicObject(14448, 1566.916259, -1232.232177, 280.793395, -39.999946, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    wedrooftxt = CreateDynamicObject(3038, 1575.239746, -1240.286254, 283.219848, 0.000051, 0.000000, 89.999839, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18022, "genintintfasta", "diner_wall5", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 19470, "forsale01", "forsale01", 0x00000000);
    wedrooftxt = CreateDynamicObject(3038, 1575.239746, -1232.246337, 283.219848, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18022, "genintintfasta", "diner_wall5", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 19470, "forsale01", "forsale01", 0x00000000);
    wedrooftxt = CreateDynamicObject(3038, 1568.384033, -1234.914550, 283.109863, 0.000035, -0.000029, 130.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18022, "genintintfasta", "diner_wall5", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 19470, "forsale01", "forsale01", 0x00000000);
    wedrooftxt = CreateDynamicObject(3038, 1582.116333, -1234.980590, 283.219848, 0.000035, 0.000029, 49.999992, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18022, "genintintfasta", "diner_wall5", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 19470, "forsale01", "forsale01", 0x00000000);
    wedrooftxt = CreateDynamicObject(3038, 1568.384033, -1242.954467, 283.109863, 0.000039, -0.000033, 130.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18022, "genintintfasta", "diner_wall5", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 19470, "forsale01", "forsale01", 0x00000000);
    wedrooftxt = CreateDynamicObject(3038, 1582.116333, -1243.020507, 283.219848, 0.000039, 0.000033, 49.999992, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18022, "genintintfasta", "diner_wall5", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 19470, "forsale01", "forsale01", 0x00000000);
    wedrooftxt = CreateDynamicObject(3038, 1568.384033, -1226.914550, 283.109863, 0.000046, -0.000037, 130.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18022, "genintintfasta", "diner_wall5", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 19470, "forsale01", "forsale01", 0x00000000);
    wedrooftxt = CreateDynamicObject(3038, 1582.116333, -1226.980590, 283.219848, 0.000046, 0.000037, 49.999992, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18022, "genintintfasta", "diner_wall5", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 19470, "forsale01", "forsale01", 0x00000000);
    wedrooftxt = CreateDynamicObject(640, 1580.265625, -1224.900878, 279.231445, 0.000082, -12.500020, 89.999786, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 4992, "airportdetail", "bevflower1", 0x00000000);
    wedrooftxt = CreateDynamicObject(640, 1570.156005, -1224.900878, 279.231445, 0.000082, -12.500020, 89.999786, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 4992, "airportdetail", "bevflower1", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1577.889526, -1226.094726, 278.239837, -89.999992, 90.000045, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1572.549072, -1226.094726, 278.239837, -89.999992, 90.000045, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1577.288940, -1229.507446, 278.219848, -89.999992, 90.000091, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1573.099609, -1229.507446, 278.219848, -89.999992, 90.000106, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1577.288940, -1231.327514, 278.219848, -89.999992, 90.000106, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1573.099609, -1231.327514, 278.219848, -89.999992, 90.000122, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1577.288940, -1233.027587, 278.219848, -89.999992, 90.000122, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1573.099609, -1233.027587, 278.219848, -89.999992, 90.000137, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1577.288940, -1234.717895, 278.219848, -89.999992, 90.000137, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1573.099609, -1234.717895, 278.219848, -89.999992, 90.000152, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1577.288940, -1236.447753, 278.219848, -89.999992, 90.000152, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1573.099609, -1236.447753, 278.219848, -89.999992, 90.000167, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1577.288940, -1238.168090, 278.219848, -89.999992, 90.000167, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1573.099609, -1238.168090, 278.219848, -89.999992, 90.000183, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1577.288940, -1239.979492, 278.219848, -89.999992, 90.000183, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1573.099609, -1239.979492, 278.219848, -89.999992, 90.000198, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1577.288940, -1240.549682, 278.219848, -89.999992, 90.000198, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1573.099609, -1240.549682, 278.219848, -89.999992, 90.000213, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1577.288940, -1241.120239, 278.219848, -89.999992, 90.000213, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1573.099609, -1241.120239, 278.219848, -89.999992, 90.000228, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1577.288940, -1241.680786, 278.219848, -89.999992, 90.000228, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1573.099609, -1241.680786, 278.219848, -89.999992, 90.000244, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1577.288940, -1242.251342, 278.219848, -89.999992, 90.000244, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1573.099609, -1242.251342, 278.219848, -89.999992, 90.000259, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1577.178833, -1222.494628, 278.999816, -89.999992, 90.000076, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(11727, 1573.379882, -1222.494628, 278.999816, -89.999992, 90.000076, 89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18981, 1597.044189, -1232.378051, 269.368957, 0.000000, 180.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1575.203979, -1230.348022, 277.759826, 89.999992, 180.000030, -89.999961, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(19482, 1572.369750, -1248.977172, 284.440795, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(wedrooftxt, 0, "WEDDING", 130, "Arial", 98, 1, 0x808080FF, 0x00000000, 1);
    wedrooftxt = CreateDynamicObject(19482, 1577.430541, -1248.977172, 284.440795, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(wedrooftxt, 0, "CEREMONY", 130, "Arial", 98, 1, 0x808080FF, 0x00000000, 1);
    wedrooftxt = CreateDynamicObject(19482, 1572.369750, -1249.027221, 284.440795, -0.000075, 0.000000, -89.999771, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(wedrooftxt, 0, "WEDDING", 130, "Arial", 98, 1, 0xFFFFFFFF, 0x00000000, 1);
    wedrooftxt = CreateDynamicObject(19482, 1577.430541, -1249.027221, 284.440795, -0.000075, 0.000000, -89.999771, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(wedrooftxt, 0, "CEREMONY", 130, "Arial", 98, 1, 0xFFFFFFFF, 0x00000000, 1);
    wedrooftxt = CreateDynamicObject(1364, 1588.448242, -1246.409301, 277.702850, 0.000018, 0.000037, 89.299972, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 3, 14651, "ab_trukstpd", "Bow_bar_flooring", 0x00000000);
    wedrooftxt = CreateDynamicObject(1364, 1565.609619, -1248.238281, 277.613037, -0.000018, 0.000040, -24.999996, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 2, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 3, 14651, "ab_trukstpd", "Bow_bar_flooring", 0x00000000);
    wedrooftxt = CreateDynamicObject(18981, 1572.065917, -1234.894531, 276.443939, 0.000000, 90.000061, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14581, "ab_mafiasuitea", "cof_wood2", 0x00000000);
    wedrooftxt = CreateDynamicObject(14387, 1568.417358, -1248.516235, 275.940673, -0.000051, 0.000000, -89.999839, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    wedrooftxt = CreateDynamicObject(14387, 1573.126220, -1248.516235, 275.940673, -0.000051, 0.000000, -89.999839, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    wedrooftxt = CreateDynamicObject(14387, 1577.846801, -1248.516235, 275.940673, -0.000051, 0.000000, -89.999839, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    wedrooftxt = CreateDynamicObject(14387, 1581.895507, -1248.516235, 275.940673, -0.000051, 0.000000, -89.999839, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1583.766357, -1243.428344, 280.619873, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1578.205810, -1243.428344, 280.619873, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1572.205078, -1243.428344, 280.619873, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1566.555053, -1243.428344, 280.619873, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1569.345581, -1243.908569, 281.679870, 0.000000, 90.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 16640, "a51", "ws_stationfloor", 0xFF323232);
    wedrooftxt = CreateDynamicObject(18762, 1580.995971, -1243.908569, 281.679870, 0.000000, 90.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 16640, "a51", "ws_stationfloor", 0xFF323232);
    wedrooftxt = CreateDynamicObject(18762, 1575.205566, -1243.428466, 282.619873, 0.000000, 90.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 16640, "a51", "ws_stationfloor", 0xFF323232);
    wedrooftxt = CreateDynamicObject(2260, 1577.393920, -1225.230712, 282.649841, 0.000000, -2.599952, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 19470, "forsale01", "forsale01", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 14811, "lee_strip2_1", "CJ_NEON_HEART", 0x00000000);
    wedrooftxt = CreateDynamicObject(2260, 1573.023437, -1225.230712, 282.649841, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 19470, "forsale01", "forsale01", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 14811, "lee_strip2_1", "CJ_NEON_HEART", 0x00000000);
    wedrooftxt = CreateDynamicObject(2260, 1572.146362, -1225.230712, 282.598571, 0.000000, -27.799957, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 19470, "forsale01", "forsale01", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 14811, "lee_strip2_1", "CJ_NEON_HEART", 0x00000000);
    wedrooftxt = CreateDynamicObject(2260, 1578.124633, -1225.230712, 282.649841, 0.000000, 11.000044, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 19470, "forsale01", "forsale01", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 14811, "lee_strip2_1", "CJ_NEON_HEART", 0x00000000);
    wedrooftxt = CreateDynamicObject(1892, 1581.513061, -1243.430175, 278.259826, 0.000000, 0.000059, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(1892, 1579.513061, -1243.430175, 278.259826, 0.000000, 0.000059, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(1892, 1569.873168, -1243.430175, 278.259826, 0.000000, 0.000067, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(1892, 1567.873168, -1243.430175, 278.259826, 0.000000, 0.000067, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(636, 1579.986206, -1244.432983, 280.660888, 0.000051, 270.000000, 89.999839, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4992, "airportdetail", "kb_ivy_256", 0x00000000);
    wedrooftxt = CreateDynamicObject(636, 1582.126464, -1244.543090, 280.590881, 0.000051, 270.000000, 89.999839, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4992, "airportdetail", "kb_ivy_256", 0x00000000);
    wedrooftxt = CreateDynamicObject(636, 1568.225952, -1244.432983, 280.670898, 0.000059, 270.000000, 89.999816, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4992, "airportdetail", "kb_ivy_256", 0x00000000);
    wedrooftxt = CreateDynamicObject(636, 1570.366210, -1244.543090, 280.590881, 0.000059, 270.000000, 89.999816, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4992, "airportdetail", "kb_ivy_256", 0x00000000);
    wedrooftxt = CreateDynamicObject(1727, 1569.131103, -1220.816040, 279.039794, 0.000029, 0.000035, 40.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 13059, "ce_fact03", "GB_truckdepot19", 0x00000000);
    wedrooftxt = CreateDynamicObject(1727, 1580.412109, -1220.198852, 279.039794, -0.000029, 0.000035, -40.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 13059, "ce_fact03", "GB_truckdepot19", 0x00000000);
    wedrooftxt = CreateDynamicObject(18981, 1564.954589, -1252.485473, 276.418945, 0.000000, 90.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4829, "airport_las", "Grass_128HV", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1592.557495, -1219.399291, 279.365509, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1553.048461, -1219.399291, 279.365509, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1557.558837, -1219.389282, 279.365509, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18981, 1548.573120, -1232.378051, 269.368957, 0.000000, 180.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18755, 1592.949218, -1221.993041, 278.849670, 0.000007, 0.000000, 89.999977, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 2, 10765, "airportgnd_sfse", "white", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 3, 15034, "genhotelsave", "walp57S", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 4, 15034, "genhotelsave", "walp57S", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 5, 14534, "ab_wooziea", "walp72S", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 6, 14534, "ab_wooziea", "walp72S", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 7, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1597.328857, -1247.983398, 275.249877, -0.000045, 0.000000, 0.000136, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1597.328857, -1253.962890, 275.249877, -0.000045, 0.000000, 0.000136, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1597.328857, -1259.932006, 275.249877, -0.000045, 0.000000, 0.000136, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1597.328857, -1265.921386, 275.249877, -0.000045, 0.000000, 0.000136, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1548.558715, -1247.933349, 275.249877, -0.000044, 0.000007, 0.000144, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1548.698486, -1253.901245, 275.249877, -0.000044, 0.000007, 4.700143, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1549.820922, -1259.463623, 275.249877, -0.000044, 0.000007, 19.600143, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1572.634155, -1268.096435, 275.498291, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1572.644165, -1266.594970, 275.508300, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19377, 1567.927246, -1264.908203, 272.013092, 0.000000, 0.000000, 90.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19377, 1558.297485, -1264.908203, 272.013092, 0.000000, 0.000000, 90.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19377, 1552.557006, -1260.007812, 272.013092, 0.000000, 0.000000, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19377, 1552.557006, -1250.377929, 272.013092, 0.000000, 0.000000, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1550.663208, -1244.586303, 275.498291, 0.000000, 0.000000, 90.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19377, 1552.567016, -1249.307006, 272.023101, 0.000000, 0.000000, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1550.893188, -1244.576293, 275.508300, 0.000000, 0.000000, 90.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1548.172607, -1246.466308, 275.498291, 0.000000, 0.000000, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1548.172607, -1249.675659, 275.498291, 0.000000, 0.000000, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1548.172607, -1252.875000, 275.498291, 0.000000, 0.000000, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1548.345947, -1256.017822, 275.498291, 0.000000, 0.000000, -173.700088, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1549.040405, -1259.098999, 275.498291, 0.000000, 0.000000, -161.100006, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1550.626098, -1262.541625, 275.498291, 0.000000, 0.000000, -147.499969, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1552.595458, -1265.053833, 275.498291, 0.000000, 0.000000, -136.399917, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1549.282836, -1259.682373, 275.498291, 0.000000, 0.000000, -161.100006, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1555.037109, -1267.083618, 275.498291, 0.000000, 0.000000, -123.399932, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1557.864379, -1268.520507, 275.498291, 0.000000, 0.000000, -110.199943, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1561.226074, -1269.397949, 275.498291, 0.000000, 0.000000, -96.799964, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1558.337280, -1268.684204, 275.508300, 0.000000, 0.000000, -110.199943, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1564.409667, -1269.600463, 275.498291, 0.000000, 0.000000, -90.199996, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1567.609985, -1269.612060, 275.498291, 0.000000, 0.000000, -90.199996, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1570.809570, -1269.623535, 275.498291, 0.000000, 0.000000, -90.199996, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1571.109863, -1269.623779, 275.478271, 0.000000, 0.000000, -90.199996, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(19377, 1557.286499, -1264.898193, 272.023101, 0.000000, 0.000000, 90.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1552.776855, -1264.426757, 275.249877, -0.000044, 0.000007, 42.100112, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1557.446411, -1267.672729, 275.249877, -0.000044, 0.000007, 67.000160, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1563.041748, -1269.082641, 275.249877, -0.000044, 0.000007, 85.600143, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1568.997192, -1269.331665, 275.249877, -0.000044, 0.000007, 89.800102, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1574.986816, -1269.353271, 275.249877, -0.000044, 0.000007, 89.800102, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1580.976196, -1269.324462, 275.249877, -0.000044, 0.000007, 90.200096, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1586.965820, -1269.303222, 275.249877, -0.000044, 0.000007, 90.200096, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(3475, 1592.945190, -1269.282104, 275.249877, -0.000044, 0.000007, 90.200096, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18763, 1596.030883, -1268.141723, 279.388702, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1559.636718, -1260.522949, 277.353881, 90.000000, 360.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1557.013061, -1260.580932, 277.777465, 0.000000, 90.000000, 90.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 19655, "mattubes", "reddirt1", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1562.173339, -1260.580932, 277.777465, 0.000000, 90.000000, 90.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 19655, "mattubes", "reddirt1", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1559.636718, -1258.703613, 276.423889, 90.000000, 360.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    wedrooftxt = CreateDynamicObject(636, 1562.535644, -1262.843383, 280.401062, 0.000059, 270.000000, 449.999816, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4992, "airportdetail", "kb_ivy_256", 0x00000000);
    wedrooftxt = CreateDynamicObject(636, 1559.535278, -1262.843383, 280.411071, 0.000059, 270.000000, 449.999816, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4992, "airportdetail", "kb_ivy_256", 0x00000000);
    wedrooftxt = CreateDynamicObject(636, 1556.556396, -1262.843383, 280.411071, 0.000059, 270.000000, 449.999816, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4992, "airportdetail", "kb_ivy_256", 0x00000000);
    wedrooftxt = CreateDynamicObject(636, 1562.535644, -1259.882690, 282.061065, 0.000059, 360.000000, 449.999816, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4992, "airportdetail", "kb_ivy_256", 0x00000000);
    wedrooftxt = CreateDynamicObject(636, 1562.535644, -1261.243041, 282.061065, 0.000059, 360.000000, 629.999816, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4992, "airportdetail", "kb_ivy_256", 0x00000000);
    wedrooftxt = CreateDynamicObject(636, 1559.634887, -1259.882690, 282.061065, 0.000067, 360.000000, 89.999794, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4992, "airportdetail", "kb_ivy_256", 0x00000000);
    wedrooftxt = CreateDynamicObject(636, 1559.634887, -1261.243041, 282.061065, 0.000051, 360.000000, -90.000160, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4992, "airportdetail", "kb_ivy_256", 0x00000000);
    wedrooftxt = CreateDynamicObject(636, 1556.584716, -1259.882690, 282.061065, 0.000075, 360.000000, 89.999771, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4992, "airportdetail", "kb_ivy_256", 0x00000000);
    wedrooftxt = CreateDynamicObject(636, 1556.584716, -1261.243041, 282.061065, 0.000044, 360.000000, -90.000137, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 4992, "airportdetail", "kb_ivy_256", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1554.975585, -1260.589355, 279.836669, -0.000007, 540.000000, -0.000037, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1559.643066, -1260.560913, 277.186523, 0.000000, 540.000000, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1564.312744, -1260.560913, 279.836669, 0.000000, 540.000000, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1562.173706, -1262.692016, 277.186523, 0.000000, 540.000000, 270.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1557.023315, -1262.692016, 277.186523, 0.000000, 540.000000, 270.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    wedrooftxt = CreateDynamicObject(16101, 1564.337280, -1258.924682, 270.997680, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 3899, "hospital2", "black", 0x00000000);
    wedrooftxt = CreateDynamicObject(16101, 1564.337280, -1262.225463, 270.997680, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 3899, "hospital2", "black", 0x00000000);
    wedrooftxt = CreateDynamicObject(16101, 1554.951049, -1262.225585, 270.997680, 0.000007, 0.000007, 179.999877, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 3899, "hospital2", "black", 0x00000000);
    wedrooftxt = CreateDynamicObject(16101, 1554.951049, -1258.924804, 270.997680, 0.000007, 0.000007, 179.999877, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 3899, "hospital2", "black", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1586.938110, -1260.027465, 279.404907, 0.000000, 0.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1586.938110, -1257.047485, 281.405029, 0.000007, 90.000000, 89.999977, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1586.938110, -1254.047119, 279.404907, 0.000000, 0.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1593.004882, -1230.714111, 276.467742, 90.000000, 360.000000, 90.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1593.004882, -1240.693969, 276.467742, 90.000000, 360.000000, 90.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1593.004882, -1250.683593, 276.467742, 90.000000, 360.000000, 90.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1593.004882, -1260.663696, 276.467742, 90.000000, 360.000000, 90.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1585.535766, -1257.033081, 276.467742, 90.000000, 360.000000, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    wedrooftxt = CreateDynamicObject(19445, 1586.959838, -1264.494750, 276.204895, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    wedrooftxt = CreateDynamicObject(19445, 1586.959838, -1248.754638, 276.204895, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1586.938110, -1269.167602, 279.404907, 0.000000, 0.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1586.938110, -1264.647094, 275.974914, 0.000000, 0.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1586.938110, -1250.278076, 275.974914, 0.000000, 0.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18765, 1575.288574, -1257.120727, 275.347442, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1576.474975, -1257.033081, 276.607696, 90.000000, 360.000000, 360.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1576.214843, -1257.033081, 276.717651, 90.000000, 360.000000, 360.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1576.004638, -1257.033081, 276.847625, 90.000000, 360.000000, 360.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1575.774414, -1257.033081, 276.977600, 90.000000, 360.000000, 360.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1575.604858, -1257.033081, 277.107635, 90.000000, 360.000000, 360.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1575.414672, -1257.033081, 277.257659, 90.000000, 360.000000, 360.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1575.215698, -1248.682983, 276.467742, 90.000000, 360.000000, 270.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1575.214477, -1255.822509, 276.607696, 89.999992, 495.000000, -45.000022, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1575.214477, -1256.082641, 276.717651, 89.999992, 495.000000, -45.000022, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1575.214477, -1256.292846, 276.847625, 89.999992, 495.000000, -45.000022, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1575.214477, -1256.523071, 276.977600, 89.999992, 495.000000, -45.000022, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1575.214477, -1256.692626, 277.107635, 89.999992, 495.000000, -45.000022, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1575.214477, -1256.882812, 277.257659, 89.999992, 495.000000, -45.000022, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 14534, "ab_wooziea", "walp72S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1579.777954, -1261.588989, 279.404907, 0.000000, 0.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1570.797363, -1261.588989, 279.404907, 0.000000, 0.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1576.787841, -1261.588989, 281.404815, 90.000000, 0.000007, 90.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1573.706665, -1261.588989, 281.404815, 90.000000, 0.000007, 90.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(1726, 1576.133178, -1260.421020, 277.829803, 0.000045, 0.000000, 179.999862, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18010, "genintrestrest2", "kbsofa333c", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 1, 14668, "711c", "cj_white_wall2", 0x00000000);
    wedrooftxt = CreateDynamicObject(1727, 1572.733520, -1260.116333, 277.809875, 0.000029, 0.000035, 490.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 14668, "711c", "cj_white_wall2", 0x00000000);
    wedrooftxt = CreateDynamicObject(1727, 1578.212768, -1259.552001, 277.809875, 0.000029, 0.000035, 940.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 14668, "711c", "cj_white_wall2", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1570.797363, -1252.648925, 279.404907, 0.000000, 0.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1579.777954, -1252.648315, 279.404907, 0.000000, 0.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1579.778320, -1258.619018, 281.404815, 90.000000, 0.000007, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1579.778320, -1255.117919, 281.404815, 90.000000, 0.000007, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1570.796752, -1258.588989, 281.404815, 90.000000, 0.000007, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1570.796752, -1254.659057, 281.404815, 90.000000, 0.000007, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1570.793212, -1255.210571, 279.446838, 0.000000, 540.000000, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1570.793212, -1259.030761, 279.446838, 0.000000, 540.000000, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1577.203857, -1261.952392, 279.446838, -0.000007, 540.000000, 89.999977, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    wedrooftxt = CreateDynamicObject(19353, 1573.383666, -1261.952392, 279.446838, -0.000007, 540.000000, 89.999977, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    wedrooftxt = CreateDynamicObject(970, 1585.290527, -1257.029663, 277.512023, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1575.245849, -1257.025146, 277.357757, 90.000000, 360.000000, 270.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1554.479370, -1227.553588, 276.652709, 90.000000, 90.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1554.479370, -1237.543457, 276.652709, 90.000000, 90.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 15034, "genhotelsave", "walp57S", 0x00000000);
    wedrooftxt = CreateDynamicObject(11691, 1554.495727, -1226.276611, 277.136993, 0.000015, 0.000000, 89.999954, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 3, 15034, "genhotelsave", "walp57S", 0x00000000);
    wedrooftxt = CreateDynamicObject(11691, 1554.495727, -1230.496582, 277.136993, 0.000015, 0.000000, 89.999954, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 3, 15034, "genhotelsave", "walp57S", 0x00000000);
    wedrooftxt = CreateDynamicObject(11691, 1554.495727, -1234.777954, 277.136993, 0.000030, 0.000000, 89.999908, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 3, 15034, "genhotelsave", "walp57S", 0x00000000);
    wedrooftxt = CreateDynamicObject(11691, 1554.495727, -1238.997924, 277.136993, 0.000030, 0.000000, 89.999908, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 1, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 2, 3899, "hospital2", "black", 0x00000000);
    SetDynamicObjectMaterial(wedrooftxt, 3, 15034, "genhotelsave", "walp57S", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1552.497802, -1227.571777, 274.662750, -0.000000, 180.000000, 89.999977, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1552.497802, -1237.532104, 274.662750, -0.000000, 180.000000, 89.999977, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1556.459228, -1237.532104, 274.662750, -0.000000, 180.000000, 89.999977, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1556.459228, -1227.570678, 274.662750, -0.000000, 180.000000, 89.999977, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1555.498657, -1237.532104, 274.662750, 0.000007, 180.000000, 89.999954, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1555.498657, -1227.570678, 274.662750, 0.000007, 180.000000, 89.999954, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1553.457885, -1227.571777, 274.662750, 0.000007, 180.000000, 89.999954, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    wedrooftxt = CreateDynamicObject(18766, 1553.457885, -1237.532104, 274.662750, 0.000007, 180.000000, 89.999954, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 18646, "matcolours", "red-4", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1548.636474, -1241.288818, 280.564849, 90.000000, 0.000007, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1548.636474, -1236.289062, 280.564849, 90.000000, 0.000007, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1548.636474, -1231.289306, 280.564849, 90.000000, 0.000007, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1548.636474, -1226.290039, 280.564849, 90.000000, 0.000007, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1548.636474, -1223.079711, 280.564849, 90.000000, 0.000007, 180.000000, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1596.982299, -1223.063842, 280.564849, 89.999992, 64.471221, -64.471221, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1596.982299, -1228.063598, 280.564849, 89.999992, 64.471221, -64.471221, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1596.982299, -1233.063354, 280.564849, 89.999992, 64.471221, -64.471221, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1596.982299, -1238.062622, 280.564849, 89.999992, 64.471221, -64.471221, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    wedrooftxt = CreateDynamicObject(18762, 1596.982299, -1241.272949, 280.564849, 89.999992, 64.471221, -64.471221, 91, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(wedrooftxt, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(2122, 1568.351928, -1232.109619, 278.783935, -0.000059, 0.000000, -89.999816, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1568.351928, -1233.840820, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1572.492919, -1233.840820, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1571.002685, -1233.840820, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1569.642211, -1233.840820, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1568.351928, -1235.529663, 278.783935, -0.000059, 0.000000, -89.999816, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1572.492919, -1235.529663, 278.783935, -0.000059, 0.000000, -89.999816, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1571.002685, -1235.529663, 278.783935, -0.000059, 0.000000, -89.999816, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1569.642211, -1235.529663, 278.783935, -0.000059, 0.000000, -89.999816, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1568.351928, -1237.260864, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1572.492919, -1237.260864, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1571.002685, -1237.260864, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1569.642211, -1237.260864, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1568.351928, -1238.981079, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1572.492919, -1238.981079, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1571.002685, -1238.981079, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1569.642211, -1238.981079, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1588.321777, -1221.814208, 277.647094, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1562.012207, -1221.814208, 277.647094, 0.000000, -0.000045, 179.999725, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1577.924560, -1232.109619, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1582.065551, -1232.109619, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1580.575317, -1232.109619, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1579.214843, -1232.109619, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1577.924560, -1233.840820, 278.783935, -0.000075, 0.000000, -89.999771, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1582.065551, -1233.840820, 278.783935, -0.000075, 0.000000, -89.999771, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1580.575317, -1233.840820, 278.783935, -0.000075, 0.000000, -89.999771, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1579.214843, -1233.840820, 278.783935, -0.000075, 0.000000, -89.999771, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1577.924560, -1235.529663, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1582.065551, -1235.529663, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1580.575317, -1235.529663, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1579.214843, -1235.529663, 278.783935, -0.000067, 0.000000, -89.999794, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1577.924560, -1237.260864, 278.783935, -0.000075, 0.000000, -89.999771, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1582.065551, -1237.260864, 278.783935, -0.000075, 0.000000, -89.999771, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1580.575317, -1237.260864, 278.783935, -0.000075, 0.000000, -89.999771, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1579.214843, -1237.260864, 278.783935, -0.000075, 0.000000, -89.999771, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1577.924560, -1238.981079, 278.783935, -0.000075, 0.000000, -89.999771, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1582.065551, -1238.981079, 278.783935, -0.000075, 0.000000, -89.999771, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1580.575317, -1238.981079, 278.783935, -0.000075, 0.000000, -89.999771, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2122, 1579.214843, -1238.981079, 278.783935, -0.000075, 0.000000, -89.999771, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19611, 1575.288696, -1221.116455, 279.029846, 0.000000, 0.000067, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(325, 1571.355102, -1224.015258, 279.047027, 78.100036, 0.000220, -0.000216, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(325, 1570.744995, -1224.015258, 279.047027, 78.100036, 0.000220, -0.000216, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(325, 1570.164672, -1224.015258, 279.047027, 78.100036, 0.000220, -0.000216, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(325, 1569.604125, -1224.015258, 279.047027, 78.100036, 0.000220, -0.000216, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(325, 1569.023925, -1224.015258, 279.047027, 78.100036, 0.000220, -0.000216, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(325, 1581.207275, -1224.015258, 279.047027, 78.100036, 0.000293, -0.000288, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(325, 1580.597167, -1224.015258, 279.047027, 78.100036, 0.000293, -0.000288, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(325, 1580.016845, -1224.015258, 279.047027, 78.100036, 0.000293, -0.000288, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(325, 1579.456298, -1224.015258, 279.047027, 78.100036, 0.000293, -0.000288, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(325, 1578.876098, -1224.015258, 279.047027, 78.100036, 0.000293, -0.000288, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1361, 1566.672363, -1225.188110, 278.993408, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1361, 1583.613037, -1225.188110, 278.993408, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1597, 1566.114379, -1239.032470, 280.703918, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1597, 1583.695922, -1239.032470, 280.703918, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1580.082519, -1232.237670, 282.599822, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1580.082519, -1224.196777, 282.599822, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1580.082519, -1240.247680, 282.599822, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1570.182861, -1232.237670, 282.599822, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1570.182861, -1224.196777, 282.599822, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1570.182861, -1240.247680, 282.599822, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1580.082519, -1232.237670, 282.599822, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1580.082519, -1224.196777, 282.599822, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1580.082519, -1240.247680, 282.599822, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1570.182861, -1232.237670, 282.599822, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1570.182861, -1224.196777, 282.599822, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1570.182861, -1240.247680, 282.599822, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1580.082519, -1232.237670, 282.599822, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1580.082519, -1224.196777, 282.599822, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1580.082519, -1240.247680, 282.599822, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1570.182861, -1232.237670, 282.599822, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1570.182861, -1224.196777, 282.599822, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1570.182861, -1240.247680, 282.599822, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1580.082519, -1232.237670, 282.599822, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1580.082519, -1224.196777, 282.599822, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1580.082519, -1240.247680, 282.599822, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1570.182861, -1232.237670, 282.599822, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1570.182861, -1224.196777, 282.599822, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1734, 1570.182861, -1240.247680, 282.599822, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1577.875976, -1226.102661, 277.829864, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1572.535278, -1226.102661, 277.829864, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1577.275390, -1229.515380, 277.809875, 0.000000, 0.000075, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1573.085815, -1229.515380, 277.809875, 0.000000, 0.000082, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1577.275390, -1231.335449, 277.809875, 0.000000, 0.000082, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1573.085815, -1231.335449, 277.809875, 0.000000, 0.000090, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1577.275390, -1233.035522, 277.809875, 0.000000, 0.000090, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1573.085815, -1233.035522, 277.809875, 0.000000, 0.000097, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1577.275390, -1234.725830, 277.809875, 0.000000, 0.000097, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1573.085815, -1234.725830, 277.809875, 0.000000, 0.000105, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1577.275390, -1236.455688, 277.809875, 0.000000, 0.000105, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1573.085815, -1236.455688, 277.809875, 0.000000, 0.000113, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1577.275390, -1238.176025, 277.809875, 0.000000, 0.000113, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1573.085815, -1238.176025, 277.809875, 0.000000, 0.000120, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1577.275390, -1239.987426, 277.809875, 0.000000, 0.000120, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1573.085815, -1239.987426, 277.809875, 0.000000, 0.000127, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1577.275390, -1240.557617, 277.809875, 0.000000, 0.000127, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1573.085815, -1240.557617, 277.809875, 0.000000, 0.000136, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1577.275390, -1241.128173, 277.809875, 0.000000, 0.000136, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1573.085815, -1241.128173, 277.809875, 0.000000, 0.000144, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1577.275390, -1241.688720, 277.809875, 0.000000, 0.000144, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1573.085815, -1241.688720, 277.809875, 0.000000, 0.000151, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1577.275390, -1242.259277, 277.809875, 0.000000, 0.000151, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 1573.085815, -1242.259277, 277.809875, 0.000000, 0.000159, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19124, 1573.362792, -1222.499877, 278.599822, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19124, 1577.162597, -1222.499877, 278.599822, 0.000000, 0.000051, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3660, 1561.297851, -1234.528198, 279.564392, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3660, 1588.277221, -1234.528198, 279.504333, 0.000045, 0.000000, 89.999862, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(628, 1583.808837, -1247.387939, 278.347747, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(628, 1566.767456, -1247.387939, 278.347747, 0.000000, 0.000045, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3439, 1572.240478, -1239.976806, 277.069885, 0.000000, 0.000059, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3439, 1578.181396, -1239.976806, 277.069885, 0.000000, 0.000059, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3439, 1572.240478, -1242.228393, 277.069885, 0.000000, 0.000067, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3439, 1578.181396, -1242.228393, 277.069885, 0.000000, 0.000067, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3439, 1572.240478, -1241.097534, 277.069885, 0.000000, 0.000075, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3439, 1578.181396, -1241.097534, 277.069885, 0.000000, 0.000075, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18756, 1592.982543, -1225.819702, 278.906005, -0.000029, 0.000007, -89.999946, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18757, 1592.976074, -1225.821411, 278.906005, -0.000029, 0.000007, -89.999946, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19157, 1562.155883, -1260.547851, 279.333770, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19157, 1557.145629, -1260.547851, 279.333770, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1825, 1562.220825, -1260.554321, 277.837646, 0.000000, 0.000000, -1.099990, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1825, 1557.059814, -1260.709472, 277.837646, 0.000000, 0.000000, -48.999996, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1554.262329, -1260.486206, 277.302032, 0.000000, 0.000000, 180.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1565.003417, -1260.486206, 277.302032, 0.000000, 0.000000, 360.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1564.376098, -1260.572753, 281.399963, 0.000000, 0.000000, 90.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1564.115844, -1260.572753, 281.370025, 0.000000, 0.000000, 270.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1554.875610, -1260.572753, 281.399963, 0.000000, 0.000000, 270.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1555.125854, -1260.572753, 281.379943, 0.000000, 0.000000, 450.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, 1588.082641, -1260.031860, 280.059783, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, 1588.082641, -1254.061035, 280.059783, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, 1588.082641, -1269.141845, 280.059783, 0.000000, 0.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, 1585.791015, -1254.061035, 280.059783, 0.000007, 0.000000, 179.999877, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, 1585.791015, -1260.031860, 280.059783, 0.000007, 0.000000, 179.999877, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, 1585.782226, -1269.141845, 280.059783, 0.000000, 0.000000, 180.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1575.105590, -1261.384399, 278.215240, 0.000000, 0.000000, 90.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1577.546875, -1261.564575, 282.275299, 0.000000, 0.000000, 90.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1573.026977, -1261.564575, 282.275299, 0.000000, 0.000000, 90.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, 1579.780761, -1260.432006, 280.059783, 0.000007, 0.000000, 449.999877, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, 1579.780761, -1253.791381, 280.059783, 0.000007, 0.000000, 629.999877, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1571.774414, -1262.112548, 281.569915, 0.000000, 0.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1574.044189, -1262.112548, 281.569915, 0.000000, 0.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1576.323730, -1262.112548, 281.569915, 0.000000, 0.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1578.623046, -1262.112548, 281.569915, 0.000000, 0.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1580.349853, -1260.525512, 281.569915, 0.000014, 0.000014, 89.999923, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1579.174072, -1261.021728, 281.569915, 0.000007, -0.000007, 179.999832, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1576.894042, -1261.021728, 281.569915, 0.000007, -0.000007, 179.999832, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1574.624267, -1261.021728, 281.569915, 0.000007, -0.000007, 179.999832, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1572.424560, -1261.021728, 281.569915, 0.000007, -0.000007, 179.999832, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1579.251342, -1253.286254, 281.539886, -0.000022, -0.000014, -90.000061, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1579.251342, -1255.566284, 281.539886, -0.000022, -0.000014, -90.000061, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1579.251342, -1257.836059, 281.539886, -0.000022, -0.000014, -90.000061, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1579.251342, -1260.035766, 281.539886, -0.000022, -0.000014, -90.000061, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1571.350097, -1260.035766, 281.539886, -0.000022, -0.000007, 89.999877, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1571.350097, -1257.755737, 281.539886, -0.000022, -0.000007, 89.999877, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1571.350097, -1255.485961, 281.539886, -0.000022, -0.000007, 89.999877, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1571.350097, -1253.286254, 281.539886, -0.000022, -0.000007, 89.999877, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1580.349853, -1258.255737, 281.569915, 0.000014, 0.000014, 89.999923, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1580.349853, -1255.976196, 281.569915, 0.000014, 0.000014, 89.999923, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1580.349853, -1253.676879, 281.569915, 0.000014, 0.000014, 89.999923, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1570.259399, -1253.676879, 281.569915, 0.000014, 0.000007, -90.000083, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1570.259399, -1255.946655, 281.569915, 0.000014, 0.000007, -90.000083, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1570.259399, -1258.226196, 281.569915, 0.000014, 0.000007, -90.000083, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1570.259399, -1260.525512, 281.569915, 0.000014, 0.000007, -90.000083, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1587.488769, -1259.316650, 281.509857, 0.000014, 0.000007, 89.999916, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1587.488769, -1257.046630, 281.509857, 0.000014, 0.000007, 89.999916, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1587.488769, -1254.836303, 281.509857, 0.000014, 0.000007, 89.999916, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1586.387695, -1254.836181, 281.509857, 0.000014, 0.000000, -90.000099, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1586.387695, -1257.106201, 281.509857, 0.000014, 0.000000, -90.000099, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1586.387695, -1259.316528, 281.509857, 0.000014, 0.000000, -90.000099, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19444, 1557.133300, -1257.877319, 277.017395, 0.000000, 90.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19444, 1557.133300, -1258.087524, 277.197418, 0.000000, 90.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19444, 1557.133300, -1258.287719, 277.367431, 0.000000, 90.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19444, 1557.133300, -1258.527954, 277.537567, 0.000000, 90.000000, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19444, 1562.084228, -1257.877319, 277.017395, 0.000000, 90.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19444, 1562.084228, -1258.087524, 277.197418, 0.000000, 90.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19444, 1562.084228, -1258.287719, 277.367431, 0.000000, 90.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19444, 1562.084228, -1258.527954, 277.537567, 0.000000, 90.000007, 0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1720, 1556.136108, -1239.820922, 277.148834, 0.000000, 0.000000, 270.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1720, 1556.136108, -1238.271240, 277.148834, 0.000000, 0.000000, 270.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1720, 1556.136108, -1235.530273, 277.148834, -0.000007, -0.000000, -89.999977, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1720, 1556.136108, -1233.980590, 277.148834, -0.000007, -0.000000, -89.999977, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1720, 1556.136108, -1231.249877, 277.148834, -0.000015, 0.000000, -89.999954, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1720, 1556.136108, -1229.700195, 277.148834, -0.000015, 0.000000, -89.999954, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1720, 1556.136108, -1227.019287, 277.148834, -0.000022, 0.000000, -89.999931, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1720, 1556.136108, -1225.469604, 277.148834, -0.000022, 0.000000, -89.999931, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1720, 1552.835327, -1225.469604, 277.148834, 0.000007, 0.000007, 89.999916, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1720, 1552.835327, -1227.019287, 277.148834, 0.000007, 0.000007, 89.999916, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1720, 1552.835327, -1229.760253, 277.148834, 0.000000, 0.000007, 89.999938, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1720, 1552.835327, -1231.309936, 277.148834, 0.000000, 0.000007, 89.999938, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1720, 1552.835327, -1234.040649, 277.148834, -0.000007, 0.000007, 89.999961, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1720, 1552.835327, -1235.590332, 277.148834, -0.000007, 0.000007, 89.999961, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1720, 1552.835327, -1238.271240, 277.148834, -0.000015, 0.000007, 89.999984, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1720, 1552.835327, -1239.820922, 277.148834, -0.000015, 0.000007, 89.999984, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1549.442749, -1241.799316, 277.324798, 0.000000, 0.000000, 360.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1549.442749, -1236.429687, 277.324798, 0.000000, 0.000000, 360.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1549.442749, -1227.948608, 277.324798, -0.000000, 0.000007, -0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(640, 1549.442749, -1222.579345, 277.324798, -0.000000, 0.000007, -0.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3935, 1549.752807, -1232.140136, 278.132354, 0.000000, 0.000000, 270.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, 1549.791015, -1242.241088, 280.059783, 0.000006, 0.000007, -0.000122, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, 1549.791015, -1234.201049, 280.059783, 0.000006, 0.000007, -0.000122, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, 1549.791015, -1230.150878, 280.059783, 0.000006, 0.000007, -0.000122, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, 1549.791015, -1220.880737, 280.059783, 0.000006, 0.000007, -0.000122, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1549.268798, -1240.445068, 280.589782, -0.000022, -0.000007, 89.999877, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1549.268798, -1238.174804, 280.589782, -0.000022, -0.000007, 89.999877, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1549.268798, -1235.934570, 280.589782, -0.000022, -0.000007, 89.999877, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1549.268798, -1227.813598, 280.589782, -0.000014, -0.000006, 89.999855, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1549.268798, -1225.543334, 280.589782, -0.000014, -0.000006, 89.999855, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1549.268798, -1223.303100, 280.589782, -0.000014, -0.000006, 89.999855, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, 1554.434204, -1242.897583, 277.310668, 0.000000, 0.000000, 270.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, 1554.434204, -1222.207519, 277.310668, 0.000000, 0.000000, 450.000000, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1596.349975, -1232.128540, 280.589782, -0.000029, -0.000014, -90.000106, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, 1595.827758, -1230.151611, 280.059783, 0.000014, 0.000000, 179.999710, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, 1595.827758, -1234.201782, 280.059783, 0.000014, 0.000000, 179.999710, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3802, 1595.827758, -1243.471923, 280.059783, 0.000014, 0.000000, 179.999710, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1596.349975, -1223.907592, 280.589782, -0.000029, -0.000014, -90.000106, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1596.349975, -1226.177856, 280.589782, -0.000029, -0.000014, -90.000106, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1596.349975, -1228.418090, 280.589782, -0.000029, -0.000014, -90.000106, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1596.349975, -1236.539062, 280.589782, -0.000021, -0.000014, -90.000137, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1596.349975, -1238.809326, 280.589782, -0.000021, -0.000014, -90.000137, 91, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2345, 1596.349975, -1241.049560, 280.589782, -0.000021, -0.000014, -90.000137, 91, 0, -1, 200.00, 200.00);
}