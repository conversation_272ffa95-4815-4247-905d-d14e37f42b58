RemoveMethBuilding(playerid)
{
    //take efedrin
    RemoveBuildingForPlayer(playerid, 17074, -1430.130, -966.226, 199.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1435.040, -962.101, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1433.030, -962.101, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1431.030, -962.101, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1437.979, -963.234, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1437.040, -962.101, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1437.979, -965.234, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1437.979, -967.234, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1437.040, -968.320, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1435.040, -968.320, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1433.030, -968.320, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1431.020, -968.320, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1429.040, -968.320, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1427.040, -968.320, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1425.040, -968.320, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1424.040, -967.296, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1423.300, -965.460, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1423.000, -962.250, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1425.040, -962.101, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1427.040, -962.101, 200.813, 0.250);
    RemoveBuildingForPlayer(playerid, 3260, -1429.030, -962.101, 200.813, 0.250);

    //lab meth
    RemoveBuildingForPlayer(playerid, 18258, -1638.359, -2239.429, 30.750, 0.250);
    RemoveBuildingForPlayer(playerid, 18259, -1633.229, -2238.840, 30.390, 0.250);
}

CreateMethExt()
{
    new STREAMER_TAG_OBJECT: methxadtx;
    //efedrin
    methxadtx = CreateDynamicObject(1491, -1425.119384, -947.394775, 201.453735, 0.000014, -0.000007, -90.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 3374, "ce_farmxref", "sw_barndoor2", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.596435, -954.895263, 202.290191, 89.999992, 225.797058, 45.802928, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.601562, -954.705078, 202.530197, 89.999992, 225.797058, 45.802928, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.481445, -954.701904, 202.530197, 89.999992, 225.797058, 45.802928, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.721435, -954.708251, 202.530197, 89.999992, 225.797058, 45.802928, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.727050, -954.518310, 202.530197, 89.999992, 225.797058, 45.802928, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.606689, -954.514892, 202.530197, 89.999992, 225.797058, 45.802928, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.487060, -954.512207, 202.530197, 89.999992, 225.797058, 45.802928, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.601562, -953.415039, 202.530197, 89.999992, 226.399810, 45.200214, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.481445, -953.411865, 202.530197, 89.999992, 226.399810, 45.200214, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.721435, -953.418212, 202.530197, 89.999992, 226.399810, 45.200214, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.727050, -953.228271, 202.530197, 89.999992, 226.399810, 45.200214, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.606689, -953.224853, 202.530197, 89.999992, 226.399810, 45.200214, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.487060, -953.222167, 202.530197, 89.999992, 226.399810, 45.200214, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.609863, -953.615478, 202.290191, 89.999992, 226.199218, 45.400772, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.856689, -948.654052, 202.270202, 89.999992, 226.199218, 45.400772, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.861816, -948.463867, 202.510208, 89.999992, 226.199218, 45.400772, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.741699, -948.460693, 202.510208, 89.999992, 226.199218, 45.400772, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.981689, -948.467041, 202.510208, 89.999992, 226.199218, 45.400772, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.987304, -948.277099, 202.510208, 89.999992, 226.199218, 45.400772, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.866943, -948.273681, 202.510208, 89.999992, 226.199218, 45.400772, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.747314, -948.270996, 202.510208, 89.999992, 226.199218, 45.400772, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.861816, -947.173828, 202.510208, 89.999992, 226.499969, 45.100063, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.741699, -947.170654, 202.510208, 89.999992, 226.499969, 45.100063, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.981689, -947.177001, 202.510208, 89.999992, 226.499969, 45.100063, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.987304, -946.987060, 202.510208, 89.999992, 226.499969, 45.100063, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.866943, -946.983642, 202.510208, 89.999992, 226.499969, 45.100063, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.747314, -946.980957, 202.510208, 89.999992, 226.499969, 45.100063, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1433.870117, -947.374267, 202.270202, 89.999992, 226.399810, 45.200214, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1434.177001, -942.332763, 202.270202, 89.999992, 226.399810, 45.200214, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1434.182128, -942.142578, 202.510208, 89.999992, 226.399810, 45.200214, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1434.062011, -942.139404, 202.510208, 89.999992, 226.399810, 45.200214, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1434.302001, -942.145751, 202.510208, 89.999992, 226.399810, 45.200214, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1434.307617, -941.955810, 202.510208, 89.999992, 226.399810, 45.200214, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1434.187255, -941.952392, 202.510208, 89.999992, 226.399810, 45.200214, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1434.067626, -941.949707, 202.510208, 89.999992, 226.399810, 45.200214, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1434.182128, -940.852539, 202.510208, 89.999992, 226.550033, 45.050014, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1434.062011, -940.849365, 202.510208, 89.999992, 226.550033, 45.050014, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1434.302001, -940.855712, 202.510208, 89.999992, 226.550033, 45.050014, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1434.307617, -940.665771, 202.510208, 89.999992, 226.550033, 45.050014, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1434.187255, -940.662353, 202.510208, 89.999992, 226.550033, 45.050014, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1434.067626, -940.659667, 202.510208, 89.999992, 226.550033, 45.050014, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1434.190429, -941.052978, 202.270202, 89.999992, 226.499969, 45.100063, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(19940, -1425.029785, -942.757080, 203.600616, 32.300003, 90.000022, 179.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1424.969726, -942.757080, 203.600616, -47.099979, 90.000022, 179.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1435.319580, -943.137451, 203.600616, 32.300003, 90.000030, 179.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1435.259765, -943.137451, 203.600616, -47.099979, 90.000030, 179.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1434.638916, -953.457763, 203.600616, 32.300003, 90.000038, 179.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1434.579101, -953.457763, 203.600616, -47.099979, 90.000038, -179.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1424.909667, -952.876708, 203.600616, 32.300003, 90.000030, 179.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1424.849609, -952.876708, 203.600616, -47.099979, 90.000030, 179.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1428.089599, -939.297851, 203.600616, 32.300006, 90.000007, -90.000106, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1428.089599, -939.237792, 203.600616, -47.099960, 90.000007, -90.000099, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1432.529541, -939.387939, 203.600616, 32.300014, 90.000007, -90.000152, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1432.529541, -939.327880, 203.600616, -47.099941, 90.000007, -90.000144, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1427.281494, -955.559326, 203.655792, 43.600006, 90.000007, -90.000144, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1427.339599, -955.499267, 203.600616, -47.099941, 90.000007, -90.000144, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1431.837646, -955.979736, 203.545761, 43.300018, 90.000007, -90.000144, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1431.809570, -955.919677, 203.626815, -40.899944, 90.000007, -90.000144, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1428.729492, -955.559326, 202.276550, 43.600006, 90.000007, -90.000144, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, -1425.136962, -948.677978, 204.136123, 0.000007, 90.000015, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, -1425.136962, -947.688232, 204.136123, 0.000007, 90.000015, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, -1425.056884, -948.677978, 204.136123, 0.000007, 90.000022, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, -1425.056884, -947.688232, 204.136123, 0.000007, 90.000022, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, -1425.096923, -948.677978, 204.136123, 0.000007, 90.000030, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, -1425.096923, -947.688232, 204.136123, 0.000007, 90.000030, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, -1425.166992, -948.677978, 204.136123, 0.000007, 90.000038, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, -1425.166992, -947.688232, 204.136123, 0.000007, 90.000038, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1425.595458, -954.558105, 201.921661, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1426.845458, -954.558105, 201.921661, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1428.115478, -954.558105, 201.921661, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(930, -1429.523437, -955.078979, 201.956069, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1433.885498, -950.918212, 201.921661, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1433.885498, -944.768310, 201.921661, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, -1421.427734, -947.306091, 201.987701, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, -1421.427734, -948.815979, 201.887695, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, -1421.427734, -950.336242, 201.987701, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, -1421.427734, -951.796142, 201.987701, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1423.706054, -939.608886, 201.841659, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, -1421.427734, -951.036254, 202.907592, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -1422.862304, -953.874023, 201.560699, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -1421.692382, -942.183837, 201.560699, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -1419.892333, -940.323486, 201.560699, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19922, -1433.804687, -953.981933, 201.379745, 0.000020, -0.000007, -87.899993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19922, -1434.262451, -941.491455, 201.379745, 0.000020, -0.000007, -87.899993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19922, -1434.033935, -947.728271, 201.379745, 0.000020, -0.000007, -87.899993, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1433.385742, -955.337402, 202.229141, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1433.385742, -955.337402, 202.319137, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1512, -1434.203857, -955.453613, 202.365310, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1517, -1434.229003, -955.036743, 202.234909, 0.000007, 90.000015, 449.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19611, -1433.583251, -954.797851, 201.111724, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1509, -1433.582763, -954.940917, 202.484100, 0.000003, 0.000015, 163.699935, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1433.573974, -954.793457, 202.699249, -0.000014, 0.000007, 89.999984, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1433.604003, -954.883544, 202.699249, 0.000014, -0.000007, -90.000061, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1510, -1433.590820, -954.696289, 202.542907, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1433.675781, -954.486572, 202.589141, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1433.484130, -954.473632, 202.530273, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19821, -1434.016601, -955.370361, 202.144317, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1433.574218, -955.403808, 202.200271, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1433.385742, -954.047363, 202.229141, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1433.574218, -955.283691, 202.200271, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1433.385742, -954.047363, 202.319137, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1512, -1434.203857, -954.163574, 202.365310, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1517, -1434.229003, -953.766357, 202.254928, 0.000007, 90.000030, 449.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19611, -1433.583251, -953.507812, 201.111724, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1509, -1433.582763, -953.650878, 202.484100, 0.000000, 0.000029, 163.699935, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1433.573974, -953.503417, 202.699249, -0.000029, 0.000007, 90.000030, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1433.604003, -953.593505, 202.699249, 0.000029, -0.000007, -90.000106, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1510, -1433.590820, -953.406250, 202.542907, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1433.675781, -953.196533, 202.589141, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1433.484130, -953.183593, 202.530273, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19821, -1434.016601, -954.080322, 202.144317, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1433.574218, -954.113769, 202.200271, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1433.574218, -953.993652, 202.200271, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19922, -1429.619262, -940.130310, 201.379745, 0.000020, -0.000007, 2.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2976, -1430.719726, -940.413330, 202.146057, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2976, -1429.679931, -940.413330, 202.146057, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1433.645996, -949.096191, 202.209136, 0.000007, 0.000022, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1433.645996, -949.096191, 202.299133, 0.000007, 0.000022, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1512, -1434.464111, -949.212402, 202.345321, 0.000007, 0.000022, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1517, -1434.489257, -948.845214, 202.224975, 0.000007, 90.000022, 359.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19611, -1433.843505, -948.556640, 201.091720, 0.000007, 0.000022, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1509, -1433.843017, -948.699707, 202.464096, 0.000001, 0.000023, 163.699935, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1433.834228, -948.552246, 202.679244, -0.000022, 0.000007, 90.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1433.864257, -948.642333, 202.679244, 0.000022, -0.000007, -90.000076, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1510, -1433.851074, -948.455078, 202.522903, 0.000007, 0.000022, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1433.936035, -948.245361, 202.569137, 0.000007, 0.000022, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1433.744384, -948.232421, 202.510269, 0.000007, 0.000022, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19821, -1434.276855, -949.129150, 202.124328, 0.000007, 0.000022, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1433.834472, -949.162597, 202.180267, 0.000007, 0.000022, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1433.645996, -947.806152, 202.209136, 0.000007, 0.000037, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1433.834472, -949.042480, 202.180267, 0.000007, 0.000022, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1433.645996, -947.806152, 202.299133, 0.000007, 0.000037, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1512, -1434.464111, -947.922363, 202.345321, 0.000007, 0.000037, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1517, -1434.489257, -947.585205, 202.224975, 0.000007, 90.000038, 449.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19611, -1433.843505, -947.266601, 201.091720, 0.000007, 0.000037, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1509, -1433.843017, -947.409667, 202.464096, -0.000001, 0.000036, 163.699935, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1433.834228, -947.262207, 202.679244, -0.000037, 0.000007, 90.000053, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1433.864257, -947.352294, 202.679244, 0.000037, -0.000007, -90.000122, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1510, -1433.851074, -947.165039, 202.522903, 0.000007, 0.000037, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1433.936035, -946.955322, 202.569137, 0.000007, 0.000037, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1433.744384, -946.942382, 202.510269, 0.000007, 0.000037, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19821, -1434.276855, -947.839111, 202.124328, 0.000007, 0.000037, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1433.834472, -947.872558, 202.180267, 0.000007, 0.000037, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1433.834472, -947.752441, 202.180267, 0.000007, 0.000037, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2976, -1428.946533, -940.686584, 202.346023, 0.000000, 90.000000, 36.599998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2976, -1430.719726, -939.822875, 202.146057, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2976, -1429.679931, -939.822875, 202.146057, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1433.966308, -942.774902, 202.209136, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1433.966308, -942.774902, 202.299133, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1512, -1434.784423, -942.891113, 202.345321, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1517, -1434.809570, -942.483947, 202.214950, 0.000007, 90.000030, 449.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19611, -1434.163818, -942.235351, 201.091720, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1509, -1434.163330, -942.378417, 202.464096, 0.000000, 0.000029, 163.699935, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1434.154541, -942.230957, 202.679244, -0.000029, 0.000007, 90.000030, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1434.184570, -942.321044, 202.679244, 0.000029, -0.000007, -90.000106, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1510, -1434.171386, -942.133789, 202.522903, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1434.256347, -941.924072, 202.569137, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1434.064697, -941.911132, 202.510269, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19821, -1434.597167, -942.807861, 202.124328, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1434.154785, -942.841308, 202.180267, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1433.966308, -941.484863, 202.209136, 0.000007, 0.000044, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1434.154785, -942.721191, 202.180267, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1433.966308, -941.484863, 202.299133, 0.000007, 0.000044, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1512, -1434.784423, -941.601074, 202.345321, 0.000007, 0.000044, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1517, -1434.809570, -941.213989, 202.224975, 0.000007, 90.000045, 449.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19611, -1434.163818, -940.945312, 201.091720, 0.000007, 0.000044, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1509, -1434.163330, -941.088378, 202.464096, -0.000003, 0.000045, 163.699935, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1434.154541, -940.940917, 202.679244, -0.000044, 0.000007, 90.000076, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1434.184570, -941.031005, 202.679244, 0.000044, -0.000007, -90.000152, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1510, -1434.171386, -940.843750, 202.522903, 0.000007, 0.000044, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1434.256347, -940.634033, 202.569137, 0.000007, 0.000044, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1434.064697, -940.621093, 202.510269, 0.000007, 0.000044, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19821, -1434.597167, -941.517822, 202.124328, 0.000007, 0.000044, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1434.154785, -941.551269, 202.180267, 0.000007, 0.000044, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1434.154785, -941.431152, 202.180267, 0.000007, 0.000044, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2976, -1428.679687, -939.822875, 202.146057, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1425.234008, -952.133789, 202.150283, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19922, -1426.217285, -942.297119, 201.379745, 0.000020, -0.000007, 92.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18258, -1424.812988, -948.106689, 201.769561, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18259, -1429.942871, -948.696533, 201.410156, 0.000007, 0.000014, 179.999938, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19922, -1426.093139, -945.684692, 201.379745, 0.000020, -0.000007, 92.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1425.774047, -952.154113, 202.190338, 90.000007, 0.000029, -147.000122, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1426.324340, -952.101867, 202.190338, 90.000007, 0.000029, 148.399902, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1425.878540, -951.185058, 202.190338, 90.000007, 0.000029, -147.000122, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19092, -1426.411376, -943.366760, 202.496002, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1426.023071, -949.752624, 202.190338, 89.999992, 0.000029, -57.000118, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1426.075317, -950.302917, 202.190338, 89.999992, -66.932609, -54.667453, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1425.922485, -949.960327, 202.190338, 89.999992, 15.233350, 22.766534, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1425.369750, -949.964355, 202.190338, 89.999992, -50.804832, 24.204738, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1425.261474, -951.374633, 202.190338, 89.999992, 57.912174, 130.987701, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1426.455688, -947.015991, 202.209136, 0.000007, 0.000037, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -1427.748291, -952.626708, 201.456024, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -1431.148925, -952.626708, 201.456024, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -1428.878662, -942.776855, 201.456024, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -1428.878662, -948.637207, 201.456024, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -1432.178955, -944.846923, 201.456024, 0.000007, 0.000014, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18664, -1425.354858, -945.362365, 203.606018, 0.000000, 0.000000, 2.700000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(17055, -1427.464965, -962.415832, 201.499023, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1425.742431, -951.102294, 202.190338, 89.999992, -8.437323, 132.737213, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1426.055297, -947.015991, 202.209136, 0.000007, 0.000037, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1425.525268, -947.015991, 202.209136, 0.000007, 0.000037, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1426.455688, -944.215759, 202.209136, 0.000007, 0.000029, 179.999877, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1426.055297, -944.215759, 202.209136, 0.000007, 0.000029, 179.999877, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1425.525268, -944.215759, 202.209136, 0.000007, 0.000029, 179.999877, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1426.455688, -944.595764, 202.209136, 0.000007, 0.000020, 179.999832, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1426.055297, -944.595764, 202.209136, 0.000007, 0.000020, 179.999832, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1425.525268, -944.595764, 202.209136, 0.000007, 0.000020, 179.999832, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1426.455688, -946.666015, 202.209136, 0.000007, 0.000014, 179.999786, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1426.055297, -946.666015, 202.209136, 0.000007, 0.000014, 179.999786, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1425.525268, -946.666015, 202.209136, 0.000007, 0.000014, 179.999786, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1425.915649, -945.466003, 202.309158, 7.000006, 94.600051, -110.700187, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1426.455688, -945.565673, 202.209136, 0.000007, 0.000020, 179.999832, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1426.095336, -946.105834, 202.209136, 0.000007, 0.000020, 179.999832, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1425.855102, -944.995605, 202.209136, 0.000007, 0.000020, 179.999832, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19836, -1425.939575, -945.618286, 202.196014, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19092, -1425.780761, -943.366760, 202.496002, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19092, -1425.780761, -941.016845, 202.496002, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19092, -1426.560913, -941.016845, 202.496002, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19092, -1426.021118, -942.386657, 202.496002, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19092, -1426.520996, -941.806701, 202.586013, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19092, -1425.680297, -941.806701, 202.275970, 0.000000, 180.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1440, -1427.774658, -937.999572, 200.675552, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1438, -1428.022216, -935.596862, 200.297241, 0.000000, 0.000000, -43.699996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3006, -1433.092285, -936.553833, 200.252716, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, -1421.427734, -953.806091, 201.887695, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, -1421.427734, -945.246032, 201.987701, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19922, -1425.907836, -950.740966, 201.379745, 0.000020, -0.000007, 92.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1426.444091, -952.133789, 202.150283, 0.000007, 0.000029, 179.999923, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18661, -1425.131347, -950.365356, 203.566055, 0.000000, 0.000000, 2.499999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(930, -1429.523437, -954.408935, 201.956069, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(930, -1430.773925, -955.078979, 201.956069, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(930, -1430.773925, -954.408935, 201.956069, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00);

    //lab meth
    methxadtx = CreateDynamicObject(1491, -1638.053588, -2240.141845, 30.434173, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 3374, "ce_farmxref", "sw_barndoor2", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.576538, -2232.641357, 31.270643, 90.000000, 90.000000, 1.600000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.571289, -2232.831542, 31.510646, 90.000000, 90.000000, 1.600000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.691406, -2232.834716, 31.510646, 90.000000, 90.000000, 1.600000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.451416, -2232.828369, 31.510646, 90.000000, 90.000000, 1.600000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.445922, -2233.018310, 31.510646, 90.000000, 90.000000, 1.600000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.566162, -2233.021728, 31.510646, 90.000000, 90.000000, 1.600000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.685913, -2233.024414, 31.510646, 90.000000, 90.000000, 1.600000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.571289, -2234.121582, 31.510646, 89.999992, 180.799850, -89.199821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.691406, -2234.124755, 31.510646, 89.999992, 180.799850, -89.199821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.451416, -2234.118408, 31.510646, 89.999992, 180.799850, -89.199821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.445922, -2234.308349, 31.510646, 89.999992, 180.799850, -89.199821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.566162, -2234.311767, 31.510646, 89.999992, 180.799850, -89.199821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.685913, -2234.314453, 31.510646, 89.999992, 180.799850, -89.199821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.563110, -2233.921142, 31.270643, 89.999992, 180.000000, -88.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.316284, -2238.882568, 31.250642, 89.999992, 180.000000, -88.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.311035, -2239.072753, 31.490646, 89.999992, 180.000000, -88.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.431152, -2239.075927, 31.490646, 89.999992, 180.000000, -88.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.191162, -2239.069580, 31.490646, 89.999992, 180.000000, -88.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.185668, -2239.259521, 31.490646, 89.999992, 180.000000, -88.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.305908, -2239.262939, 31.490646, 89.999992, 180.000000, -88.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.425659, -2239.265625, 31.490646, 89.999992, 180.000000, -88.399993, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.311035, -2240.362792, 31.490646, 89.999992, 181.199920, -89.599868, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.431152, -2240.365966, 31.490646, 89.999992, 181.199920, -89.599868, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.191162, -2240.359619, 31.490646, 89.999992, 181.199920, -89.599868, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.185668, -2240.549560, 31.490646, 89.999992, 181.199920, -89.599868, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.305908, -2240.552978, 31.490646, 89.999992, 181.199920, -89.599868, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.425659, -2240.555664, 31.490646, 89.999992, 181.199920, -89.599868, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.302856, -2240.162353, 31.250642, 89.999992, 180.799850, -89.199821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1628.995971, -2245.203857, 31.250642, 89.999992, 180.799850, -89.199821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1628.990722, -2245.394042, 31.490646, 89.999992, 180.799850, -89.199821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.110839, -2245.397216, 31.490646, 89.999992, 180.799850, -89.199821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1628.870849, -2245.390869, 31.490646, 89.999992, 180.799850, -89.199821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1628.865356, -2245.580810, 31.490646, 89.999992, 180.799850, -89.199821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1628.985595, -2245.584228, 31.490646, 89.999992, 180.799850, -89.199821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.105346, -2245.586914, 31.490646, 89.999992, 180.799850, -89.199821, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1628.990722, -2246.684082, 31.490646, 89.999992, 181.399978, -89.799911, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.110839, -2246.687255, 31.490646, 89.999992, 181.399978, -89.799911, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1628.870849, -2246.680908, 31.490646, 89.999992, 181.399978, -89.799911, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1628.865356, -2246.870849, 31.490646, 89.999992, 181.399978, -89.799911, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1628.985595, -2246.874267, 31.490646, 89.999992, 181.399978, -89.799911, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1629.105346, -2246.876953, 31.490646, 89.999992, 181.399978, -89.799911, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    methxadtx = CreateDynamicObject(19828, -1628.982543, -2246.483642, 31.250642, 89.999992, 181.199920, -89.599868, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(methxadtx, 0, 18646, "matcolours", "grey-93-percent", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(19940, -1638.143188, -2244.779541, 32.581069, 32.299995, 90.000007, -0.000003, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1638.203125, -2244.779541, 32.581069, -47.099998, 90.000007, 0.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1627.853271, -2244.399169, 32.581069, 32.299995, 90.000015, -0.000009, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1627.913208, -2244.399169, 32.581069, -47.099994, 90.000015, 0.000014, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1628.533935, -2234.078857, 32.581069, 32.299995, 90.000022, -0.000014, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1628.593872, -2234.078857, 32.581069, -47.099990, 90.000022, 0.000024, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1638.263305, -2234.659912, 32.581069, 32.299995, 90.000015, -0.000009, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1638.323242, -2234.659912, 32.581069, -47.099994, 90.000015, 0.000014, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1635.083251, -2248.238769, 32.581069, 32.300003, 90.000015, 89.999946, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1635.083251, -2248.298828, 32.581069, -47.099987, 90.000015, 89.999961, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1630.643432, -2248.148681, 32.581069, 32.300010, 90.000015, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1630.643432, -2248.208740, 32.581069, -47.099964, 90.000015, 89.999916, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1635.891357, -2231.977294, 32.636238, 43.600002, 90.000015, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1635.833374, -2232.037353, 32.581069, -47.099964, 90.000015, 89.999916, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1631.335205, -2231.556884, 32.526206, 43.300010, 90.000015, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1631.363281, -2231.616943, 32.607257, -40.899963, 90.000015, 89.999916, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, -1634.443481, -2231.977294, 31.256992, 43.600002, 90.000015, 89.999900, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, -1638.036010, -2238.858642, 33.116565, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, -1638.036010, -2239.848388, 33.116565, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, -1638.116088, -2238.858642, 33.116565, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, -1638.116088, -2239.848388, 33.116565, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, -1638.076049, -2238.858642, 33.116565, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, -1638.076049, -2239.848388, 33.116565, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, -1638.005981, -2238.858642, 33.116565, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19939, -1638.005981, -2239.848388, 33.116565, 0.000000, 90.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1637.577514, -2232.978515, 30.902101, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1636.327392, -2232.978515, 30.902101, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1635.057495, -2232.978515, 30.902101, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1633.798095, -2232.978515, 30.902101, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1629.287353, -2236.618408, 30.902101, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1629.287353, -2242.768310, 30.902101, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1641.507324, -2242.267822, 30.902101, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1641.507324, -2236.217773, 30.902101, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1641.507324, -2239.078125, 30.902101, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1641.507324, -2233.667968, 30.902101, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1639.466796, -2247.927734, 30.822097, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, -1641.516845, -2247.927734, 30.822097, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -1640.310546, -2233.662597, 30.541147, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -1641.480590, -2245.352783, 30.541147, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -1643.280639, -2247.213134, 30.541147, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19922, -1629.368286, -2233.554687, 30.360197, 0.000007, 0.000000, 92.099975, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19922, -1628.910522, -2246.045166, 30.360197, 0.000007, 0.000000, 92.099975, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19922, -1629.139038, -2239.808349, 30.360197, 0.000007, 0.000000, 92.099975, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1629.787231, -2232.199218, 31.209583, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1629.787231, -2232.199218, 31.299585, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1512, -1628.969116, -2232.083007, 31.345760, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1517, -1628.943847, -2232.270019, 31.315364, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19611, -1629.589721, -2232.738769, 30.092172, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1509, -1629.590087, -2232.595703, 31.464546, 0.000000, 0.000000, -16.299999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1629.598876, -2232.743164, 31.679698, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1629.568847, -2232.653076, 31.679698, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1510, -1629.582153, -2232.840332, 31.523347, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1629.497192, -2233.050048, 31.569585, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.688720, -2233.062988, 31.510721, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19821, -1629.156372, -2232.166259, 31.124769, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.598632, -2232.132812, 31.180715, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1629.787231, -2233.489257, 31.209583, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.598632, -2232.252929, 31.180715, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1629.787231, -2233.489257, 31.299585, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1512, -1628.969116, -2233.373046, 31.345760, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1517, -1628.943847, -2233.560058, 31.315364, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19611, -1629.589721, -2234.028808, 30.092172, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1509, -1629.590087, -2233.885742, 31.464546, -0.000003, 0.000014, -16.299999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1629.598876, -2234.033203, 31.679698, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1629.568847, -2233.943115, 31.679698, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1510, -1629.582153, -2234.130371, 31.523347, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1629.497192, -2234.340087, 31.569585, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.688720, -2234.353027, 31.510721, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19821, -1629.156372, -2233.456298, 31.124769, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.598632, -2233.422851, 31.180715, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.598632, -2233.542968, 31.180715, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1575, -1628.982666, -2234.868896, 31.079141, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1579, -1629.701293, -2234.864990, 31.053699, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1580, -1629.396118, -2234.854248, 31.201086, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1629.526977, -2238.440429, 31.189582, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1629.526977, -2238.440429, 31.279584, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1512, -1628.708862, -2238.324218, 31.325759, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1517, -1628.683593, -2238.511230, 31.295364, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19611, -1629.329467, -2238.979980, 30.072172, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1509, -1629.329833, -2238.836914, 31.444545, -0.000000, 0.000007, -16.299999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1629.338623, -2238.984375, 31.659698, -0.000007, 0.000000, -89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1629.308593, -2238.894287, 31.659698, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1510, -1629.321899, -2239.081542, 31.503347, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1629.236938, -2239.291259, 31.549585, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.428466, -2239.304199, 31.490720, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19821, -1628.896118, -2238.407470, 31.104768, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.338378, -2238.374023, 31.160715, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1629.526977, -2239.730468, 31.189582, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.338378, -2238.494140, 31.160715, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1629.526977, -2239.730468, 31.279584, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1512, -1628.708862, -2239.614257, 31.325759, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1517, -1628.683593, -2239.801269, 31.295364, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19611, -1629.329467, -2240.270019, 30.072172, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1509, -1629.329833, -2240.126953, 31.444545, -0.000006, 0.000019, -16.299999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1629.338623, -2240.274414, 31.659698, -0.000022, 0.000000, -89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1629.308593, -2240.184326, 31.659698, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1510, -1629.321899, -2240.371582, 31.503347, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1629.236938, -2240.581298, 31.549585, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.428466, -2240.594238, 31.490720, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19821, -1628.896118, -2239.697509, 31.104768, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.338378, -2239.664062, 31.160715, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.338378, -2239.784179, 31.160715, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1575, -1628.722412, -2241.110107, 31.059141, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1579, -1629.441040, -2241.106201, 31.033699, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1580, -1629.135864, -2241.095458, 31.181085, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1629.206665, -2244.761718, 31.189582, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1629.206665, -2244.761718, 31.279584, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1512, -1628.388549, -2244.645507, 31.325759, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1517, -1628.363281, -2244.832519, 31.295364, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19611, -1629.009155, -2245.301269, 30.072172, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1509, -1629.009521, -2245.158203, 31.444545, -0.000003, 0.000014, -16.299999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1629.018310, -2245.305664, 31.659698, -0.000014, 0.000000, -89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1628.988281, -2245.215576, 31.659698, 0.000014, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1510, -1629.001586, -2245.402832, 31.503347, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1628.916625, -2245.612548, 31.549585, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.108154, -2245.625488, 31.490720, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19821, -1628.575805, -2244.728759, 31.104768, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.018066, -2244.695312, 31.160715, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1629.206665, -2246.051757, 31.189582, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.018066, -2244.815429, 31.160715, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1629.206665, -2246.051757, 31.279584, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1512, -1628.388549, -2245.935546, 31.325759, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1517, -1628.363281, -2246.122558, 31.295364, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19611, -1629.009155, -2246.591308, 30.072172, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1509, -1629.009521, -2246.448242, 31.444545, -0.000007, 0.000029, -16.299999, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1629.018310, -2246.595703, 31.659698, -0.000029, 0.000000, -89.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2711, -1628.988281, -2246.505615, 31.659698, 0.000029, 0.000000, 89.999908, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1510, -1629.001586, -2246.692871, 31.503347, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, -1628.916625, -2246.902587, 31.549585, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.108154, -2246.915527, 31.490720, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19821, -1628.575805, -2246.018798, 31.104768, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.018066, -2245.985351, 31.160715, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, -1629.018066, -2246.105468, 31.160715, 0.000000, 0.000029, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1575, -1628.402099, -2247.431396, 31.059141, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1579, -1629.120727, -2247.427490, 31.033699, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1580, -1628.815551, -2247.416748, 31.181085, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18258, -1638.359985, -2239.429931, 30.750000, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18259, -1633.229980, -2238.840087, 30.390600, 0.000000, 0.000000, 0.000009, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1764, -1637.594970, -2236.263183, 30.388710, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, -1633.579956, -2247.562500, 31.100914, 0.000007, 0.000000, 92.299942, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, -1636.247558, -2247.669677, 31.100914, 0.000007, 0.000000, 92.299942, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, -1637.295532, -2246.011962, 31.100914, 0.000007, 0.000000, 182.299942, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(638, -1637.401977, -2243.364990, 31.100914, 0.000007, 0.000000, 182.299942, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1575, -1637.875366, -2232.684082, 31.381004, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1575, -1637.065673, -2232.644042, 31.381004, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1579, -1636.213989, -2232.574707, 31.393909, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1579, -1635.313964, -2232.574707, 31.343908, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1580, -1634.497802, -2232.563720, 31.372856, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1580, -1633.707641, -2232.503662, 31.342859, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -1635.424682, -2234.909912, 30.436473, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -1632.024047, -2234.909912, 30.436473, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -1634.294189, -2244.759765, 30.436473, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -1634.294189, -2238.899414, 30.436473, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2840, -1630.994018, -2242.689697, 30.436473, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18667, -1638.032592, -2237.265625, 32.666423, 0.000000, 0.000000, -177.399978, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18663, -1637.846313, -2240.966064, 32.619056, 0.000000, 0.000000, -177.300003, 0, 0, -1, 200.00, 200.00);
}