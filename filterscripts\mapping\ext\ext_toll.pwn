CreateSMBToll()
{
    new STREAMER_TAG_OBJECT:tlllxts;
    //BAYSIDE - SF
    tlllxts = CreateDynamicObject(19935, -2677.099853, 1270.277221, 59.846164, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    tlllxts = CreateDynamicObject(19935, -2667.297851, 1270.277221, 59.846164, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    tlllxts = CreateDynamicObject(19133, -2677.098144, 1270.092651, 60.330295, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 1273, "icons3", "greengrad32", 0x00000000);
    tlllxts = CreateDynamicObject(18661, -2667.281250, 1270.053710, 60.350498, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(tlllxts, 0, "X", 100, "Arial", 120, 1, 0xFFFF0000, 0x00000000, 1);
    tlllxts = CreateDynamicObject(19935, -2695.819091, 1278.932983, 59.846164, -0.000007, -0.000007, -89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    tlllxts = CreateDynamicObject(19133, -2695.821044, 1279.117553, 60.330295, -0.000007, -0.000007, -89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 1273, "icons3", "greengrad32", 0x00000000);
    tlllxts = CreateDynamicObject(19935, -2686.115234, 1278.915405, 59.846164, -0.000015, -0.000007, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    tlllxts = CreateDynamicObject(18661, -2686.131835, 1279.139038, 60.350498, -0.000015, -0.000007, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(tlllxts, 0, "X", 100, "Arial", 120, 1, 0xFFFF0000, 0x00000000, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(966, -2680.506591, 1285.100952, 54.393600, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, -2671.666748, 1285.100952, 54.393608, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(968, -2671.625000, 1285.095825, 55.316364, 0.000000, -270.000000, 0.000000, 0, 0, -1, 200.00, 200.00);
    CreateDynamicObject(19967, -2690.464111, 1263.878173, 54.543540, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, -2682.743652, 1263.878173, 54.523540, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, -2699.176757, 1263.878173, 54.753559, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, -2689.628173, 1264.555419, 54.393600, 0.000007, -0.000007, 179.999832, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(968, -2689.561279, 1264.551025, 55.266365, -0.000007, 90.000007, 0.000007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, -2698.129882, 1264.550903, 54.403598, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00);
    CreateDynamicObject(19967, -2680.903076, 1285.648437, 54.503509, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, -2672.481933, 1285.648437, 54.563503, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, -2663.901367, 1285.648437, 54.723484, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19966, -2689.179443, 1266.411743, 54.304012, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19966, -2698.040527, 1266.411743, 54.304031, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19966, -2673.829589, 1284.032226, 54.344062, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19966, -2664.949707, 1284.032226, 54.374061, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, -2680.762695, 1266.994750, 55.034366, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, -2680.772705, 1278.995361, 55.034366, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, -2682.963378, 1278.995361, 55.034366, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, -2682.964843, 1266.994750, 55.034366, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, -2673.512207, 1278.995361, 55.034366, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, -2671.311035, 1278.995361, 55.034366, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, -2689.476074, 1266.994750, 55.034366, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, -2691.506347, 1266.994750, 55.034366, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19935, -2699.048583, 1264.485595, 54.610900, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19935, -2663.896484, 1285.085449, 54.500919, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 

    //LS - LV MULHOLLAND
    tlllxts = CreateDynamicObject(19935, 1744.822875, 526.127258, 32.118862, 0.000000, 0.000000, 71.099983, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    tlllxts = CreateDynamicObject(19935, 1754.209472, 522.914245, 32.118862, 0.000000, 0.000000, 71.099983, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    tlllxts = CreateDynamicObject(19935, 1738.858520, 537.268737, 31.638860, 0.000000, 0.000000, 71.099983, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    tlllxts = CreateDynamicObject(19935, 1730.152343, 540.248352, 31.638860, 0.000000, 0.000000, 71.099983, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    tlllxts = CreateDynamicObject(18667, 1754.137084, 522.687011, 32.648509, 0.000000, 0.000000, 71.299980, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(tlllxts, 0, "X", 100, "Arial", 100, 1, 0xFFFF0000, 0x00000000, 1);
    tlllxts = CreateDynamicObject(18667, 1738.926025, 537.505981, 32.138507, 0.000000, 0.000000, 251.299987, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(tlllxts, 0, "X", 100, "Arial", 100, 1, 0xFFFF0000, 0x00000000, 1);
    tlllxts = CreateDynamicObject(19130, 1744.770874, 525.967529, 32.608470, 0.000000, 0.000000, 70.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 19962, "samproadsigns", "greenbackgroundsign", 0x00000000);
    tlllxts = CreateDynamicObject(19130, 1730.201538, 540.410583, 32.118469, 0.000000, 0.000000, 70.799987, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 19962, "samproadsigns", "greenbackgroundsign", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(966, 1762.372070, 535.325866, 25.756151, 0.000000, 0.000000, -20.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, 1752.805664, 538.807434, 25.756151, 0.000000, 0.000000, -20.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9623, 1742.256713, 531.393676, 29.146507, -3.299999, 0.000000, -18.899995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, 1722.026489, 527.597961, 26.976131, 0.000000, 0.000000, 160.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, 1731.065307, 524.307250, 26.976131, 0.000000, 0.000000, 160.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, 1740.298828, 523.895385, 27.161104, 0.000000, 3.000000, 70.899986, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, 1744.232910, 535.257324, 26.530988, 0.000000, 3.000000, 70.899986, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, 1742.106811, 535.993591, 26.530988, 0.000000, 3.000000, 70.899986, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, 1738.186279, 524.552246, 27.164762, 0.000000, 3.000000, 70.899986, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 1745.709228, 546.282165, 26.233743, 0.000000, 3.699999, 71.000015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 1747.752563, 552.249023, 25.835889, 0.000000, 3.699999, 71.000015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 1749.865356, 552.241210, 25.828239, 1.202844, -3.656922, -109.326766, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 1747.779907, 546.289916, 26.241394, 1.202844, -3.656922, -109.326766, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 1735.652465, 517.080444, 28.090654, 0.000000, 3.299999, 71.000015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 1733.371826, 510.484893, 28.483047, 0.000000, 3.299999, 71.000015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 1737.515747, 516.438842, 28.090654, 0.000000, -3.400001, -109.699974, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 1735.201538, 509.917907, 28.481693, 0.000000, -3.400001, -109.699974, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, 1722.026489, 527.597961, 26.976131, 0.000000, 0.000000, 160.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(968, 1731.016601, 524.323425, 27.886989, 0.000000, 89.400001, 340.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(968, 1762.437622, 535.300170, 26.656980, 0.000000, 89.500015, 520.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 1746.641357, 541.899841, 25.619665, 0.000000, 0.000000, 159.399902, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 1755.550537, 538.176940, 25.629663, 0.000000, 0.000000, 159.399902, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 1762.513305, 535.666137, 25.699665, 0.000000, 0.000000, 159.399902, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 1737.042358, 521.876342, 26.919662, 0.000000, 0.000000, 339.399902, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 1728.973876, 524.908813, 26.959663, 0.000000, 0.000000, 339.399902, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 1721.076416, 527.631774, 26.939662, 0.000000, 0.000000, 339.399902, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19966, 1720.915771, 528.429992, 26.901681, 0.000000, 0.000000, 159.799987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19966, 1731.493652, 524.537475, 26.921682, 0.000000, 0.000000, 159.799987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19966, 1752.712890, 538.244812, 25.761674, 0.000000, 0.000000, 339.799987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19966, 1762.756591, 534.548645, 25.781675, 0.000000, 0.000000, 339.799987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, 1732.073852, 526.669799, 27.164762, 0.000000, 3.000000, 70.899986, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, 1730.080322, 527.360290, 27.164762, 0.000000, 3.000000, 70.899986, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, 1751.018310, 532.907897, 26.530988, 0.000000, 3.000000, 70.899986, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, 1753.135009, 532.174987, 26.530988, 0.000000, 3.000000, 70.899986, 0, 0, -1, 200.00, 200.00); 

    //SMB
    tlllxts = CreateDynamicObject(18763, 51.067920, -1531.309448, 6.425199, 0.000000, 0.000000, -8.699998, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    tlllxts = CreateDynamicObject(2261, 51.305015, -1529.317749, 5.665010, 0.000000, 0.000000, 171.399734, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 1, 14534, "ab_wooziea", "mcstraps_window", 0x00000000);
    tlllxts = CreateDynamicObject(2261, 50.776084, -1533.283203, 5.665010, 0.000000, 0.000000, 351.399719, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 1, 14534, "ab_wooziea", "mcstraps_window", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(17002, 52.890598, -1532.030029, 7.742187, 0.000000, 0.000000, -6.952610, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, 48.003002, -1543.266967, 4.261613, 0.000000, 0.000000, 37.400020, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1238, 51.852592, -1533.359252, 4.573608, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 76.113334, -1534.700439, 5.005285, 0.000000, -1.499999, -7.599997, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 66.836570, -1533.412841, 4.911489, 0.000000, 0.200000, -8.199996, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 59.823680, -1531.964721, 4.722113, 0.000000, 1.100000, -12.699998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(970, 55.210289, -1530.925292, 4.812912, 0.000000, 1.100000, -12.699998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18757, 54.202857, -1532.809936, 6.292826, 0.000000, 0.000000, -8.799998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, 50.118179, -1528.866088, 4.206274, -0.000037, -0.000000, -97.599891, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 52.096134, -1529.906250, 7.569704, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 51.216133, -1529.826171, 7.569704, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 50.416110, -1529.676025, 7.569704, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 49.949066, -1532.681518, 7.569704, 0.000007, -0.000007, 178.299819, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(997, 50.963291, -1521.881958, 4.221613, 0.000000, 0.000000, 37.400020, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 50.826305, -1532.787719, 7.569704, 0.000007, -0.000007, 178.299819, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19280, 51.621425, -1532.961547, 7.569704, 0.000007, -0.000007, 178.299819, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1238, 52.872589, -1529.659423, 4.573608, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3463, 69.000968, -1533.970214, 4.311776, 0.000007, 0.000000, 82.099967, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3463, 62.929164, -1533.127685, 4.311776, 0.000007, 0.000000, 82.099967, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3463, 41.261955, -1529.702636, 4.311776, 0.000000, 0.000000, 81.600013, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3463, 33.436794, -1528.547363, 4.571774, 0.000000, 0.000000, 81.600013, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3463, 76.519111, -1535.012573, 4.311776, 0.000007, 0.000000, 82.099967, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3463, 25.671009, -1527.400512, 4.031773, 0.000000, 0.000000, 81.600013, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 20.270978, -1525.814331, 4.069880, 0.000000, 0.000000, -10.899995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 13.888244, -1524.585815, 3.519881, 0.000000, 0.000000, -10.899995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 8.094688, -1523.470703, 3.099880, 0.000000, 0.000000, -10.899995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 3.499123, -1522.587158, 2.789880, 0.000000, 0.000000, -10.899995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -1.489227, -1521.628417, 2.439882, 0.000000, 0.000000, -10.899995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, 51.763629, -1533.968994, 4.206274, -0.000030, 0.000007, 82.400024, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 47.196281, -1530.998779, 4.849876, 0.000000, 0.000000, -10.899994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 45.821540, -1530.734497, 4.849876, 0.000000, 0.000000, -10.899994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 44.515529, -1530.482910, 4.849876, 0.000000, 0.000000, -10.899994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 56.355834, -1532.191528, 4.849876, 0.000000, 0.000000, -10.899994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 57.612728, -1532.432617, 4.849876, 0.000000, 0.000000, -10.899994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 58.948196, -1532.690063, 4.849876, 0.000000, 0.000000, -10.899994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 96.477569, -1551.474121, 6.177104, 0.000000, -1.899999, -12.799992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 87.335258, -1549.826782, 5.867107, 0.000000, -1.899999, -7.599997, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 78.072578, -1548.590332, 5.557100, 0.000000, -1.899999, -7.599997, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 81.022682, -1521.566284, 4.652245, -0.299997, 1.599997, 176.500076, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 29.572019, -1540.410400, 4.780237, 0.000000, -3.299998, -21.099994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 20.854051, -1537.046630, 4.241436, 0.000000, -3.299998, -21.099994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 12.121226, -1533.676391, 3.601536, 0.000000, -4.399999, -21.099994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 3.399857, -1530.310302, 2.982409, 0.000000, -3.299998, -21.099994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, -5.429090, -1527.234497, 2.392540, 0.000000, -3.999999, -17.299987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, -12.998556, -1525.113159, 1.842162, 0.000000, -3.999999, -13.799983, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, -11.674922, -1512.374267, 1.809154, 0.000000, 4.000000, 174.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, -2.382436, -1513.269775, 2.490041, 0.000000, 4.300001, 174.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 6.920805, -1514.164916, 3.121674, 0.000000, 3.299998, 174.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 16.220394, -1515.059936, 3.790507, 0.000000, 4.900000, 174.500015, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(973, 25.528202, -1515.955932, 4.521905, 0.000000, 4.000000, 174.500015, 0, 0, -1, 200.00, 200.00); 

    //LS - ANGEL PINE (atas)
    CreateDynamicObject(966, -24.987840, -1339.328857, 9.951666, 0.000000, 0.000000, 129.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(4641, -24.697145, -1336.747802, 11.627638, 0.000000, 0.000000, 39.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, -4.121726, -1365.526489, 9.731661, 0.000000, 0.000000, 309.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(4641, -4.440920, -1368.071411, 11.437635, 0.000000, 0.000000, 219.000000, 0, 0, -1, 200.00, 200.00);
    CreateDynamicObject(19121, -4.525135, -1370.988769, 11.473058, 0.000000, 0.000000, 40.599998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -10.129638, -1375.713500, 11.543061, 0.000000, 0.000000, 40.599998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -16.950063, -1381.401367, 11.543061, 0.000000, 0.000000, 40.599998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -23.147768, -1332.267089, 10.673048, 0.000000, 0.000000, 40.599998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -16.291549, -1326.390502, 10.673048, 0.000000, 0.000000, 40.599998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -9.569840, -1320.786499, 10.673048, 0.000000, 0.000000, 40.599998, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3463, -19.860937, -1366.787353, 11.281604, 0.000000, 1.399999, -50.999965, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3463, -27.872207, -1373.467651, 11.277941, 0.000000, 1.399999, -50.999965, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3463, -9.287874, -1339.002685, 10.396254, 0.000000, 1.399999, -50.999965, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3463, 0.011226, -1331.433593, 10.396986, 0.000000, 1.399999, -50.999965, 0, 0, -1, 200.00, 200.00); 

    //flint - blueberry
    tlllxts = CreateDynamicObject(19460, -86.558219, -915.443847, 16.311111, -4.900002, 0.000006, -26.300001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 16640, "a51", "carparkwall12_256", 0x00000000);
    tlllxts = CreateDynamicObject(19460, -81.750610, -905.717773, 15.380901, -4.900002, 0.000006, -26.300001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 16640, "a51", "carparkwall12_256", 0x00000000);
    tlllxts = CreateDynamicObject(19460, -77.005111, -896.115051, 14.462677, -4.900002, 0.000006, -26.300001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 16640, "a51", "carparkwall12_256", 0x00000000);
    tlllxts = CreateDynamicObject(19460, -91.374465, -925.188415, 17.243011, -4.900002, 0.000006, -26.300001, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 16640, "a51", "carparkwall12_256", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(966, -93.982551, -930.600891, 18.463560, 0.000000, 0.000000, 513.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, -74.337173, -890.700927, 14.673538, 0.000000, 0.000000, 1053.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(967, -73.865043, -889.701904, 14.585863, 0.000000, 0.000000, 63.599983, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -74.677124, -891.328308, 15.294324, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(967, -94.526367, -931.638732, 18.555852, 0.000000, 0.000000, 243.599975, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -79.327110, -900.807922, 16.204322, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -84.117218, -910.508178, 17.114332, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -88.907203, -920.238830, 18.074335, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -93.677177, -929.918823, 18.934337, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2599, -81.121215, -887.294189, 15.248361, 0.000000, 0.000000, -44.800003, 0, 0, -1, 200.00, 200.00); 

    //blueberry-whetstone
    tlllxts = CreateDynamicObject(18762, -959.930603, -354.959899, 35.935573, 90.000000, -11.699997, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 3474, "freightcrane", "yellowcabchev_256", 0x00000000);
    tlllxts = CreateDynamicObject(18762, -971.460266, -306.729614, 35.935573, 90.000000, -11.699997, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 0, 3474, "freightcrane", "yellowcabchev_256", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(966, -962.178588, -351.646728, 35.356395, 0.000000, 0.000000, 348.600006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, -968.059082, -310.403778, 35.696464, 0.000000, 0.000000, 167.599990, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19460, -967.848388, -345.249664, 35.055622, -0.000007, 0.000044, -11.099976, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19460, -965.996154, -335.809875, 35.055622, -0.000007, 0.000044, -11.099976, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19460, -964.140258, -326.350189, 35.055622, -0.000007, 0.000044, -11.099976, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19460, -962.290588, -316.919464, 35.055622, -0.000007, 0.000044, -11.099976, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(4640, -961.166259, -353.768371, 37.028709, 0.000000, 0.000000, -101.799995, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(4640, -969.432495, -308.297302, 37.418670, 0.000000, 0.000000, 78.200004, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, -961.388671, -311.870239, 34.826469, 0.000000, 0.000000, 347.599975, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, -968.893554, -350.292572, 34.386394, 0.000000, 0.000000, 528.599975, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1238, -961.195495, -311.188171, 35.631793, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1238, -969.015075, -350.968139, 35.581794, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -961.136840, -310.554351, 35.873252, 0.000000, 0.000000, -10.299994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -959.594238, -302.287353, 35.873252, 0.000000, 0.000000, -10.299994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -957.937683, -293.451232, 35.873252, 0.000000, 0.000000, -10.299994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -969.156860, -351.551666, 35.823261, 0.000000, 0.000000, -10.299994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -970.786376, -359.792938, 35.833274, 0.000000, 0.000000, -10.299994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, -972.426696, -368.428680, 35.823280, 0.000000, 0.000000, -10.299994, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, -958.940551, -342.740173, 39.350032, -0.000000, 0.000007, -10.899990, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, -956.639953, -331.431091, 39.350032, -0.000000, 0.000007, -10.899990, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, -954.140747, -319.141723, 39.400032, -0.000000, 0.000007, -10.899990, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, -976.508728, -338.989929, 39.480030, -0.000000, 0.000007, 169.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, -974.109375, -327.170593, 39.470027, -0.000000, 0.000007, 169.100006, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1226, -971.703491, -315.311370, 39.450038, -0.000000, 0.000007, 169.100006, 0, 0, -1, 200.00, 200.00); 

    //red county - fort carson (gray)
    CreateDynamicObject(966, 526.446655, 475.992767, 17.920701, 0.000000, 0.000000, 393.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(966, 469.434478, 533.653930, 17.890701, 0.000000, 0.000000, 573.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 478.227935, 532.980346, 17.361753, 0.000000, 0.000000, 35.199985, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 484.142059, 524.596435, 17.361753, 0.000000, 0.000000, 35.199985, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 489.971496, 516.263366, 17.361753, 0.000000, 0.000000, 35.199985, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 495.818511, 507.905395, 17.361753, 0.000000, 0.000000, 35.199985, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 501.688354, 499.514984, 17.361753, 0.000000, 0.000000, 35.199985, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 507.583709, 491.105773, 17.361753, 0.000000, 0.000000, 35.199985, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 513.464111, 482.734771, 17.361753, 0.000000, 0.000000, 35.199985, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19456, 517.662597, 476.750427, 17.361753, 0.000000, 0.000000, 35.199985, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 475.344177, 537.104370, 18.462680, 0.000000, 0.000000, -54.599987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 481.184112, 528.765502, 18.472681, 0.000000, 0.000000, -54.599987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 487.015991, 520.421325, 18.462680, 0.000000, 0.000000, -54.599987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 492.880554, 512.100402, 18.462680, 0.000000, 0.000000, -54.599987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 498.746063, 503.743347, 18.462680, 0.000000, 0.000000, -54.599987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 504.622070, 495.319793, 18.472681, 0.000000, 0.000000, -54.599987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 510.510894, 486.930145, 18.462680, 0.000000, 0.000000, -54.599987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19121, 520.519897, 472.586608, 18.472681, 0.000000, 0.000000, -54.599987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(967, 468.710906, 534.802490, 18.086246, 0.000000, 0.000000, 304.799987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(967, 527.059448, 475.058410, 18.116247, 0.000000, 0.000000, 124.899971, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1238, 474.958618, 537.719726, 18.226774, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1238, 521.008544, 471.889587, 18.246774, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 524.745605, 475.406280, 17.927028, 0.000000, 0.000000, 33.500034, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 521.993713, 473.585021, 17.927028, 0.000000, 0.000000, 33.500034, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 471.167480, 534.249755, 17.927028, 0.000000, 0.000000, 33.500034, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19425, 473.918975, 536.070617, 17.927028, 0.000000, 0.000000, 33.500034, 0, 0, -1, 200.00, 200.00); 

    //blueberry - fort carson (red)
    tlllxts = CreateDynamicObject(984, -176.944610, 333.392852, 11.677027, 0.000000, 0.000000, -14.799998, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 9, 14710, "a51", "carparkwall12_256", 0x00000000);
    tlllxts = CreateDynamicObject(984, -173.526779, 346.329010, 11.677027, 0.000000, 0.000000, -14.799998, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 9, 14710, "a51", "carparkwall12_256", 0x00000000);
    tlllxts = CreateDynamicObject(984, -170.091094, 359.332855, 11.677027, 0.000000, 0.000000, -14.799998, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 9, 14710, "a51", "carparkwall12_256", 0x00000000);
    tlllxts = CreateDynamicObject(984, -166.660476, 372.317382, 11.677027, 0.000000, 0.000000, -14.799998, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 9, 14710, "a51", "carparkwall12_256", 0x00000000);
    tlllxts = CreateDynamicObject(984, -163.245269, 385.243804, 11.677027, 0.000000, 0.000000, -14.799998, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 9, 14710, "a51", "carparkwall12_256", 0x00000000);
    tlllxts = CreateDynamicObject(984, -160.183410, 396.950134, 11.677027, 0.000000, 0.000000, -14.799998, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(tlllxts, 9, 14710, "a51", "carparkwall12_256", 0x00000000);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(966, -164.960693, 405.634155, 11.049748, 0.000000, 0.000000, 164.000000, 0, 0, -1, 200.00, 200.00);
    CreateDynamicObject(966, -172.140670, 324.760009, 11.039748, 0.000000, 0.000000, 344.000000, 0, 0, -1, 200.00, 200.00);
    CreateDynamicObject(19121, -178.626083, 326.934936, 11.624607, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00);
    CreateDynamicObject(19121, -175.226104, 339.844909, 11.614606, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00);
    CreateDynamicObject(19121, -171.836181, 352.824829, 11.614606, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00);
    CreateDynamicObject(19121, -168.426132, 365.804901, 11.604606, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00);
    CreateDynamicObject(19121, -165.006179, 378.734680, 11.624606, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00);
    CreateDynamicObject(19121, -158.506195, 403.394836, 11.624606, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00);
    CreateDynamicObject(967, -164.621200, 406.758605, 11.260991, 0.000000, 0.000000, 255.200012, 0, 0, -1, 200.00, 200.00);
    CreateDynamicObject(967, -172.491378, 323.651611, 11.260991, 0.000000, 0.000000, 435.200012, 0, 0, -1, 200.00, 200.00);
    CreateDynamicObject(1238, -158.238540, 404.300628, 11.388125, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00);
    CreateDynamicObject(1238, -178.779281, 326.146942, 11.388126, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00);
}