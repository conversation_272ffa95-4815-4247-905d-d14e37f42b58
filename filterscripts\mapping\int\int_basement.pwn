CreateBasementInt()
{
    static bsmtxt;
    bsmtxt = CreateDynamicObject(2789, -1683.075073, 1018.311462, 20.711666, 0.000000, 0.000000, 90.000000, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bsmtxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bsmtxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bsmtxt = CreateDynamicObject(18661, -1683.215820, 1018.295776, 21.349300, 0.000000, 0.000000, 0.199999, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "RESERVED", 130, "Arial", 110, 1, 0xFFFFFF00, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, -1683.215820, 1018.295776, 20.199281, 0.000000, 0.000000, 0.199999, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "PARKING", 130, "Arial", 110, 1, 0xFFFFFF00, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(2789, -1683.075073, 1048.892822, 20.711666, 0.000007, 0.000000, 89.999977, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bsmtxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bsmtxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bsmtxt = CreateDynamicObject(18661, -1683.215820, 1048.877197, 21.349300, 0.000000, 0.000007, 0.199999, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "RESERVED", 130, "Arial", 110, 1, 0xFFFFFF00, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, -1683.215820, 1048.877197, 20.199281, 0.000000, 0.000007, 0.199999, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "PARKING", 130, "Arial", 110, 1, 0xFFFFFF00, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(2789, -1683.075073, 987.233154, 20.711666, 0.000015, 0.000000, 89.999954, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(bsmtxt, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(bsmtxt, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    bsmtxt = CreateDynamicObject(18661, -1683.215820, 987.217529, 21.349300, 0.000000, 0.000015, 0.199999, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "RESERVED", 130, "Arial", 110, 1, 0xFFFFFF00, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, -1683.215820, 987.217529, 20.199281, 0.000000, 0.000015, 0.199999, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "PARKING", 130, "Arial", 110, 1, 0xFFFFFF00, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, 2511.887451, 2403.185791, 6.226177, 0.000000, 0.000000, 90.099998, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "RESERVED", 90, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, 2511.887451, 2403.185791, 5.406172, 0.000000, 0.000000, 90.099998, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "PARKING", 90, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, 2521.958740, 2403.185791, 6.226177, 0.000007, -0.000000, 90.099975, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "RESERVED", 90, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, 2521.958740, 2403.185791, 5.406172, 0.000007, -0.000000, 90.099975, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "PARKING", 90, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, 2531.949951, 2403.185791, 6.226177, 0.000015, -0.000000, 90.099952, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "RESERVED", 90, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, 2531.949951, 2403.185791, 5.406172, 0.000015, -0.000000, 90.099952, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "PARKING", 90, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, 2541.541259, 2403.185791, 6.226177, 0.000022, -0.000000, 90.099929, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "RESERVED", 90, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, 2541.541259, 2403.185791, 5.406172, 0.000022, -0.000000, 90.099929, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "PARKING", 90, "Arial", 55, 1, 0xFFFFFFFF, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, 2486.990478, 2379.244140, 5.812586, -13.399958, 89.400009, -91.799987, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "g", 30, "Wingdings 3", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, 2494.382812, 2379.010253, 3.978582, -13.799958, 89.400009, -91.799987, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "g", 30, "Wingdings 3", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, 2494.282958, 2374.608398, 4.029948, 14.000014, 89.400009, 88.200012, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "g", 30, "Wingdings 3", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, 2486.979003, 2374.835937, 5.810388, 14.000014, 89.400009, 88.200012, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "g", 30, "Wingdings 3", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, -1746.884765, 987.866088, 17.079942, -172.800003, -90.200004, 90.199989, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "g", 20, "Wingdings 3", 100, 1, 0xFFFFFF00, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, -1738.079467, 992.336303, 16.583698, 179.599960, -89.700027, 155.499862, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "g", 20, "Wingdings 3", 100, 1, 0xFFFFFF00, 0x00000000, 1);
    bsmtxt = CreateDynamicObject(18661, -1746.857299, 981.935546, 17.089170, 172.599838, -90.200004, 270.199981, -1, -1, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(bsmtxt, 0, "g", 20, "Wingdings 3", 100, 1, 0xFFFFFF00, 0x00000000, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(18981, -1750.600463, 984.970581, 20.363685, 0.000000, 0.000000, 0.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(978, -1745.522094, 984.973022, 17.803733, 0.000000, -5.799998, 180.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(978, -1736.863281, 987.115600, 17.340684, 0.000000, 0.399999, -152.299926, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(997, -1732.609008, 987.089599, 16.713880, 0.000000, 0.000000, 90.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(997, -1732.609008, 985.529541, 16.713880, 0.000000, 0.000000, 90.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(10149, -1750.017822, 982.001342, 19.007978, 0.000000, 0.000000, 90.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(18981, 2481.710449, 2376.626953, 6.613592, 0.000000, 0.000000, 0.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(10149, 2482.226562, 2374.806884, 8.441161, 0.000000, 0.000000, 90.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(978, 2486.475341, 2377.111328, 6.665755, 0.000000, -14.800009, 179.999954, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(18981, 2481.710449, 2376.626953, 6.613592, 0.000000, 0.000000, 0.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(978, 2493.056396, 2377.101318, 4.926161, 0.000000, -14.800009, 179.999954, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(10149, 2482.226562, 2379.157714, 8.441161, 0.000000, 0.000000, 90.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(1214, 2496.897705, 2394.643554, 3.186684, 0.000000, 0.000000, 0.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(1214, 2496.897705, 2396.554931, 3.186684, 0.000000, 0.000000, 0.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(1214, 2496.897705, 2398.695800, 3.186684, 0.000000, 0.000000, 0.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(1214, 2496.897705, 2400.717285, 3.186684, 0.000000, 0.000000, 0.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(10149, -1750.017822, 987.681701, 19.007978, 0.000000, 0.000000, 90.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 2482.202636, 2375.867187, 5.534807, 0.000000, 0.000000, 90.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, 2482.202636, 2373.766357, 5.534807, 0.000000, 0.000000, 90.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, -1750.042236, 980.962463, 16.172748, 0.000000, 0.000000, 90.000000, -1, -1, -1, 200.00, 200.00); 
    CreateDynamicObject(19967, -1750.042236, 983.032409, 16.172748, 0.000000, 0.000000, 90.000000, -1, -1, -1, 200.00, 200.00); 
}