CreatePecelLeleExt()
{
    new STREAMER_TAG_OBJECT: Pecellawdk;
    Pecellawdk = CreateDynamicObject(18981, 2029.378295, -1828.030639, 3.547068, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(18981, 2004.498779, -1828.030639, 3.547068, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(18981, 1984.708618, -1828.010620, 3.547068, 0.000007, 0.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2037.081054, -1826.023437, 14.167682, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10763, "airport1_sfse", "yellowscum64", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2037.101074, -1823.712646, 14.157683, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10763, "airport1_sfse", "yellowscum64", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2040.181518, -1827.594116, 14.167682, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2038.621337, -1827.574096, 14.167682, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16093, "a51_ext", "ws_whitewall2_top", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2041.791625, -1823.882934, 14.167682, 0.000000, 0.000000, 179.499969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2041.771606, -1826.133178, 14.167682, 0.000000, 0.000000, 179.499969, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 12944, "ce_bankalley2", "sw_brick04", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2040.429321, -1822.078247, 14.862218, 180.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 9515, "bigboxtemp1", "ws_garagedoor3_white", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2038.429931, -1822.078247, 14.862218, 180.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 9515, "bigboxtemp1", "ws_garagedoor3_white", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2040.131469, -1825.833740, 12.467679, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 11100, "bendytunnel_sfse", "Bow_sub_walltiles", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2038.691406, -1825.833740, 12.477680, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 11100, "bendytunnel_sfse", "Bow_sub_walltiles", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2038.691406, -1823.623046, 12.487680, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 11100, "bendytunnel_sfse", "Bow_sub_walltiles", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2040.141357, -1823.623046, 12.497679, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 11100, "bendytunnel_sfse", "Bow_sub_walltiles", 0x00000000);
    Pecellawdk = CreateDynamicObject(2255, 2038.091552, -1826.856079, 14.074085, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 11389, "hubint1_sfse", "ws_STDcalendar", 0x00000000);
    Pecellawdk = CreateDynamicObject(2448, 2037.624023, -1826.252929, 11.860119, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 3, 14668, "711c", "cj_white_wall2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2041.211669, -1818.915405, 13.511355, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2041.211669, -1819.865234, 13.511355, 90.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19939, 2041.209594, -1819.399780, 13.644268, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(19939, 2041.209594, -1819.399780, 14.084269, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(19570, 2041.222290, -1819.089721, 13.646277, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16377, "des_byofficeint", "water_cool2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19570, 2041.222290, -1819.249633, 13.646277, -13.100000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16377, "des_byofficeint", "water_cool2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19570, 2041.286010, -1819.563720, 13.716279, 0.000000, 90.000000, 135.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16377, "des_byofficeint", "water_cool2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19570, 2041.222290, -1819.689941, 13.646277, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16377, "des_byofficeint", "water_cool2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19570, 2041.222290, -1819.089721, 14.086270, 0.000000, 0.000014, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16377, "des_byofficeint", "water_cool2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19570, 2041.087768, -1819.400024, 14.156269, 0.000004, 90.000015, 44.999988, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16377, "des_byofficeint", "water_cool2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19570, 2041.222290, -1819.500000, 14.086270, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16377, "des_byofficeint", "water_cool2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19570, 2041.222290, -1819.689941, 14.086270, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16377, "des_byofficeint", "water_cool2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19939, 2041.209594, -1819.399780, 14.514275, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(19939, 2041.210571, -1819.380004, 13.174263, 0.000007, 0.000014, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(19570, 2041.197875, -1819.690063, 13.176262, 0.000007, 0.000022, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16377, "des_byofficeint", "water_cool2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19570, 2041.332397, -1819.379760, 13.246261, 0.000015, 90.000015, -135.000045, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16377, "des_byofficeint", "water_cool2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19570, 2041.197875, -1819.279785, 13.176262, 0.000007, 0.000029, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16377, "des_byofficeint", "water_cool2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19570, 2041.197875, -1819.089843, 13.176262, 0.000007, 0.000029, 179.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16377, "des_byofficeint", "water_cool2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2722, 2034.772949, -1821.605712, 15.371842, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "TOKO MAS\nBUDIONO", 90, "Arial", 28, 1, 0xFFFFFFFF, 0xFF000000, 1);
    Pecellawdk = CreateDynamicObject(2599, 2041.146118, -1818.335449, 12.992001, 0.000000, 0.000000, 135.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "JUAL\nBENSIN", 100, "Arial", 55, 1, 0xFFFFFFFF, 0xFF000000, 1);
    Pecellawdk = CreateDynamicObject(19482, 2039.375000, -1821.809814, 15.401925, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "WAROENG AH TONG", 130, "Arial", 30, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(19940, 2035.320678, -1821.826416, 14.287337, 585.000000, 450.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2034.839233, -1821.816406, 13.452947, 675.000000, 450.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2034.060546, -1821.826416, 13.863436, -90.700027, 450.000000, 89.799949, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2033.389648, -1821.823852, 13.925247, -90.700027, 450.000000, 89.799949, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2034.510620, -1821.904663, 14.067685, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 9515, "bigboxtemp1", "ws_garagedoor3_white", 0x00000000);
    Pecellawdk = CreateDynamicObject(18980, 2036.329101, -1822.107788, 3.333358, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    Pecellawdk = CreateDynamicObject(18980, 2032.598754, -1822.107788, 3.333358, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    Pecellawdk = CreateDynamicObject(18762, 2034.429931, -1822.136352, 15.339111, 0.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2033.413818, -1821.823852, 11.945392, -90.700027, 450.000000, 89.799949, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2034.084594, -1821.826416, 11.893583, -90.700027, 450.000000, 89.799949, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14652, "ab_trukstpa", "mp_diner_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2032.311401, -1823.892700, 14.167682, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16640, "a51", "a51_wall1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2032.312377, -1825.983154, 14.167682, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16640, "a51", "a51_wall1", 0x00000000);
    Pecellawdk = CreateDynamicObject(10282, 2028.341674, -1823.242919, 13.336879, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 5, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 6, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 7, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 8, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 9, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 10, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 11, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 12, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 13, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 14, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 15, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(3632, 2031.362182, -1819.588134, 12.636874, 90.000000, 32.100002, 1.400000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2029.398071, -1825.970214, 12.495871, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10871, "blacksky_sfse", "ws_slatetiles", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2029.398071, -1823.769653, 12.496871, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10871, "blacksky_sfse", "ws_slatetiles", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2032.018188, -1825.970214, 12.497872, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10871, "blacksky_sfse", "ws_slatetiles", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2032.018188, -1823.768920, 12.498872, 0.000000, 90.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10871, "blacksky_sfse", "ws_slatetiles", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2027.600341, -1823.892700, 14.167682, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16640, "a51", "concretewall22_256", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2027.601318, -1825.983154, 14.167682, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16640, "a51", "concretewall22_256", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2029.296875, -1827.599487, 14.167682, 0.000030, 0.000015, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16640, "a51", "a51_wall1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2031.387329, -1827.598510, 14.167682, 0.000030, 0.000015, 89.999877, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16640, "a51", "a51_wall1", 0x00000000);
    Pecellawdk = CreateDynamicObject(3632, 2031.610229, -1819.963378, 12.636874, 90.000000, 32.100002, 1.400000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 2772, "airp_prop", "CJ_red_COUNTER", 0x00000000);
    Pecellawdk = CreateDynamicObject(2643, 2032.316650, -1818.598144, 12.666875, 270.000000, 90.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 18246, "cw_junkyard2cs_t", "Was_scrpyd_tires", 0x00000000);
    Pecellawdk = CreateDynamicObject(2643, 2032.316650, -1818.658203, 12.876876, 270.000000, 93.299995, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3629, "arprtxxref_las", "wheel02_64", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 2032.844238, -1818.140625, 12.672814, 0.000000, 0.000000, 136.800094, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 2032.899291, -1818.104003, 12.672809, 0.000000, 0.000000, 136.699981, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "TAMBAL BAN", 130, "Arial", 60, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(19426, 2033.028442, -1820.237670, 12.956880, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(1450, 2028.041137, -1821.359008, 13.136876, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(2467, 2028.755249, -1825.864379, 11.526867, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    Pecellawdk = CreateDynamicObject(2467, 2029.805419, -1825.863403, 11.527868, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    Pecellawdk = CreateDynamicObject(2578, 2027.792358, -1823.875000, 14.272808, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 2, 2464, "rc_shop_acc", "CJ_RC_3", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 4, 2464, "rc_shop_acc", "CJ_RC_2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 5, 2464, "rc_shop_acc", "CJ_RC_1", 0x00000000);
    Pecellawdk = CreateDynamicObject(2608, 2030.891967, -1827.357543, 14.622809, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 2464, "rc_shop_acc", "CJ_RC_2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 5, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 6, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 7, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(2256, 2028.830200, -1827.492553, 14.404612, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3017, "arch_plx", "arch_plans", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2027.580322, -1823.892700, 14.167682, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3437, "ballypillar01", "ballywall01_64", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2027.581298, -1825.983154, 14.167682, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3437, "ballypillar01", "ballywall01_64", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2025.950805, -1827.613647, 14.167682, 0.000000, 0.000015, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3437, "ballypillar01", "ballywall01_64", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2023.099609, -1823.892700, 14.167682, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3437, "ballypillar01", "ballywall01_64", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2023.100585, -1825.983154, 14.167682, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3437, "ballypillar01", "ballywall01_64", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2024.780395, -1827.614624, 14.167682, 0.000000, 0.000015, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3437, "ballypillar01", "ballywall01_64", 0x00000000);
    Pecellawdk = CreateDynamicObject(2467, 2024.165893, -1822.843505, 11.787868, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    Pecellawdk = CreateDynamicObject(2467, 2025.615966, -1822.843505, 11.787868, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    Pecellawdk = CreateDynamicObject(2467, 2025.615966, -1823.993774, 11.787868, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    Pecellawdk = CreateDynamicObject(2467, 2029.324829, -1825.583129, 11.526867, -0.000000, 0.000007, -0.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    Pecellawdk = CreateDynamicObject(2467, 2028.274658, -1825.584106, 11.527868, -0.000000, 0.000007, -0.000015, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    Pecellawdk = CreateDynamicObject(2467, 2023.675781, -1822.563354, 11.787868, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(2467, 2025.126342, -1822.563354, 11.787868, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(2467, 2025.896972, -1823.483276, 11.787868, 0.000000, 0.000000, 630.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(1716, 2025.588134, -1824.043457, 13.711827, 0.000045, -0.000031, 179.999572, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 5150, "wiresetc_las2", "ganggraf01_LA_m", 0xFFFFFFFF);
    SetDynamicObjectMaterial(Pecellawdk, 1, -1, "none", "none", 0xFF303030);
    Pecellawdk = CreateDynamicObject(2266, 2025.353149, -1823.728515, 13.821930, -0.000009, -0.000045, -90.000221, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2266, 2026.354003, -1823.748535, 13.821930, 0.000009, 0.000045, 89.999916, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(19808, 2025.562866, -1823.605712, 13.781845, -0.000031, -0.000029, -90.000053, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, -1, "none", "none", 0xFF404040);
    Pecellawdk = CreateDynamicObject(19874, 2025.591552, -1824.082885, 13.761828, -0.000029, 0.000031, 0.000076, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 18064, "ab_sfammuunits", "gun_blackbox", 0xFFFFFFFF);
    Pecellawdk = CreateDynamicObject(2608, 2023.390869, -1826.607788, 14.652809, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 18866, "mobilephone2", "mobilephone2-2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 5, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 6, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 7, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(2608, 2023.390869, -1825.017700, 14.652809, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 18866, "mobilephone2", "mobilephone2-2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 5, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 6, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 7, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(2608, 2023.390869, -1825.017700, 13.682806, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 18866, "mobilephone2", "mobilephone2-2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 5, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 6, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 7, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(2608, 2023.390869, -1826.648559, 13.682806, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 18866, "mobilephone2", "mobilephone2-2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 5, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 6, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 7, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(2259, 2023.669799, -1823.165771, 14.366880, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 11389, "hubint1_sfse", "ws_STDcalendar", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2024.766723, -1825.970214, 12.495871, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10871, "blacksky_sfse", "ws_slatetiles", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2024.766723, -1823.769653, 12.496871, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10871, "blacksky_sfse", "ws_slatetiles", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2027.386840, -1825.970214, 12.497872, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10871, "blacksky_sfse", "ws_slatetiles", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2027.386840, -1823.768920, 12.498872, 0.000000, 90.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10871, "blacksky_sfse", "ws_slatetiles", 0x00000000);
    Pecellawdk = CreateDynamicObject(2578, 2026.752075, -1827.386840, 14.472807, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 2, 18865, "mobilephone1", "mobilephone1-2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 4, 18866, "mobilephone2", "mobilephone2-2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 5, 18867, "mobilephone3", "mobilephone3-2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 6, 18869, "mobilephone5", "mobilephone5-2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2578, 2025.741699, -1827.386840, 14.472807, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 2967, "mobile93a", "mobile93a", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 18865, "mobilephone1", "mobilephone1-2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 4, 18866, "mobilephone2", "mobilephone2-2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 5, 18867, "mobilephone3", "mobilephone3-2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 6, 18869, "mobilephone5", "mobilephone5-2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19172, 2025.180541, -1819.317993, 15.242822, 0.000000, -0.000007, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3267, "milbase", "lightred2_32", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1714, "cj_office", "BLUE_FABRIC", 0x00000000);
    Pecellawdk = CreateDynamicObject(2722, 2026.067626, -1819.275512, 15.472822, 0.000000, -0.000007, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 7650, "vgnusedcar", "lightyellow2_32", 0x00000000);
    Pecellawdk = CreateDynamicObject(19866, 2030.586914, -1821.995849, 15.306889, 180.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 9514, "711_sfw", "sw_sheddoor2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19866, 2025.396362, -1821.995849, 14.636886, 360.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 9514, "711_sfw", "sw_sheddoor2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2255, 2024.370727, -1818.807617, 14.972819, 0.000000, -0.000007, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14489, "carlspics", "AH_picture2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 2025.273071, -1819.269409, 15.552823, 0.000000, -0.000007, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14489, "carlspics", "AH_picture2", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "INDOSAT", 130, "Arial", 50, 1, 0xFFFF0000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2026.093627, -1819.269409, 15.552824, 0.000000, -0.000007, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14489, "carlspics", "AH_picture2", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "- 10gb: Rp20.000", 130, "Arial", 35, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2026.093627, -1819.269409, 15.392820, 0.000000, -0.000007, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14489, "carlspics", "AH_picture2", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "- 25gb: Rp35.000", 130, "Arial", 35, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2026.093627, -1819.269409, 15.232817, 0.000000, -0.000007, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14489, "carlspics", "AH_picture2", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "- 50gb: Rp65.000", 130, "Arial", 35, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2026.033569, -1819.269409, 15.082814, 0.000000, -0.000007, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14489, "carlspics", "AH_picture2", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "- 120gb: Rp115.000", 130, "Arial", 35, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2025.713256, -1819.269409, 15.792823, 0.000000, -0.000007, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14489, "carlspics", "AH_picture2", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "TELKOMSEL", 130, "Arial", 80, 1, 0xFFFF0000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2025.303710, -1819.269409, 15.402821, 0.000000, -0.000007, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14489, "carlspics", "AH_picture2", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "-20gb: Rp40.000", 130, "Arial", 25, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2025.303710, -1819.269409, 15.282820, 0.000000, -0.000007, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14489, "carlspics", "AH_picture2", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "-50gb: Rp60.000", 130, "Arial", 25, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2025.263671, -1819.269409, 15.172818, 0.000000, -0.000007, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14489, "carlspics", "AH_picture2", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "-100gb: Rp160.000", 130, "Arial", 25, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2025.643188, -1819.269409, 14.742822, 0.000000, -0.000007, 179.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14489, "carlspics", "AH_picture2", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "IWAN PONSEL", 130, "Arial", 80, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(19353, 2027.552246, -1826.089233, 14.075723, 0.000000, 0.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(2643, 2027.539916, -1824.771362, 14.127912, -0.000045, 0.000000, -89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(2643, 2027.539916, -1824.771362, 14.127912, -0.000045, 0.000000, -89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(2643, 2027.539916, -1824.771362, 13.697910, -0.000045, 0.000000, -89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(2643, 2027.539916, -1824.731323, 13.937911, -0.000045, 0.000000, -89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(2643, 2027.539916, -1825.461547, 14.117912, -0.000045, 0.000000, -89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(2643, 2027.539916, -1825.461547, 13.707912, -0.000045, 0.000000, -89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(2643, 2027.538940, -1826.241333, 13.957912, -0.000045, 0.000000, -89.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(323, 2027.385253, -1825.243041, 14.891885, 37.199985, 3.100056, -0.000033, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(323, 2027.385253, -1825.226928, 14.903976, 37.199985, 3.100056, -0.000033, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(323, 2027.385253, -1825.211059, 14.916069, 37.199985, 3.100056, -0.000033, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(323, 2027.385253, -1825.195190, 14.928161, 37.199985, 3.100056, -0.000033, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(323, 2027.385253, -1825.171386, 14.946300, 37.199985, 3.100056, -0.000033, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(323, 2027.385253, -1825.303100, 14.971887, 37.199985, 3.100066, -0.000039, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(323, 2027.385253, -1825.286987, 14.983978, 37.199985, 3.100066, -0.000039, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(323, 2027.385253, -1825.271118, 14.996070, 37.199985, 3.100066, -0.000039, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(323, 2027.385253, -1825.255249, 15.008163, 37.199985, 3.100066, -0.000039, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(323, 2027.385253, -1825.231445, 15.026302, 37.199985, 3.100066, -0.000039, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(323, 2027.385253, -1825.215454, 15.038394, 37.199985, 3.100066, -0.000039, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(323, 2027.379882, -1825.155151, 14.958858, 37.199985, 3.100066, -0.000039, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(323, 2027.379882, -1825.139282, 14.970949, 37.199985, 3.100066, -0.000039, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(323, 2027.384277, -1825.193115, 15.042533, 37.199985, 3.100066, -0.000039, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2027.552246, -1823.848876, 14.075723, 0.000000, 0.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(19426, 2022.784545, -1822.062011, 11.476878, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(19426, 2018.664306, -1822.062011, 11.476878, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2023.069580, -1823.892700, 14.167682, 0.000000, 0.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "sl_vicwall01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2023.070556, -1825.983154, 14.167682, 0.000000, 0.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "sl_vicwall01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2018.388549, -1823.892700, 14.167682, 0.000000, 0.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14535, "ab_wooziec", "ab_wallpaper01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2018.389526, -1825.983154, 14.167682, 0.000000, 0.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14535, "ab_wooziec", "ab_wallpaper01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2020.099609, -1827.583129, 14.167682, 0.000000, 0.000038, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16639, "a51_labs", "studiowall4_law", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2021.380126, -1827.582153, 14.167682, 0.000000, 0.000038, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 16639, "a51_labs", "studiowall4_law", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2021.552612, -1824.009399, 12.469874, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14534, "ab_wooziea", "ab_tileDiamond", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2020.022460, -1824.009399, 12.468873, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14534, "ab_wooziea", "ab_tileDiamond", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2021.552612, -1825.870971, 12.468873, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14534, "ab_wooziea", "ab_tileDiamond", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2020.022460, -1825.870971, 12.467873, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14534, "ab_wooziea", "ab_tileDiamond", 0x00000000);
    Pecellawdk = CreateDynamicObject(19866, 2023.030517, -1825.454833, 13.555819, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 11100, "bendytunnel_sfse", "Bow_sub_walltiles", 0x00000000);
    Pecellawdk = CreateDynamicObject(19466, 2022.958618, -1824.135620, 14.695814, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19466, 2022.958618, -1826.365722, 14.695814, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19866, 2023.030517, -1825.454833, 15.755824, 0.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2018.890258, -1825.092651, 12.972401, 0.000000, 0.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, 2018.724365, -1825.829223, 12.721973, 0.000000, 124.200027, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, 2019.042968, -1825.829223, 12.721369, 0.000000, 57.200004, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, 2018.724365, -1823.619262, 12.721973, 0.000000, 124.200035, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, 2019.042968, -1823.619262, 12.721369, 0.000000, 57.200012, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2018.900268, -1824.312500, 12.982401, 0.000000, 0.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 2018.515014, -1825.902343, 14.715818, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 2018.515014, -1824.162597, 14.715818, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19172, 2020.642456, -1827.475219, 14.436879, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 18081, "cj_barb", "barberspic3", 0x00000000);
    Pecellawdk = CreateDynamicObject(2707, 2022.482177, -1824.469360, 15.625818, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    Pecellawdk = CreateDynamicObject(2707, 2022.482177, -1823.688964, 15.625818, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    Pecellawdk = CreateDynamicObject(2707, 2022.482177, -1825.248779, 15.625818, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    Pecellawdk = CreateDynamicObject(2707, 2022.482177, -1826.048828, 15.625818, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    Pecellawdk = CreateDynamicObject(2707, 2022.482177, -1826.838745, 15.625818, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x90FFFFFF);
    Pecellawdk = CreateDynamicObject(19353, 2013.717773, -1823.892700, 14.167682, 0.000000, 0.000053, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "sl_vicwall02", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2013.718750, -1825.983154, 14.167682, 0.000000, 0.000053, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "sl_vicwall02", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2018.368286, -1823.892700, 14.167682, 0.000000, 0.000053, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "sl_vicwall02", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2018.369262, -1825.983154, 14.167682, 0.000000, 0.000053, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "sl_vicwall02", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2016.927856, -1827.603393, 14.167682, 0.000000, 0.000053, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "sl_vicwall02", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2015.357177, -1827.602416, 14.167682, 0.000000, 0.000053, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "sl_vicwall02", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2016.680786, -1822.661499, 11.722806, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1706, "kbcouch1", "kbwood_panel4_128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2017.290161, -1822.646240, 14.671350, -0.000007, 270.000000, -89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2016.110229, -1822.645263, 14.671350, -0.000007, 270.000000, -89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(19426, 2018.210815, -1823.371826, 13.152816, 0.000000, -0.000015, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1706, "kbcouch1", "kbwood_panel4_128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19426, 2015.140625, -1823.371826, 13.152816, 0.000000, -0.000015, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1706, "kbcouch1", "kbwood_panel4_128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2016.680786, -1824.082885, 11.722806, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1706, "kbcouch1", "kbwood_panel4_128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2017.290161, -1824.067626, 14.671350, -0.000015, 270.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2016.110229, -1824.066650, 14.671350, -0.000015, 270.000000, -89.999961, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 2015.703613, -1822.637329, 13.932811, 0.000000, 89.999984, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 2017.693725, -1822.617309, 13.932811, 0.000000, 89.999984, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 18065, "ab_sfammumain", "shelf_glas", 0x00000000);
    Pecellawdk = CreateDynamicObject(1897, 2017.335205, -1822.673095, 13.772826, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(1897, 2016.234497, -1822.673095, 13.772826, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(1897, 2016.284545, -1822.673095, 13.412826, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(1897, 2017.014892, -1822.673095, 13.412826, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(1897, 2017.014892, -1822.673095, 14.752827, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(1897, 2016.284179, -1822.673095, 14.752827, 89.999992, 179.999984, -89.999984, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(19939, 2015.670043, -1822.922973, 13.722808, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    Pecellawdk = CreateDynamicObject(19939, 2015.670043, -1822.922973, 14.072812, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    Pecellawdk = CreateDynamicObject(3104, 2015.843627, -1823.015502, 13.762809, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(3104, 2015.903686, -1823.015502, 13.762809, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(3104, 2015.903686, -1822.895385, 13.762809, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(3104, 2016.003784, -1822.955444, 13.762809, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(3104, 2016.113891, -1822.955444, 13.762809, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(19939, 2017.711669, -1822.922973, 13.722808, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    Pecellawdk = CreateDynamicObject(19939, 2017.711669, -1822.922973, 14.072812, 0.000022, 0.000000, 89.999931, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14652, "ab_trukstpa", "CJ_WOOD6", 0x00000000);
    Pecellawdk = CreateDynamicObject(3104, 2017.885253, -1823.015502, 13.762809, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(3104, 2017.945312, -1823.015502, 13.762809, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(3104, 2017.945312, -1822.895385, 13.762809, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(3104, 2018.045410, -1822.955444, 13.762809, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(3104, 2018.155517, -1822.955444, 13.762809, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(11718, 2015.507202, -1823.475219, 13.342812, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(11718, 2015.717407, -1823.815551, 13.332817, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2016.588745, -1823.892700, 12.507680, 0.000000, 90.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 15034, "genhotelsave", "bathtile05_int", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2015.588134, -1823.892700, 12.508681, 0.000000, 90.000045, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 15034, "genhotelsave", "bathtile05_int", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2016.588745, -1826.053833, 12.508681, 0.000000, 90.000053, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 15034, "genhotelsave", "bathtile05_int", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2015.588134, -1826.053833, 12.509681, 0.000000, 90.000053, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 15034, "genhotelsave", "bathtile05_int", 0x00000000);
    Pecellawdk = CreateDynamicObject(2256, 2017.145996, -1827.483764, 14.645616, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19172, 2013.850585, -1824.666748, 14.365619, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 2813, "gb_books01", "GB_novels01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2017.246093, -1818.490600, 12.963318, 0.000053, 0.000007, 89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, 2017.982666, -1818.656494, 12.712890, 0.000053, 124.200004, 89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, 2017.982666, -1818.337890, 12.712286, 0.000053, 57.199981, 89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, 2015.772705, -1818.656494, 12.712890, 0.000053, 124.200012, 89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, 2015.772705, -1818.337890, 12.712286, 0.000053, 57.199989, 89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2016.465942, -1818.480590, 12.973319, 0.000053, 0.000007, 89.999809, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2017.246093, -1820.961303, 12.963318, 0.000061, 0.000007, 89.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, 2017.982666, -1821.127197, 12.712890, 0.000061, 124.200004, 89.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, 2017.982666, -1820.808593, 12.712286, 0.000061, 57.199981, 89.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, 2015.772705, -1821.127197, 12.712890, 0.000061, 124.200012, 89.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, 2015.772705, -1820.808593, 12.712286, 0.000061, 57.199989, 89.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2016.465942, -1820.951293, 12.973319, 0.000061, 0.000007, 89.999786, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19172, 2020.913208, -1819.333374, 15.232815, 0.000000, -0.000015, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 8391, "ballys01", "CJ_blackplastic", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(19172, 2020.173217, -1819.332397, 15.232815, 0.000000, -0.000015, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 8391, "ballys01", "CJ_blackplastic", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, 2020.568847, -1819.302001, 14.792815, 360.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 18081, "cj_barb", "barberspic3", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 2021.277099, -1819.290771, 15.772814, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "PANGKAS", 130, "Arial", 100, 1, 0xFF45B6FE, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2019.777221, -1819.290771, 15.772814, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "RAMBUT", 130, "Arial", 100, 1, 0xFF45B6FE, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2020.607421, -1819.290771, 15.462801, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "MANG ALEX", 130, "Arial", 70, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2020.606933, -1819.290771, 15.162814, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 7088, "casinoshops1", "GB_nastybar19", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "A  S  G  A  R", 130, "Arial", 80, 1, 0xFFFF0000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18066, 2022.382812, -1819.321044, 15.112810, 0.000000, 270.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 3335, "ceroadsigns", "sw_barberpole", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 2018.672485, -1819.321044, 15.112810, 0.000000, 270.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 3335, "ceroadsigns", "sw_barberpole", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(19173, 2016.842651, -1819.326538, 15.662815, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10412, "hotel1", "carpet_red_256", 0x00000000);
    Pecellawdk = CreateDynamicObject(19173, 2014.791870, -1819.326538, 15.662815, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10412, "hotel1", "carpet_red_256", 0x00000000);
    Pecellawdk = CreateDynamicObject(19173, 2016.842651, -1819.325561, 15.242814, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10412, "hotel1", "carpet_red_256", 0x00000000);
    Pecellawdk = CreateDynamicObject(19173, 2014.791870, -1819.325561, 15.242814, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10412, "hotel1", "carpet_red_256", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 2017.462402, -1819.287231, 15.752818, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "MIE", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2016.362304, -1819.287231, 15.752818, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "AYAM", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2014.652099, -1819.287231, 15.752818, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "BAKSO", 130, "Arial", 150, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2015.592407, -1819.287231, 15.752818, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "&", 130, "Arial", 100, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2015.872680, -1819.287231, 15.302823, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "MAS PENDEK", 130, "Arial", 80, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2269, 2017.304931, -1818.837280, 14.888944, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 2767, "cb_details", "wrapfood_cb", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(2269, 2014.444580, -1818.837280, 14.888944, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 2767, "cb_details", "wrapfood_cb", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 2016.453125, -1819.287231, 15.022821, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "4 SEHAT", 130, "Arial", 60, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2015.352661, -1819.287231, 15.022821, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "5 WAREG", 130, "Arial", 60, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(19353, 2013.657714, -1823.892700, 14.167682, 0.000000, 0.000061, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 4835, "airoads_las", "ws_carparkwall1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2013.658691, -1825.983154, 14.167682, 0.000000, 0.000061, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 4835, "airoads_las", "ws_carparkwall1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2009.005493, -1823.892700, 14.167682, 0.000000, 0.000068, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 4835, "airoads_las", "ws_carparkwall1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2009.006469, -1825.983154, 14.167682, 0.000000, 0.000068, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 4835, "airoads_las", "ws_carparkwall1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2010.636718, -1827.553222, 14.167683, 0.000000, 0.000068, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 4835, "airoads_las", "ws_carparkwall1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2012.047119, -1827.552246, 14.167683, 0.000000, 0.000068, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 4835, "airoads_las", "ws_carparkwall1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2010.650512, -1823.998413, 12.546875, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3306, "cunte_house1", "tilered", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2012.061279, -1823.998413, 12.545874, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3306, "cunte_house1", "tilered", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2010.650512, -1825.719482, 12.545874, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3306, "cunte_house1", "tilered", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2012.061279, -1825.719482, 12.544874, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3306, "cunte_house1", "tilered", 0x00000000);
    Pecellawdk = CreateDynamicObject(2578, 2013.469360, -1823.820312, 14.533524, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 5, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 6, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(2578, 2013.469360, -1824.860229, 14.533524, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 5, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 6, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(2578, 2013.469360, -1825.890625, 14.533524, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 2, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 5, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 6, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(2608, 2010.942260, -1827.357543, 14.622811, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 2535, "cj_ss_4", "CJ_FISHY", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 5, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 6, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 7, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2008.955444, -1823.892700, 14.167682, 0.000000, 0.000076, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 13060, "ce_fact01", "puttywall1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2008.956420, -1825.983154, 14.167682, 0.000000, 0.000076, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 13060, "ce_fact01", "puttywall1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2004.315063, -1823.892700, 14.167682, 0.000000, 0.000083, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 4001, "civic03_lan", "twintWall2_LAn", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2004.316040, -1825.983154, 14.167682, 0.000000, 0.000083, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 4001, "civic03_lan", "twintWall2_LAn", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2005.926269, -1827.613769, 14.167682, 0.000000, 0.000083, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14674, "civic02cj", "ab_hosWallUpr", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2007.306884, -1827.612792, 14.167682, 0.000000, 0.000083, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14674, "civic02cj", "ab_hosWallUpr", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2005.888183, -1823.998413, 12.546875, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 13724, "docg01_lahills", "ab_tile2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2007.298950, -1823.998413, 12.545874, 0.000007, 90.000000, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 13724, "docg01_lahills", "ab_tile2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2005.888183, -1825.719482, 12.545874, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 13724, "docg01_lahills", "ab_tile2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2007.298950, -1825.719482, 12.544874, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 13724, "docg01_lahills", "ab_tile2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2467, 2008.225097, -1822.843505, 11.787868, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    Pecellawdk = CreateDynamicObject(2467, 2006.825195, -1822.843505, 11.787868, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 14581, "ab_mafiasuitea", "barbersmir1", 0x00000000);
    Pecellawdk = CreateDynamicObject(2608, 2007.762084, -1827.357543, 14.462808, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 2813, "gb_books01", "GB_novels03", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 3, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 4, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 5, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 6, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 7, 19480, "signsurf", "sign", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2004.275024, -1823.892700, 14.167682, 0.000000, 0.000091, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14534, "ab_wooziea", "ab_redwall", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 2004.276000, -1825.983154, 14.167682, 0.000000, 0.000091, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14534, "ab_wooziea", "ab_redwall", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 1994.983764, -1823.892700, 14.167682, 0.000000, 0.000099, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14534, "ab_wooziea", "ab_redwall", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 1994.984741, -1825.983154, 14.167682, 0.000000, 0.000099, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14534, "ab_wooziea", "ab_redwall", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 1999.485107, -1827.593505, 14.167684, 0.000000, 0.000099, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14853, "gen_pol_vegas", "blue_carpet_256", 0x00000000);
    Pecellawdk = CreateDynamicObject(19426, 2004.093139, -1822.062011, 11.476878, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(19426, 1999.972900, -1822.062011, 11.476878, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 1999.485107, -1825.813232, 12.477682, 0.000000, 90.000099, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 18022, "genintintfasta", "CJ_TILE1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 1999.485107, -1823.922363, 12.476680, 0.000000, 90.000099, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 18022, "genintintfasta", "CJ_TILE1", 0x00000000);
    Pecellawdk = CreateDynamicObject(2115, 2002.276977, -1825.544433, 12.563618, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(1716, 2003.378173, -1825.667602, 13.308564, 0.000022, 0.000006, -90.000068, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 5150, "wiresetc_las2", "ganggraf01_LA_m", 0xFFFFFFFF);
    SetDynamicObjectMaterial(Pecellawdk, 1, -1, "none", "none", 0xFF303030);
    Pecellawdk = CreateDynamicObject(2266, 2003.063232, -1825.902587, 13.418667, 0.000028, -0.000022, -0.000366, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2266, 2003.083251, -1824.901733, 13.418667, -0.000029, 0.000022, -179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(19808, 2002.940429, -1825.692871, 13.378582, 0.000006, -0.000006, -0.000198, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, -1, "none", "none", 0xFF404040);
    Pecellawdk = CreateDynamicObject(19874, 2003.417602, -1825.664184, 13.358565, -0.000006, -0.000006, 89.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 18064, "ab_sfammuunits", "gun_blackbox", 0xFFFFFFFF);
    Pecellawdk = CreateDynamicObject(2247, 2002.142944, -1825.496093, 13.823619, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(2610, 2002.329833, -1825.582885, 12.347581, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3967, "cj_airprt", "CJ_BLACK_RUB2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(19866, 1997.487792, -1827.519775, 13.570858, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 1996.623657, -1822.222656, 14.167682, 0.000000, 0.000099, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14534, "ab_wooziea", "ab_redwall", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, 1998.283813, -1822.223632, 14.167682, 0.000000, 0.000099, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14534, "ab_wooziea", "ab_redwall", 0x00000000);
    Pecellawdk = CreateDynamicObject(19866, 1996.658081, -1823.039550, 13.570858, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "walp45S", 0x00000000);
    Pecellawdk = CreateDynamicObject(2256, 2001.243652, -1827.488403, 14.463618, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 2362, "rc_shop_figure", "CJ_RC_11", 0x00000000);
    Pecellawdk = CreateDynamicObject(1716, 1998.973388, -1827.003662, 13.618567, 0.000068, 0.000007, 89.999763, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 5150, "wiresetc_las2", "ganggraf01_LA_m", 0xFFFFFFFF);
    SetDynamicObjectMaterial(Pecellawdk, 1, -1, "none", "none", 0xFF303030);
    Pecellawdk = CreateDynamicObject(2266, 1999.288330, -1826.768676, 13.728671, 0.000029, -0.000068, 179.999465, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2266, 1999.268310, -1827.769531, 13.728671, -0.000028, 0.000068, 0.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(19808, 1999.411132, -1826.978393, 13.688585, 0.000007, -0.000052, 179.999633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, -1, "none", "none", 0xFF404040);
    Pecellawdk = CreateDynamicObject(19874, 1998.933959, -1827.007080, 13.668568, -0.000052, -0.000006, -89.999824, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 18064, "ab_sfammuunits", "gun_blackbox", 0xFFFFFFFF);
    Pecellawdk = CreateDynamicObject(1716, 1995.353149, -1827.003662, 13.618567, 0.000076, 0.000007, 89.999740, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 5150, "wiresetc_las2", "ganggraf01_LA_m", 0xFFFFFFFF);
    SetDynamicObjectMaterial(Pecellawdk, 1, -1, "none", "none", 0xFF303030);
    Pecellawdk = CreateDynamicObject(2266, 1995.668090, -1826.768676, 13.728671, 0.000029, -0.000076, 179.999420, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2266, 1995.648071, -1827.769531, 13.728671, -0.000028, 0.000076, 0.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(19808, 1995.790893, -1826.978393, 13.688585, 0.000007, -0.000060, 179.999588, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, -1, "none", "none", 0xFF404040);
    Pecellawdk = CreateDynamicObject(19874, 1995.313720, -1827.007080, 13.668568, -0.000060, -0.000006, -89.999801, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 18064, "ab_sfammuunits", "gun_blackbox", 0xFFFFFFFF);
    Pecellawdk = CreateDynamicObject(1716, 1997.153808, -1827.003662, 13.618567, 0.000083, 0.000007, 89.999717, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 5150, "wiresetc_las2", "ganggraf01_LA_m", 0xFFFFFFFF);
    SetDynamicObjectMaterial(Pecellawdk, 1, -1, "none", "none", 0xFF303030);
    Pecellawdk = CreateDynamicObject(2266, 1997.468750, -1826.768676, 13.728671, 0.000029, -0.000083, 179.999374, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2266, 1997.448730, -1827.769531, 13.728671, -0.000028, 0.000083, 0.000061, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(19808, 1997.591552, -1826.978393, 13.688585, 0.000007, -0.000067, 179.999542, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, -1, "none", "none", 0xFF404040);
    Pecellawdk = CreateDynamicObject(19874, 1997.114379, -1827.007080, 13.668568, -0.000067, -0.000006, -89.999778, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 18064, "ab_sfammuunits", "gun_blackbox", 0xFFFFFFFF);
    Pecellawdk = CreateDynamicObject(2610, 1998.960205, -1827.092651, 12.347581, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3967, "cj_airprt", "CJ_BLACK_RUB2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(2610, 1996.869873, -1827.092651, 12.347581, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3967, "cj_airprt", "CJ_BLACK_RUB2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(2610, 1995.359619, -1827.092651, 12.347581, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3967, "cj_airprt", "CJ_BLACK_RUB2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(1716, 1996.210937, -1822.773925, 13.618567, 0.000076, 0.000007, -90.000228, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 5150, "wiresetc_las2", "ganggraf01_LA_m", 0xFFFFFFFF);
    SetDynamicObjectMaterial(Pecellawdk, 1, -1, "none", "none", 0xFF303030);
    Pecellawdk = CreateDynamicObject(2266, 1995.895996, -1823.008911, 13.728671, 0.000029, -0.000076, -0.000640, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2266, 1995.916015, -1822.008056, 13.728671, -0.000028, 0.000076, -179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(19808, 1995.773193, -1822.799194, 13.688585, 0.000007, -0.000060, -0.000473, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, -1, "none", "none", 0xFF404040);
    Pecellawdk = CreateDynamicObject(19874, 1996.250366, -1822.770507, 13.668568, -0.000060, -0.000006, 90.000137, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 18064, "ab_sfammuunits", "gun_blackbox", 0xFFFFFFFF);
    Pecellawdk = CreateDynamicObject(1716, 1998.660400, -1822.773925, 13.618567, 0.000068, 0.000007, -90.000205, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 5150, "wiresetc_las2", "ganggraf01_LA_m", 0xFFFFFFFF);
    SetDynamicObjectMaterial(Pecellawdk, 1, -1, "none", "none", 0xFF303030);
    Pecellawdk = CreateDynamicObject(2266, 1998.345458, -1823.008911, 13.728671, 0.000029, -0.000068, -0.000640, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19894, "laptopsamp1", "laptopscreen2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2266, 1998.365478, -1822.008056, 13.728671, -0.000028, 0.000068, -179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(19808, 1998.222656, -1822.799194, 13.688585, 0.000007, -0.000052, -0.000473, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, -1, "none", "none", 0xFF404040);
    Pecellawdk = CreateDynamicObject(19874, 1998.699829, -1822.770507, 13.668568, -0.000052, -0.000006, 90.000114, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 18064, "ab_sfammuunits", "gun_blackbox", 0xFFFFFFFF);
    Pecellawdk = CreateDynamicObject(19172, 1995.062866, -1824.931274, 14.523616, 0.000000, 0.000000, 450.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 2362, "rc_shop_figure", "CJ_RC_10", 0x00000000);
    Pecellawdk = CreateDynamicObject(2610, 1998.030273, -1822.712280, 12.347581, 0.000000, 0.000000, 720.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3967, "cj_airprt", "CJ_BLACK_RUB2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(2610, 1995.399902, -1822.712280, 12.347581, 0.000000, 0.000000, 720.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3967, "cj_airprt", "CJ_BLACK_RUB2", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 2, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2039.813720, -1824.064453, 15.927247, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2030.183715, -1824.034423, 15.927247, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2020.553710, -1824.034423, 15.927247, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2010.923706, -1824.034423, 15.927247, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2001.293701, -1824.034423, 15.927247, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 1991.663696, -1824.034423, 15.927247, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 1982.033691, -1824.034423, 15.927247, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 1976.514160, -1824.034423, 15.928247, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2039.813720, -1826.595458, 15.928247, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2030.183715, -1826.595458, 15.928247, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2020.553710, -1826.595458, 15.928247, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2010.923706, -1826.595458, 15.928247, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2001.293701, -1826.595458, 15.928247, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 1991.663696, -1826.595458, 15.928247, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 1982.033691, -1826.595458, 15.928247, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 1976.514160, -1826.595458, 15.929247, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ws_rooftarmac1", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2039.813720, -1824.064453, 15.922246, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2030.183715, -1824.034423, 15.922246, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2020.553710, -1824.034423, 15.922246, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2010.923706, -1824.034423, 15.922246, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2001.293701, -1824.034423, 15.922246, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 1991.663696, -1824.034423, 15.922246, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 1982.033691, -1824.034423, 15.922246, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 1976.514160, -1824.034423, 15.923247, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2039.813720, -1826.595458, 15.923247, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2030.183715, -1826.595458, 15.923247, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2020.553710, -1826.595458, 15.923247, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2010.923706, -1826.595458, 15.923247, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2001.293701, -1826.595458, 15.923247, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 1991.663696, -1826.595458, 15.923247, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 1982.033691, -1826.595458, 15.923247, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 1976.514160, -1826.595458, 15.924247, 0.000015, 90.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3063, "col_wall1x", "mp_diner_ceilingdirt", 0x00000000);
    Pecellawdk = CreateDynamicObject(19173, 2011.202270, -1819.330444, 15.662815, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(19173, 2011.202270, -1819.329467, 15.222811, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 2010.432983, -1819.291137, 15.252817, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "MAN", 90, "Arial", 90, 1, 0xFFFF0000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2011.592041, -1819.291137, 15.792819, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "MADURA", 130, "Arial", 90, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2010.462036, -1819.291137, 15.792819, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "STRIKE", 130, "Arial", 90, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(19173, 2010.881958, -1819.330444, 15.662815, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(19173, 2010.881958, -1819.329467, 15.222811, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 2011.653198, -1819.291137, 15.252817, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "KAIL", 90, "Arial", 90, 1, 0xFFFF0000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2030.404052, -1821.774169, 15.442817, 0.000000, -0.000022, 179.999649, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14489, "carlspics", "AH_picture2", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "UCOK BAN", 130, "Arial", 80, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2029.303833, -1821.774169, 15.442817, 0.000000, -0.000022, 179.999649, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14489, "carlspics", "AH_picture2", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "SEREP", 130, "Arial", 80, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(19173, 2006.831787, -1819.330444, 15.662815, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 2007.263183, -1819.291137, 15.572820, 0.000000, -0.000030, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "PANG", 90, "Arial", 75, 1, 0xFFFF0000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2005.983032, -1819.291137, 15.572820, 0.000000, -0.000030, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "LONG", 90, "Arial", 75, 1, 0xFFFF0000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2006.573242, -1819.291137, 15.192816, 0.000000, -0.000030, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "SUMBER MAKMUR", 130, "Arial", 65, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(19173, 2006.371826, -1819.329467, 15.662815, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    Pecellawdk = CreateDynamicObject(19173, 2006.831787, -1819.329467, 15.382811, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    Pecellawdk = CreateDynamicObject(19173, 2006.371826, -1819.328491, 15.382811, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1426, "break_scaffold", "CJ_BLUE_WOOD", 0x00000000);
    Pecellawdk = CreateDynamicObject(19172, 2001.733276, -1819.324584, 15.233620, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 13761, "lahills_whisky", "discharger", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 2002.126464, -1819.334716, 15.716874, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 2001.355957, -1819.334716, 15.716874, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 19480, "signsurf", "sign", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 2002.188842, -1819.277099, 15.736879, 0.000000, -0.000022, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "WARNET", 130, "Arial", 70, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 2001.298461, -1819.277099, 15.736879, 0.000000, -0.000022, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 3603, "bevmans01_la", "aamanbev1x", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "JASFAN", 130, "Arial", 70, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18766, 1972.182006, -1824.735839, 11.063532, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(18766, 1972.182983, -1826.017089, 11.063532, 0.000000, 90.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10101, "2notherbuildsfe", "ferry_build14", 0x00000000);
    Pecellawdk = CreateDynamicObject(19172, 1992.723266, -1821.820312, 14.073626, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1239, "icons6", "lyellow32", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1993.325683, -1821.831298, 14.156876, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1992.125244, -1821.831298, 14.156876, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1992.125244, -1821.831298, 14.066874, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1993.326416, -1821.831298, 14.066874, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 1992.751464, -1821.777465, 14.562825, 0.000000, -0.000030, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "RUKO INI", 130, "Arial", 120, 1, 0xFFFF0000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1993.001708, -1821.767456, 14.092820, 0.000000, -0.000038, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "D I S E W A K", 130, "Arial", 90, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1991.881713, -1821.767456, 14.092820, 0.000000, -0.000038, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "A N", 130, "Arial", 90, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(11710, 1992.987426, -1821.856201, 13.736883, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(11710, 1992.477172, -1821.856201, 13.736883, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 1992.751464, -1821.777465, 13.722816, 0.000000, -0.000045, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "HUBUNGI", 130, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1993.372070, -1821.767456, 13.452811, 0.000000, -0.000061, 179.999633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "+62 851", 130, "Arial", 70, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1992.661743, -1821.767456, 13.452811, 0.000000, -0.000061, 179.999633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "- 3351", 130, "Arial", 70, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1992.061279, -1821.767456, 13.452811, 0.000000, -0.000061, 179.999633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "- 6623", 130, "Arial", 70, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(19172, 1988.000976, -1821.820312, 14.073626, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1239, "icons6", "lyellow32", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1988.603393, -1821.831298, 14.156876, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1987.402954, -1821.831298, 14.156876, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1987.402954, -1821.831298, 14.066874, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1988.604125, -1821.831298, 14.066874, 0.000000, -0.000007, 179.999954, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 1988.029174, -1821.777465, 14.562825, 0.000000, -0.000038, 179.999771, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "RUKO INI", 130, "Arial", 120, 1, 0xFFFF0000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1988.279418, -1821.767456, 14.092820, 0.000000, -0.000045, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "D I S E W A K", 130, "Arial", 90, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1987.159423, -1821.767456, 14.092820, 0.000000, -0.000045, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "A N", 130, "Arial", 90, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(11710, 1988.265136, -1821.856201, 13.736883, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(11710, 1987.754882, -1821.856201, 13.736883, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 1988.029174, -1821.777465, 13.722816, 0.000000, -0.000053, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "HUBUNGI", 130, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1988.649780, -1821.767456, 13.452811, 0.000000, -0.000068, 179.999588, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "+62 851", 130, "Arial", 70, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1987.939453, -1821.767456, 13.452811, 0.000000, -0.000068, 179.999588, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "- 3351", 130, "Arial", 70, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1987.338989, -1821.767456, 13.452811, 0.000000, -0.000068, 179.999588, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "- 6623", 130, "Arial", 70, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(19172, 1983.329101, -1821.820312, 14.073626, 0.000000, -0.000015, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1239, "icons6", "lyellow32", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1983.931518, -1821.831298, 14.156876, 0.000000, -0.000015, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1982.731079, -1821.831298, 14.156876, 0.000000, -0.000015, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1982.731079, -1821.831298, 14.066874, 0.000000, -0.000015, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1983.932250, -1821.831298, 14.066874, 0.000000, -0.000015, 179.999908, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 1983.357299, -1821.777465, 14.562825, 0.000000, -0.000045, 179.999725, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "RUKO INI", 130, "Arial", 120, 1, 0xFFFF0000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1983.607543, -1821.767456, 14.092820, 0.000000, -0.000053, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "D I S E W A K", 130, "Arial", 90, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1982.487548, -1821.767456, 14.092820, 0.000000, -0.000053, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "A N", 130, "Arial", 90, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(11710, 1983.593261, -1821.856201, 13.736883, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(11710, 1983.083007, -1821.856201, 13.736883, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 1983.357299, -1821.777465, 13.722816, 0.000000, -0.000061, 179.999633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "HUBUNGI", 130, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1983.977905, -1821.767456, 13.452811, 0.000000, -0.000076, 179.999542, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "+62 851", 130, "Arial", 70, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1983.267578, -1821.767456, 13.452811, 0.000000, -0.000076, 179.999542, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "- 3351", 130, "Arial", 70, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1982.667114, -1821.767456, 13.452811, 0.000000, -0.000076, 179.999542, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "- 6623", 130, "Arial", 70, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(19172, 1978.658569, -1821.820312, 14.073626, 0.000000, -0.000022, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1239, "icons6", "lyellow32", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1979.260986, -1821.831298, 14.156876, 0.000000, -0.000022, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1978.060546, -1821.831298, 14.156876, 0.000000, -0.000022, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1978.060546, -1821.831298, 14.066874, 0.000000, -0.000022, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1979.261718, -1821.831298, 14.066874, 0.000000, -0.000022, 179.999862, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 1978.686767, -1821.777465, 14.562825, 0.000000, -0.000053, 179.999679, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "RUKO INI", 130, "Arial", 120, 1, 0xFFFF0000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1978.937011, -1821.767456, 14.092820, 0.000000, -0.000061, 179.999633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "D I S E W A K", 130, "Arial", 90, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1977.817016, -1821.767456, 14.092820, 0.000000, -0.000061, 179.999633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "A N", 130, "Arial", 90, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(11710, 1978.922729, -1821.856201, 13.736883, 0.000000, 0.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(11710, 1978.412475, -1821.856201, 13.736883, 0.000000, 0.000030, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 1978.686767, -1821.777465, 13.722816, 0.000000, -0.000068, 179.999588, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "HUBUNGI", 130, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1979.307373, -1821.767456, 13.452811, 0.000000, -0.000083, 179.999496, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "+62 851", 130, "Arial", 70, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1978.597045, -1821.767456, 13.452811, 0.000000, -0.000083, 179.999496, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "- 3351", 130, "Arial", 70, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1977.996582, -1821.767456, 13.452811, 0.000000, -0.000083, 179.999496, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "- 6623", 130, "Arial", 70, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(19172, 1974.018188, -1821.820312, 14.073626, 0.000000, -0.000030, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1239, "icons6", "lyellow32", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1974.620605, -1821.831298, 14.156876, 0.000000, -0.000030, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1973.420166, -1821.831298, 14.156876, 0.000000, -0.000030, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1973.420166, -1821.831298, 14.066874, 0.000000, -0.000030, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(18066, 1974.621337, -1821.831298, 14.066874, 0.000000, -0.000030, 179.999816, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 1975, "texttest", "kb_red", 0x00000000);
    SetDynamicObjectMaterial(Pecellawdk, 1, 1975, "texttest", "kb_red", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 1974.046386, -1821.777465, 14.562825, 0.000000, -0.000061, 179.999633, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "RUKO INI", 130, "Arial", 120, 1, 0xFFFF0000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1974.296630, -1821.767456, 14.092820, 0.000000, -0.000068, 179.999588, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "D I S E W A K", 130, "Arial", 90, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1973.176635, -1821.767456, 14.092820, 0.000000, -0.000068, 179.999588, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "A N", 130, "Arial", 90, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(11710, 1974.282348, -1821.856201, 13.736883, 0.000000, 0.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(11710, 1973.772094, -1821.856201, 13.736883, 0.000000, 0.000038, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "black64", 0x00000000);
    Pecellawdk = CreateDynamicObject(2662, 1974.046386, -1821.777465, 13.722816, 0.000000, -0.000076, 179.999542, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "HUBUNGI", 130, "Arial", 50, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1974.666992, -1821.767456, 13.452811, 0.000000, -0.000091, 179.999450, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "+62 851", 130, "Arial", 70, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1973.956665, -1821.767456, 13.452811, 0.000000, -0.000091, 179.999450, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "- 3351", 130, "Arial", 70, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(2662, 1973.356201, -1821.767456, 13.452811, 0.000000, -0.000091, 179.999450, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "- 6623", 130, "Arial", 70, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(19866, 2020.575927, -1821.995849, 15.026888, 360.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 9514, "711_sfw", "sw_sheddoor2", 0x00000000);
    Pecellawdk = CreateDynamicObject(19866, 2016.164794, -1821.995849, 14.766885, 360.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3820, "boxhses_sfsx", "ws_garagedoor2_blue", 0x00000000);
    Pecellawdk = CreateDynamicObject(19866, 2011.184448, -1821.995849, 14.536882, 360.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3820, "boxhses_sfsx", "ws_garagedoor2_white", 0x00000000);
    Pecellawdk = CreateDynamicObject(19866, 2006.654174, -1821.995849, 14.536882, 360.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3820, "boxhses_sfsx", "ws_garagedoor3_pink", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, 2036.789672, -1821.916625, 13.706883, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, -1, "none", "none", 0x00FFFFFF);
    Pecellawdk = CreateDynamicObject(19445, 2027.589965, -1821.916625, 13.706883, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, -1, "none", "none", 0x00FFFFFF);
    Pecellawdk = CreateDynamicObject(19445, 2008.999145, -1821.916625, 13.706883, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, -1, "none", "none", 0x00FFFFFF);
    Pecellawdk = CreateDynamicObject(19445, 2013.259765, -1821.916625, 13.706883, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, -1, "none", "none", 0x00FFFFFF);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(1808, 2037.378784, -1825.637695, 11.230188, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, 2037.498901, -1825.307373, 11.230188, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, 2037.749145, -1825.537597, 11.230188, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, 2037.839233, -1825.197265, 11.230188, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1776, 2041.622924, -1824.750976, 13.441091, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1977, 2041.713745, -1823.677490, 12.484156, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 2040.385498, -1827.337524, 13.793836, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 2040.385498, -1827.337524, 14.303837, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19940, 2040.385498, -1827.337524, 14.763847, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(321, 2042.003051, -1822.720703, 13.364343, 360.000000, 90.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19564, 2041.040039, -1827.421142, 13.797795, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19564, 2040.839843, -1827.421142, 13.797795, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19564, 2040.639648, -1827.421142, 13.797795, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 2040.216552, -1827.422485, 13.803644, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 2040.006469, -1827.422485, 13.803644, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19563, 2039.786376, -1827.422485, 13.803644, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(918, 2038.357177, -1825.734985, 12.908025, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(918, 2038.837524, -1825.734985, 12.908025, 0.000000, 0.000000, 175.200042, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(918, 2038.756347, -1825.266235, 12.908025, 0.000000, 0.000000, 122.200050, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1808, 2037.578979, -1825.487548, 11.660188, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, 2041.220336, -1825.854003, 13.110248, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(935, 2041.220336, -1825.844116, 14.250254, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 2040.123535, -1827.388061, 14.303497, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 2039.923339, -1827.388061, 14.303497, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19569, 2039.733154, -1827.388061, 14.303497, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, 2041.202270, -1820.924560, 13.008514, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(930, 2037.344726, -1821.503051, 13.003142, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19834, 2035.551879, -1821.800292, 13.534108, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19834, 2033.921875, -1821.790283, 13.534108, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1025, 2032.614746, -1821.489135, 14.276890, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1025, 2032.614746, -1821.489135, 13.156887, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1025, 2032.403320, -1818.546386, 13.306888, -0.000011, -0.000010, -131.699951, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1025, 2032.224243, -1818.747558, 13.306888, 0.000011, 0.000010, 48.300018, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2482, 2032.869628, -1820.048339, 12.546875, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19921, 2032.354370, -1820.046875, 13.686876, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19921, 2032.374389, -1820.287109, 12.746870, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19921, 2032.374389, -1820.177001, 12.946874, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19621, 2032.778320, -1820.633422, 13.686883, 0.000000, 0.000000, -75.299987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19621, 2032.807373, -1820.036376, 14.016887, 0.000000, 0.000000, -75.299987, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19898, 2030.753295, -1820.023803, 12.586873, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14693, 2028.467041, -1820.046875, 12.632810, 0.000000, 0.000000, 141.299942, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14693, 2028.467041, -1820.046875, 12.302805, 0.000000, 0.000000, 141.299942, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2134, 2031.434814, -1826.654541, 12.151806, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2063, 2031.925781, -1824.402221, 13.432808, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2048, 2032.203247, -1825.794677, 15.102808, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1514, 2028.219970, -1825.716674, 13.701807, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1514, 2023.617553, -1822.681762, 13.986877, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18865, 2024.059326, -1822.683593, 13.726874, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18872, 2024.339599, -1822.703369, 13.726874, 0.000000, 0.000000, 18.200000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2121, 2024.251586, -1824.113281, 12.986876, 0.000000, 0.000000, 152.299972, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2121, 2027.075439, -1826.077148, 12.981806, 0.000007, 0.000000, 269.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2121, 2027.075439, -1825.067260, 12.981806, 0.000007, 0.000000, 269.999969, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14409, 2020.687011, -1818.685913, 5.890450, 90.399955, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14409, 2020.687011, -1825.426635, 5.843400, 90.399955, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19466, 2023.420043, -1824.255737, 12.555811, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2343, 2021.363159, -1823.670776, 13.135813, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2343, 2021.363159, -1826.890747, 13.135813, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2343, 2021.363159, -1825.310424, 13.135813, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2749, 2022.707397, -1823.266113, 13.655812, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, 2022.658325, -1823.485229, 13.695811, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, 2022.790161, -1823.650512, 13.615818, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2750, 2022.484863, -1824.215454, 13.697820, 90.299934, 36.799999, 89.500007, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2749, 2022.707397, -1826.265502, 13.655812, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2751, 2022.658325, -1826.484619, 13.695811, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2752, 2022.790161, -1826.649902, 13.615818, 0.000000, 0.000007, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2750, 2022.484863, -1827.214843, 13.697820, 89.699981, 216.799987, -90.499992, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(948, 2018.860961, -1826.872558, 12.594808, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(938, 2022.502685, -1824.466308, 15.535812, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(938, 2022.502685, -1826.046630, 15.535812, 0.000015, 0.000000, 89.999954, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2418, 2016.699096, -1823.446044, 12.382812, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2418, 2015.668457, -1823.446044, 12.381812, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19923, 2016.716796, -1823.408203, 12.376871, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19519, 2015.396972, -1822.989257, 13.882811, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19519, 2015.647216, -1822.989257, 13.882811, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19811, 2015.445068, -1822.938354, 14.132812, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19811, 2015.445068, -1822.938354, 14.232811, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11744, 2015.885742, -1822.933471, 14.102819, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19519, 2017.438598, -1822.989257, 13.882811, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19519, 2017.688842, -1822.989257, 13.882811, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19811, 2017.486694, -1822.938354, 14.132812, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19811, 2017.486694, -1822.938354, 14.232811, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11744, 2017.927368, -1822.933471, 14.102819, 0.000000, 0.000022, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11722, 2017.314208, -1822.929565, 13.862815, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11722, 2017.904418, -1822.929565, 14.222819, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11722, 2015.884277, -1822.929565, 14.222819, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19927, 2017.166137, -1827.090332, 12.595618, 0.000000, 0.000000, 2070.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1977, 2014.841796, -1827.048461, 12.484156, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2762, 2016.778076, -1819.696289, 12.956877, 0.000000, 0.000015, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2436, 2012.859619, -1822.699829, 13.086872, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2435, 2011.223510, -1822.572143, 12.546875, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18632, 2013.475708, -1824.885742, 14.162816, 270.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1843, 2009.631347, -1825.778076, 12.595549, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18632, 2013.475708, -1824.885742, 13.742813, 270.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18632, 2013.475708, -1824.885742, 13.822814, 270.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18632, 2013.475708, -1824.885742, 14.212815, 270.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18632, 2013.475708, -1824.885742, 14.812816, 270.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18632, 2013.475708, -1824.885742, 14.892816, 270.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(18632, 2013.475708, -1824.885742, 15.242818, 270.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2261, 2012.750488, -1826.929931, 14.136141, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1810, 2011.033813, -1823.723754, 12.632812, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(964, 2005.027099, -1826.902343, 12.632812, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(964, 2005.027099, -1826.902343, 13.562812, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(9131, 2006.085083, -1827.143310, 13.742814, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(3633, 2007.621459, -1826.894287, 13.072812, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2063, 2008.525146, -1824.779907, 13.455845, 0.000000, 0.000000, 270.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14409, 2001.995605, -1818.685913, 5.890450, 89.599922, -179.998901, -0.001123, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(14409, 2001.995605, -1825.426635, 5.843400, 89.599922, 179.998901, -179.998901, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2121, 2003.051757, -1827.125488, 13.063621, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2254, 2003.200927, -1827.493286, 14.253616, 0.000000, 0.000000, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2121, 1999.451538, -1825.745361, 13.063621, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2121, 1997.481811, -1825.745361, 13.063621, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2121, 1995.631469, -1825.745361, 13.063621, 0.000000, 0.000000, 360.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2121, 1998.350952, -1824.065795, 13.063621, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2121, 1995.910278, -1824.065795, 13.063621, 0.000000, 0.000000, 540.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(1584, 2004.175537, -1823.795532, 12.562618, 0.000000, 0.000000, 90.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16779, 2020.507202, -1824.626098, 15.974815, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16779, 2025.427978, -1824.626098, 15.974815, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16779, 2029.938354, -1824.626098, 15.974815, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16779, 2039.439331, -1824.626098, 15.974815, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16779, 2015.799560, -1824.622192, 15.974815, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16779, 2011.299560, -1824.622192, 15.974815, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16779, 2006.699340, -1824.622192, 15.974815, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(16779, 1999.369506, -1824.622192, 15.974815, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 

    //pecel lele
    Pecellawdk = CreateDynamicObject(16101, -40.607357, -462.505584, 5.013254, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14651, "ab_trukstpd", "Bow_bar_tabletop_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(16101, -48.507339, -458.225982, 4.953250, 0.000000, 180.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14651, "ab_trukstpd", "Bow_bar_tabletop_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, -48.446292, -466.360351, 3.603269, -1.799999, 0.000000, 58.600006, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 15041, "bigsfsave", "ah_greencarp", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, -47.545677, -464.789459, 5.182490, -1.799999, 90.000000, 58.600006, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3820, "boxhses_sfsx", "LAbluewall", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, -47.532493, -464.809204, 5.133144, -1.799999, 90.000000, 58.600006, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 19267, "mapmarkers", "samporange", 0x00000000);
    Pecellawdk = CreateDynamicObject(16101, -44.824245, -472.530120, 2.473248, 0.000000, 180.000000, 32.599994, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14651, "ab_trukstpd", "Bow_bar_tabletop_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(16101, -56.015583, -465.264587, 2.443248, 0.000000, 180.000000, -40.099975, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14651, "ab_trukstpd", "Bow_bar_tabletop_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(16101, -44.806568, -470.145019, 3.653247, 0.000000, 180.000000, 32.599994, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14651, "ab_trukstpd", "Bow_bar_tabletop_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(16101, -53.695323, -464.505584, 3.273247, 0.000000, 180.000000, 32.599994, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14651, "ab_trukstpd", "Bow_bar_tabletop_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, -45.735656, -461.829284, 5.182490, -1.799993, 90.000007, 58.599994, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3820, "boxhses_sfsx", "LAbluewall", 0x00000000);
    Pecellawdk = CreateDynamicObject(19445, -45.722473, -461.849029, 5.133144, -1.799993, 90.000007, 58.599994, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 19267, "mapmarkers", "samporange", 0x00000000);
    Pecellawdk = CreateDynamicObject(19426, -47.152637, -458.303466, 4.574593, 138.299972, -87.399848, -33.099956, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3820, "boxhses_sfsx", "LAbluewall", 0x00000000);
    Pecellawdk = CreateDynamicObject(19426, -44.183025, -460.114105, 4.692467, 138.299972, -87.399848, -33.099956, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3820, "boxhses_sfsx", "LAbluewall", 0x00000000);
    Pecellawdk = CreateDynamicObject(19426, -41.933101, -461.511566, 4.788076, 138.299972, -87.399848, -33.099956, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3820, "boxhses_sfsx", "LAbluewall", 0x00000000);
    Pecellawdk = CreateDynamicObject(2047, -43.793811, -460.805511, 3.309350, 0.000007, -0.000012, 148.800018, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2047, -45.316356, -459.883422, 3.309350, 0.000007, 360.000000, 148.800018, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2047, -45.316356, -459.883422, 2.489351, 0.000007, 179.999984, 148.800018, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2047, -43.785255, -460.810577, 2.489351, 0.000007, -0.000012, 148.800018, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    Pecellawdk = CreateDynamicObject(18667, -44.518321, -460.247039, 2.903731, 0.000000, 0.000000, -120.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "SOTO LAMONGAN", 130, "Arial", 55, 1, 0xFF8C00FF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18667, -44.518321, -460.247039, 3.323730, 0.000000, 0.000000, -120.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "PECEL   LELE", 140, "Arial", 90, 1, 0xFFF13602, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18667, -44.506004, -460.265716, 3.343730, 0.000000, 0.000000, -120.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "PECEL   LELE", 140, "Arial", 90, 1, 0xFFFF0000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18667, -44.496002, -460.248382, 2.933731, 0.000000, 0.000000, -120.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "SOTO LAMONGAN", 130, "Arial", 55, 1, 0xFF00FF00, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18667, -44.461364, -460.268402, 2.513731, 0.000000, 0.000000, -120.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "SAMBEL IJO", 140, "Arial", 35, 1, 0xFF00FF00, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18667, -44.440387, -460.292083, 2.333731, 0.000000, 0.000000, -120.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "GORENG - BAKAR", 140, "Arial", 50, 1, 0xFF8C00FF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18667, -43.370185, -460.898620, 2.333731, 0.000000, 0.000000, -120.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "SATE KULIT", 140, "Arial", 35, 1, 0xFF00FF00, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18667, -45.561214, -459.633544, 2.333731, 0.000000, 0.000000, -120.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "TERONG PECAK", 140, "Arial", 35, 1, 0xFF00FF00, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18667, -43.474098, -460.838562, 2.973731, 0.000000, 0.000000, -120.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "TAHU", 140, "Arial", 35, 1, 0xFF00FF00, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18667, -43.470436, -460.852081, 2.843730, 0.000000, 0.000000, -120.000000, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "TEMPE", 140, "Arial", 35, 1, 0xFF8C00FF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18667, -45.583377, -459.618499, 2.973731, -0.000006, -0.000003, -119.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "ATI", 140, "Arial", 35, 1, 0xFF00FF00, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18667, -45.588375, -459.627014, 2.843730, -0.000006, -0.000003, -119.999977, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterialText(Pecellawdk, 0, "AMPELA", 140, "Arial", 35, 1, 0xFF8C00FF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(19940, -45.093990, -466.437042, 1.434770, -2.599998, 0.000003, 54.899936, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, -44.578041, -467.002441, 1.218016, -2.599998, 124.200004, 54.899936, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, -44.394821, -466.741790, 1.217414, -2.599998, 57.199977, 54.899936, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, -46.384307, -465.732971, 1.117763, -2.599998, 124.200012, 54.899936, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, -46.201087, -465.472320, 1.117159, -2.599998, 57.199985, 54.899936, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, -45.726211, -465.980468, 1.409371, -2.599998, 0.000003, 54.899936, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, -48.652297, -463.886871, 1.281667, -2.109023, 0.345266, 54.914108, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, -48.138648, -464.452270, 1.059522, -2.109023, 124.545272, 54.914108, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, -47.955444, -464.191619, 1.057000, -2.109023, 57.545238, 54.914108, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, -49.945865, -463.182800, 0.978188, -2.109023, 124.545280, 54.914108, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, -49.762660, -462.922149, 0.975666, -2.109023, 57.545246, 54.914108, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, -49.284751, -463.430297, 1.262891, -2.109023, 0.345266, 54.914108, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19899, -50.547149, -459.482055, 0.620501, -4.600001, -0.599992, -32.699947, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    Pecellawdk = CreateDynamicObject(19353, -51.729259, -460.739105, 0.195737, 2.800004, -0.000006, 147.600006, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    Pecellawdk = CreateDynamicObject(2762, -44.616741, -465.121246, 1.362285, 0.000000, -2.899998, -35.799999, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    Pecellawdk = CreateDynamicObject(2762, -48.155475, -462.566467, 1.191143, -2.599998, -0.399998, -35.799999, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14581, "ab_mafiasuitea", "wood02S", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, -47.162342, -461.525970, 1.113754, -2.453844, 0.836719, 54.933666, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, -46.648693, -462.093658, 0.897540, -2.453844, 125.036727, 54.933666, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, -46.465488, -461.833038, 0.892289, -2.453844, 58.036689, 54.933666, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, -48.455909, -460.825103, 0.802918, -2.453844, 125.036735, 54.933666, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, -48.272705, -460.564483, 0.797667, -2.453844, 58.036697, 54.933666, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, -47.794795, -461.069610, 1.090196, -2.453844, 0.836719, 54.933666, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, -43.722347, -463.926025, 1.323752, -2.453836, 0.836723, 54.933662, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, -43.208698, -464.493713, 1.107540, -2.453836, 125.036735, 54.933662, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, -43.025493, -464.233093, 1.102288, -2.453836, 58.036693, 54.933662, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, -45.015914, -463.225158, 1.012917, -2.453836, 125.036743, 54.933662, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19793, -44.832710, -462.964538, 1.007666, -2.453836, 58.036701, 54.933662, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 14652, "ab_trukstpa", "wood01", 0x00000000);
    Pecellawdk = CreateDynamicObject(19940, -44.354801, -463.469665, 1.300196, -2.453836, 0.836723, 54.933662, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14786, "ab_sfgymbeams", "knot_wood128", 0x00000000);
    Pecellawdk = CreateDynamicObject(2047, -43.793811, -460.805511, 3.309350, 0.000007, -0.000012, 328.800018, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2047, -45.316356, -459.883422, 3.309350, 0.000007, 360.000000, 328.800018, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2047, -45.316356, -459.883422, 2.489351, 0.000007, 179.999984, 328.800018, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    Pecellawdk = CreateDynamicObject(2047, -43.819465, -460.789825, 2.489351, 0.000007, -0.000012, 328.800018, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14668, "711c", "cj_white_wall2", 0x00000000);
    Pecellawdk = CreateDynamicObject(16101, -44.393035, -468.824401, 2.083247, 0.000000, 180.000000, 32.599994, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14651, "ab_trukstpd", "Bow_bar_tabletop_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(16101, -52.295936, -464.002929, 1.743247, 0.000000, 180.000000, 32.599994, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 14651, "ab_trukstpd", "Bow_bar_tabletop_wood", 0x00000000);
    Pecellawdk = CreateDynamicObject(19426, -48.336051, -466.413055, 4.394416, 89.099945, 148.600036, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 3820, "boxhses_sfsx", "LAbluewall", 0x00000000);
    Pecellawdk = CreateDynamicObject(18667, -48.286853, -466.327575, 4.461401, 1.368228, 0.469022, -121.407974, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "PARTAI DEMOKRAT", 100, "Arial", 25, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18667, -48.537940, -466.168884, 3.888494, 0.568266, 0.468912, -121.401443, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "KUKUH HARYANTO", 100, "Arial", 25, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(19832, -48.183227, -466.656250, 3.898092, 89.099945, 58.600036, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(19832, -48.507686, -466.458129, 3.892995, 89.099945, 58.600036, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(19832, -48.823410, -466.265319, 3.888035, 89.099945, 58.600036, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(19832, -49.138973, -466.072723, 3.883078, 89.099945, 58.600036, 89.999900, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(19562, -47.804302, -466.682037, 3.697983, -0.468890, 0.768236, 148.603027, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 0, 10765, "airportgnd_sfse", "white", 0x00000000);
    Pecellawdk = CreateDynamicObject(18667, -47.773002, -466.624053, 3.897413, 0.568266, 0.468912, -121.401443, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "4", 100, "Arial", 50, 1, 0xFF000000, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18667, -48.506122, -466.190002, 4.068872, 0.568266, 0.468912, -121.401443, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "Jangan lupa coblos no. papat!!", 100, "Arial", 15, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18667, -48.140552, -466.414306, 4.193132, 0.568266, 0.468912, -121.401443, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "MATUR NUWUN !!", 100, "Arial", 20, 1, 0xFFFFFFFF, 0x00000000, 1);
    Pecellawdk = CreateDynamicObject(18667, -48.630039, -466.111267, 3.747431, 0.568266, 0.468912, -121.401443, 0, 0, -1, 200.00, 200.00); 
    SetDynamicObjectMaterial(Pecellawdk, 1, 10765, "airportgnd_sfse", "black64", 0x00000000);
    SetDynamicObjectMaterialText(Pecellawdk, 0, "CALEG DPRD KAB. WONOGIRI DAPIL 1", 100, "Arial", 15, 1, 0xFFFFFFFF, 0x00000000, 1);
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    /////////////////////////////////////////////////////////////////////////////////////////////////////////////////
    CreateDynamicObject(19087, -44.814533, -468.576293, 5.402521, -40.600025, 0.000007, -1.100008, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19088, -44.830196, -470.153930, 3.553179, -55.700000, 0.000011, 0.800004, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19087, -52.218471, -464.075225, 5.032513, -40.600025, 0.000015, -73.799980, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19088, -53.729396, -464.529418, 3.183171, -55.700000, 0.000023, -71.899940, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19087, -46.048381, -459.424987, 2.207894, 0.000000, 64.800003, -26.100000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19087, -46.447105, -459.240661, 1.990213, 0.000000, 64.800003, -26.100000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19087, -46.100555, -459.410583, 3.692914, 0.000000, 98.900009, -26.100000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19087, -46.203945, -459.371154, 3.700052, 0.000000, 98.900009, -26.100000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19087, -40.886043, -462.265777, 1.801337, 0.000000, 98.900009, -26.100000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19087, -40.465953, -462.482666, 1.717198, 0.000000, 98.900009, -26.100000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19087, -40.921962, -462.303680, 4.250432, 0.000000, 77.099967, -26.100000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(19087, -40.495422, -462.524047, 4.350080, 0.000000, 77.099967, -26.100000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2336, -51.869285, -461.932098, 0.805854, 0.000006, 3.800003, 56.199970, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11722, -48.184513, -462.426635, 1.716307, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11722, -44.674530, -464.966674, 1.896306, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11723, -44.696048, -465.091125, 1.905336, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(11723, -48.046058, -462.480987, 1.735337, 0.000000, 0.000000, 0.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2831, -51.864757, -461.720184, 1.863085, 3.599998, -0.599999, 180.000000, 0, 0, -1, 200.00, 200.00); 
    CreateDynamicObject(2049, -47.166507, -466.989532, 4.154872, -0.468890, 0.768229, 148.603027, 0, 0, -1, 200.00, 200.00); 
}