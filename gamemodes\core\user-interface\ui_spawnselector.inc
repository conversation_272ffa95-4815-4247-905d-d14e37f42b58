new Text:SpawnSelectorTD[14];

CreateSpawnSelectorTD()
{
    SpawnSelectorTD[0] = TextDrawCreate(37.000, 90.000, "LD_BUM:blkdot");
    TextDrawTextSize(SpawnSelectorTD[0], 100.000, 19.000);
    TextDrawAlignment(SpawnSelectorTD[0], 1);
    TextDrawColor(SpawnSelectorTD[0], -72336436);
    TextDrawSetShadow(SpawnSelectorTD[0], 0);
    TextDrawSetOutline(SpawnSelectorTD[0], 0);
    TextDrawBackgroundColor(SpawnSelectorTD[0], 255);
    TextDrawFont(SpawnSelectorTD[0], 4);
    TextDrawSetProportional(SpawnSelectorTD[0], 1);

    SpawnSelectorTD[1] = TextDrawCreate(37.000, 113.500, "LD_BUM:blkdot");
    TextDrawTextSize(SpawnSelectorTD[1], 100.000, 19.000);
    TextDrawAlignment(SpawnSelectorTD[1], 1);
    TextDrawColor(SpawnSelectorTD[1], 454894591);
    TextDrawSetShadow(SpawnSelectorTD[1], 0);
    TextDrawSetOutline(SpawnSelectorTD[1], 0);
    TextDrawBackgroundColor(SpawnSelectorTD[1], 255);
    TextDrawFont(SpawnSelectorTD[1], 4);
    TextDrawSetProportional(SpawnSelectorTD[1], 1);
    TextDrawSetSelectable(SpawnSelectorTD[1], 1);

    SpawnSelectorTD[2] = TextDrawCreate(37.000, 137.000, "LD_BUM:blkdot");
    TextDrawTextSize(SpawnSelectorTD[2], 100.000, 19.000);
    TextDrawAlignment(SpawnSelectorTD[2], 1);
    TextDrawColor(SpawnSelectorTD[2], 454894591);
    TextDrawSetShadow(SpawnSelectorTD[2], 0);
    TextDrawSetOutline(SpawnSelectorTD[2], 0);
    TextDrawBackgroundColor(SpawnSelectorTD[2], 255);
    TextDrawFont(SpawnSelectorTD[2], 4);
    TextDrawSetProportional(SpawnSelectorTD[2], 1);
    TextDrawSetSelectable(SpawnSelectorTD[2], 1);

    SpawnSelectorTD[3] = TextDrawCreate(37.000, 160.500, "LD_BUM:blkdot");
    TextDrawTextSize(SpawnSelectorTD[3], 100.000, 19.000);
    TextDrawAlignment(SpawnSelectorTD[3], 1);
    TextDrawColor(SpawnSelectorTD[3], 454894591);
    TextDrawSetShadow(SpawnSelectorTD[3], 0);
    TextDrawSetOutline(SpawnSelectorTD[3], 0);
    TextDrawBackgroundColor(SpawnSelectorTD[3], 255);
    TextDrawFont(SpawnSelectorTD[3], 4);
    TextDrawSetProportional(SpawnSelectorTD[3], 1);
    TextDrawSetSelectable(SpawnSelectorTD[3], 1);

    SpawnSelectorTD[4] = TextDrawCreate(37.000, 184.000, "LD_BUM:blkdot");
    TextDrawTextSize(SpawnSelectorTD[4], 100.000, 19.000);
    TextDrawAlignment(SpawnSelectorTD[4], 1);
    TextDrawColor(SpawnSelectorTD[4], 454894591);
    TextDrawSetShadow(SpawnSelectorTD[4], 0);
    TextDrawSetOutline(SpawnSelectorTD[4], 0);
    TextDrawBackgroundColor(SpawnSelectorTD[4], 255);
    TextDrawFont(SpawnSelectorTD[4], 4);
    TextDrawSetProportional(SpawnSelectorTD[4], 1);
    TextDrawSetSelectable(SpawnSelectorTD[4], 1);

    SpawnSelectorTD[5] = TextDrawCreate(37.000, 207.500, "LD_BUM:blkdot");
    TextDrawTextSize(SpawnSelectorTD[5], 100.000, 19.000);
    TextDrawAlignment(SpawnSelectorTD[5], 1);
    TextDrawColor(SpawnSelectorTD[5], 454894591);
    TextDrawSetShadow(SpawnSelectorTD[5], 0);
    TextDrawSetOutline(SpawnSelectorTD[5], 0);
    TextDrawBackgroundColor(SpawnSelectorTD[5], 255);
    TextDrawFont(SpawnSelectorTD[5], 4);
    TextDrawSetProportional(SpawnSelectorTD[5], 1);
    TextDrawSetSelectable(SpawnSelectorTD[5], 1);

    SpawnSelectorTD[6] = TextDrawCreate(37.000, 231.000, "LD_BUM:blkdot");
    TextDrawTextSize(SpawnSelectorTD[6], 100.000, 19.000);
    TextDrawAlignment(SpawnSelectorTD[6], 1);
    TextDrawColor(SpawnSelectorTD[6], -72336436);
    TextDrawSetShadow(SpawnSelectorTD[6], 0);
    TextDrawSetOutline(SpawnSelectorTD[6], 0);
    TextDrawBackgroundColor(SpawnSelectorTD[6], 255);
    TextDrawFont(SpawnSelectorTD[6], 4);
    TextDrawSetProportional(SpawnSelectorTD[6], 1);
    TextDrawSetSelectable(SpawnSelectorTD[6], 1);

    SpawnSelectorTD[7] = TextDrawCreate(87.000, 95.000, "Pilih lokasi spawn");
    TextDrawLetterSize(SpawnSelectorTD[7], 0.190, 0.899);
    TextDrawAlignment(SpawnSelectorTD[7], 2);
    TextDrawColor(SpawnSelectorTD[7], -1);
    TextDrawSetShadow(SpawnSelectorTD[7], 1);
    TextDrawSetOutline(SpawnSelectorTD[7], 1);
    TextDrawBackgroundColor(SpawnSelectorTD[7], 0);
    TextDrawFont(SpawnSelectorTD[7], 1);
    TextDrawSetProportional(SpawnSelectorTD[7], 1);

    SpawnSelectorTD[8] = TextDrawCreate(87.000, 118.500, "Bandara Kota Arivena");
    TextDrawLetterSize(SpawnSelectorTD[8], 0.190, 0.899);
    TextDrawAlignment(SpawnSelectorTD[8], 2);
    TextDrawColor(SpawnSelectorTD[8], -1);
    TextDrawSetShadow(SpawnSelectorTD[8], 1);
    TextDrawSetOutline(SpawnSelectorTD[8], 1);
    TextDrawBackgroundColor(SpawnSelectorTD[8], 0);
    TextDrawFont(SpawnSelectorTD[8], 1);
    TextDrawSetProportional(SpawnSelectorTD[8], 1);

    SpawnSelectorTD[9] = TextDrawCreate(87.000, 142.000, "Pelabuhan Kota Arivena");
    TextDrawLetterSize(SpawnSelectorTD[9], 0.190, 0.899);
    TextDrawAlignment(SpawnSelectorTD[9], 2);
    TextDrawColor(SpawnSelectorTD[9], -1);
    TextDrawSetShadow(SpawnSelectorTD[9], 1);
    TextDrawSetOutline(SpawnSelectorTD[9], 1);
    TextDrawBackgroundColor(SpawnSelectorTD[9], 0);
    TextDrawFont(SpawnSelectorTD[9], 1);
    TextDrawSetProportional(SpawnSelectorTD[9], 1);

    SpawnSelectorTD[10] = TextDrawCreate(87.000, 165.500, "Rusun Sewaan");
    TextDrawLetterSize(SpawnSelectorTD[10], 0.190, 0.899);
    TextDrawAlignment(SpawnSelectorTD[10], 2);
    TextDrawColor(SpawnSelectorTD[10], -1);
    TextDrawSetShadow(SpawnSelectorTD[10], 1);
    TextDrawSetOutline(SpawnSelectorTD[10], 1);
    TextDrawBackgroundColor(SpawnSelectorTD[10], 0);
    TextDrawFont(SpawnSelectorTD[10], 1);
    TextDrawSetProportional(SpawnSelectorTD[10], 1);

    SpawnSelectorTD[11] = TextDrawCreate(87.000, 189.000, "Rumah");
    TextDrawLetterSize(SpawnSelectorTD[11], 0.190, 0.899);
    TextDrawAlignment(SpawnSelectorTD[11], 2);
    TextDrawColor(SpawnSelectorTD[11], -1);
    TextDrawSetShadow(SpawnSelectorTD[11], 1);
    TextDrawSetOutline(SpawnSelectorTD[11], 1);
    TextDrawBackgroundColor(SpawnSelectorTD[11], 0);
    TextDrawFont(SpawnSelectorTD[11], 1);
    TextDrawSetProportional(SpawnSelectorTD[11], 1);

    SpawnSelectorTD[12] = TextDrawCreate(87.000, 212.500, "Kantor Fraksi");
    TextDrawLetterSize(SpawnSelectorTD[12], 0.190, 0.899);
    TextDrawAlignment(SpawnSelectorTD[12], 2);
    TextDrawColor(SpawnSelectorTD[12], -1);
    TextDrawSetShadow(SpawnSelectorTD[12], 1);
    TextDrawSetOutline(SpawnSelectorTD[12], 1);
    TextDrawBackgroundColor(SpawnSelectorTD[12], 0);
    TextDrawFont(SpawnSelectorTD[12], 1);
    TextDrawSetProportional(SpawnSelectorTD[12], 1);

    SpawnSelectorTD[13] = TextDrawCreate(87.000, 236.000, "> Tombol Spawn <");
    TextDrawLetterSize(SpawnSelectorTD[13], 0.190, 0.899);
    TextDrawAlignment(SpawnSelectorTD[13], 2);
    TextDrawColor(SpawnSelectorTD[13], -1);
    TextDrawSetShadow(SpawnSelectorTD[13], 1);
    TextDrawSetOutline(SpawnSelectorTD[13], 1);
    TextDrawBackgroundColor(SpawnSelectorTD[13], 0);
    TextDrawFont(SpawnSelectorTD[13], 1);
    TextDrawSetProportional(SpawnSelectorTD[13], 1);
}

ShowSpawnSelectorTD(playerid)
{
    HideHBETD(playerid);
	HideServerNameTD(playerid);

    for(new x; x < 14; x++)
    {
        TextDrawShowForPlayer(playerid, SpawnSelectorTD[x]);
    }

    SelectTextDraw(playerid, 0xb159ddcc);
    SendClientMessage(playerid, Y_SERVER, "(Server) "WHITE"Gunakan "CMDEA"'/cursor' "WHITE"jika mouse hilang dari layar/textdraw tidak bisa ditekan!");
}

HideSpawnSelectorTD(playerid)
{
    for(new x; x < 14; x++)
    {
        TextDrawHideForPlayer(playerid, SpawnSelectorTD[x]);
    }
    
    ShowHBETD(playerid);
	ShowServerNameTD(playerid);

    CancelSelectTextDraw(playerid);
}