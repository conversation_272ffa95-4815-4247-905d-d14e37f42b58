/*
Legal:
	Version: MPL 1.1
	
	The contents of this file are subject to the Mozilla Public License Version 
	1.1 the "License"; you may not use this file except in compliance with 
	the License. You may obtain a copy of the License at 
	http://www.mozilla.org/MPL/
	
	Software distributed under the License is distributed on an "AS IS" basis,
	WITHOUT WARRANTY OF ANY KIND, either express or implied. See the License
	for the specific language governing rights and limitations under the
	License.
	
	The Original Code is the YSI framework.
	
	The Initial Developer of the Original Code is Alex "Y_Less" Cole.
	Portions created by the Initial Developer are Copyright (c) 2022
	the Initial Developer. All Rights Reserved.

Contributors:
	Y_Less
	koolk
	JoeBullet/Google63
	g_aSlice/Slice
	Misiur
	samphunter
	tianmeta
	maddinat0r
	spacemud
	Crayder
	Dayvison
	Ahmad45123
	Zeex
	irinel1996
	Yiin-
	Chaprnks
	Konstantinos
	Masterchen09
	Southclaws
	PatchwerkQWER
	m0k1
	paulommu
	udan111
	Cheaterman

Thanks:
	JoeBullet/Google63 - Handy arbitrary ASM jump code using SCTRL.
	ZeeX - Very productive conversations.
	koolk - IsPlayerinAreaEx code.
	TheAlpha - Danish translation.
	breadfish - German translation.
	Fireburn - Dutch translation.
	yom - French translation.
	50p - Polish translation.
	Zamaroht - Spanish translation.
	Los - Portuguese translation.
	Dracoblue, sintax, mabako, Xtreme, other coders - Producing other modes for
		me to strive to better.
	Pixels^ - Running XScripters where the idea was born.
	Matite - Pestering me to release it and using it.

Very special thanks to:
	Thiadmer - PAWN, whose limits continue to amaze me!
	Kye/Kalcor - SA:MP.
	SA:MP Team past, present and future - SA:MP.

Optional plugins:
	Gamer_Z - GPS.
	Incognito - Streamer.
	Me - sscanf2, fixes2, Whirlpool.
*/

static
	YSI_g_sValue = 0;

@lctrl(*********) YSI_LCTRL(pri, alt)
{
	#pragma unused pri, alt
	return YSI_g_sValue;
}

@sctrl(*********) YSI_SCTRL(pri, alt)
{
	YSI_g_sValue = pri + alt;
}

@test(.group = "y_ctrl") LCTRL_SCTRL()
{
	new p0, a0, p1, a1;
	ASSERT_EQ(YSI_g_sValue, 0);
	ASSERT_EQ(a0, 0);
	ASSERT_EQ(p0, 0);
	ASSERT_EQ(a1, 0);
	ASSERT_EQ(p1, 0);
	#emit CONST.pri 4
	#emit CONST.alt 5
	#emit SCTRL *********
	#emit STOR.S.pri p0
	#emit STOR.S.alt a0
	ASSERT_EQ(YSI_g_sValue, 9);
	ASSERT_EQ(a0, 5);
	ASSERT_EQ(p0, 4);
	ASSERT_EQ(a1, 0);
	ASSERT_EQ(p1, 0);
	#emit CONST.pri 1
	#emit CONST.alt 2
	#emit LCTRL *********
	#emit STOR.S.pri p1
	#emit STOR.S.alt a1
	ASSERT_EQ(YSI_g_sValue, 9);
	ASSERT_EQ(a0, 5);
	ASSERT_EQ(p0, 4);
	ASSERT_EQ(a1, 2);
	ASSERT_EQ(p1, 9);
}

